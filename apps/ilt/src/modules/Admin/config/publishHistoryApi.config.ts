import { handleQueryStringForApi } from '@mindtickle/api-helpers';

let apiUrls = {
  getHistory({ companyId, moduleId }: { companyId: string; moduleId: string }) {
    return {
      url: `/wapi/company/${companyId}/ilt/${moduleId}/publish_history`,
      mock: 'publishHistory',
      mockType: 'success',
    };
  },
  getSummary({ companyId, moduleId }: { companyId: string; moduleId: string }) {
    return {
      url: `/wapi/company/${companyId}/ilt/${moduleId}/publish_summary`,
      mock: 'publishSummary',
      mockType: 'success',
    };
  },
  discardChanges({ companyId, moduleId }: { companyId: string; moduleId: string }) {
    return {
      url: `/wapi/company/${companyId}/ilt/${moduleId}/discard_unpublished_changes`,
      mock: 'discardUnpublished',
      mockType: 'success',
    };
  },
  getInvitedLearnerCounts({ companyId }: { companyId: string }) {
    return {
      url: `/${companyId}/learners/module_states`,
    };
  },
  publishChanges({ companyId, moduleId }: { companyId: string; moduleId: string }) {
    return {
      url: `/wapi/company/${companyId}/ilt/${moduleId}/publishILT`,
    };
  },
  getTodos({ companyId, moduleId }: { companyId: string; moduleId: string }) {
    return {
      url: `/wapi/company/${companyId}/ilt/${moduleId}/todos`,
      mock: 'getTodos',
      mockType: 'success',
    };
  },
  editSummary({ companyId, moduleId }: { companyId: string; moduleId: string }) {
    return {
      url: `/wapi/company/${companyId}/ilt/${moduleId}/edit_summary`,
      mock: 'draftSummary',
      mockType: 'success',
    };
  },
};

export default handleQueryStringForApi(apiUrls);
