import moment from 'moment';

import {
  WATCHED_RECORDING_TYPES,
  COMPLETION_STATUS_TYPES,
  ENROLL_DRAWER_BOTTOM_MENU,
  ENROLL_DRAWER_LISTING_MENU,
} from '~/modules/Admin/constants/track';
import { defaultDateFormatter } from '~/utils';

import { ILT_ENTITIES } from './constants';

// TODO:(Akhil) Make this file as similat for sessions.constants.js, i.e. move constants to constant file and move messages to messages,
// combine them in this file so that all the imports from this file will not have to make any change

export { WATCHED_RECORDING_TYPES };
export { COMPLETION_STATUS_TYPES };

export const SUPPORTED_FILTERS = {
  GROUPS: 'applyGroups',
  SEARCH: 'search',
  ENROLLEMENT_STATUS: 'enrollmentStatus',
  INVITED_ON: 'invitedOn',
  ATTENDED: 'attended',
  MODULE_RELEVANCE: 'moduleRelevance',
} as const;

export const TABLE_TYPES = {
  UPCOMING_SESSIONS: 'upcomingSessions',
  INDIVIDUAL_SESSION: 'individualSession',
};

export const SUPPORTED_SORTINGS = {
  NAME: 'name',
  INVITED_ON: 'invitedOn',
  JOINED_ON: 'joinedOn',
  SESSION_SCORE: 'score',
  ILT_SCORE: 'iltScore',
  MODULE_RELEVANCE: 'moduleRelevance',
};

export const ENROLLMENT_STATUS_TYPES = {
  DID_NOT_ENROLLED: 'didNotEnroll',
  ENROLLED: 'enrolled',
  WAITING: 'waiting',
  ENROLLED_BUT_CANCELLED: 'enrolled_but_cancelled',
  WAITING_BUT_CANCELLED: 'waiting_but_cancelled',
} as const;

export const ATTENDED_TYPES = {
  DID_NOT_ATTEND: 'didNotAttend',
  ATTENDED: 'attended',
  UNMARKED: 'unmarked',
};

export const ENROLLMENT_STATUS = {
  [ENROLLMENT_STATUS_TYPES.DID_NOT_ENROLLED]: {
    displayValue: 'Did Not Enroll',
    value: 'NOT_ENROLLED',
    apiOperation: 'UN_ENROLL',
    bulkTitle: 'Are you sure you want to remove the selected Learners from these Sessions?',
    chooseSession: false,
    icon: 'delete',
    mixpanel: 'Did Not Enroll',
    ddDisplayValue: 'Did Not Enroll',
  },
  [ENROLLMENT_STATUS_TYPES.ENROLLED]: {
    displayValue: 'Enrolled',
    value: 'ENROLLED',
    apiOperation: 'ENROLL',
    bulkTitle: 'Enroll selected Learners to a Session.',
    chooseSession: true,
    icon: 'tick',
    mixpanel: 'Enrolled',
    ddDisplayValue: 'Enrolled',
  },
  [ENROLLMENT_STATUS_TYPES.WAITING]: {
    displayValue: 'Waiting',
    value: 'WAITING',
    apiOperation: 'MOVE_TO_WAITING',
    bulkTitle: 'Move selected Learners to the Waiting List of following Session:',
    chooseSession: true,
    icon: 'reminder',
    mixpanel: 'Waiting',
    ddDisplayValue: 'Waiting',
  },
  [ENROLLMENT_STATUS_TYPES.ENROLLED_BUT_CANCELLED]: {
    displayValue: 'Enrolled',
    value: 'ENROLLED_BUT_CANCELLED',
    apiOperation: 'NONE',
    bulkTitle: 'Enrolled in a canceled session:',
    chooseSession: false,
    icon: 'reminder',
    mixpanel: 'Enrolled_Cancelled',
    ddDisplayValue: 'Change Session',
  },
  [ENROLLMENT_STATUS_TYPES.WAITING_BUT_CANCELLED]: {
    displayValue: 'Waiting',
    value: 'WAITING_BUT_CANCELLED',
    apiOperation: 'NONE',
    bulkTitle: 'Waiting in a canceled session:',
    chooseSession: false,
    icon: 'reminder',
    mixpanel: 'Waiting_Cancelled',
    ddDisplayValue: 'Change Session',
  },
};
export const ATTENDANCE = {
  [ATTENDED_TYPES.DID_NOT_ATTEND]: {
    displayValue: 'Did not attend',
    value: 'NOT_ATTENDED',
    bulkTitle: 'Are you sure you want to mark the selected Learners as did not attend?',
    icon: 'not-attended',
  },
  [ATTENDED_TYPES.ATTENDED]: {
    displayValue: 'Attended',
    value: 'ATTENDED',
    bulkTitle: 'Are you sure you want to mark the selected Learners as attended?',
    icon: 'attended',
  },
  [ATTENDED_TYPES.UNMARKED]: {
    displayValue: 'Unmarked',
    value: 'ATTENDANCE_NOT_MARKED',
  },
};
const ENROLLMENT_STATUS_DD_OPTIONS_CANCELLED = [
  {
    title: 'Enroll to Session:',
    titleForSelectedSession: 'Enroll in this session',
    operation: ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.ENROLLED].apiOperation,
    chooseSession: ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.ENROLLED].chooseSession,
    type: ENROLLMENT_STATUS_TYPES.ENROLLED,
  },
  {
    title: 'Add to Waiting List of the Session:',
    titleForSelectedSession: 'Add to Waiting List in this Session',
    operation: ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.WAITING].apiOperation,
    chooseSession: ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.WAITING].chooseSession,
    type: ENROLLMENT_STATUS_TYPES.WAITING,
  },
];

export const ENROLLMENT_STATUS_DD_OPTIONS = {
  [ENROLLMENT_STATUS_TYPES.DID_NOT_ENROLLED]: [
    {
      title: 'Enroll to Session:',
      titleForSelectedSession: 'Enroll in this session',
      operation: ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.ENROLLED].apiOperation,
      chooseSession: ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.ENROLLED].chooseSession,
      type: ENROLLMENT_STATUS_TYPES.ENROLLED,
    },
    {
      title: 'Add to Waiting List of the Session:',
      titleForSelectedSession: 'Add to Waiting List in this Session',
      operation: ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.WAITING].apiOperation,
      chooseSession: ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.WAITING].chooseSession,
      type: ENROLLMENT_STATUS_TYPES.WAITING,
    },
  ],
  [ENROLLMENT_STATUS_TYPES.ENROLLED]: [
    {
      title: 'Unenroll from the enrolled Session:',
      titleForSelectedSession: 'Remove from this Session',
      operation: ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.DID_NOT_ENROLLED].apiOperation,
      chooseSession: ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.DID_NOT_ENROLLED].chooseSession,
      type: ENROLLMENT_STATUS_TYPES.DID_NOT_ENROLLED,
    },
    {
      title: 'Change Session:',
      titleForSelectedSession: 'Enroll in this Session',
      operation: ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.ENROLLED].apiOperation,
      chooseSession: ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.ENROLLED].chooseSession,
      type: ENROLLMENT_STATUS_TYPES.ENROLLED,
    },
    {
      title: 'Add to Waiting List of the Session:',
      titleForSelectedSession: 'Add to Waiting List in this Session',
      operation: ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.WAITING].apiOperation,
      chooseSession: ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.WAITING].chooseSession,
      type: ENROLLMENT_STATUS_TYPES.WAITING,
    },
  ],
  [ENROLLMENT_STATUS_TYPES.WAITING]: [
    {
      title: 'Enroll to Session:',
      titleForSelectedSession: 'Enroll in this Session',
      operation: ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.ENROLLED].apiOperation,
      chooseSession: ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.ENROLLED].chooseSession,
      type: ENROLLMENT_STATUS_TYPES.ENROLLED,
    },
    {
      title: 'Remove from the waiting',
      titleForSelectedSession: 'Remove from this Session',
      operation: ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.DID_NOT_ENROLLED].apiOperation,
      chooseSession: ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.DID_NOT_ENROLLED].chooseSession,
      type: ENROLLMENT_STATUS_TYPES.DID_NOT_ENROLLED,
    },
  ],
  [ENROLLMENT_STATUS_TYPES.ENROLLED_BUT_CANCELLED]: ENROLLMENT_STATUS_DD_OPTIONS_CANCELLED,
  [ENROLLMENT_STATUS_TYPES.WAITING_BUT_CANCELLED]: ENROLLMENT_STATUS_DD_OPTIONS_CANCELLED,
};

export const ENROLLMENT_STATUS_DD_OPTIONS_PAST_SSN = {
  [ENROLLMENT_STATUS_TYPES.ENROLLED]: [
    {
      title: 'Unenroll from this Session',
      titleForSelectedSession: 'Remove from this Session',
      operation: ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.DID_NOT_ENROLLED].apiOperation,
      type: ENROLLMENT_STATUS_TYPES.DID_NOT_ENROLLED,
    },
    {
      title: 'Enroll to New Session',
      titleForSelectedSession: 'Enroll to New Session',
      operation: ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.ENROLLED].apiOperation,
      chooseSession: ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.ENROLLED].chooseSession,
      type: ENROLLMENT_STATUS_TYPES.ENROLLED,
    },
    {
      title: 'Add to Waiting List of the Session:',
      titleForSelectedSession: 'Add to Waiting List in this Session',
      operation: ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.WAITING].apiOperation,
      chooseSession: ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.WAITING].chooseSession,
      type: ENROLLMENT_STATUS_TYPES.WAITING,
    },
  ],
  [ENROLLMENT_STATUS_TYPES.WAITING]: [
    {
      title: 'Enroll to New Session',
      titleForSelectedSession: 'Enroll to New Session',
      operation: ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.ENROLLED].apiOperation,
      chooseSession: ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.ENROLLED].chooseSession,
      type: ENROLLMENT_STATUS_TYPES.ENROLLED,
    },
    {
      title: 'Remove from the waiting',
      titleForSelectedSession: 'Remove from this Session',
      operation: ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.DID_NOT_ENROLLED].apiOperation,
      type: ENROLLMENT_STATUS_TYPES.DID_NOT_ENROLLED,
    },
  ],
  [ENROLLMENT_STATUS_TYPES.ENROLLED_BUT_CANCELLED]: ENROLLMENT_STATUS_DD_OPTIONS_CANCELLED,
  [ENROLLMENT_STATUS_TYPES.WAITING_BUT_CANCELLED]: ENROLLMENT_STATUS_DD_OPTIONS_CANCELLED,
};

export const ENROLLMENT_STATUS_FOR_SESSION_DD_OPTIONS = {
  [ENROLLMENT_STATUS_TYPES.DID_NOT_ENROLLED]: [
    {
      title: 'Enroll in this Session',
      operation: ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.ENROLLED].apiOperation,
      type: ENROLLMENT_STATUS_TYPES.ENROLLED,
    },
    {
      title: 'Add to Waiting List in this Session',
      operation: ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.WAITING].apiOperation,
      type: ENROLLMENT_STATUS_TYPES.WAITING,
    },
  ],
  [ENROLLMENT_STATUS_TYPES.ENROLLED]: [
    {
      title: 'Remove from this Session',
      operation: ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.DID_NOT_ENROLLED].apiOperation,
      type: ENROLLMENT_STATUS_TYPES.DID_NOT_ENROLLED,
    },
    {
      title: 'Add to Waiting List in this Session',
      operation: ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.WAITING].apiOperation,
      type: ENROLLMENT_STATUS_TYPES.WAITING,
    },
  ],
  [ENROLLMENT_STATUS_TYPES.WAITING]: [
    {
      title: 'Enroll in this Session',
      operation: ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.ENROLLED].apiOperation,
      type: ENROLLMENT_STATUS_TYPES.ENROLLED,
    },
    {
      title: 'Remove from this Session',
      operation: ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.DID_NOT_ENROLLED].apiOperation,
      type: ENROLLMENT_STATUS_TYPES.DID_NOT_ENROLLED,
    },
  ],
  [ENROLLMENT_STATUS_TYPES.ENROLLED_BUT_CANCELLED]: ENROLLMENT_STATUS_DD_OPTIONS_CANCELLED,
  [ENROLLMENT_STATUS_TYPES.WAITING_BUT_CANCELLED]: ENROLLMENT_STATUS_DD_OPTIONS_CANCELLED,
};

export const MANAGE_UPCOMING_SESSIONS_FILTERS = [
  SUPPORTED_FILTERS.INVITED_ON,
  SUPPORTED_FILTERS.GROUPS,
  // SUPPORTED_FILTERS.ENROLLEMENT_STATUS,
  SUPPORTED_FILTERS.ATTENDED,
  SUPPORTED_FILTERS.MODULE_RELEVANCE,
  SUPPORTED_FILTERS.SEARCH,
];
export const MANAGE_SESSIONS_FILTERS = [
  SUPPORTED_FILTERS.GROUPS,
  SUPPORTED_FILTERS.ENROLLEMENT_STATUS,
  SUPPORTED_FILTERS.SEARCH,
];

export const WATCHED_RECORDINGS_STATUS = {
  [WATCHED_RECORDING_TYPES.COMPLETED]: {
    displayValue: 'Yes',
    value: 'COMPLETED',
    mixpanel: 'Watched',
    ddDisplayValue: 'Watched',
    showInDropdown: true,
  },
  [WATCHED_RECORDING_TYPES.NOT_STARTED]: {
    displayValue: 'No',
    value: 'NOT_STARTED',
    mixpanel: 'Did not watch',
    ddDisplayValue: 'Did not watch',
    showInDropdown: true,
  },
  [WATCHED_RECORDING_TYPES.IN_PROGRESS]: {
    displayValue: 'In progress',
    value: 'IN_PROGRESS',
    mixpanel: 'In progress',
    ddDisplayValue: 'In progress',
    showInDropdown: true,
  },
  [WATCHED_RECORDING_TYPES.NOT_APPLICABLE]: {
    displayValue: 'N/A',
    value: 'NOT_APPLICABLE',
    mixpanel: 'N/A',
    ddDisplayValue: 'N/A',
  },
};

export const COMPLETION_STATUS = {
  [COMPLETION_STATUS_TYPES.COMPLETED]: {
    displayValue: 'Yes',
    value: 'COMPLETED',
    mixpanel: 'Completed',
    ddDisplayValue: 'Completed',
    showInDropdown: true,
  },
  [COMPLETION_STATUS_TYPES.NOT_COMPLETED]: {
    displayValue: 'No',
    value: 'NOT_COMPLETED',
    mixpanel: 'Not completed',
    ddDisplayValue: 'Not completed',
    showInDropdown: true,
  },
  [COMPLETION_STATUS_TYPES.NOT_APPLICABLE]: {
    displayValue: 'N/A',
    value: 'NOT_APPLICABLE',
    mixpanel: 'N/A',
    ddDisplayValue: 'N/A',
    showInDropdown: false,
  },
};

export const OPERATIONS = {
  GET_UPDATED_LEARNERS: 'getUpdatedLearners',
  SORT_LEARNERS: 'sortLearners',
  SEARCH_LEARNERS: 'searchLearners',
  PAGINATE_LEARNERS: 'paginateLearners',
  GET_LEARNERS: 'getLearners',
  SEND_REMINDERS: 'sendReminders',
  REMOVE_LEARNERS: 'removeLearners',
  VIEW_LEARNER_PROFILE: 'viewProfile',
  GET_GROUPS: 'getGroups',
  GET_POLL_STATUS: 'getPollStatus',
  MARK_ATTENDANCE: 'markAttendance',
  CHANGE_LEARNER_STATUS: 'changeLearnerStatus',
  CHANGE_SCORE: 'changeScore',
  CHANGE_RELEVANCE: 'changeRelevance',
  EXPORT_LEARNERS: 'exportLearners',
  LOAD_MORE: 'loadMore',
  GET: 'get',
};

export const DEFAULT_PAGINATION = {
  start: 0,
  rows: 100,
};

export const SUPPORTED_SORTINGS_API_KEY = {
  [SUPPORTED_SORTINGS.NAME]: 'NAME',
  [SUPPORTED_SORTINGS.INVITED_ON]: 'INVITED_ON',
  [SUPPORTED_SORTINGS.JOINED_ON]: 'ENROLMENT_STATUS_UPDATE_TIME',
  [SUPPORTED_SORTINGS.SESSION_SCORE]: 'SESSION_SCORE',
  [SUPPORTED_SORTINGS.ILT_SCORE]: 'ENTITY_SCORE',
};

export const DD_OPTION_VALUE_ALL = 'all';

export const DD_OPTIONS_ATTENDED = [
  {
    value: DD_OPTION_VALUE_ALL,
    text: 'Attendance Status - All',
  },
  {
    value: true,
    text: 'Session Attended',
  },
  {
    value: false,
    text: 'Session Not Attended',
  },
];

export const DD_OPTIONS_RELEVANCE = [
  {
    value: 'REQ',
    text: 'Required',
  },
  {
    value: 'OPT',
    text: 'Optional',
  },
  {
    value: 'NONE',
    text: 'Unmarked',
  },
];

export const RELEVANCE_TYPE = {
  REQ: 'REQ',
  OPT: 'OPT',
  NONE: 'NONE',
} as const;

export const DD_OPTIONS_ENROLLEMENT_STATUS = (() => {
  let options = Object.values(ENROLLMENT_STATUS).map(status => ({
    text: status.displayValue,
    value: status.value,
  }));
  options.unshift({
    text: 'Enrollment Status- All',
    value: '',
  });
  options = options.filter(
    option =>
      option.value !== ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.DID_NOT_ENROLLED].value &&
      option.value !== ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.ENROLLED_BUT_CANCELLED].value &&
      option.value !== ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.WAITING_BUT_CANCELLED].value
  );
  return options;
})();

export const DD_OPTIONS_ENROLLEMENT_STATUS_FOR_CANCELLED = (() => {
  let options = Object.values(ENROLLMENT_STATUS).map(status => ({
    text: status.displayValue,
    value: status.value,
  }));
  options.unshift({
    text: 'Enrollment Status- All',
    value: '',
  });
  options = options.filter(
    option =>
      option.value !== ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.DID_NOT_ENROLLED].value &&
      option.value !== ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.ENROLLED].value &&
      option.value !== ENROLLMENT_STATUS[ENROLLMENT_STATUS_TYPES.WAITING].value
  );
  return options;
})();

export const SORTING_ORDER = {
  DESC: 'desc',
  ASC: 'asc',
};

export const DEFAULT_SORT = {
  type: SUPPORTED_SORTINGS.NAME,
  order: SORTING_ORDER.ASC,
};
export const DEBOUNCE_TIME = 200;

export const MESSAGES = {
  [OPERATIONS.REMOVE_LEARNERS]: {
    LOADING() {
      return 'Removing Learners ...';
    },
    SUCCESS({ successCount, failCount }: { successCount: number; failCount: number }) {
      if (failCount === 0) {
        return `${successCount} Learner${successCount > 1 ? 's' : ''} removed successfully`;
      } else {
        return `${successCount} Learner${
          successCount > 1 ? 's' : ''
        } removed successfully. ${failCount} Learner${
          failCount > 1 ? 's' : ''
        } could not be removed.`;
      }
    },
    ERROR() {
      return `Learners could not be removed from the module. Please try again later. `;
    },
  },
  [OPERATIONS.CHANGE_RELEVANCE]: {
    LOADING() {
      return 'Changing module relevance For Learners ...';
    },
    SUCCESS() {
      return `Changed module relevance of selected learner(s)`;
    },
    ERROR() {
      return `Could not change module relevance For learner(s). Please try again later. `;
    },
  },
  INFO: {
    UPCOMING_SESSION_ATTENDANCE:
      'You will be able to mark the attendance of enrolled learners after the session is over.',
    UPCOMING_EVENT_ATTENDANCE:
      'You can mark the attendance for enrolled learners once the event is over.',
    AUTO_ENROLL:
      'Automatic enrollment is enabled, changing enrollment status manually is disabled.',
    WAITING_LIST_DISABLED: 'Waiting List is disabled',
    PARTIAL_FULL: 'Not enough seats left',
    SEATS_FULL: 'Seats full',
  },
};

export const MINDTICKLE_SAMPLE_COLUMNS = [
  {
    column: 'Name',
    key: 'name',
  },
  {
    column: 'Username',
    key: 'email',
  },
  {
    column: 'Session name',
    key: 'sessionName',
  },
  {
    column: 'Attended',
    key: 'attendedStatus',
  },
];

export const MINDTICKLE_EVENT_SAMPLE_COLUMNS = [
  {
    column: 'Name',
    key: 'name',
  },
  {
    column: 'Username',
    key: 'email',
  },
  {
    column: 'Event name',
    key: 'eventName',
  },
  {
    column: 'Attended',
    key: 'attendedStatus',
  },
];

export const SUPPORTED_ATTENDED_VALUE_FOR_BULK = {
  YES: ['Yes', 'yes', 'YES'],
  NO: ['No', 'NO', 'no'],
};

export const EXPORT_ATTENDANCE_INSTRUCTIONS = `This is an Excel with a list of Enroled Learners in a Session. Admin can use this Excel as a roster for an upcoming Session, as well as to upload attendance for enroled Learners in a past Session.
> There are 4 columns in the Enrollment List sheet - Name, Username, Session Name, Attended.
> To use it as a roster, this sheet can be printed and handed over to the class coordinator.
> To use it for uploading attendance in bulk, please fill the 'Attended' column with ${SUPPORTED_ATTENDED_VALUE_FOR_BULK.YES[0]} or ${SUPPORTED_ATTENDED_VALUE_FOR_BULK.NO[0]} and upload it in a Session. If you do not want to mark attendance for any Learner, please leave the corresponding 'Attended' row blank. Please make sure that the Username column is populated for each learner.
`;

export const EXPORT_EVENT_ATTENDANCE_INSTRUCTIONS = `This is an Excel with a list of Enroled Learners in an event. Admin can use this Excel as a roster for an event, as well as to upload attendance for Learners in a past Session.
> There are 4 columns in the Enrollment List sheet - Name, Username, Event Name, Attended.
> To use it as a roster, this sheet can be printed and handed over to the class coordinator.
> To use it for uploading attendance in bulk, please fill the 'Attended' column with ${SUPPORTED_ATTENDED_VALUE_FOR_BULK.YES[0]} or ${SUPPORTED_ATTENDED_VALUE_FOR_BULK.NO[0]} and upload it in an event. If you do not want to mark attendance for any Learner, please leave the corresponding 'Attended' row blank. Please make sure that the Username column is populated for each learner.
`;

export const ENROLL_DRAWER_SUPPORTED_SORTING = {
  SESSION_NAME: 'NAME',
  START_TIME: 'START_TIME',
};

export const EMAIL_SCHEDULED_TEXT = 'Email scheduled';
export const FAILED_TEXT = 'Failed';
export const NO_RESULTS_FOUND_TEXT = 'No Results Found';
export const ADJUST_FILTER_TEXT = 'Try adjusting your filters/search';

export const ENROLL_DRAWER_SUPPORTED_FILTERS = {
  ENROLLMENT_STATUS: 'enrollmentStatus',
  ATTENDANCE_STATUS: 'attendanceStatus',
  SESSION_STATE: 'sessionState',
  SEARCH: 'search',
  ILT_DATE_RANGE_DD: 'dateRange',
  SESSION_TYPE: 'sessionType',
  SESSION_STATUS: 'sessionStatus',
  ENTITY_TYPE: 'entityType',
} as const;

export const ENROLL_DRAWER_SUPPORTED_FILTERS_API_NAME = {
  [ENROLL_DRAWER_SUPPORTED_FILTERS.SESSION_STATE]: 'scheduleStatus',
  [ENROLL_DRAWER_SUPPORTED_FILTERS.SEARCH]: 'query',
  [ENROLL_DRAWER_SUPPORTED_FILTERS.SESSION_TYPE]: 'meetingType',
  [ENROLL_DRAWER_SUPPORTED_FILTERS.SESSION_STATUS]: 'entityState',
  [ENROLL_DRAWER_SUPPORTED_FILTERS.ENTITY_TYPE]: 'entityType',
} as const;

export const ENROLL_DRAWER_ENROLL_STATUS = {
  ENROLL: {
    value: 'enrolled',
    displayText: 'Enrolled',
  },
  MOVE_TO_WAITING: {
    value: 'waitlisted',
    displayText: 'Waitlisted',
  },
  UN_ENROLL: {
    value: 'notEnrolled',
    displayText: 'Not enrolled',
  },
};

export const ENROLL_DRAWER_ATTENDANCE_STATUS = {
  NOT_ATTENDED: {
    value: 'NOT_ATTENDED',
    displayText: 'Did not attend',
  },
  ATTENDED: {
    value: 'ATTENDED',
    displayText: 'Attended',
  },
  UNMARKED_ATTENDANCE: {
    value: 'UNMARKED',
    displayText: 'Unmarked attendance',
  },
};

export const DRAWER_ENROLL_STATUS_ACTIONS = {
  ENROLL: 'ENROLL',
  UN_ENROLL: 'UN_ENROLL',
  MOVE_TO_WAITING: 'MOVE_TO_WAITING',
};

export const ENROLL_DRAWER_ENROLLMENT_STATES = {
  ENROLLED: 'ENROLLED',
  NOT_ENROLLED: 'NOT_ENROLLED',
  WAITLISTED: 'WAITLISTED',
};

export const ENROLL_DRAWER_ENROLLMENT_MAP = {
  ENROLL: 'ENROLLED',
  UN_ENROLL: 'NOT_ENROLLED',
  MOVE_TO_WAITING: 'WAITLISTED',
};

export const DRAWER_ENROLL_STATUS_ACTION_MAP = {
  ENROLLED: 'ENROLL',
  NOT_ENROLLED: 'UN_ENROLL',
  WAITLISTED: 'MOVE_TO_WAITING',
};

export const UPDATE_ENROLLMENT_STATUS_ACTION_MAP = {
  [ENROLLMENT_STATUS_TYPES.ENROLLED]: 'ENROLLED',
  [ENROLLMENT_STATUS_TYPES.DID_NOT_ENROLLED]: 'NOT_ENROLLED',
  [ENROLLMENT_STATUS_TYPES.WAITING]: 'WAITLISTED',
};

export const UPDATE_ENROLLMENT_ATTENDANCE_STATUS_ACTION_MAP = {
  [ATTENDED_TYPES.UNMARKED]: 'ATTENDANCE_NOT_MARKED',
  [ATTENDED_TYPES.DID_NOT_ATTEND]: 'NOT_ATTENDED',
  [ATTENDED_TYPES.ATTENDED]: 'ATTENDED',
};

export const ENROLLMENT_BUTTON_TYPES = {
  UN_ENROLL: {
    initialText: 'Unenroll',
    updatingText: 'Unenrolling',
    updatedText: 'Not Enrolled',
  },
  ENROLL: {
    initialText: 'Enroll',
    updatingText: 'Enrolling',
    updatedText: 'Enrolled',
  },
  MOVE_TO_WAITING: {
    initialText: 'Waitlist',
    updatingText: 'Waitlisting',
    updatedText: 'Waitlisted',
  },
};

const today = moment();
const tomorrow = moment().add(1, 'day');
const yesterday = moment().subtract(1, 'day');
const lastSevenFrom = moment().subtract(7, 'days');
const nextSevenTo = moment().add(7, 'days');

export const DATE_FILTER_OPTIONS = {
  ALL: {
    key: '0',
    content: 'All',
    resolver: '',
  },
  TODAY: {
    key: '1',
    content: 'Today',
    resolver: {
      from: moment(today).startOf('day'),
      to: moment(today).endOf('day'),
      display: today.format("D MMMM' YY"),
    },
  },
  LAST_SEVEN: {
    key: '2',
    content: 'Last 7 Days',
    resolver: {
      from: lastSevenFrom,
      to: yesterday,
      display: defaultDateFormatter(lastSevenFrom, yesterday),
    },
  },
  NEXT_SEVEN: {
    key: '3',
    content: 'Next 7 Days',
    resolver: {
      from: tomorrow,
      to: nextSevenTo,
      display: defaultDateFormatter(tomorrow, nextSevenTo),
    },
  },
  CUSTOM: {
    key: '4',
    content: 'Custom Range',
    resolver: 'range-picker',
  },
};

export const ENROLL_DRAWER_MESSAGES = {
  AUTO_ENROLL_ENROLL:
    'To enroll learners manually, disable auto enrollment in edit session settings',
  AUTO_ENROLL_ENROLL_EVENT:
    'To enroll learners manually, disable auto enrollment in edit event settings',
  AUTO_ENROLL_MOVE_TO_WAITING:
    'To waitlist learners manually, disable auto enrollment in edit session settings',
  AUTO_ENROLL_MOVE_TO_WAITING_EVENT:
    'To waitlist learners manually, disable auto enrollment in edit event settings',
  AUTO_ENROLL_UN_ENROLL:
    'To unenroll learners manually, disable auto enrollment in edit session settings',
  AUTO_ENROLL_UN_ENROLL_EVENT:
    'To unenroll learners manually, disable auto enrollment in edit event settings',
  SEATS_FULL: 'Available seats: 0. Increase seat limit to enroll learners. ',
  WAITING_LIST_DISABLED: 'Enable waitlist in edit session settings.',
  WAITING_LIST_DISABLED_EVENT: 'Enable waitlist in edit event settings.',
  SEARCH_TOOLTIP: "Search sessions by session's name, instructor's name or email",
  NOTIFY_LEARNERS_TOOLTIP:
    'If enabled, learners would be sent an email every time their enrollment status is changed. Disable this to avoid sending an email for every session.',
  LEARNER_CALENDAR_AUTO_SYNC_INFO:
    'If this is switched on, it will automatically sync all events with the learner calendars',
  MULTIPRESENT_INFO_TOOLTIP: `Any actions taken on learners in this ILT will be reflected in all other series that this ILT is present in`,
};

export const getEnrollDrawerTableColumns = ({
  drawerModifyEnroll,
  isEventEnrollDrawerActive,
  isMultidayEnabled,
}: {
  drawerModifyEnroll: boolean;
  isEventEnrollDrawerActive: boolean;
  isMultidayEnabled: boolean;
}) => [
  {
    title: !isMultidayEnabled || isEventEnrollDrawerActive ? 'Session' : 'Session/event',
    dataIndex: 'sessionName',
    sorter: true,
    sortDirections: ['ascend', 'descend', 'ascend'],
    className: 'name-column',
    width: '36%',
  },
  {
    title:
      !isMultidayEnabled || isEventEnrollDrawerActive ? 'Session date and time' : 'Date and time',
    dataIndex: 'sessionDateTime',
    sorter: true,
    sortDirections: ['ascend', 'descend', 'ascend'],
    defaultSortOrder: 'descend',
    width: '25%',
  },
  {
    title: 'Enrollment status',
    dataIndex: 'sessionEnrollStatus',
    width: '30%',
  },
  {
    title: '',
    dataIndex: 'eventEnrollOperations',
    width: '9%',
    render: (element: JSX.Element, record: any, index: any) => {
      if (drawerModifyEnroll && record.entityType === ILT_ENTITIES.EVENT) {
        return element;
      }
      return null;
    },
  },
];

export const SESSION_STATUS_TYPES = {
  UPCOMING: 'UPCOMING',
  LIVE: 'LIVE',
  PAST: 'PAST',
};

export const SESSION_STATUS_INFO = {
  LIVE: {
    value: 'LIVE',
    displayText: 'Live',
  },
  UPCOMING: {
    value: 'UPCOMING',
    displayText: 'Upcoming',
  },
  PAST: {
    value: 'Past',
    displayText: 'Past',
  },
};

export const SESSION_TYPE_TYPES = {
  CLASSROOM: 'CLASSROOM',
  WEBINAR: 'WEBINAR',
  HYBRID: 'HYBRID',
} as const;

export const SESSION_TYPE_INFO = {
  CLASSROOM: {
    displayText: 'Classroom',
  },
  WEBINAR: {
    displayText: 'Video conferencing',
  },
  HYBRID: {
    displayText: 'Hybrid',
  },
} as const;

export { ENROLL_DRAWER_BOTTOM_MENU, ENROLL_DRAWER_LISTING_MENU };

export const MANAGE_ENROLLMENT_TEXT = 'Manage enrollment';

export const DEFAULT_LEARNERS_FILTER = {
  query: '',
  moduleRelevance: [],
  groups: [],
  invitedOn: 'NONE',
};

export const DEFAULT_LEARNERS_SORT = {
  order: 'asc',
  type: 'name',
};

export const ENTITY_TYPE_FOR_MESSAGE = {
  [ILT_ENTITIES.EVENT]: 'event',
  [ILT_ENTITIES.SESSION]: 'session',
  [ILT_ENTITIES.SESSION_WITHIN_EVENT]: 'session',
};
