import { handleQueryStringForApi } from '@mindtickle/api-helpers';

let apiUrls = {
  getInvitedLearnersCount({ moduleId }: { moduleId: string }) {
    return {
      url: `/wapi/ilt/${moduleId}/inviteTypeAggs`,
      mock: 'getInvitedLearnersCount',
      mockType: 'success',
    };
  },
  getTodos({ companyId, moduleId }: { companyId: string; moduleId: string }) {
    return {
      url: `/wapi/company/${companyId}/ilt/${moduleId}/todos`,
      mock: 'getTodos',
      mockType: 'success',
    };
  },
  getLearners({ companyId }: { companyId: string }) {
    return {
      url: `/${companyId}/learners/search`,
      mock: 'getFullLearners',
      mockType: 'success',
      noPrefix: true,
    };
  },
  getSelectedLearners({ companyId, seriesId }: { companyId: string; seriesId: string }) {
    return {
      url: `/${companyId}/series/${seriesId}/learners`,
      mock: 'getFullLearners',
      mockType: 'success',
    };
  },
  getGroups({ companyId }: { companyId: string }) {
    return {
      url: `/${companyId}/learners/groups`,
      mock: 'getGroups',
      mockType: 'success',
    };
  },
  getProfileFields({ companyId }: { companyId: string }) {
    return {
      url: `/${companyId}/profile_fields`,
      mock: 'getProfileFields',
      mockType: 'success',
    };
  },
  getProfileKeyData({ companyId }: { companyId: string }) {
    return {
      url: `/${companyId}/analytics/getProfileKeyData`,
      mock: 'getProfileKeyData',
      mockType: 'success',
    };
  },
  publishData({ companyId, moduleId }: { companyId: string; moduleId: string }) {
    return {
      url: `/wapi/company/${companyId}/ilt/${moduleId}/publishILT`,
      mock: 'publishData',
      mockType: 'success',
    };
  },
};

export default handleQueryStringForApi(apiUrls);
