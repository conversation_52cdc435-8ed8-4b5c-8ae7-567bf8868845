import moment from 'moment';
import { FormattedMessage } from 'react-intl';

import { hoursToMiliseconds } from '@mindtickle/utils';

import { nextIntervalTimestamp } from '~/utils';
import { defaultDateFormatter as defaultFormatter } from '~/utils';
import { subtractOffsetDiff } from '~/utils/timezone';

import {
  SUPPORTED_FILTERS,
  SUPPORTED_FILTERS_API_NAME,
  SUPPORTED_FILTERS_API_KEY,
  OPERATIONS,
  OPERATIONS_NEEDING_ASYNC_LIVECHALLENGE_FETCH,
  OPERATIONS_NEEDING_STATS_FETCH,
  OPERATIONS_NEEDING_LOCAL_TIME_FETCH,
  RANGE_PICKER_STATE,
  SESSION_TYPES_ENUM,
  WEBINAR_TIME_VALIDATION_ENUM,
  WEBINAR_VALIDATION_ENUM,
  WEBINAR_SOURCE,
  INTEGRATION_SOURCE,
  WEBI<PERSON><PERSON>_SOURCE_LABELS,
  INTEGRATION_URLDOMAIN_IDENTIFIER,
  WEBEX_MEETING_SETTING_KEY,
  ZOOM_MEETING_SETTING_KEY,
  MS_TEAMS_MEETING_SETTING_KEY,
  LOCATION_TYPE,
  LOCATION_TYPE_ICON_TYPE,
  WEBINAR_MEETING_TYPES,
  WEBINAR_MAX_COHOST_LENGTH,
  MODES,
  DEFAULT_REMINDER_SECONDS,
  DEFAULT_PAGINATION,
  SESSION_WEBINAR_URL_VALIDATION_STATUS_TYPES,
  SESSION_WEBINAR_STATUS_TYPES,
  WEBINAR_HOST_ERRORS,
  WEBINAR_CREATION_ERRORS,
  DEFAULT_WEBINAR_SETTINGS,
  INTEGRATION_KEY_NAMES,
  ENROLLMENT_FREEZE_STATUSES,
  ILT_CALENDAR_AUTO_SYNC_CONFIG_KEY,
  COMPLETION_CRITERIA_ENUM,
} from '../constants/sessions';
import {
  SESSION_STATE as MESSAGES_SESSION_STATE,
  ENTITY_TYPES as MESSAGES_ENTITY_TYPE,
  SESSION_STATUS as MESSAGES_SESSION_STATUS,
  SESSION_TYPES as MESSAGES_SESSION_TYPES,
  WEBINAR_VALIDATIONS as MESSAGES_WEBINAR_VALIDATIONS,
  VALIDATION_MESSAGES as MESSAGES_GENERIC_VALIDATION,
  COMMON_MESSAGES as MESSAGES_SESSION_COMMON,
  LOCATION_DETAILS as MESSAGES_LOCATION_DETAILS,
  SESSION_AUTO_ATTENDANCE as MESSAGES_SESSION_AUTO_ATTENDANCE,
  COMPLETION_CRITERIA as MESSAGES_COMPLETION_CRITERIA,
  MESSAGES_SESSION_INFO,
  MESSAGES_CHECKIN,
} from '../messages/sessions';
import EXTRA_FILTER_SESSION_TYPES from '../messages/sessions/sessionTypes/additionalFilters';
import MESSAGE_URL_VALIDATION_STATUS from '../messages/sessions/urlValidationStatus';
import { formatDuration } from '../utils/timing';

const today = moment();
const tomorrow = moment().add(1, 'day');
const yesterday = moment().subtract(1, 'day');
const lastSevenFrom = moment().subtract(7, 'days');
const nextSevenTo = moment().add(7, 'days');

export {
  SUPPORTED_FILTERS,
  SUPPORTED_FILTERS_API_NAME,
  SUPPORTED_FILTERS_API_KEY,
  OPERATIONS,
  OPERATIONS_NEEDING_ASYNC_LIVECHALLENGE_FETCH,
  OPERATIONS_NEEDING_STATS_FETCH,
  OPERATIONS_NEEDING_LOCAL_TIME_FETCH,
  WEBINAR_TIME_VALIDATION_ENUM,
  WEBINAR_VALIDATION_ENUM,
  WEBINAR_SOURCE,
  INTEGRATION_SOURCE,
  WEBINAR_SOURCE_LABELS,
  INTEGRATION_URLDOMAIN_IDENTIFIER,
  WEBEX_MEETING_SETTING_KEY,
  ZOOM_MEETING_SETTING_KEY,
  MS_TEAMS_MEETING_SETTING_KEY,
  LOCATION_TYPE,
  LOCATION_TYPE_ICON_TYPE,
  WEBINAR_MEETING_TYPES,
  WEBINAR_MAX_COHOST_LENGTH,
  MODES,
  DEFAULT_REMINDER_SECONDS,
  DEFAULT_PAGINATION,
  SESSION_WEBINAR_URL_VALIDATION_STATUS_TYPES,
  SESSION_WEBINAR_STATUS_TYPES,
  WEBINAR_HOST_ERRORS,
  WEBINAR_CREATION_ERRORS,
  DEFAULT_WEBINAR_SETTINGS,
  INTEGRATION_KEY_NAMES,
  ENROLLMENT_FREEZE_STATUSES,
  ILT_CALENDAR_AUTO_SYNC_CONFIG_KEY,
  COMPLETION_CRITERIA_ENUM,
};

export const SESSION_STATE = {
  ALL: {
    filterValue: SUPPORTED_FILTERS_API_KEY[SUPPORTED_FILTERS.SESSION_STATE].ALL,
    displayValue: MESSAGES_SESSION_STATE.ALL,
  },
  LIVE: {
    filterValue: SUPPORTED_FILTERS_API_KEY[SUPPORTED_FILTERS.SESSION_STATE].LIVE,
    displayValue: MESSAGES_SESSION_STATE.LIVE,
  },
  UPCOMING: {
    filterValue: SUPPORTED_FILTERS_API_KEY[SUPPORTED_FILTERS.SESSION_STATE].UPCOMING,
    displayValue: MESSAGES_SESSION_STATE.UPCOMING,
  },
  PAST: {
    filterValue: SUPPORTED_FILTERS_API_KEY[SUPPORTED_FILTERS.SESSION_STATE].PAST,
    displayValue: MESSAGES_SESSION_STATE.PAST,
  },
};

export const SESSION_STATE_FILTER_OPTIONS = {
  UPCOMING: {
    filterValue: SUPPORTED_FILTERS_API_KEY[SUPPORTED_FILTERS.SESSION_STATE].UPCOMING,
    displayValue: MESSAGES_SESSION_STATE.UPCOMING,
  },
  LIVE: {
    filterValue: SUPPORTED_FILTERS_API_KEY[SUPPORTED_FILTERS.SESSION_STATE].LIVE,
    displayValue: MESSAGES_SESSION_STATE.LIVE,
  },
  PAST: {
    filterValue: SUPPORTED_FILTERS_API_KEY[SUPPORTED_FILTERS.SESSION_STATE].PAST,
    displayValue: MESSAGES_SESSION_STATE.PAST,
  },
};

export const ENTITY_TYPE_FILTER_OPTIONS = {
  SESSION: {
    filterValue: SUPPORTED_FILTERS_API_KEY[SUPPORTED_FILTERS.ENTITY_TYPE].INDEPENDENT_SESSION,
    displayValue: MESSAGES_ENTITY_TYPE.SESSION,
  },
  EVENT: {
    filterValue: SUPPORTED_FILTERS_API_KEY[SUPPORTED_FILTERS.ENTITY_TYPE].EVENTS_WITH_ITS_SESSIONS,
    displayValue: MESSAGES_ENTITY_TYPE.EVENT,
  },
};

export const MAP_SESSION_STATE_TO_DISPLAY_VALUE = {
  ALL: SESSION_STATE.ALL.displayValue,
  LIVE: SESSION_STATE.LIVE.displayValue,
  UPCOMING: SESSION_STATE.UPCOMING.displayValue,
  PAST: SESSION_STATE.PAST.displayValue,
};

export const SESSION_STATUS = {
  PUBLISHED: {
    filterValue: SUPPORTED_FILTERS_API_KEY[SUPPORTED_FILTERS.SESSION_STATUS].PUBLISHED,
    displayValue: MESSAGES_SESSION_STATUS.PUBLISHED,
  },
  UNPUBLISHED: {
    filterValue: SUPPORTED_FILTERS_API_KEY[SUPPORTED_FILTERS.SESSION_STATUS].UNPUBLISHED,
    displayValue: MESSAGES_SESSION_STATUS.UNPUBLISHED,
  },
  CANCELLED: {
    filterValue: SUPPORTED_FILTERS_API_KEY[SUPPORTED_FILTERS.SESSION_STATUS].CANCELLED,
    displayValue: MESSAGES_SESSION_STATUS.CANCELLED,
  },
};

export const MAP_SESSION_STATUS_TO_DISPLAY_VALUE = {
  CANCELLED: SESSION_STATUS.CANCELLED.displayValue,
  UNPUBLISHED: SESSION_STATUS.UNPUBLISHED.displayValue,
  PUBLISHED_NOT_CANCELLED: SESSION_STATUS.PUBLISHED.displayValue,
};

export const SESSION_TYPES = {
  CLASSROOM: {
    value: SESSION_TYPES_ENUM.CLASSROOM,
    displayValue: MESSAGES_SESSION_TYPES.CLASSROOM,
  },
  WEBINAR: {
    value: SESSION_TYPES_ENUM.WEBINAR,
    displayValue: MESSAGES_SESSION_TYPES.WEBINAR,
  },
  HYBRID: {
    value: SESSION_TYPES_ENUM.HYBRID,
    displayValue: MESSAGES_SESSION_TYPES.HYBRID,
  },
};

export const ENROLLMENT_FREEZE_TYPES = {
  RELATIVE: {
    value: 'RELATIVE',
    displayValue: 'Number of days before session date',
  },
  ABSOLUTE: {
    value: 'ABSOLUTE',
    displayValue: 'On a specific date and time',
  },
};

export const SESSION_TYPES_FILTER_OPTIONS = {
  ALL: {
    value: 'ALL',
    displayValue: EXTRA_FILTER_SESSION_TYPES.ALL,
  },
  ...SESSION_TYPES,
};

export const WEBINAR_TIME_VALIDATION_MESSAGES = MESSAGES_WEBINAR_VALIDATIONS;

export const WEBINAR_VALIDATION_MESSAGES = MESSAGES_WEBINAR_VALIDATIONS;

export const GENERIC_VALIDATION_MESSAGES = MESSAGES_GENERIC_VALIDATION;

export const EDIT_HOST_COHOST_VALIDATION_MESSAGES = {
  INVALID_EMAIL_MESSAGE: GENERIC_VALIDATION_MESSAGES.ENTER_VALID_EMAIL,
  REQUIRED_EMAIL_MESSAGE: GENERIC_VALIDATION_MESSAGES.REQUIRED_EMAIL_ID,
  EXISTING_COHOST_EMAIL_MESSAGE: GENERIC_VALIDATION_MESSAGES.EXISTING_COHOST_EMAIL_MESSAGE,
  SAVE_CHANGES_MESSAGE: GENERIC_VALIDATION_MESSAGES.SAVE_CHANGES_MESSAGE,
};

export const LOCATION_DROPDOWN_LABEL = MESSAGES_SESSION_COMMON.LOCATION_DROPDOWN_LABEL;

export const ADD_AS_MEETING_COHOST_TEXT = MESSAGES_SESSION_COMMON.ADD_AS_MEETING_COHOST;

export const LOCATION_SESSION_TYPE_MAP = {
  [LOCATION_TYPE.FACE_TO_FACE]: SESSION_TYPES.CLASSROOM.value,
  [LOCATION_TYPE.WEBEX_MEETING]: SESSION_TYPES.WEBINAR.value,
  [LOCATION_TYPE.ZOOM_MEETING]: SESSION_TYPES.WEBINAR.value,
  [LOCATION_TYPE.MS_TEAMS_MEETING]: SESSION_TYPES.WEBINAR.value,
  [LOCATION_TYPE.LINK]: SESSION_TYPES.WEBINAR.value,
};

export const LOCATION_TYPE_PRIMARY_TEXT = {
  [LOCATION_TYPE.FACE_TO_FACE]: MESSAGES_LOCATION_DETAILS.PRIMARY_FACE_TO_FACE,
  [LOCATION_TYPE.LINK]: MESSAGES_LOCATION_DETAILS.PRIMARY_LINK,
  [LOCATION_TYPE.WEBEX_MEETING]: MESSAGES_LOCATION_DETAILS.PRIMARY_WEBEX_MEETING,
  [LOCATION_TYPE.ZOOM_MEETING]: MESSAGES_LOCATION_DETAILS.PRIMARY_ZOOM_MEETING,
  [LOCATION_TYPE.MS_TEAMS_MEETING]: MESSAGES_LOCATION_DETAILS.PRIMARY_MS_TEAMS_MEETING,
};

export const LOCATION_TYPE_SECONDARY_TEXT = {
  [LOCATION_TYPE.FACE_TO_FACE]: MESSAGES_LOCATION_DETAILS.SECONDARY_FACE_TO_FACE,
  [LOCATION_TYPE.LINK]: MESSAGES_LOCATION_DETAILS.SECONDARY_LINK,
  [LOCATION_TYPE.WEBEX_MEETING]: MESSAGES_LOCATION_DETAILS.SECONDARY_WEBEX_MEETING,
  [LOCATION_TYPE.ZOOM_MEETING]: MESSAGES_LOCATION_DETAILS.SECONDARY_ZOOM_MEETING,
  [LOCATION_TYPE.MS_TEAMS_MEETING]: MESSAGES_LOCATION_DETAILS.SECONDARY_MS_TEAMS_MEETING,
};

export const LOCATION_TYPE_VIEW_TEXT = {
  [LOCATION_TYPE.FACE_TO_FACE]: MESSAGES_LOCATION_DETAILS.PRIMARY_FACE_TO_FACE,
  [LOCATION_TYPE.LINK]: MESSAGES_LOCATION_DETAILS.VIEW_LINK,
  [LOCATION_TYPE.WEBEX_MEETING]: MESSAGES_LOCATION_DETAILS.VIEW_WEBEX_MEETING,
  [LOCATION_TYPE.ZOOM_MEETING]: MESSAGES_LOCATION_DETAILS.VIEW_ZOOM_MEETING,
  [LOCATION_TYPE.MS_TEAMS_MEETING]: MESSAGES_LOCATION_DETAILS.VIEW_MS_TEAMS_MEETING,
};

export const MAP_SESSION_TYPE_TO_DISPLAY_VALUE = {
  ALL: SESSION_TYPES_FILTER_OPTIONS.ALL.displayValue,
  CLASSROOM: SESSION_TYPES_FILTER_OPTIONS.CLASSROOM.displayValue,
  WEBINAR: SESSION_TYPES_FILTER_OPTIONS.WEBINAR.displayValue,
};

export const SESSION_STATE_DD_OPTIONS = Object.values(SESSION_STATE_FILTER_OPTIONS).map(state => ({
  label: <FormattedMessage {...state.displayValue} />,
  value: state.filterValue,
}));

export const ENTITY_TYPE_DD_OPTIONS = Object.values(ENTITY_TYPE_FILTER_OPTIONS).map(state => ({
  label: <FormattedMessage {...state.displayValue} />,
  value: state.filterValue,
}));

export const ENROLLMENT_FREEZE_TYPE_OPTIONS = Object.values(ENROLLMENT_FREEZE_TYPES).map(type => ({
  content: type.displayValue,
  key: type.value,
}));

export const SESSION_TYPES_DD_OPTIONS = Object.values(SESSION_TYPES).map(type => ({
  text: type.displayValue,
  value: type.value,
}));
export const SESSION_STATUS_DD_OPTIONS = Object.values(SESSION_STATUS).map(state => ({
  label: <FormattedMessage {...state.displayValue} />,
  value: state.filterValue,
}));
export const SESSION_TYPES_FILTER_DD_OPTIONS = Object.values(SESSION_TYPES).map(type => ({
  label: <FormattedMessage {...type.displayValue} />,
  value: type.value,
}));
export const SESSION_TYPES_DD_OPTIONS_WITHOUT_HYBRID = SESSION_TYPES_FILTER_DD_OPTIONS.filter(
  ({ value }) => value !== SESSION_TYPES.HYBRID.value
);

export const DEFAULT_SESSION_DURATION = hoursToMiliseconds(1); // 1hours

export const MINIMUM_SESSION_DURATION = hoursToMiliseconds(1 / 12);

export const GET_DEFAULT_TIMING = () => {
  const startTime = nextIntervalTimestamp(Date.now(), 30);
  const endTime = +moment(startTime).add(DEFAULT_SESSION_DURATION, 'milliseconds').seconds(0);
  const currentOffset = new Date().getTimezoneOffset();
  return {
    startTime,
    endTime,
    currentOffset,
    duration: {
      displayValue: formatDuration(DEFAULT_SESSION_DURATION / 1000),
      value: DEFAULT_SESSION_DURATION,
    },
    minStartTime: 0, //set to epoch start 1970
  };
};

export const MIN_START_TIME_IN_TIMEZONE = (offset: number) => {
  const startTime = nextIntervalTimestamp(Date.now(), 30);
  const offsetInMilliseconds = hoursToMiliseconds(offset);
  return subtractOffsetDiff(startTime, offsetInMilliseconds);
};

export const MESSAGES = {
  INFO: MESSAGES_SESSION_INFO,
};

export const SESSION_TYPE_NAMES = SESSION_TYPES_ENUM;

export const LOCATION_TYPE_PLACEHOLDERS = {
  CLASSROOM_LOCATION: MESSAGES_LOCATION_DETAILS.PLACEHOLDER_CLASSROOM_LOCATION,
  LINK: MESSAGES_LOCATION_DETAILS.PLACEHOLDER_LINK,
};

export const SESSION_AUTO_ATTENDANCE_MESSAGES = MESSAGES_SESSION_AUTO_ATTENDANCE;

export function getWebinarURLValidationMessage(
  validationStatus: string,
  { webinarSource = '' }: { webinarSource?: string }
) {
  switch (validationStatus) {
    case SESSION_WEBINAR_URL_VALIDATION_STATUS_TYPES.SUCCESS:
      return <FormattedMessage {...MESSAGE_URL_VALIDATION_STATUS.SUCCESS} />;
    case SESSION_WEBINAR_URL_VALIDATION_STATUS_TYPES.DISABLED:
      return <FormattedMessage {...MESSAGE_URL_VALIDATION_STATUS.DISABLED} />;
    case SESSION_WEBINAR_URL_VALIDATION_STATUS_TYPES.FAILED:
      return (
        <FormattedMessage
          {...MESSAGE_URL_VALIDATION_STATUS.FAILED}
          values={{ sourceLabel: WEBINAR_SOURCE_LABELS[webinarSource] }}
        />
      );
    case SESSION_WEBINAR_URL_VALIDATION_STATUS_TYPES.MEETING_TYPE_NOT_SUPPORTED:
      return <FormattedMessage {...MESSAGE_URL_VALIDATION_STATUS.MEETING_TYPE_NOT_SUPPORTED} />;
    case SESSION_WEBINAR_URL_VALIDATION_STATUS_TYPES.MEETING_DOES_NOT_EXIST:
      return (
        <FormattedMessage
          {...MESSAGE_URL_VALIDATION_STATUS.MEETING_DOES_NOT_EXIST}
          values={{ sourceLabel: WEBINAR_SOURCE_LABELS[webinarSource] }}
        />
      );
    case SESSION_WEBINAR_URL_VALIDATION_STATUS_TYPES.REPORT_API_ACCESS_NOT_AVAILABLE:
      return (
        <FormattedMessage
          {...MESSAGE_URL_VALIDATION_STATUS.REPORT_API_ACCESS_NOT_AVAILABLE}
          values={{ sourceLabel: WEBINAR_SOURCE_LABELS[webinarSource] }}
        />
      );
    case SESSION_WEBINAR_URL_VALIDATION_STATUS_TYPES.INTEGRATION_AUTHENTICATION_ERROR:
      return (
        <FormattedMessage
          {...MESSAGE_URL_VALIDATION_STATUS.INTEGRATION_AUTHENTICATION_ERROR}
          values={{ sourceLabel: WEBINAR_SOURCE_LABELS[webinarSource] }}
        />
      );
    case SESSION_WEBINAR_URL_VALIDATION_STATUS_TYPES.INTEGRATION_AUTH_NOT_SETUP:
      return (
        <FormattedMessage
          {...MESSAGE_URL_VALIDATION_STATUS.INTEGRATION_AUTH_NOT_SETUP}
          values={{ sourceLabel: WEBINAR_SOURCE_LABELS[webinarSource] }}
        />
      );
    case SESSION_WEBINAR_URL_VALIDATION_STATUS_TYPES.UNAUTHORIZED_ACTION:
      return webinarSource === WEBINAR_SOURCE.ZOOM ? (
        <FormattedMessage
          {...MESSAGE_URL_VALIDATION_STATUS.UNAUTHORIZED_ACTION}
          values={{ sourceLabel: WEBINAR_SOURCE_LABELS[webinarSource] }}
        />
      ) : (
        ''
      );
    case SESSION_WEBINAR_URL_VALIDATION_STATUS_TYPES.PARTICIPANT_FETCH_NOT_ALLOWED:
      return (
        <FormattedMessage
          {...MESSAGE_URL_VALIDATION_STATUS.PARTICIPANT_FETCH_NOT_ALLOWED}
          values={{ sourceLabel: WEBINAR_SOURCE_LABELS[webinarSource] }}
        />
      );
    case SESSION_WEBINAR_URL_VALIDATION_STATUS_TYPES.MEETING_LINK_EMPTY:
      return <FormattedMessage {...MESSAGE_URL_VALIDATION_STATUS.MEETING_LINK_EMPTY} />;
    case SESSION_WEBINAR_URL_VALIDATION_STATUS_TYPES.INVALID_MEETING_LINK:
      return <FormattedMessage {...MESSAGE_URL_VALIDATION_STATUS.INVALID_MEETING_LINK} />;
    case SESSION_WEBINAR_STATUS_TYPES.MEETING_ID_INVALID:
      return <FormattedMessage {...MESSAGE_URL_VALIDATION_STATUS.MEETING_ID_INVALID} />;
    case SESSION_WEBINAR_STATUS_TYPES.USER_LACKING_HOST_PRIVILEGES:
      return <FormattedMessage {...MESSAGE_URL_VALIDATION_STATUS.USER_LACKING_HOST_PRIVILEGES} />;
    case SESSION_WEBINAR_STATUS_TYPES.UNKNOWN:
      return <FormattedMessage {...MESSAGE_URL_VALIDATION_STATUS.UNKNOWN} />;
    default: // SESSION_WEBINAR_URL_VALIDATION_STATUS_TYPES.NONE, WEBCONF_SOURCE_NOT_SUPPORTED,SOURCE_NOT_SUPPORTED or any other
      return '';
  }
}

// TODO: get the labels of this filter from a messages files
export const DATE_FILTER_OPTIONS = {
  ALL: {
    key: '0',
    content: 'All time',
    resolver: {
      from: null,
      to: null,
    },
  },
  TODAY: {
    key: '1',
    content: 'Today',
    resolver: {
      from: moment(today).startOf('day'),
      to: moment(today).endOf('day'),
      display: today.format("D MMMM' YY"),
    },
  },
  LAST_SEVEN: {
    key: '2',
    content: 'Last 7 days',
    resolver: {
      from: lastSevenFrom,
      to: yesterday,
      display: defaultFormatter(lastSevenFrom, yesterday),
    },
  },
  NEXT_SEVEN: {
    key: '3',
    content: 'Next 7 days',
    resolver: {
      from: tomorrow,
      to: nextSevenTo,
      display: defaultFormatter(tomorrow, nextSevenTo),
    },
  },
  CUSTOM: {
    key: '4',
    content: 'Custom',
    resolver: RANGE_PICKER_STATE,
  },
};

// TODO: get the following texts of all following texts from messages intl
export const SESSION_LIST_FILTER_TITLES = {
  SESSION_STATE: 'Time',
  SESSION_STATUS: 'Status',
  SESSION_TYPE: 'Format',
  ENTITY_TYPE: 'Type',
};
export const SESSION_LIST_FILTER_PLACEHOLDERS = {
  SEARCH_SESSIONS: 'Search',
  DATE_RANGE: 'Date',
};
export const ENROLLMENT_LOCK_WARNING = {
  PAST: 'Enrollments locked on ',
  FUTURE: 'Enrollments will be locked on ',
  NO_DATE: 'Enrollments for this session are locked',
  NO_DATE_EVENT: 'Enrollments for this event are locked',
};

export const INSTRUCTOR_CALENDAR_AUTO_SYNC_INFO =
  MESSAGES_SESSION_INFO.INSTRUCTOR_CALENDAR_AUTO_SYNC_INFO;

export const LEARNER_CALENDAR_AUTO_SYNC_INFO =
  MESSAGES_SESSION_INFO.LEARNER_CALENDAR_AUTO_SYNC_INFO;

export const COMPLETION_CRITERIA_TEXTS = {
  [COMPLETION_CRITERIA_ENUM.ATTENDED]: MESSAGES_COMPLETION_CRITERIA.ATTENDED,
  [COMPLETION_CRITERIA_ENUM.ATTENDED_OR_WATCHED]: MESSAGES_COMPLETION_CRITERIA.ATTENDED_OR_WATCHED,
};

export const CHECKIN_TEXTS = {
  CODE_UNAVAILABLE_NOTE: MESSAGES_CHECKIN.CODE_UNAVAILABLE_NOTE,
  AUTO_ATTENDANCE_ENABLED_NOTE: MESSAGES_CHECKIN.AUTO_ATTENDANCE_ENABLED,
  ENABLE_CHECKIN_TOGGLE: MESSAGES_CHECKIN.ENABLE_CHECKIN,
};

export const SESSION_STATUS_TYPE_LABELS = {
  PAST: 'Past',
  UPCOMING: 'Upcoming',
  LIVE: 'Live',
  CANCELLED: 'Canceled',
  PUBLISHED: 'Published',
  UNPUBLISHED: 'Unpublished',
};

export const MAX_SESSION_NAME_LENGTH = 200;
