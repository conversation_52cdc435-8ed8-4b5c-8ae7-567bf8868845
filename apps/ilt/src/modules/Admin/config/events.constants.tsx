import { FormattedMessage } from 'react-intl';

import EVENT_ENROLLMENT_MESSAGES from '~/modules/Admin/messages/events/enrollment';
import ENROLLMENT_FREEZE_MESSAGES from '~/modules/Admin/messages/events/enrollmentFreeze';
import MESSAGES_EVENT_TYPES from '~/modules/Admin/messages/events/eventTypes';

import {
  EVENT_ENROLLMENT_ENUM,
  EVENT_ENROLLMENT_FREEZE_ENUMS,
  EVENT_TYPES_ENUM,
} from '../constants/events';

export const EVENT_ENROLLMENT_TEXTS = {
  [EVENT_ENROLLMENT_ENUM.EVENT]: EVENT_ENROLLMENT_MESSAGES.EVENT,
  [EVENT_ENROLLMENT_ENUM.SESSIONS]: EVENT_ENROLLMENT_MESSAGES.SESSIONS,
};

export const EVENT_TYPES = {
  CLASSROOM: {
    value: EVENT_TYPES_ENUM.CLASSROOM,
    displayValue: MESSAGES_EVENT_TYPES.CLASSROOM,
  },
  WEBINAR: {
    value: EVENT_TYPES_ENUM.WEBINAR,
    displayValue: MESSAGES_EVENT_TYPES.WEBINAR,
  },
  HYBRID: {
    value: EVENT_TYPES_ENUM.HYBRID,
    displayValue: MESSAGES_EVENT_TYPES.HYBRID,
  },
};

export const EVENT_ENROLLMENT_FREEZE_TYPES = {
  RELATIVE: {
    value: EVENT_ENROLLMENT_FREEZE_ENUMS.RELATIVE,
    displayValue: <FormattedMessage {...ENROLLMENT_FREEZE_MESSAGES.DROPDOWN_RELATIVE} />,
  },
  ABSOLUTE: {
    value: EVENT_ENROLLMENT_FREEZE_ENUMS.ABSOLUTE,
    displayValue: <FormattedMessage {...ENROLLMENT_FREEZE_MESSAGES.DROPDOWN_ABSOLUTE} />,
  },
};

export const EVENT_ENROLLMENT_FREEZE_TYPE_OPTIONS = Object.values(
  EVENT_ENROLLMENT_FREEZE_TYPES
).map(type => ({
  content: type.displayValue,
  key: type.value,
}));
