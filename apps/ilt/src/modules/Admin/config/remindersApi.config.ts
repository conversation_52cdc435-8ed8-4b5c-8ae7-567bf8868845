import { handleQueryStringForApi } from '@mindtickle/api-helpers';
let apiUrls = {
  getMailTemplates({ companyId }: { companyId: string }) {
    return {
      url: `/${companyId}/mail/templates`,
      mock: 'reminderTemplates',
      mockType: 'success',
    };
  },
  getReminderAutomations({ moduleId, companyId }: { moduleId: string; companyId: string }) {
    return {
      url: `/${companyId}/mail/automation/${moduleId}`,
      mock: 'reminderAutomation',
      mockType: 'success',
    };
  },
  operateReminders({ moduleId, companyId }: { moduleId: string; companyId: string }) {
    return {
      url: `/${companyId}/mail/automation/${moduleId}`,
      mock: 'reminderAutomationUpdate',
      mockType: 'success',
    };
  },
};

export default handleQueryStringForApi(apiUrls);
