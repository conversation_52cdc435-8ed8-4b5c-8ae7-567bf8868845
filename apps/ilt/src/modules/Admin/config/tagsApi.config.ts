import { handleQueryStringForApi } from '@mindtickle/api-helpers';
let apiUrls = {
  getAppliedTags({ moduleId, companyId }: { moduleId: string; companyId: string }) {
    return {
      url: `/${companyId}/${moduleId}/tags`,
      mock: 'getAppliedTags',
      mockType: 'success',
    };
  },
  getTags({ companyId }: { companyId: string }) {
    return {
      url: `/${companyId}/categories/tags`,
      mock: 'getAppliedTags',
      mockType: 'success',
    };
  },
  createTag({ companyId, categoryId }: { companyId: string; categoryId: string }) {
    return {
      url: `/${companyId}/categories/${categoryId}/tag`,
    };
  },
  removeTag({ companyId, moduleId }: { companyId: string; moduleId: string }) {
    return {
      url: `/${companyId}/${moduleId}/tags`,
    };
  },
  applyTags({ companyId, moduleId }: { companyId: string; moduleId: string }) {
    return {
      url: `/${companyId}/${moduleId}/tags`,
    };
  },
  getSuggestedTags({ companyId }: { companyId: string }) {
    return {
      url: `/${companyId}/tags/suggest`,
    };
  },
};

export default handleQueryStringForApi(apiUrls);
