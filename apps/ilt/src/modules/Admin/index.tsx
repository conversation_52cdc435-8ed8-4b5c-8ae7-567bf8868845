import { useCallback, useEffect, useState } from 'react';

import queryString from 'query-string';
import { FormattedMessage } from 'react-intl';
import { connect } from 'react-redux';
import { useParams, useNavigate } from 'react-router-dom';
import { compose } from 'redux';

import Layout from '@mindtickle/layout';
import Loader from '@mindtickle/loader';
import { getActions } from '@mindtickle/medux/Action';
import { injectReducer } from '@mindtickle/medux/Reducer';
import { injectSaga } from '@mindtickle/medux/Saga';
import { errorToast as ErrorToast } from '@mindtickle/toast';
import { reload } from '@mindtickle/utils';

import { MT_MODULES } from '~/config/global.config';
import ErrorBoundary from '~/modules/Admin/components/ErrorBoundary';
import AttachmentsReduxMaintain from '~/modules/Admin/containers/Attachments';
import RemindersReduxMaintain from '~/modules/Admin/containers/Reminders';
import { RESET_SETTINGS } from '~/modules/Admin/containers/Settings/actionTypes';
import { EMPTY_OBJECT_READONLY } from '~/mt-ui-core/config/global.config';
import { sessionsViewPageURL } from '~/utils';
import {
  ALL_SESSIONS_QUERY_PARAM,
  BACK_OPERATION_LABEL_MAP,
  BACK_TO_QUERY_PARAM,
} from '~/utils/constants';
import { getSeriesUrl, getIltAnalyticsUrl } from '~/utils/generateUrls';

import {
  GET_DATA,
  MANIPULATE_DATA,
  RESET_BUILD_DATA,
  UPDATE_INVITED_LEARNER_COUNT,
  HANDLE_UNPUBLISHED_CHANGES_FLAG,
  GET_INTEGRATIONS,
} from './actionTypes';
import AdminRoutes from './components/AdminRoutes';
import GoBack from './components/GoBack';
import AdminHeader from './components/ModuleHeader';
import NavBarTab from './components/NavBarTabs';
import {
  MODULE_OPTION,
  STAGES,
  OPERATIONS,
  OPERATION_MODULE_EVENT_TRACK_MAP,
} from './config/constants';
import GET_ERROR_MESSAGES from './config/error.messages';
import LiveChallengeStateManger from './containers/LiveChallengeStateManger';
import RecordingsManager from './containers/Recordings';
import { useILTAdminSnowplowTracker } from './hooks/useILTAdminSnowplowTracker';
import reducer from './reducers';
import integrationReducer from './reducers/integrationReducer';
import saga, { updateModuleDetails as updateModuleDetailsGenerator } from './saga';
import { styledLayoutContainer, StyledBottomSectionWrapper as Wrapper } from './styles';
import { adminModuleDefaultProps } from './typeDefs';

import type { TAdminContainerProps } from './typeDefs';

const { DISCARD, ARCHIVE } = MODULE_OPTION;
const { REFETCH } = OPERATIONS;
const { Content } = Layout;

// eslint-disable-next-line max-statements
function Admin(props: TAdminContainerProps) {
  const { moduleId, seriesId: seriesIdParam } = useParams();
  const [activeTab, setActiveTab] = useState('');
  const [backOperationQueryParam, setBackOperationQueryParam] = useState<string>('');
  const tracker = useILTAdminSnowplowTracker();
  const navigate = useNavigate();

  const {
    userData,
    moduleData,
    companySettings,
    seriesData,
    companyData,
    hasUnpubChanges,
    status: { loaded, hasError },
    actions,
    enabledFeatures,
    integrations,
    operationStatus: {
      hasError: operationHasError,
      error: operationError,
      loaded: operationLoaded,
      data: { operation } = {},
    },
    getIntegrationConfig,
  } = props;

  const {
    manipulateData,
    resetBuild,
    resetSettings,
    updateHasPublishedChanges,
    updateInvitedLearnersCount,
    getData,
  } = actions;

  const { isPublished, name: moduleName } = moduleData || {};
  const { id: seriesId, name: seriesName = '' } = seriesData || {};
  const { isNewDashboard, id: companyId } = companyData || {};
  const { id: userId, permissions: globalPermissions } = userData;

  function update(operation: string, value: any) {
    manipulateData({
      operation,
      name: value,
    });
    if (OPERATION_MODULE_EVENT_TRACK_MAP[operation]) {
      tracker.trackStructuredEvent({ eventName: OPERATION_MODULE_EVENT_TRACK_MAP[operation] });
    }
  }

  const goToSeries = useCallback(() => {
    const url = getSeriesUrl(seriesId, isNewDashboard);
    reload(url);
  }, [seriesId, isNewDashboard]);

  const goToSessionView = () => {
    navigate(sessionsViewPageURL, { replace: true });
  };

  useEffect(() => {
    const queryParams = queryString.parse(window.location.search);
    const backOperation = queryParams?.[BACK_TO_QUERY_PARAM];
    if (backOperation && backOperation === ALL_SESSIONS_QUERY_PARAM) {
      setBackOperationQueryParam(ALL_SESSIONS_QUERY_PARAM);
    }
  }, []);

  const onPublish = () => {
    manipulateData({ operation: REFETCH });
    resetBuild();
    resetSettings();
  };

  const onInvite = () => {
    manipulateData({ operation: REFETCH });
  };

  const onSettingUpdate = () => {
    isPublished && updateHasPublishedChanges();
    resetBuild();
  };

  // recheck this replacement of componentDidUpdate might have to use usePrevious
  // it may not work as expected
  useEffect(() => {
    if (operationHasError) {
      ErrorToast({
        message: <FormattedMessage {...GET_ERROR_MESSAGES(operationError)} />,
        timeout: 3000,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps -- should be executed only when hasError changed
  }, [operationHasError]);

  useEffect(() => {
    if (operationLoaded && !operationHasError && [DISCARD, ARCHIVE].includes(operation)) {
      goToSeries();
    }
  }, [operationLoaded, operationHasError, operation, goToSeries]);

  useEffect(() => {
    if (!loaded) {
      getData({
        moduleId,
        seriesId: seriesIdParam,
        companyId,
        userId,
        globalPermissions,
      });
    }
    getIntegrationConfig();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (loaded && !hasError) {
    return (
      <Layout css={styledLayoutContainer}>
        <Content>
          {backOperationQueryParam ? (
            <GoBack
              label={BACK_OPERATION_LABEL_MAP[backOperationQueryParam]}
              goToDestination={goToSessionView}
            />
          ) : (
            <GoBack label={seriesName} goToDestination={goToSeries} />
          )}
          <ErrorBoundary>
            <AdminHeader
              key="moduleHeader"
              actions={{
                updateModule: update,
              }}
              moduleData={moduleData}
              seriesData={seriesData}
              companyData={companyData}
              moduleOperations={MODULE_OPTION}
              operationHasError={operationHasError}
              operationError={operationError}
            />
          </ErrorBoundary>
          <Wrapper>
            <NavBarTab
              hasUnpublishedChanges={hasUnpubChanges}
              isPublished={isPublished}
              activeTab={activeTab}
              setActiveTab={setActiveTab}
              globalPermissions={userData.permissions}
              seriesPermissions={seriesData.permissions}
              isSiteOwner={userData.isSiteOwner}
              analyticsUrl={getIltAnalyticsUrl({ seriesId, moduleId, moduleName })}
            />
            <AdminRoutes
              key="routes"
              setActiveTab={setActiveTab}
              moduleData={moduleData}
              companySettings={companySettings}
              seriesData={seriesData}
              userData={userData}
              companyData={companyData}
              actions={{
                onSettingUpdate: onSettingUpdate,
                onPublish: onPublish,
                moduleUpdater: updateModuleDetailsGenerator,
                learnersCountUpdater: updateInvitedLearnersCount,
                onInvite: onInvite,
              }}
              stages={STAGES}
              enabledFeatures={enabledFeatures}
              integrations={integrations}
            />
          </Wrapper>
          <RecordingsManager />
          <RemindersReduxMaintain />
          <AttachmentsReduxMaintain />
          {!!userData.tempFeatureConfig.isIltLcIntegrationEnabled && <LiveChallengeStateManger />}
        </Content>
      </Layout>
    );
  }
  return <Loader size="sizeSmall" />;
}

Admin.defaultProps = adminModuleDefaultProps;

const mapDispatchToProps = (dispatch: (arg0: any) => void) => ({
  getData: (context: any) => {
    dispatch(getActions(GET_DATA)(context));
  },
  manipulateData: (data: { operation: any }) => {
    dispatch(
      getActions(MANIPULATE_DATA)(data, {
        loadingData: {
          operation: data.operation,
        },
      })
    );
  },
  resetBuild: () => {
    dispatch(getActions(RESET_BUILD_DATA)());
  },
  resetSettings: () => {
    dispatch(getActions(RESET_SETTINGS)());
  },
  updateHasPublishedChanges: () => {
    dispatch(
      getActions(HANDLE_UNPUBLISHED_CHANGES_FLAG)({
        hasChanges: true,
      })
    );
  },
  updateInvitedLearnersCount: () => {
    dispatch(getActions(UPDATE_INVITED_LEARNER_COUNT)());
  },
  getIntegrationConfig: () => {
    dispatch(getActions(GET_INTEGRATIONS)());
  },
});

const mapStateToProps = (state: { ilt?: any; integrations?: any; auth?: any }) => {
  const {
    loaded,
    isLoading,
    hasError,
    series,
    staticData = EMPTY_OBJECT_READONLY,
    companySettings,
    unpublishedChanges: { hasChanges: hasUnpubChanges = false } = {},
    operationStatus,
    usersInfo = EMPTY_OBJECT_READONLY,
  } = state.ilt.details || {};
  const { integrations = EMPTY_OBJECT_READONLY, auth } = state;
  const moduleCreatesByInfo = usersInfo[staticData.createdBy] || EMPTY_OBJECT_READONLY;

  return {
    auth,
    series,
    loaded,
    hasError,
    isLoading,
    staticData,
    companySettings,
    moduleCreatesByInfo,
    operationStatus,
    hasUnpubChanges,
    integrations,
  };
};

const mergeProps = (
  stateProps: {
    auth: { [x: string]: any; company: any; features?: Readonly<{}> | undefined };
    series: any;
    staticData: { companyId: any };
    moduleCreatesByInfo: any;
    hasUnpubChanges: any;
    loaded: any;
    isLoading: any;
    hasError: any;
    operationStatus: any;
    companySettings: any;
    integrations: any;
  },
  dispatchProps: {
    getData: any;
    manipulateData: any;
    resetBuild: any;
    resetSettings: any;
    updateInvitedLearnersCount: any;
    updateHasPublishedChanges: any;
    getIntegrationConfig: any;
  },
  ownProps: any
) => {
  const { company, features: enabledFeatures = EMPTY_OBJECT_READONLY, ...auth } = stateProps.auth;

  return {
    seriesData: stateProps.series,
    moduleData: {
      ...stateProps.staticData,
      createdByInfo: stateProps.moduleCreatesByInfo,
      type: MT_MODULES.ILT,
    },
    hasUnpubChanges: stateProps.hasUnpubChanges,
    status: {
      loaded: stateProps.loaded,
      isLoading: stateProps.isLoading,
      hasError: stateProps.hasError,
    },
    operationStatus: stateProps.operationStatus,
    companySettings: stateProps.companySettings,
    companyData: {
      id: stateProps.staticData.companyId,
      ...company,
    },
    userData: auth,
    enabledFeatures: enabledFeatures,
    actions: {
      getData: dispatchProps.getData,
      manipulateData: dispatchProps.manipulateData,
      resetBuild: dispatchProps.resetBuild,
      resetSettings: dispatchProps.resetSettings,
      updateInvitedLearnersCount: dispatchProps.updateInvitedLearnersCount,
      updateHasPublishedChanges: dispatchProps.updateHasPublishedChanges,
    },
    getIntegrationConfig: dispatchProps.getIntegrationConfig,
    integrations: stateProps.integrations,
    ...ownProps,
  };
};

const withReducer = injectReducer([
  {
    name: 'ilt',
    reducer: reducer,
  },
  {
    name: 'integrations',
    reducer: integrationReducer,
  },
]);

const withConnect = connect(mapStateToProps, mapDispatchToProps, mergeProps);
const withSaga = injectSaga({ name: 'ilt', saga: saga });

export default compose<React.ComponentType>(withReducer, withSaga, withConnect)(Admin);
