import { createReducers } from '@mindtickle/medux/Reducer';

import {
  <PERSON>NA<PERSON>_EVENT_POLLING,
  <PERSON>NAGE_EVENT_GET_DATA,
  MANAGE_EVENT_UPDATE_EVENT_ID,
  MANAGE_EVENT_UPDATE_LEARNERS_DETAILS,
  MANAGE_EVENT_UPDATE_SEARCH_LEARNERS,
  MANAGE_EVENT_UPDATE_ENTITY_STATS_MAP,
  MANAGE_EVENT_GET_SERIES_INFO,
  MANAGE_EVENT_MANIPULATE_DATA,
  MANAGE_EVENT_RESET,
  MANAGE_EVENT_PAGE_GET_ENTITY_STATS,
  MANAGE_EVENT_UPDATE_ALL_LEARNERS_FOR_SAMPLE_FILE,
  MANAGE_EVENT_UPDATE_EVENT_DETAILS,
  MANAGE_EVENT_TRIGGER_SIDE_EFFECT,
} from '../actionTypes';

const manageEvent = createReducers(
  [
    {
      name: <PERSON><PERSON><PERSON>_EVENT_POLLING,
      options: {
        async: true,
        key: 'poll',
      },
    },
    {
      name: <PERSON><PERSON><PERSON>_EVENT_GET_SERIES_INFO,
      options: {
        key: 'seriesInfoMap',
        async: true,
      },
    },
    {
      name: MANAGE_EVENT_UPDATE_EVENT_DETAILS,
      options: {
        key: 'sessionsMap',
      },
    },
    {
      name: MANAGE_EVENT_UPDATE_ENTITY_STATS_MAP,
      options: {
        key: 'entityStatsMap',
      },
    },
    {
      name: MANAGE_EVENT_UPDATE_EVENT_ID,
      options: {
        key: 'eventId',
      },
    },
    {
      name: MANAGE_EVENT_PAGE_GET_ENTITY_STATS,
      options: {
        key: 'eventPageStatsFetched',
        async: true,
      },
    },
    {
      name: MANAGE_EVENT_UPDATE_ALL_LEARNERS_FOR_SAMPLE_FILE,
      options: {
        key: 'allLearnersForSampleFile',
      },
    },
    {
      name: MANAGE_EVENT_GET_DATA,
      options: {
        async: true,
      },
    },
    {
      name: MANAGE_EVENT_UPDATE_LEARNERS_DETAILS,
      options: {
        key: 'learnersMap',
      },
    },
    {
      name: MANAGE_EVENT_UPDATE_SEARCH_LEARNERS,
      options: {
        key: 'searchedLearners',
      },
    },
    {
      name: MANAGE_EVENT_MANIPULATE_DATA,
      options: {
        key: 'operationStatus',
        async: true,
      },
    },
    {
      name: MANAGE_EVENT_TRIGGER_SIDE_EFFECT,
      options: {
        key: 'triggerSideEffectInfo',
      },
    },
  ],
  { resetActionType: MANAGE_EVENT_RESET }
);

export default manageEvent;
