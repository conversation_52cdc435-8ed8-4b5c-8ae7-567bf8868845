import { combineReducers } from 'redux';

import { createReducers } from '@mindtickle/medux/Reducer';

import { SET_CONTEXT } from '../actionTypes';

import attachmentsReducer from './attachmentsReducer';
import buildReducer from './buildReducer';
import liveChallengeReducer from './liveChallengeReducers';
import manageEventPanelReducer from './manageEventPanelReducer';
import manageEventReducer from './manageEventReducer';
import manageSessionReducer from './manageSessionReducer';
import mixpanelReducer from './mixpanelReducer';
import moduleDetailReducer from './moduleDetailReducer';
import recordingsReducer from './recordingsReducer';
import trackReducer from './trackReducer';

export default combineReducers({
  build: buildReducer,
  details: moduleDetailReducer,
  recordings: recordingsReducer,
  attachments: attachmentsReducer,
  track: trackReducer,
  manageSession: manageSessionReducer,
  manageEvent: manageEventReducer,
  mixpanel: mixpanelReducer,
  manageEventPanel: manageEventPanelReducer,
  liveChallenge: liveChallengeReducer,
  context: createReducers(SET_CONTEXT),
});
