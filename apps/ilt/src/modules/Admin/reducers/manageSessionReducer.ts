import { createReducers } from '@mindtickle/medux/Reducer';

import {
  <PERSON><PERSON>GE_SESSION_POLLING,
  MA<PERSON>GE_SESSION_GET_DATA,
  <PERSON><PERSON>GE_SESSION_UPDATE_EVENT_ID,
  <PERSON><PERSON><PERSON>_SESSION_UPDATE_SESSION_ID,
  <PERSON><PERSON><PERSON>_SESSION_UPDATE_LEARNERS_DETAILS,
  MANAGE_SESSION_UPDATE_SEARCH_LEARNERS,
  MANAGE_SESSION_UPDATE_ALL_SESSIONS,
  <PERSON><PERSON><PERSON>_SESSION_ARE_SESSION_STATS_FETCHED,
  <PERSON><PERSON><PERSON>_SESSION_UPDATE_ENTITY_STATS_MAP,
  <PERSON><PERSON><PERSON>_SESSION_GET_SERIES_INFO,
  MANAGE_SESSION_MANIPULATE_DATA,
  <PERSON><PERSON><PERSON>_SESSION_RESET,
  MANAGE_SESSION_SESSION_PAGE_GET_SESSION_STATS,
  MANA<PERSON>_SESSION_UPDATE_ALL_LEARNERS_FOR_SESSION,
  <PERSON>NA<PERSON>_SESSION_UPDATE_SESSION_DETAILS,
  <PERSON>NAGE_SESSION_TRIGGER_SIDE_EFFECT,
} from '../actionTypes';

const manageSession = createReducers(
  [
    {
      name: MANAGE_SESSION_POLLING,
      options: {
        async: true,
        key: 'poll',
      },
    },
    {
      name: MANAGE_SESSION_GET_SERIES_INFO,
      options: {
        key: 'seriesInfoMap',
        async: true,
      },
    },
    {
      name: MANAGE_SESSION_UPDATE_SESSION_DETAILS,
      options: {
        key: 'sessionsMap',
      },
    },
    {
      name: MANAGE_SESSION_UPDATE_ENTITY_STATS_MAP,
      options: {
        key: 'entityStatsMap',
      },
    },
    {
      name: MANAGE_SESSION_UPDATE_SESSION_ID,
      options: {
        key: 'sessionId',
      },
    },
    {
      name: MANAGE_SESSION_UPDATE_EVENT_ID,
      options: {
        key: 'eventId',
      },
    },
    {
      name: MANAGE_SESSION_SESSION_PAGE_GET_SESSION_STATS,
      options: {
        key: 'sessionPageStatsFetched',
        async: true,
      },
    },
    {
      name: MANAGE_SESSION_UPDATE_ALL_LEARNERS_FOR_SESSION,
      options: {
        key: 'allLearnersForSession',
      },
    },
    {
      name: MANAGE_SESSION_GET_DATA,
      options: {
        async: true,
      },
    },
    {
      name: MANAGE_SESSION_UPDATE_LEARNERS_DETAILS,
      options: {
        key: 'learnersMap',
      },
    },
    {
      name: MANAGE_SESSION_UPDATE_SEARCH_LEARNERS,
      options: {
        key: 'searchedLearners',
      },
    },
    {
      name: MANAGE_SESSION_UPDATE_ALL_SESSIONS,
      options: {
        key: 'allSessions',
      },
    },
    {
      name: MANAGE_SESSION_ARE_SESSION_STATS_FETCHED,
      options: {
        key: 'areSessionStatsFetched',
      },
    },
    {
      name: MANAGE_SESSION_MANIPULATE_DATA,
      options: {
        key: 'operationStatus',
        async: true,
      },
    },
    {
      name: MANAGE_SESSION_UPDATE_ALL_SESSIONS,
      options: {
        key: 'allSessions',
      },
    },
    {
      name: MANAGE_SESSION_TRIGGER_SIDE_EFFECT,
      options: {
        key: 'triggerSideEffectInfo',
      },
    },
    {
      name: MANAGE_SESSION_ARE_SESSION_STATS_FETCHED,
      options: {
        key: 'areSessionStatsFetched',
      },
    },
  ],
  { resetActionType: MANAGE_SESSION_RESET }
);

export default manageSession;
