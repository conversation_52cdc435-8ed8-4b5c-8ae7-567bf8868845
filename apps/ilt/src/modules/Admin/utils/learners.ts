import { isObject, isUndefined } from '@mindtickle/utils';

import { TOperationStatus } from '~/modules/Admin/containers/ManageSession/typeDefs';

export const getLearnerIds = (learners: any = []) =>
  learners.reduce(
    (result: any, learnerDetail: any) => {
      if (isObject(learnerDetail)) {
        result.emailIds.push(learnerDetail.email);
        result.learnerIds.push(learnerDetail.learnerId);
      }
      return result;
    },
    { emailIds: [], learnerIds: [] }
  );

export const getErrorCode = ({
  error,
  isNewUM,
}: {
  error: { errorCode: string };
  isNewUM: boolean;
}) => {
  if (isNewUM) return '';
  return error.errorCode || '';
};

export const checkDataLoading = ({
  action,
  operationStatus,
}: {
  action: any;
  operationStatus: TOperationStatus;
}) => {
  if (!Array.isArray(action)) {
    action = [action];
  }
  const { isLoading, loadingData: { operation } = {} as object } = operationStatus;

  if (isUndefined(isLoading)) return false;
  if (action.includes(operation)) {
    return !!isLoading;
  }
  return false;
};
