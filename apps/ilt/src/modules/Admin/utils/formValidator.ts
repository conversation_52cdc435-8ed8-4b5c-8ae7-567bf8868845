import emailValidator from 'email-validator';

import { UID_LIMITS } from '~/config/constants';

export function required(value?: string) {
  return !value ? 'Required' : null;
}

export function email(value?: string) {
  return value && !emailValidator.validate(value) ? 'Invalid Email' : null;
}

export function noSpaces(value?: string) {
  return value && value.indexOf(' ') >= 0 ? "Usernames can't include spaces" : null;
}

export function lengthValidator(value?: string) {
  return value &&
    (value.trim().length < UID_LIMITS.MIN_LENGTH || value.trim().length > UID_LIMITS.MAX_LENGTH)
    ? `Usernames must be between ${UID_LIMITS.MIN_LENGTH} to ${UID_LIMITS.MAX_LENGTH} characters`
    : null;
}
