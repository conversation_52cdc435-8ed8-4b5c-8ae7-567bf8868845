import moment from 'moment';
import { LIFECYCLE_STAGES } from 'ui_shell/GlobalConstants';

import { TOAST_TYPES } from '@mindtickle/toast';
import { isString, hoursToMiliseconds } from '@mindtickle/utils';

import ILTSessionService from '~/modules/Admin/api/sessionService';
import { getLifecycleStageUrl } from '~/utils';
import { addOffsetDiff } from '~/utils/timezone';

import Routes from '../config/routes';
import {
  SESSION_TYPES,
  ENROLLMENT_FREEZE_TYPES,
  SESSION_WEBINAR_URL_VALIDATION_STATUS_TYPES,
  INTEGRATION_SOURCE,
  INTEGRATION_URLDOMAIN_IDENTIFIER,
  OPERATIONS,
  WEBINAR_SOURCE_LABELS,
  WEBINAR_MEETING_TYPES,
  getWebinarURLValidationMessage,
} from '../config/sessions.constants';
import {
  MINDTICKLE_SAMPLE_COLUMNS,
  EXPORT_ATTENDANCE_INSTRUCTIONS,
  EXPORT_EVENT_ATTENDANCE_INSTRUCTIONS,
  SUPPORTED_ATTENDED_VALUE_FOR_BULK,
  ATTENDED_TYPES,
  ENROLLMENT_STATUS_TYPES,
  DD_OPTIONS_RELEVANCE,
  RELEVANCE_TYPE,
  MINDTICKLE_EVENT_SAMPLE_COLUMNS,
} from '../config/track.constants';

import { checkIsEvent } from './checkEntityType';
import {
  getWebinarMeetingSettingsKeyByLocationType,
  getWebinarSourceByIntegration,
  getWebinarSourceByLocationType,
} from './sessionEdit';

export const updateSessionStatusDetails = (sessionData = {}) => {
  let { isPublished, timezone: { offset } = {} } = sessionData;
  offset = hoursToMiliseconds(offset);
  const endTime = addOffsetDiff(sessionData.endTime, offset);
  const startTime = addOffsetDiff(sessionData.startTime, offset);

  const isOver = Date.now() - endTime > 0 && isPublished ? true : false;
  const isOngoing = Date.now() - startTime > 0 && !isOver && isPublished ? true : false;
  return {
    ...sessionData,
    isOver,
    isOngoing,
    isPublished,
    isUpcoming: !!(!(isOver || isOngoing) && isPublished),
  };
};
export const updateEventStatusDetails = (eventData = {}) => {
  let { isPublished, startTime } = eventData;
  if (!isPublished && !startTime) {
    return {
      ...eventData,
      isOver: false,
      isOngoing: false,
      isUpcoming: false,
    };
  }
  // TODO: May have to introduce timezone change here or in the service itself
  return updateSessionStatusDetails(eventData);
};

export const createSessionTableData = ({
  sessions = [],
  sessionsMap,
  entityFilterHiddenMap = {},
  eventSessionListMap = {},
  entityStatsMap = {},
}) => {
  const parsedSessions = sessions.map(id => {
    let sessionData = sessionsMap[id];
    if (!sessionData) return null;
    const convertedEntityData = checkIsEvent(sessionData?.entityType)
      ? updateEventStatusDetails(sessionData)
      : updateSessionStatusDetails(sessionData);
    convertedEntityData.isFilterHidden = !!entityFilterHiddenMap[id];
    const hasStatsLoaded = !entityStatsMap[id] || !entityStatsMap[id].loaded ? false : true;
    convertedEntityData.hasStatsLoaded = hasStatsLoaded;
    if (hasStatsLoaded) {
      const { attended, enrolled, waiting } = entityStatsMap[id];
      convertedEntityData.attended = attended;
      convertedEntityData.enrolled = enrolled;
      convertedEntityData.waiting = waiting;
    }

    if (eventSessionListMap[id]) {
      convertedEntityData.sessions = createSessionTableData({
        sessions: eventSessionListMap[id] || [],
        sessionsMap,
        entityFilterHiddenMap,
        eventSessionListMap,
        entityStatsMap,
      });
    }
    return convertedEntityData;
  });
  return parsedSessions.filter(Boolean);
};

export const createManageSessionTableData = (
  learners = [],
  learnersMap = {},
  learnersMultipresenceInfo = {}
) =>
  learners.map(learnerId => {
    const { enrolledSessionDetails = {}, ...rest } = learnersMap[learnerId];
    return {
      ...rest,
      enrolledSessionDetails: {
        ...enrolledSessionDetails,
        ...updateSessionStatusDetails(enrolledSessionDetails),
      },
      multipresentSeriesList: learnersMultipresenceInfo[learnerId],
    };
  });

export const checkSessionIsOngoing = sessionData =>
  updateSessionStatusDetails(sessionData).isOngoing;

export const getEnrolledLearners = (learners = [], learnersMap = {}) =>
  learners.filter(learner => {
    let learnerId = typeof learner === 'string' ? learner : learner.learnerId;
    if (!learnerId && learner.email) {
      learnerId = getLearnerIdByEmail(learner.email, learnersMap);
    }
    const { enrolledSessionDetails = {} } = learnersMap[learnerId];
    return enrolledSessionDetails.enrollmentStatus === ENROLLMENT_STATUS_TYPES.ENROLLED;
  });

export const getLearnerIdByEmail = (email, learnersMap = {}) => {
  let learnerId;
  Object.keys(learnersMap).forEach(id => {
    if (learnersMap[id] && learnersMap[id].email === email) {
      learnerId = id;
    }
  });
  return learnerId;
};

export const sessionTimeFormatter = timestamp => moment(timestamp).format('MMM DD YYYY, h:mm a ');

export const sessionDateTimeFormatter = timestamp => moment(timestamp).format('MMM D, h:mm a ');

export const getSessionDateFormat = timestamp => moment(timestamp).format('MMM DD, YYYY');

export const getSessionTimeFormat = timestamp => moment(timestamp).format('hh:mm a');

export const getSessionsTableDateFormat = timestamp => moment(timestamp).format('MMMM D, YYYY');

export const getSessionsTableTimeFormat = timestamp => moment(timestamp).format('hh:mm a');

export const eventEnrolmentLockWarningTimeFormatter = timestamp =>
  moment(timestamp).format('h:mm a ddd, MMM D, YYYY');

export const getSessionTypeDDTitle = type =>
  Object.values(SESSION_TYPES).filter(sessionType => sessionType.value === type)[0].displayValue;

export const getEnrollmentFreezeTitle = type =>
  Object.values(ENROLLMENT_FREEZE_TYPES).filter(
    // eslint-disable-next-line eqeqeq
    enrollmentFreezeType => enrollmentFreezeType.value == type
  )[0];

export const getManageUpcomingSessionsUrl = baseUrl =>
  getLifecycleStageUrl({
    baseUrl,
    stage: LIFECYCLE_STAGES.INVITE,
    routes: Routes.lifecycle,
  });

export const getBuildSessionUrl = baseUrl =>
  getLifecycleStageUrl({
    baseUrl,
    stage: LIFECYCLE_STAGES.BUILD,
    routes: Routes.lifecycle,
  });

export const getPublishUrl = baseUrl =>
  getLifecycleStageUrl({
    baseUrl,
    stage: LIFECYCLE_STAGES.PUBLISH,
    routes: Routes.lifecycle,
  });

export const getManageSessionUrl = (baseUrl, path, sessionId) => {
  if (!(sessionId && baseUrl && path)) return;
  path = path.split('/');
  path.pop();
  path.push(sessionId);
  path = path.join('/');
  return `${baseUrl}${path}`;
};

export const getStatusByMap = (status, map) =>
  // eslint-disable-next-line eqeqeq
  Object.keys(map).filter(key => map[key].value == status)[0];

export const transformToMindtickleFormat = (
  learners = [],
  sampleColumns = MINDTICKLE_SAMPLE_COLUMNS
) => {
  let rows = [];
  learners.forEach(learner => {
    let row = {};
    sampleColumns.forEach(({ column, key }) => {
      row[column] = learner[key];
    });
    rows.push(row);
  });
  if (!rows.length) {
    const row = sampleColumns.reduce((result, { column }) => {
      result[column] = '';
      return result;
    }, {});
    return [row];
  }
  return rows;
};

export const getExportFileName = sessionName => `${sessionName}_enrolment_list`;

export const getModuleRelevanceText = key => {
  const relevanceKey = key ? key : RELEVANCE_TYPE.NONE;
  const option = DD_OPTIONS_RELEVANCE.find(option => option.value === relevanceKey);
  return option.text;
};

export const getExportRosterData = (learners = []) => {
  const attendees = transformToMindtickleFormat(learners, MINDTICKLE_SAMPLE_COLUMNS);
  return {
    'ENROLLMENT LIST': attendees,
    INSTRUCTIONS: [
      {
        Instructions: EXPORT_ATTENDANCE_INSTRUCTIONS,
      },
    ],
  };
};

export const getEventExportRosterData = (learners = []) => {
  const attendees = transformToMindtickleFormat(learners, MINDTICKLE_EVENT_SAMPLE_COLUMNS);
  return {
    'ENROLLMENT LIST': attendees,
    INSTRUCTIONS: [
      {
        Instructions: EXPORT_EVENT_ATTENDANCE_INSTRUCTIONS,
      },
    ],
  };
};

export const getAttendedValueFromSheet = value => {
  value = value && isString(value) ? value.trim() : '';
  if (SUPPORTED_ATTENDED_VALUE_FOR_BULK.YES.includes(value)) return ATTENDED_TYPES.ATTENDED;
  else if (SUPPORTED_ATTENDED_VALUE_FOR_BULK.NO.includes(value))
    return ATTENDED_TYPES.DID_NOT_ATTEND;
  else return ATTENDED_TYPES.UNMARKED;
};

export const isSessionCancelled = status =>
  status === ENROLLMENT_STATUS_TYPES.ENROLLED_BUT_CANCELLED ||
  status === ENROLLMENT_STATUS_TYPES.WAITING_BUT_CANCELLED;

function checkHostCohostMismatch({ sentSessionInfo: sentSession, receivedSessionInfo: session }) {
  let hasHostError = false;
  let cohostNotCreatedCount = 0;
  const meetingSettingsKey = getWebinarMeetingSettingsKeyByLocationType(sentSession.locationType);
  const { hostEmail: sentHostEmail, coHosts: sentCoHosts = [] } = sentSession[meetingSettingsKey];
  if (session) {
    const { hostEmail, coHosts = [] } = session[meetingSettingsKey];
    if (sentHostEmail !== hostEmail) {
      hasHostError = true;
    }
    let searchValue;
    const findFunc = value => value.email === searchValue.email;
    const notCreatedCohosts = sentCoHosts.reduce((accumulator, value) => {
      searchValue = value;
      if (!coHosts.some(findFunc)) {
        accumulator.push(value);
      }
      return accumulator;
    }, []);
    cohostNotCreatedCount = notCreatedCohosts.length;
  }
  return {
    hasHostError,
    cohostNotCreatedCount,
  };
}

// TODO: when what fix for multiday is disabled or no longer need this function can be removed
function whatFixMultidayChangeFlowTrigger(sessionInfo) {
  try {
    // session created with more than 24 hr duration
    if (sessionInfo.endTime - sessionInfo.startTime >= 24 * 60 * 60 * 1000) {
      const LOCAL_STORAGE_VAR = 'ilt_multiday_session_created_manually';
      const status = localStorage.getItem(LOCAL_STORAGE_VAR) || '';
      if (status !== 'true') {
        localStorage.setItem(LOCAL_STORAGE_VAR, 'true');
      }
    }
  } catch (err) {
    window.console.log('failed whafix localstore update', err);
  }
}

export function getInfoAfterSingleSessionSaved({ operationStatus }) {
  let cohostNotCreatedCount = 0;
  const {
    data: {
      operation,
      postData: { sessions: sentSessions = [] } = {},
      response: { entityMap = {}, entityIds = [] } = {},
    } = {},
  } = operationStatus;
  const sentSession = sentSessions[0];
  // In case of copy, we have ID of sentSession
  const originalSessionId = sentSession?.id;
  whatFixMultidayChangeFlowTrigger(sentSession);

  //This condition would not be executed as we are emptying the location type on copy, so below assumption holds good
  if (sentSession && WEBINAR_MEETING_TYPES.includes(sentSession.locationType)) {
    let session;
    if (operation === OPERATIONS.ADD) {
      session = entityMap[entityIds[0]];
    } else if (operation === OPERATIONS.UPDATE) {
      session = entityMap[sentSession.id];
    }
    const data = checkHostCohostMismatch({
      sentSessionInfo: sentSession,
      receivedSessionInfo: session,
    });
    cohostNotCreatedCount = data.cohostNotCreatedCount;
  }

  const operationMessage =
    OPERATIONS.ADD === operation ? (originalSessionId ? 'copied' : 'created') : 'updated';

  const toastMessages = [];
  const toastStartPosition = 0;
  toastMessages.push({
    message: `Session ${operationMessage} successfully`,
    timeout: 3000,
    hideBtn: true,
  });
  if (cohostNotCreatedCount) {
    toastMessages.push({
      message: `Unable to add ${cohostNotCreatedCount} co-hosts due to invalid ${
        WEBINAR_SOURCE_LABELS[getWebinarSourceByLocationType(sentSession.locationType)]
      } account.`,
      type: TOAST_TYPES.ERROR,
      timeout: 3000,
      hideBtn: true,
    });
  }

  return {
    toastInfo: { messages: toastMessages, toastStartPosition },
  };
}

export const isValidURL = (url = '') =>
  !!url.match(
    // eslint-disable-next-line no-useless-escape
    /^http(s)?:\/\/(www\.)?[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/
  );

export const getIntegrationSource = (url = '') => {
  try {
    const { hostname } = new URL(url);
    if (hostname) {
      if (hostname.includes(INTEGRATION_URLDOMAIN_IDENTIFIER.zoom)) {
        return INTEGRATION_SOURCE.zoom;
      } else if (hostname.includes(INTEGRATION_URLDOMAIN_IDENTIFIER.webex)) {
        return INTEGRATION_SOURCE.webex;
      } else if (hostname.includes(INTEGRATION_URLDOMAIN_IDENTIFIER.ms_teams)) {
        return INTEGRATION_SOURCE.ms_teams;
      }
    }
  } catch (err) {
    // not a valid url
  }
  return false;
};

export const checkUrlEligibilityForIntegrations = ({ location, integrations = {} }) => {
  if (location && isValidURL(location)) {
    const integrationSource = getIntegrationSource(location);
    if (integrationSource && !!integrations[integrationSource]) {
      const webinarSource = getWebinarSourceByIntegration(integrationSource);
      return {
        eligible: true,
        integrationSource,
        webinarSource,
      };
    }
  }
  return {
    eligible: false,
  };
};

export const validateWebinarURL = async ({
  location,
  password,
  webinarSource,
  signal,
  hostEmail,
}) => {
  let result = {};
  if (isValidURL(location)) {
    try {
      let response = await ILTSessionService.validateWebinarURL({
        location,
        password,
        hostEmail,
        webinarSource,
        signal,
      });
      const { errorCode = null, status = false, meetingVo, data } = response;
      result = {
        isLinkValidated: errorCode ? false : status,
        isAutoAttendanceEnabled: errorCode ? false : true,
        lastValidityStatus: status
          ? SESSION_WEBINAR_URL_VALIDATION_STATUS_TYPES.SUCCESS
          : SESSION_WEBINAR_URL_VALIDATION_STATUS_TYPES[errorCode]
          ? SESSION_WEBINAR_URL_VALIDATION_STATUS_TYPES[errorCode]
          : SESSION_WEBINAR_URL_VALIDATION_STATUS_TYPES.NONE,
        webinarSource: webinarSource,
      };
      if (status && (meetingVo || data)) {
        result.data = meetingVo || data;
      } else if (!status) {
        result.errorCode = errorCode;
      }
    } catch (error) {
      result = {
        isLinkValidated: false,
        lastValidityStatus: SESSION_WEBINAR_URL_VALIDATION_STATUS_TYPES.NONE,
        isAutoAttendanceEnabled: false,
        canRetry: true,
        webinarSource: webinarSource,
      };
    }
    return result;
  }
};

export const getValidationMessage = options => {
  const {
    webAutoAttendanceSettings: {
      lastValidityStatus = SESSION_WEBINAR_URL_VALIDATION_STATUS_TYPES.NONE,
      isAutoAttendanceEnabled,
      webinarSource,
    },
    canRetry = false,
    isIntegrationAvailable = false,
    isLocationEmpty,
  } = options;

  if (isLocationEmpty) {
    return null;
  }
  return isIntegrationAvailable && canRetry
    ? getWebinarURLValidationMessage(SESSION_WEBINAR_URL_VALIDATION_STATUS_TYPES.FAILED, {
        webinarSource,
      })
    : lastValidityStatus === SESSION_WEBINAR_URL_VALIDATION_STATUS_TYPES.SUCCESS &&
      !isAutoAttendanceEnabled
    ? getWebinarURLValidationMessage(SESSION_WEBINAR_URL_VALIDATION_STATUS_TYPES.DISABLED, {
        webinarSource,
      })
    : getWebinarURLValidationMessage(lastValidityStatus, {
        webinarSource,
      });
};
