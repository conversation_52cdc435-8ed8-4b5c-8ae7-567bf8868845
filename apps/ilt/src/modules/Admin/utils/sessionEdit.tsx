import { FormattedMessage } from 'react-intl';

import { hoursToMiliseconds } from '@mindtickle/utils';

import ILTSessionService from '~/modules/Admin/api/sessionService';
import type { TIntegrationDataMap } from '~/modules/Admin/components/SessionEditDrawer/typeDefs';
import {
  INTEGRATION_SOURCE,
  LOCATION_TYPE,
  SESSION_AUTO_ATTENDANCE_MESSAGES,
  WEBINAR_TIME_VALIDATION_ENUM,
  WEBINAR_VALIDATION_ENUM,
  WEBINAR_TIME_VALIDATION_MESSAGES,
  WEBINAR_VALIDATION_MESSAGES,
  WEBINAR_SOURCE,
  WEBINAR_SOURCE_LABELS,
  WEBEX_MEETING_SETTING_KEY,
  WEBINAR_MAX_COHOST_LENGTH,
  MS_TEAMS_MEETING_SETTING_KEY,
  ZOOM_MEETING_SETTING_KEY,
} from '~/modules/Admin/config/sessions.constants';
import { addOffsetDiff } from '~/utils/timezone';

const TEN_MIN_IN_MS = 10 * 60 * 1000;
const ONE_DAY_IN_MS = 24 * 60 * 60 * 1000;

export function checkIntegrationUserAuthEnabled(
  integrationDataMap: TIntegrationDataMap,
  integrationSource: string
) {
  return integrationDataMap[integrationSource]?.paramsValue?.userLevelAuthEnabled === true;
}

export function checkWebinarSourceToUseNewUrlValidationApi(webinarSource: string) {
  return webinarSource === WEBINAR_SOURCE.WEBEX || webinarSource === WEBINAR_SOURCE.MS_TEAMS;
}

export function checkUrlValidationWebinarSourceHasSettingKey(webinarSource: string) {
  if (webinarSource === WEBINAR_SOURCE.WEBEX) {
    return WEBEX_MEETING_SETTING_KEY;
  }
  if (webinarSource === WEBINAR_SOURCE.MS_TEAMS) {
    return MS_TEAMS_MEETING_SETTING_KEY;
  }
  return '';
}

export function getIntegrationSourceByWebinarSource(webinarSource: string) {
  if (webinarSource === WEBINAR_SOURCE.WEBEX) {
    return INTEGRATION_SOURCE.webex;
  }
  if (webinarSource === WEBINAR_SOURCE.MS_TEAMS) {
    return INTEGRATION_SOURCE.ms_teams;
  }
  if (webinarSource === WEBINAR_SOURCE.ZOOM) {
    return INTEGRATION_SOURCE.zoom;
  }
  return '';
}

export const getWebinarSourceByIntegration = (integrationSource: string) => {
  if (integrationSource === INTEGRATION_SOURCE.webex) {
    return WEBINAR_SOURCE.WEBEX;
  }
  if (integrationSource === INTEGRATION_SOURCE.zoom) {
    return WEBINAR_SOURCE.ZOOM;
  }
  if (integrationSource === INTEGRATION_SOURCE.ms_teams) {
    return WEBINAR_SOURCE.MS_TEAMS;
  }
};

export const getWebinarSourceByLocationType = (locationType: string) => {
  if (locationType === LOCATION_TYPE.WEBEX_MEETING) {
    return WEBINAR_SOURCE.WEBEX;
  }
  if (locationType === LOCATION_TYPE.MS_TEAMS_MEETING) {
    return WEBINAR_SOURCE.MS_TEAMS;
  }
  return WEBINAR_SOURCE.ZOOM;
};

export function getWebinarMeetingSettingsKeyByLocationType(locationType: string) {
  if (locationType === LOCATION_TYPE.WEBEX_MEETING) {
    return WEBEX_MEETING_SETTING_KEY;
  }
  if (locationType === LOCATION_TYPE.MS_TEAMS_MEETING) {
    return MS_TEAMS_MEETING_SETTING_KEY;
  }
  return ZOOM_MEETING_SETTING_KEY;
}

export const getWebinarMeetingSettingsKeyByWebinarSource = (webinarSource: string): string => {
  switch (webinarSource) {
    case WEBINAR_SOURCE.WEBEX:
      return WEBEX_MEETING_SETTING_KEY;
    case WEBINAR_SOURCE.ZOOM:
      return ZOOM_MEETING_SETTING_KEY;
    case WEBINAR_SOURCE.MS_TEAMS:
      return MS_TEAMS_MEETING_SETTING_KEY;
    default:
      return '';
  }
};

export const getWebinarMeetingMaxCohostLengthByLocationType = (locationType: string) =>
  locationType === LOCATION_TYPE.WEBEX_MEETING
    ? WEBINAR_MAX_COHOST_LENGTH.WEBEX
    : locationType === LOCATION_TYPE.MS_TEAMS_MEETING
    ? WEBINAR_MAX_COHOST_LENGTH.MS_TEAMS
    : WEBINAR_MAX_COHOST_LENGTH.ZOOM;

export const getWebinarMeetingMaxCohostLengthByWebinarSource = (webinarSource: string) =>
  webinarSource === WEBINAR_SOURCE.ZOOM
    ? WEBINAR_MAX_COHOST_LENGTH.ZOOM
    : webinarSource === WEBINAR_SOURCE.MS_TEAMS
    ? WEBINAR_MAX_COHOST_LENGTH.MS_TEAMS
    : WEBINAR_MAX_COHOST_LENGTH.WEBEX;

export const checkIfInValidWebinarMeetingTime = (
  startTime: number | undefined,
  endTime: number | undefined,
  webinarSource: string
) => {
  if (!startTime || !endTime) {
    return WEBINAR_TIME_VALIDATION_ENUM.MEETING_TIME_REQUIRED;
  }
  if (endTime - Date.now() < 1000) {
    return WEBINAR_TIME_VALIDATION_ENUM.PAST_MEETING_TIME;
  }
  if (startTime - Date.now() < 1000) {
    return WEBINAR_TIME_VALIDATION_ENUM.ONGOING_MEETING_TIME;
  }
  if (webinarSource === WEBINAR_SOURCE.WEBEX) {
    if (endTime - startTime < TEN_MIN_IN_MS || endTime - startTime >= ONE_DAY_IN_MS) {
      return WEBINAR_TIME_VALIDATION_ENUM.OUT_OF_ELIGIBLE_MEETING_TIME_WINDOW;
    }
  }
  return '';
};

export const getWebinarValidationMessage = (validationEnum: string | undefined) => {
  switch (validationEnum) {
    case WEBINAR_TIME_VALIDATION_ENUM.MEETING_TIME_REQUIRED:
      return <FormattedMessage {...WEBINAR_TIME_VALIDATION_MESSAGES.TIME_REQUIRED} />;
    case WEBINAR_TIME_VALIDATION_ENUM.ONGOING_MEETING_TIME:
    case WEBINAR_TIME_VALIDATION_ENUM.PAST_MEETING_TIME:
      return <FormattedMessage {...WEBINAR_TIME_VALIDATION_MESSAGES.START_TIME_PAST} />;
    case WEBINAR_TIME_VALIDATION_ENUM.OUT_OF_ELIGIBLE_MEETING_TIME_WINDOW:
      return <FormattedMessage {...WEBINAR_TIME_VALIDATION_MESSAGES.WEBEX_OUT_OF_MEETING_WINDOW} />;
    case WEBINAR_VALIDATION_ENUM.WRITE_ACCESS_NOT_GRANTED:
      return <FormattedMessage {...WEBINAR_VALIDATION_MESSAGES.WRITE_ACCESS_REQUIRED} />;
    default:
  }
};

export const getStartEndTimeWithOffset = ({
  startTime,
  endTime,
  timezone,
}: {
  startTime: any;
  endTime: any;
  timezone: any;
}) => {
  let timezoneOffset = 0;
  if (timezone) {
    timezone = timezone.originalTimezoneString || timezone;
    try {
      let tzObj = JSON.parse(timezone);
      timezoneOffset = tzObj.offset;
    } catch (e) {
      timezoneOffset = hoursToMiliseconds(timezone.offset);
    }
  }
  return {
    startTimeWithOffSet: addOffsetDiff(startTime, timezoneOffset),
    endTimeWithOffSet: addOffsetDiff(endTime, timezoneOffset),
  };
};

export const createWebinarMeeting = ({
  webinarSource,
  endTimeWithOffSet,
  startTimeWithOffSet,
  meetingTitle,
  hostEmail,
}: {
  webinarSource: string;
  endTimeWithOffSet: number;
  startTimeWithOffSet: number;
  meetingTitle: string | undefined;
  hostEmail: string;
}) =>
  ILTSessionService.createVideoConferencingMeeting({
    webinarSource: webinarSource,
    endTime: endTimeWithOffSet,
    startTime: startTimeWithOffSet,
    title: meetingTitle || `${WEBINAR_SOURCE_LABELS[webinarSource]} meeting`,
    hostEmail,
  });

export const getWebConfOAuthUrl = ({
  companyId,
  integrationSource,
}: {
  companyId: string;
  integrationSource: string;
}) => ILTSessionService.getOAuthUrl({ companyId, integrationSource });

export const deleteWebinarMeeting = (
  webinarSource: string,
  meetingId: string,
  { hostEmail }: { hostEmail: string | undefined } = { hostEmail: undefined }
) =>
  ILTSessionService.deleteVideoConferencingMeeting({
    webinarSource: webinarSource,
    meetingId,
    hostEmail,
  });

export const getClassroomLocationTooltip = (integrationList: string[] = []) => {
  const tooltipTexts: { primaryText: React.ReactNode; secondaryText: React.ReactNode } = {
    primaryText: '',
    secondaryText: '',
  };
  let integrationListLength = integrationList.length;
  if (integrationListLength) {
    const integrationTypeSupportText = integrationListLength > 1 ? ' options are' : ' option is';
    const integrationTypeLabels = integrationList
      .map(
        integrationSource =>
          WEBINAR_SOURCE_LABELS[getWebinarSourceByIntegration(integrationSource) || '']
      )
      .join(', ')
      .replace(/, ([^,]*)$/, ' or $1');
    const integrationTypes = integrationTypeLabels + integrationTypeSupportText;
    tooltipTexts.primaryText = (
      <FormattedMessage {...SESSION_AUTO_ATTENDANCE_MESSAGES.INTEGRATION_ENABLED_TOOLTIP} />
    );
    tooltipTexts.secondaryText = (
      <FormattedMessage
        {...SESSION_AUTO_ATTENDANCE_MESSAGES.SESSION_TYPE_AUTO_ATTENDANCE_TOOLTIP}
        values={{ integrationTypes }}
      />
    );
  }
  return tooltipTexts;
};
