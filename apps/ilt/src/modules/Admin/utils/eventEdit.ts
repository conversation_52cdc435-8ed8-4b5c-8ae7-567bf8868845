import moment from 'moment';

import { hoursToMiliseconds } from '@mindtickle/utils';

import { addOffsetDiff } from '~/utils/timezone';

import { ENROLLMENT_FREEZE_STATUSES } from '../config/sessions.constants';

import type { TTimezone } from '../components/SessionsTable/typeDefs';

export const parseEventEnrollmentFreezeEpoch = ({
  startTime,
  enrollmentFreezeStatus,
  enrollmentFreezeEpoch,
  enrollmentFreezeDaysBeforeEvent,
  enrollmentFreezeTimezone,
}: {
  startTime: number;
  enrollmentFreezeStatus: string;
  enrollmentFreezeEpoch: number;
  enrollmentFreezeDaysBeforeEvent?: number;
  enrollmentFreezeTimezone?: TTimezone;
}) => {
  const enrollmentFreezeTimezoneOffset = hoursToMiliseconds(enrollmentFreezeTimezone?.offset);
  const eventEnrollmentFreezeTime =
    enrollmentFreezeStatus === ENROLLMENT_FREEZE_STATUSES.RELATIVE
      ? moment(startTime).subtract(enrollmentFreezeDaysBeforeEvent, 'days').valueOf()
      : addOffsetDiff(enrollmentFreezeEpoch, enrollmentFreezeTimezoneOffset);

  return eventEnrollmentFreezeTime;
};
