import { defineMessages } from 'react-intl';

export default defineMessages({
  INTERNAL_SERVER_ERROR: 'An error occurred. Please try again.',
  INVALID_JSON_BODY: 'An error occurred. Please try again.',
  URL_TOO_LONG: 'An error occurred. Please try again.',
  NAME_ALREADY_EXISTS: 'Name already exists. Please choose another name.',
  ENROLMENT_FAILED_SESSION_FULL: "The learner can't be enrolled in this session because it's full.",
  ENROLMENT_FAILED_SESSION_NOT_ENOUGH_SEATS:
    "Selected learners can't be enrolled in this session because the session doesn't have enough seats.",
  ENROLLMENT_LOCK_DATE_GREATER_THAN_SESSION_DATE:
    "Enrollment lock date can't be greater than the session date.",
  RATE_LIMIT_BREACHED:
    'Unable to update Zoom meeting. You exceeded the daily meeting creation limit.',
  MEETING_ID_INVALID:
    "The meeting you're trying to update can't be found. Please create a new meeting.",
  ACCESS_TOKEN_INVALID:
    'Unable to update video conferencing meeting. Please re-authorize and try again.',
  ENTITY_NAME_INVALID: 'Module name should not contain code tags.',
  DEFAULT: 'An error occurred. Please try again.',
});
