import { defineMessages } from 'react-intl';

export default defineMessages({
  FREEZE_ENROLLMENT:
    'Set a date and time to lock enrollments for this event. Learners will be unable to enroll or unenroll after the set date. However, the admin can change this setting if required.',
  FREEZE_ENROLLMENT_WARNING:
    'Ensure the enrollment lock date is set before the actual event date {date}',
  FREEZE_ENROLLMENT_WARNING_UPLOAD:
    'Enrollment lock date must be set before the actual event date.',
  FREEZE_ENROLLMENT_PAST:
    'Enrollment lock date must be set between now and the event start date {date}',
  ENROLLMENT_FREEZE_SETTING: 'Edit enrollment lock details in session settings.',
  ENROLL_FREEZE_WARNING: 'Learners will be unable to enroll or unenroll after ',
  FREEZE_ENROLLMENT_PAST_EVENT_WARNING: 'Disabled for past event',
  ABSOLUTE_SUPPORT_TEXT: ' days before event date',
  TITLE: 'Enrollment lock for the event',
  SUBTITLE: 'Set a date after which enrollments will be locked for the event',
  DROPDOWN_RELATIVE: 'Number of days before event date',
  DROPDOWN_ABSOLUTE: 'On a specific date and time',
});
