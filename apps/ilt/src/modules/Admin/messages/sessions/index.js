// do not do the way following imports are done, just export a default messages a big ENUM Object
// following are done just so that all the messages from session.constants are properly mapped
export { default as SESSION_STATE } from './sessionStates';
export { default as SESSION_STATUS } from './sessionStatus';
export { default as SESSION_TYPES } from './sessionTypes';
export { default as MESSAGES_LIVE_CHALLENGE } from './liveChallenge';
export { default as WEBINAR_VALIDATIONS } from './webinarMeetings';
export { default as VALIDATION_MESSAGES } from './validations';
export { default as COMMON_MESSAGES } from './common';
export { default as LOCATION_DETAILS } from './locationDetails';
export { default as SESSION_AUTO_ATTENDANCE } from './sessionAutoAttendance';
export { default as MESSAGES_SESSION_INFO } from './infoMessages';
export { default as COMPLETION_CRITERIA } from './completionCriteria';
export { default as MESSAGES_CHECKIN } from './checkin';
export { default as ENTITY_TYPES } from './entityType';
