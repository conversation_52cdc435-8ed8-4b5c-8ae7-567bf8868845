import { defineMessages } from 'react-intl';

export default defineMessages({
  URL_VERIFICATION_IN_PROGRESS:
    'Checking this URL for {sourceLabel} auto-attendance eligibility...',
  CHECKIN_UPDATE_WARNING:
    'Please note: You can record webinar attendance with session check-in or auto-attendance. To use session check-in, disable auto-attendance.',
  CHECKIN_DISABLED_TOOLTIP:
    "Session check-in isn't available for sessions with auto-attendance for {webinarSource} enabled.",
  MANA<PERSON>_UPCOMING_SESSION_BANNER:
    'Auto-attendance has been enabled for this {sourceLabel} meeting. Click on the edit icon to change this setting.',
  <PERSON><PERSON><PERSON>_PAST_SESSION_BANNER:
    'Attendance automatically recorded. Data will be available within 2 hours of the {sourceLabel} session ending.',
  SESSION_TYPE_AUTO_ATTENDANCE_TOOLTIP:
    'You can also mark attendance for these sessions automatically if {integrationTypes} selected.',
  AUTO_ATTENDANCE_TOGGLE_TOOLTIP:
    'Learner attendance will be recorded automatically if this is toggled on. When toggled off, you must mark the attendance manually or via session check-in.',
  INTEGRATION_ENABLED_TOOLTIP: 'Select how to conduct this session.',
});
