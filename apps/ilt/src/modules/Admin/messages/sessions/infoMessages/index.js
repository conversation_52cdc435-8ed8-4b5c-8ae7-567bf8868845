import { defineMessages } from 'react-intl';

export default defineMessages({
  MAX_SCORE: 'This score will be awarded to the learner once they are marked as attended.',
  REMINDERS:
    'Remind learners about the enrolled session. A reminder email and notification will be sent to the enrolled learner before the session.',
  MAX_SEATS: 'The maximum number of learners allowed to enroll in this session.',
  WAITING_LIST:
    'Enabling this will allow learners to join the waitlist for the session when maximum enrollment is reached.',
  WAITING_LIST_EVENT:
    'Enabling this will allow learners to join the waitlist for the event when maximum enrollment is reached.',
  INSTRUCTOR:
    "Learners will automatically receive this score when their session is marked as complete. Users with 'manage series' or 'assign content' permissions can change their score anytime.",
  AUTO_ENROLL:
    'When automatic enrollment is enabled, changing enrollment status manually will be disabled.',
  NO_INVITED_LEARNERS: 'Please invite learners before managing their status.',
  NO_BUILD_PERMISSION:
    'This capability is disabled for you because you have limited access to this series.',
  NO_TRACK_PERMISSION:
    'This capability is disabled for you because you have limited access to this series.',
  INVITING_ON_UNPUBLISHED: 'Please publish your module to invite learners.',
  MANAGING_UNPUBLISHED:
    'Please publish your module and invite learners to manage their enrollment across events and sessions.',
  LIVE_EDIT_DISABLED:
    "You can't change the start time, end time or time zone if the session is live.",
  PAST_EDIT_DISABLED:
    "You can't change the start time, end time or time zone if the session is over.",
  EMAIL_INSTRUCTOR_MESSAGE: '',
  AUTO_ATTENDANCE_ENABLE_WARNING:
    'Note: To use the check-in feature, please disable auto-attendance. The check-in feature is unavailable for sessions with auto-attendance.',
  AUTO_ATTENDANCE_ENABLE_WARNING_FOR_WEBEX:
    'Note: To use the check-in feature, please disable auto-attendance. The check-in feature is unavailable for sessions with auto-attendance enabled.',
  // ZOOM_INTEGRATION_INFO:
  //   'Auto-attendance via Zoom is enabled. Please enter the Zoom webinar link/url to use this feature.',
  // WEBEX_INTEGRATION_INFO:
  //   'Auto-attendance via Webex is enabled. Please enter the Webex link/url to use this feature.',
  // ATTENDANCE_VIA_ZOOM_MESSAGE: 'Attendance via Zoom integration',
  // ATTENDANCE_VIA_WEBEX_MESSAGE: 'Attendance via Webex integration',
  FREEZE_ENROLLMENT:
    "Set a date and time to lock enrollments for this session. Learners can't enroll or unenroll after the set date. However, the admin can change this setting if required.",
  FREEZE_ENROLLMENT_WARNING:
    'Set a new date to lock enrollments as the original session date has been changed. Ensure the enrollment lock date is set before the actual session date.',
  FREEZE_ENROLLMENT_WARNING_UPLOAD:
    'Enrollment lock date must be set before the actual session date.',
  FREEZE_ENROLLMENT_PAST:
    'Enrollment lock date must be set between now and the session start date.',
  FREEZE_ENROLLMENT_PAST_EVENT:
    'Event enrollment lock date must be set between now and the session start date.',
  ENROLLMENT_FREEZE_SETTING: 'Edit enrollment lock details in session settings.',
  ENROLLMENT_FREEZE_SETTING_EVENT: 'Edit enrollment lock details in event settings.',
  ENROLL_FREEZE_WARNING: 'Learners will be unable to enroll or unenroll after ',
  FREEZE_ENROLLMENT_PAST_SESSION_WARNING: 'Disabled for past sessions.',
  INSTRUCTOR_CALENDAR_AUTO_SYNC_INFO:
    "If this is switched on, it'll automatically sync all events with the instructor calendars.",
  LEARNER_CALENDAR_AUTO_SYNC_INFO:
    "If this is selected, it'll automatically sync this event with the learner calendar.",
  ENROLLMENT_FREEZE_BEFORE_EVENT:
    'Session start date is before the enrollment lock date for the event',
  DRAWER_CHANGE_MODAL_CONFIRMATION:
    'All the changes you have made so far will be discarded and you will be redirected to the event settings panel.',
  DRAWER_CHANGE_MODAL_TITLE: 'Change settings?', // https://mindtickle.atlassian.net/browse/LA2-1565
  DRAWER_CHANGE_MODAL_OK_BUTTON: 'Change',
  EVENT_LEVEL_ENROLLMENT_ENABLED:
    'Enrollment has been enabled for the complete event only. To change this',
  DISCARD_WITH_CONFIRMATION_CONTENT: 'All the changes you have made so far will be discarded.',
  DISCARD_WITH_CONFIRMATION_TITLE: 'Discard changes?',
  DISCARD_WITH_CONFIRMATION_OK_BUTTON: 'Yes, discard',
});
