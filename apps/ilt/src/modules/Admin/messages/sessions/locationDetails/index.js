import { defineMessages } from 'react-intl';

export default defineMessages({
  PRIMARY_FACE_TO_FACE: 'Classroom location',
  PRIMARY_LINK: 'Enter meeting link',
  VIEW_LINK: 'Meeting link',
  PRIMARY_WEBEX_MEETING: 'Generate a Webex meeting link.',
  VIEW_WEBEX_MEETING: 'Webex meeting',
  PRIMARY_ZOOM_MEETING: 'Generate Zoom meeting link',
  VIEW_ZOOM_MEETING: 'Zoom meeting',
  PRIMARY_MS_TEAMS_MEETING: 'Generate MS Teams meeting link',
  VIEW_MS_TEAMS_MEETING: 'MS Teams meeting',
  SECONDARY_FACE_TO_FACE: 'Meet at a physical location',
  SECONDARY_LINK: 'Zoom, Webex, Google Meet etc.',
  SECONDARY_WEBEX_MEETING: 'A Webex meeting will be automatically created.',
  SECONDARY_ZOOM_MEETING: 'A Zoom meeting will be automatically created.',
  SECONDARY_MS_TEAMS_MEETING: 'An MS Teams meeting will be automatically created.',
  PLACEHOLDER_CLASSROOM_LOCATION: 'Enter location or meeting link',
  PLACEHOLDER_LINK: 'Enter meeting URL',
});
