import { defineMessages } from 'react-intl';

export default defineMessages({
  LIVE_CHALLENGE: 'Live challenge',
  MODERATOR_LINK: 'Moderator link:',
  PLAYER_LINK: 'Player link:',
  COPY_MODERATOR_LINK: 'Copy moderator link',
  COPY_PLAYER_LINK: 'Copy player link',
  EMAIL_LINKS: 'Email links',
  REPLACE: 'Replace',
  DELETE: 'Delete',
  QUESTIONS: 'Questions',
  EXPORT_LC_REPORT: 'Export live-challenge report',
  SESSION: 'Session',
  EXPORT_LC_REPORT_DESC: 'Send a report of the live-challenge to the email addresses below',
  EXPORTED_SUCCESSFULLY: 'Live challenge report exported successfully.',
  EXPORT_FAILED: 'Unable to export the live challenge report. Please try again.',
  CREATING_LC_LOADER_MESSAGE: 'Please wait while we create your live challenge.',
  PLAYER_LINK_COPIED: 'Player link copied successfully.',
  MODER<PERSON><PERSON>_LINK_COPIED: 'Moderator link copied successfully.',
  <PERSON><PERSON><PERSON>_SHARE_SUCCESS: 'Emails with game links successfully sent.',
  <PERSON><PERSON><PERSON>_SHARE_FAIL: 'Unable to send emails with game links.',
});
