import { defineMessages } from 'react-intl';

export default defineMessages({
  CREATE_LIVE_CHALLENGE_TITLE: 'Create live challenge',
  CREATE_LIVE_CHALLENGE_TOOLTIP_TITLE:
    'Create a live quiz as an in-session activity to engage your audience. The moderator can start the quiz anytime during the session.',
  <PERSON><PERSON><PERSON>OAD_SAMPLE: 'Download sample',
  CREATE_LC_MODAL_SUB_HEADER:
    'You can now create a live quiz game as an in-session activity to engage your audience. To create, download the sample file and populate your questions.',
  LEARN_HOW: 'Learn how',
  DROP_FILE_TEXT: 'Drop your file here to create a live challenge',
  SUPPORTED_FILE_TYPES: 'Supported file types: .xlsx, .xlsb, .xlsm, .xls, .csv, .txt',
  MAX_4_MB: 'Maximum file size: 4 MB',
});
