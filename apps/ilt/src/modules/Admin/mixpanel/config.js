import { PROJECT_TYPE } from '~/config/constants';
import { MT_MODULES } from '~/config/global.config';

export const mixpanelIdentityPath = {
  moduleType: MT_MODULES.ILT,
  moduleName: '@@store.ilt.details.staticData.name',
  moduleId: '@@store.ilt.details.staticData.id',
  seriesId: '@@store.ilt.details.series.id',
  seriesName: '@@store.ilt.details.series.name',
  companyName: '@@store.auth.company.id',
  orgId: '@@store.auth.company.orgId',
  companyType: '@@store.auth.company.companyType',
  projectType: PROJECT_TYPE,
  zoom_integration_enabled: '@@store.integrations.data.zoom.enabled',
  userId: '@@store.auth.id',
};
