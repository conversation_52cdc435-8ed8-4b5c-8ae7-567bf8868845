import _get from 'lodash/get';
import moment from 'moment';

import { isBoolean } from '@mindtickle/utils';

import { sessionTimeFormatter } from '~/modules/Admin/utils';
import { findKeyWithDotNotaion } from '~/utils';
import { getTimezoneFromString } from '~/utils/timezone';

import {
  SUPPORTED_FILTERS as SESSION_SUPPORTED_FILTERS,
  SESSION_STATE,
} from '../config/sessions.constants';
import { ENROLLMENT_FREEZE_STATUSES } from '../config/sessions.constants';
import { ENROLLMENT_STATUS } from '../config/track.constants';
import { updateSessionStatusDetails } from '../utils';

export const getSessionKey = (value, { sessions = [] } = {}) => {
  const session = sessions[0];
  value = findKeyWithDotNotaion(value, session);
  if (isBoolean(value)) return value ? 'yes' : 'no';
  return value;
};

export const getNotifyLearnersOnCancellation = (
  operation,
  { notifyLearnersOnCancellation = true }
) => notifyLearnersOnCancellation;

// eslint-disable-next-line no-unused-vars
export const getDomLoadTime = (op, response) =>
  window.performance.timing.domComplete - window.performance.timing.requestStart;

// eslint-disable-next-line no-unused-vars
export const getDomInteractiveTime = (op, response) => {
  window.performance.timing.domInteractive - window.performance.timing.requestStart;
};

// eslint-disable-next-line no-unused-vars
export const getPercievedPageLoad = (op, response) => {
  const startTime =
    window.pagePerformanceData && window.pagePerformanceData.startTime
      ? window.pagePerformanceData.startTime
      : 0;
  return Date.now() - startTime;
};

// eslint-disable-next-line no-unused-vars
export const isFirstLoad = (op, response) =>
  window.pagePerformanceData && window.pagePerformanceData.from ? false : true;

export const getSessionKeyLength = (value, { session = {} } = {}) => {
  value = findKeyWithDotNotaion(value, session);
  if (value && Array.isArray(value)) {
    return value.length;
  }
};

export const getMode = (operation, { session = {} } = {}) => (session.id ? 'edit' : 'new');

export const getCreationMethod = (value, { sessions = [] } = {}) =>
  sessions.map(session => (session && session.id ? 'duplicate' : 'new'));

export const getSessionStatus = (operation, { session = {} } = {}) => {
  const updatedDetails = updateSessionStatusDetails(session);
  if (!updatedDetails.isPublished) return 'not published';
  else if (updatedDetails.isUpcoming) return 'upcoming';
  else if (updatedDetails.isOngoing) return 'live';
  else if (updatedDetails.isOver) return 'over';
};

export const getIdsForDeletion = (type, { processIds = [], learners = [] } = {}) => {
  switch (type) {
    case 'learners':
      return learners.join(',');
    case 'sessions':
      return processIds.join(',');
    default: //
  }
};

export const getIdsForLockedDeletion = (type, { processIds = [], sessionsMap = {} } = {}) => {
  const checkEnrollmentLock = sessionID => {
    let enrollmentFreezeEnabled =
      _get(sessionsMap, `[${sessionID}].enrollmentFreezeStatus`) !==
      ENROLLMENT_FREEZE_STATUSES.DISABLED;
    return enrollmentFreezeEnabled;
  };

  let enrollmentLockedSessionIDs = processIds.filter(checkEnrollmentLock);
  return enrollmentLockedSessionIDs.join(',');
};

export const getDeletedSessionsData = (type, { processIds = [], sessionsMap = {} } = {}) =>
  processIds.map(sessionId => _get(sessionsMap, `[${sessionId}].${type}`));

export const getIdsForLockedDeletionLength = (type, data) => {
  const enrollmentLockedIds = getIdsForLockedDeletion(type, data) || '';
  return enrollmentLockedIds ? enrollmentLockedIds.split(',').length : 0;
};

export const getIdsForDeletionLength = (type, data) => {
  const ids = getIdsForDeletion(type, data) || '';
  return ids.split(',').length;
};

export const getLearnerIdForViewProfile = (type, { learners = [] } = {}) => learners[0];

export const getLearnersCount = (key, { learners = [] } = {}) => learners.length;

export const getFilters = (fieldType, { filters = {} } = {}) => {
  const result = {
    type: [],
    value: [],
  };
  let filterValue;
  Object.keys(filters).forEach(filterKey => {
    const filterType = filterKey;
    switch (filterType) {
      case SESSION_SUPPORTED_FILTERS.SESSION_STATE:
        result.type.push('Session State');
        filterValue = Object.values(SESSION_STATE).filter(
          // eslint-disable-next-line eqeqeq
          ({ filterValue }) => filterValue == filters[filterKey]
        );
        result.value.push(filterValue.displayValue);
        break;
      case SESSION_SUPPORTED_FILTERS.SEARCH:
        result.type.push('SEARCH');
        result.value.push(filters[filterKey]);
        break;
      default:
        result.type.push(filterType);
        result.value.push(filters[filterKey]);
        break;
    }
  });
  return result[fieldType] && result[fieldType].join(',');
};

export const getSessionsFilters = (fieldType, { filters = [] } = {}) => {
  const result = {
    type: [],
    value: [],
  };
  filters.forEach(filter => {
    const filterType = filter.type;
    switch (filterType) {
      case SESSION_SUPPORTED_FILTERS.SESSION_STATE:
        if (filter.value) {
          result.type.push('session_state');
          result.value.push({ type: 'session_state', value: filter.value });
        }
        break;
      case SESSION_SUPPORTED_FILTERS.SEARCH:
        if (filter.value) {
          result.type.push('search');
          result.value.push({ type: 'search', value: filter.value });
        }
        break;
      case SESSION_SUPPORTED_FILTERS.ILT_DATE_RANGE_DD:
        if (filter.value.length > 0) {
          result.type.push('date_range');
          result.value.push({ type: 'date_range', value: filter.value[0] + '-' + filter.value[1] });
        }
        break;
      case SESSION_SUPPORTED_FILTERS.SESSION_TYPE:
        if (filter.value) {
          result.type.push('session_type');
          result.value.push({ type: 'session_type', value: filter.value });
        }
        break;
      case SESSION_SUPPORTED_FILTERS.SESSION_STATUS:
        if (filter.value.length > 0) {
          result.type.push('session_status');
          result.value.push({ type: 'session_status', value: filter.value });
        }
        break;
      default:
        result.type.push(filterType);
        result.value.push({ type: filterType, value: filter.value });
        break;
    }
  });
  return result[fieldType];
};

export const getLearnerKey = (key, { learners = [] } = {}) =>
  learners
    .map(learner => {
      const value = findKeyWithDotNotaion(key, learner);
      if (isBoolean(value)) return value ? 'yes' : 'no';
      return value;
    })
    .unique()
    .join(',');

export const getIsEnrollmentLockedForLearners = (key, { learners = [] } = {}) => {
  const value = _get(learners[0], key, 'DISABLED');
  return value !== 'DISABLED';
};

export const getLearnerEnrollmentStatus = (type, { learners = [], learnersMap = {} }) => {
  const { learnerIds, status } = learners.reduce(
    (result, learner) => {
      result.learnerIds.push(learner.learnerId);
      result.status.push(learner.operation);
      return result;
    },
    { learnerIds: [], status: [] }
  );
  switch (type) {
    case 'old':
      return learnerIds
        .map(id => {
          const details = (learnersMap[id] && learnersMap[id].enrolledEntityDetails) || {};
          const statusDetails = ENROLLMENT_STATUS[details.enrollmentStatus];
          return statusDetails.mixpanel;
        })
        .join(',');
    case 'new':
      return status
        .map(value => {
          let result;
          Object.values(ENROLLMENT_STATUS).forEach(details => {
            // eslint-disable-next-line eqeqeq
            if (details.apiOperation == value) {
              result = details.mixpanel;
            }
          });
          return result;
        })
        .join(',');
    default: //
  }
};

export const getManageSessionId = (value, { sessionId } = {}) => sessionId;

export const getZoomIntegration = (value, { sessionId } = {}) => sessionId;

export const getAutoAttendance = (value, { sessions = [] } = {}) =>
  _get(sessions, '[0].webAutoAttendanceSettings.isAutoAttendanceEnabled', false);

export const getIsEnrollmentLocked = (value, { sessions = [] } = {}) => {
  const enrollmentFreezeEnabled =
    _get(sessions, '[0].enrollmentFreezeStatus') !== ENROLLMENT_FREEZE_STATUSES.DISABLED;
  return enrollmentFreezeEnabled;
};

export const getEnrollmentLockType = (value, { sessions = [] } = {}) => {
  const enrollmentFreezeStatus = _get(
    sessions,
    '[0].enrollmentFreezeStatus',
    ENROLLMENT_FREEZE_STATUSES.DISABLED
  );
  let modifiedEnrollmentFreezeStatus = '';

  switch (enrollmentFreezeStatus) {
    case ENROLLMENT_FREEZE_STATUSES.RELATIVE:
      modifiedEnrollmentFreezeStatus = 'days_before';
      break;
    case ENROLLMENT_FREEZE_STATUSES.ABSOLUTE:
      modifiedEnrollmentFreezeStatus = 'specific_timestamp';
      break;
    default:
      modifiedEnrollmentFreezeStatus = '';
      break;
  }
  return modifiedEnrollmentFreezeStatus;
};

export const getEnrollmentLockValue = (value, { sessions = [] } = {}) => {
  const enrollmentFreezeStatus = _get(
    sessions,
    '[0].enrollmentFreezeStatus',
    ENROLLMENT_FREEZE_STATUSES.DISABLED
  );
  const enrollmentFreezeEpoch = _get(sessions, '[0].enrollmentFreezeEpoch', 0);
  const sessionStartEpoch = _get(sessions, '[0].startTime', 0);
  const timezone = _get(sessions, '[0].timezone', '');
  const timezoneObj =
    timezone === undefined || typeof timezone === 'object'
      ? timezone
      : getTimezoneFromString(timezone)?.timezone;
  let enrollmentFeezeTime;

  switch (enrollmentFreezeStatus) {
    case ENROLLMENT_FREEZE_STATUSES.RELATIVE:
      enrollmentFeezeTime = moment(sessionStartEpoch).diff(moment(enrollmentFreezeEpoch), 'days');
      break;
    case ENROLLMENT_FREEZE_STATUSES.ABSOLUTE:
      enrollmentFeezeTime =
        sessionTimeFormatter(enrollmentFreezeEpoch) + timezoneObj.shortDisplayName;
      break;
    default:
      enrollmentFeezeTime = '';
      break;
  }
  return enrollmentFeezeTime;
};
