import { LIFECYCLE_STAGES } from 'ui_shell/GlobalConstants';

import { MODULE_EVENT_NAME } from '../hooks/useILTAdminSnowplowTracker/constants';

export const OPERATIONS = {
  RENAME: 'rename',
  DISCARD: 'discard',
  ARCHIVE: 'archive',
  <PERSON><PERSON><PERSON><PERSON>: 'refetch',
};

export const OPERATION_MODULE_EVENT_TRACK_MAP = {
  [OPERATIONS.RENAME]: MODULE_EVENT_NAME.CHANGE_MODULE_NAME,
  [OPERATIONS.DISCARD]: MODULE_EVENT_NAME.DELETE_CONTENT,
  [OPERATIONS.ARCHIVE]: MODULE_EVENT_NAME.ARCHIVE_CONTENT,
};

export const MODULE_OPTION = {
  RENAME: OPERATIONS.RENAME,
  DISCARD: OPERATIONS.DISCARD,
  ARCHIVE: OPERATIONS.ARCHIVE,
  // VIEW_ANALYTICS: 'view-analytics',
  MODULE_URL: 'module-url',
};

export const STAGES = {
  [LIFECYCLE_STAGES.BUILD]: 'Sessions',
  [LIFECYCLE_STAGES.SETTINGS]: 'Settings',
  [LIFECYCLE_STAGES.PUBLISH]: 'Publish',
  // [LIFECYCLE_STAGES.INVITE]: "Manage"
};

export const ILT_ENTITY_LEARNER_URL_IDENTIFIERS = {
  EVENT: 'event',
  SESSION: 'session',
} as const;

export const ILT_ENTITIES = {
  EVENT: 'EVENT',
  SESSION: 'INDEPENDENT_SESSION',
  SESSION_WITHIN_EVENT: 'SESSION_WITHIN_EVENT',
} as const;

export const PAGE_LOAD_DATA = {
  ILT_BUILD_PAGE: {
    PAGE_NAME: 'module_build_page',
  },
  ILT_SETTINGS_PAGE: {
    PAGE_NAME: 'module_settings_page',
  },
  ILT_SETTINGS_GENERAL: {
    PAGE_NAME: 'module_settings_general_page',
  },
  ILT_SETTINGS_REMINDERS: {
    PAGE_NAME: 'module_settings_reminders_page',
  },
  ILT_SETTINGS_SCORING: {
    PAGE_NAME: 'module_settings_scoring_page',
  },
  ILT_MANAGE_ALL_SESSIONS_PAGE: {
    PAGE_NAME: 'module_manage_all_sessions_page',
  },
  ILT_MANAGE_SESSION_PAGE: {
    PAGE_NAME: 'module_manage_session_page',
  },
  ILT_MANAGE_EVENT_PAGE: {
    PAGE_NAME: 'module_manage_event_page',
  },
  ILT_PUBLISH_PAGE: {
    PAGE_NAME: 'module_publish_page',
  },
  ILT_INVITE_AND_TRACK_PAGE: {
    PAGE_NAME: 'module_invite_and_track_page',
  },
};
