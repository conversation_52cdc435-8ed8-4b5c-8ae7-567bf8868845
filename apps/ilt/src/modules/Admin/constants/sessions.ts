export const SUPPORTED_FILTERS = {
  SESSION_STATE: 'sessionState',
  SEARCH: 'search',
  ILT_DATE_RANGE_DD: 'dateRange',
  SESSION_TYPE: 'sessionType',
  SESSION_STATUS: 'sessionStatus',
  ENTITY_TYPE: 'entityType',
} as const;

export const SUPPORTED_FILTERS_API_NAME = {
  [SUPPORTED_FILTERS.SESSION_STATE]: 'scheduleStatus',
  [SUPPORTED_FILTERS.SEARCH]: 'query',
  [SUPPORTED_FILTERS.SESSION_TYPE]: 'meetingType',
  [SUPPORTED_FILTERS.SESSION_STATUS]: 'entityState',
  [SUPPORTED_FILTERS.ENTITY_TYPE]: 'entityType',
} as const;

export const SUPPORTED_FILTERS_API_KEY = {
  [SUPPORTED_FILTERS.SESSION_STATE]: {
    ALL: 'ALL',
    LIVE: 'LIVE',
    UPCOMING: 'UPCOMING',
    PAST: 'PAST',
  },
  [SUPPORTED_FILTERS.SEARCH]: 'query',
  [SUPPORTED_FILTERS.SESSION_STATUS]: {
    PUBLISHED: 'PUBLISHED_NOT_CANCELLED',
    UNPUBLISHED: 'UNPUBLISHED',
    CANCELLED: 'CANCELLED',
  },
  [SUPPORTED_FILTERS.SESSION_TYPE]: {
    ALL: 'ALL',
    CLASSROOM: 'CLASSROOM',
    WEBINAR: 'WEBINAR',
  },
  [SUPPORTED_FILTERS.ENTITY_TYPE]: {
    ALL: 'ALL',
    EVENTS_WITH_ITS_SESSIONS: 'EVENTS_WITH_ITS_SESSIONS',
    INDEPENDENT_SESSION: 'INDEPENDENT_SESSION',
  },
} as const;

export const OPERATIONS = {
  GET: 'get',
  ADD: 'add',
  ADD_EVENT: 'addEvent',
  ADD_BULK: 'addBulk',
  COPY: 'copy',
  COPY_EVENT: 'copyEvent',
  SEARCH: 'search',
  REMOVE: 'remove',
  UPDATE: 'update',
  UPDATE_EVENT: 'updateEvent',
  LOAD_MORE: 'loadMore',
  FETCH_STATS: 'fetchStats',
  CANCEL: 'cancel',
  CANCEL_EVENT: 'cancelEvent',
  LOAD_SESSION: 'loadSession',
  SHARE_CHECKIN_CODE: 'shareCheckinCode',
  SORT: 'sort',
} as const;

export const OPERATIONS_NEEDING_STATS_FETCH = [
  OPERATIONS.GET,
  OPERATIONS.SEARCH,
  OPERATIONS.LOAD_MORE,
  OPERATIONS.SORT,
];

export const OPERATIONS_NEEDING_LOCAL_TIME_FETCH = [
  OPERATIONS.GET,
  OPERATIONS.SEARCH,
  OPERATIONS.LOAD_MORE,
  OPERATIONS.SORT,
];

export const OPERATIONS_NEEDING_ASYNC_LIVECHALLENGE_FETCH = [
  OPERATIONS.GET,
  OPERATIONS.SEARCH,
  OPERATIONS.LOAD_MORE,
  OPERATIONS.SORT,
];

export const RANGE_PICKER_STATE = 'range-picker';

export const SESSION_TYPES_ENUM = {
  WEBINAR: 'WEBINAR',
  CLASSROOM: 'CLASSROOM',
  HYBRID: 'HYBRID',
} as const;

export const WEBINAR_TIME_VALIDATION_ENUM = {
  MEETING_TIME_REQUIRED: 'MEETING_TIME_REQUIRED',
  OUT_OF_ELIGIBLE_MEETING_TIME_WINDOW: 'OUT_OF_ELIGIBLE_MEETING_TIME_WINDOW',
  PAST_MEETING_TIME: 'PAST_MEETING_TIME',
  ONGOING_MEETING_TIME: 'ONGOING_MEETING_TIME',
};

export const WEBINAR_VALIDATION_ENUM = {
  WRITE_ACCESS_NOT_GRANTED: 'WRITE_ACCESS_NOT_GRANTED',
};

// ILT saves it
export const WEBINAR_SOURCE = {
  NONE: 'NONE',
  ZOOM: 'ZOOM',
  WEBEX: 'WEBEX',
  MS_TEAMS: 'MSTEAMS',
};

// comes from integration webconf api
export const INTEGRATION_SOURCE = {
  zoom: 'zoom',
  webex: 'webex',
  ms_teams: 'ms-teams',
} as const;

export const WEBINAR_SOURCE_LABELS = {
  [WEBINAR_SOURCE.ZOOM]: 'Zoom',
  [WEBINAR_SOURCE.WEBEX]: 'Webex',
  [WEBINAR_SOURCE.MS_TEAMS]: 'MS Teams',
} as const;

export const INTEGRATION_URLDOMAIN_IDENTIFIER = {
  zoom: 'zoom.',
  webex: 'webex.',
  ms_teams: 'teams.',
} as const;

export const WEBEX_MEETING_SETTING_KEY = 'webexMeetingSettings';
export const ZOOM_MEETING_SETTING_KEY = 'zoomMeetingSettings';
export const MS_TEAMS_MEETING_SETTING_KEY = 'msteamsMeetingSettings';

export const LOCATION_TYPE = {
  FACE_TO_FACE: 'FACE_TO_FACE', // This was proposed as FACE_TO_FACE but now it is classroom location.
  LINK: 'LINK',
  WEBEX_MEETING: 'WEBEX_MEETING',
  ZOOM_MEETING: 'ZOOM_MEETING',
  MS_TEAMS_MEETING: 'MSTEAMS_MEETING',
};

export const LOCATION_TYPE_ICON_TYPE = {
  [LOCATION_TYPE.FACE_TO_FACE]: 'classroom',
  [LOCATION_TYPE.LINK]: 'link',
  [LOCATION_TYPE.WEBEX_MEETING]: 'webex',
  [LOCATION_TYPE.ZOOM_MEETING]: 'zoom',
  [LOCATION_TYPE.MS_TEAMS_MEETING]: 'msteams',
};

export const LOCATION_TYPE_TO_DISABLED_CHECKIN_MESSAGE = {
  [LOCATION_TYPE.LINK]: 'all meeting links',
  [LOCATION_TYPE.WEBEX_MEETING]: 'Webex',
  [LOCATION_TYPE.ZOOM_MEETING]: 'Zoom',
  [LOCATION_TYPE.MS_TEAMS_MEETING]: 'MS Teams',
};

export const WEBINAR_MEETING_TYPES = [
  LOCATION_TYPE.WEBEX_MEETING,
  LOCATION_TYPE.ZOOM_MEETING,
  LOCATION_TYPE.MS_TEAMS_MEETING,
];

export const WEBINAR_MAX_COHOST_LENGTH = {
  ZOOM: 10,
  MS_TEAMS: 10,
  WEBEX: 30,
};

export const MODES = {
  ADD: 'add',
  ADD_BULK: 'addBulk',
  EDIT: 'edit',
  VIEW: 'view',
  COPY: 'copy',
  VIEW_URL: 'viewUrl',
  CANCEL: 'cancel',
};

export const DEFAULT_REMINDER_SECONDS = 86400;

export const DEFAULT_PAGINATION = {
  rows: 20,
  start: 0,
};

export const COMPLETION_CRITERIA_ENUM = {
  ATTENDED: 'ATTENDED',
  ATTENDED_OR_WATCHED: 'ATTENDED_OR_WATCHED',
};

export const SESSION_WEBINAR_URL_VALIDATION_STATUS_TYPES = {
  SUCCESS: 'SUCCESS',
  DISABLED: 'DISABLED',
  INTEGRATION_AUTH_NOT_SETUP: 'INTEGRATION_AUTH_NOT_SETUP',
  INTEGRATION_AUTHENTICATION_ERROR: 'INTEGRATION_AUTHENTICATION_ERROR',
  INTEGRATION_USER_AUTH_NOT_SETUP: 'INTEGRATION_USER_AUTH_NOT_SETUP',
  MEETING_DOES_NOT_EXIST: 'MEETING_DOES_NOT_EXIST',
  MEETING_TYPE_NOT_SUPPORTED: 'MEETING_TYPE_NOT_SUPPORTED',
  REPORT_API_ACCESS_NOT_AVAILABLE: 'REPORT_API_ACCESS_NOT_AVAILABLE',
  WEBCONF_SOURCE_NOT_SUPPORTED: 'WEBCONF_SOURCE_NOT_SUPPORTED',
  FAILED: 'FAILED',
  NONE: 'NONE',
  // webex
  PARTICIPANT_FETCH_NOT_ALLOWED: 'PARTICIPANT_FETCH_NOT_ALLOWED',
  INVALID_MEETING_LINK: 'INVALID_MEETING_LINK',
  MEETING_LINK_EMPTY: 'MEETING_LINK_EMPTY',
  UNAUTHORIZED_ACTION: 'UNAUTHORIZED_ACTION',
  INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR',
};

export const SESSION_WEBINAR_URL_LAST_VALIDATION_STATUS_ERROR_TYPES = [
  SESSION_WEBINAR_URL_VALIDATION_STATUS_TYPES.INTEGRATION_USER_AUTH_NOT_SETUP,
  SESSION_WEBINAR_URL_VALIDATION_STATUS_TYPES.UNAUTHORIZED_ACTION,
  SESSION_WEBINAR_URL_VALIDATION_STATUS_TYPES.INTERNAL_SERVER_ERROR,
];

// Following are the error codes which we can receive in meeting creation flow
export const SESSION_WEBINAR_STATUS_TYPES = Object.assign(
  {},
  SESSION_WEBINAR_URL_VALIDATION_STATUS_TYPES,
  {
    TITLE_EMPTY: 'TITLE_EMPTY', // title cannot be null or empty
    START_TIME_REQUIRED: 'START_TIME_REQUIRED', // start epoch is required
    END_TIME_REQUIRED: 'END_TIME_REQUIRED', // end epoch is required
    EMAIL_INVALID: 'EMAIL_INVALID',
    START_AFTER_END_TIME: 'START_AFTER_END_TIME',
    HOST_EMAIL_EMPTY: 'HOST_EMAIL_EMPTY',
    AUTH_TOKEN_NOT_FOUND: 'AUTH_TOKEN_NOT_FOUND',
    USER_AUTH_TOKEN_NOT_FOUND: 'USER_AUTH_TOKEN_NOT_FOUND',
    EPOCH_CONVERSION_FAILURE: 'EPOCH_CONVERSION_FAILURE', // epoch <> ISO8601 conversion failure
    RESPONSE_CONVERSION_FAILURE: 'RESPONSE_CONVERSION_FAILURE',
    MULTIPLE_MEETINGS: 'MULTIPLE_MEETINGS',
    SOURCE_NOT_SUPPORTED: 'SOURCE_NOT_SUPPORTED',
    MAX_MANAGE_COHOST_LIMIT_BREACH: 'MAX_MANAGE_COHOST_LIMIT_BREACH',
    MAX_COHOST_ERROR_LIMIT_BREACH: 'MAX_COHOST_ERROR_LIMIT_BREACH',
    PASSWORD_EMPTY: 'PASSWORD_EMPTY',
    MEETING_ID_EMPTY: 'MEETING_ID_EMPTY',
    MEETING_NOT_ENDED: 'MEETING_NOT_ENDED',
    ACCESS_TOKEN_INVALID: 'ACCESS_TOKEN_INVALID',
    RATE_LIMIT_BREACHED: 'RATE_LIMIT_BREACHED',
    // flow specific subcodes
    /**
     * Create meeting
     */

    TITLE_TOO_LONG: 'TITLE_TOO_LONG', // length of the title greater than the length of the title supported by tool
    MEETING_TIME_BEFORE_CURRENT_TIME: 'MEETING_TIME_BEFORE_CURRENT_TIME', // meeting start or end time before current time
    HOST_EMAIL_NOT_ALLOWED: 'HOST_EMAIL_NOT_ALLOWED', // host email is either {not part of org, lacking permissions to be host, not part of webex}

    /**
     * Update/Delete meeting
     */
    MEETING_ID_INVALID: 'MEETING_ID_INVALID', // meeting id is not valid
    USER_LACKING_HOST_PRIVILEGES: 'USER_LACKING_HOST_PRIVILEGES', //

    /**
     * DEFAULT
     */
    UNKNOWN: 'UNKNOWN',
    INVALID_JSON: 'INVALID_JSON',
    NULL_RESPONSE: 'NULL_RESPONSE',
    EMPTY_RESPONSE: 'EMPTY_RESPONSE',
  }
);

export const WEBINAR_HOST_ERRORS = [
  SESSION_WEBINAR_STATUS_TYPES.EMAIL_INVALID,
  SESSION_WEBINAR_STATUS_TYPES.HOST_EMAIL_EMPTY,
  SESSION_WEBINAR_STATUS_TYPES.HOST_EMAIL_NOT_ALLOWED,
  SESSION_WEBINAR_STATUS_TYPES.USER_LACKING_HOST_PRIVILEGES,
  SESSION_WEBINAR_STATUS_TYPES.RATE_LIMIT_BREACHED,
  SESSION_WEBINAR_STATUS_TYPES.AUTH_TOKEN_NOT_FOUND,
  SESSION_WEBINAR_STATUS_TYPES.USER_AUTH_TOKEN_NOT_FOUND,
];

export const WEBINAR_CREATION_ERRORS = [SESSION_WEBINAR_STATUS_TYPES.ACCESS_TOKEN_INVALID];

export const DEFAULT_WEBINAR_SETTINGS = {
  isLinkValidated: false,
  isAutoAttendanceEnabled: false,
  lastValidityStatus: SESSION_WEBINAR_URL_VALIDATION_STATUS_TYPES.NONE,
};

// TODO: May not be in use if not the do remove as similar kind of mapping is already created as const INTEGRATION_SOURCE
export const INTEGRATION_KEY_NAMES = {
  ZOOM: 'zoom',
};

export const ENROLLMENT_FREEZE_STATUSES = {
  DISABLED: 'DISABLED',
  ABSOLUTE: 'ABSOLUTE',
  RELATIVE: 'RELATIVE',
};

export const ILT_CALENDAR_AUTO_SYNC_CONFIG_KEY = 'isILTSessionCalendarAutoSyncEnabled';
