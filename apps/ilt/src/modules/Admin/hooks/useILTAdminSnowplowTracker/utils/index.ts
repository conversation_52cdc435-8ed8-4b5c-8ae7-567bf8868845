import { noop } from '@mindtickle/utils';

import type { TSnowplowTrackerType, TEventDataMap } from '../typeDefs';

export function sendEventUsingOperationStatus(
  tracker: TSnowplowTrackerType,
  {
    operationStatus,
    extraTrackingProperties = {},
  }: { operationStatus: any; extraTrackingProperties?: { [key: string]: any } },
  {
    eventOperationMap,
    eventDataMap,
  }: {
    eventOperationMap: { [key: string]: string };
    eventDataMap: TEventDataMap;
  }
) {
  try {
    const {
      data: { postData, response, operation } = {
        postData: undefined,
        response: undefined,
        operation: undefined,
      },
    } = operationStatus;
    if (!operation || !eventOperationMap[operation]) return;
    const eventName = eventOperationMap[operation];
    const eventPureData = eventDataMap[eventName] as unknown as {
      [key: string]: { value?: string; processor?: (...params: any) => any };
    };

    if (!eventPureData) return;

    const eventData = Object.keys(eventPureData).reduce((acc, eventDatakey: string) => {
      const { value = undefined, processor = noop } = eventPureData[eventDatakey] || {};
      const eventValue = processor(value, postData, response);
      acc[eventDatakey] = eventValue;
      return acc;
    }, {} as { [key: string]: any });
    tracker.trackStructuredEvent({
      eventName: eventName,
      ...eventData,
      ...extraTrackingProperties,
    });
  } catch (err) {
    // do nothing
  }
}
