import { OPERATIONS as LEARNER_OPERATIONS } from '~/modules/Admin/config/track.constants';
import * as processor from '~/modules/Admin/hooks/useILTAdminSnowplowTracker/utils/processors';

export const TRACK_PAGE_EVENTS = {
  REMOVE_LEARNER: 'Remove Learner',
  LEARNER_PROFILE: 'Learners Profile',
  learner_session_status_updated: 'learner_session_status_updated',
  learner_session_attendance: 'learner_session_attendance',
  edit_learner_score: 'edit_learner_score',
  manage_ilt_sessions_filter: 'manage_ilt_sessions_filter',
  ilt_report_successfully_emailed: 'ilt_report_successfully_emailed',
  email_ilt_report_clicked: 'email_ilt_report_clicked',
  upload_attendance_clicked: 'upload_attendance_clicked',
  attendance_uploaded: 'attendance_uploaded',
  ilt_enrolling_learners_clicked: 'ilt_enrolling_learners_clicked',
  edit_session_enrollment_status_clicked: 'edit_session_enrollment_status_clicked',
};

export const TRACK_PAGE_OPERATION_EVENT_MAP = {
  [LEARNER_OPERATIONS.REMOVE_LEARNERS]: TRACK_PAGE_EVENTS.REMOVE_LEARNER,
  [LEARNER_OPERATIONS.VIEW_LEARNER_PROFILE]: TRACK_PAGE_EVENTS.LEARNER_PROFILE,
  [LEARNER_OPERATIONS.CHANGE_LEARNER_STATUS]: TRACK_PAGE_EVENTS.learner_session_status_updated,
  [LEARNER_OPERATIONS.MARK_ATTENDANCE]: TRACK_PAGE_EVENTS.learner_session_attendance,
  [LEARNER_OPERATIONS.CHANGE_SCORE]: TRACK_PAGE_EVENTS.edit_learner_score,
  [LEARNER_OPERATIONS.SEARCH_LEARNERS]: TRACK_PAGE_EVENTS.manage_ilt_sessions_filter,
  [LEARNER_OPERATIONS.EXPORT_LEARNERS]: TRACK_PAGE_EVENTS.ilt_report_successfully_emailed,
};

export const EVENT_DATA_MAP = {
  [TRACK_PAGE_EVENTS.REMOVE_LEARNER]: {
    LearnerIds: {
      value: 'learners',
      processor: processor.getIdsForDeletion,
    },
  },
  [TRACK_PAGE_EVENTS.LEARNER_PROFILE]: {
    LearnerId: { processor: processor.getLearnerIdForViewProfile },
  },
  [TRACK_PAGE_EVENTS.learner_session_status_updated]: {
    session_id: {
      value: 'sessionId',
      processor: processor.getManageSessionId,
    },
    event_id: {
      value: 'eventId',
      processor: processor.getManageEventId,
    },
    session_name: {
      value: 'sessionName',
      processor: processor.getLearnerKey,
    },
    learner_id: {
      value: 'learnerId',
      processor: processor.getLearnerKey,
    },
    count_learners: {
      value: 'countLearners',
      processor: processor.getLearnersCount,
    },
    notify_learners: {
      value: 'notify',
      processor: processor.getLearnerKey,
    },
    old_learner_status: {
      value: 'old',
      processor: processor.getLearnerEnrollmentStatus,
    },
    updated_enrollment_status: {
      value: 'new',
      processor: processor.getLearnerEnrollmentStatus,
    },
    enrollment_locked: {
      value: 'enrollmentFreezeStatus',
      processor: processor.getIsEnrollmentLockedForLearners,
    },
    is_bulk: {
      value: 'isBulk',
      processor: processor.getIsBulkKey,
    },
  },
  [TRACK_PAGE_EVENTS.learner_session_attendance]: {
    ilt_session_id: {
      value: 'sessionId',
      processor: processor.getManageSessionId,
    },
    ilt_event_id: {
      value: 'eventId',
      processor: processor.getManageEventId,
    },
    ilt_session_name: {},
    LearnerIds: {
      value: 'learnerId',
      processor: processor.getLearnerKey,
    },
    attendance_status: {
      value: 'attendedStatus',
      processor: processor.getLearnerKey,
    },
    notify_learners: {
      value: 'notify',
      processor: processor.getLearnerKey,
    },
  },
  [TRACK_PAGE_EVENTS.edit_learner_score]: {
    ilt_session_id: {
      processor: processor.getManageSessionId,
    },
    ilt_event_id: {
      value: 'eventId',
      processor: processor.getManageEventId,
    },
    LearnerIds: {
      value: 'learnerId',
      processor: processor.getLearnerKey,
    },
    score: {
      value: 'score',
      processor: processor.getLearnerKey,
    },
    notify_learners: {
      value: 'notify',
      processor: processor.getLearnerKey,
    },
  },
  [TRACK_PAGE_EVENTS.ilt_report_successfully_emailed]: {
    event_id: {
      value: 'eventId',
      processor: processor.getManageEventId,
    },
    session_id: {
      value: 'sessionId',
      processor: processor.getManageSessionId,
    },
  },
  [TRACK_PAGE_EVENTS.manage_ilt_sessions_filter]: {
    filter_type: {
      value: 'type',
      processor: processor.getFilters,
    },
    filter_value: {
      value: 'value',
      processor: processor.getFilters,
    },
  },
};
