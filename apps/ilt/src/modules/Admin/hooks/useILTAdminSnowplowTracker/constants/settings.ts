export const MODULE_SETTINGS_EVENTS = {
  MODULE_SETTINGS_UPDATED: 'module_settings_updated',
  MODULE_SETTINGS_CLICKED: 'module_settings_clicked',
  MODULE_ILT_SESSIONS_MINIMUM_ENROLLMENT_REMINDER_UPDATED:
    'module_ilt_sessions_minimum_enrollment_reminder_updated',
};

export const SETTINGS_UPDATED_TRACK_PROPERTIES = {
  SETTING_TYPE: 'setting_type',
  SUB_SETTING_TYPE: 'sub_setting_type',
  SUB_SETTING_VALUE: 'sub_setting_value',
};

export const MIXPANEL_SETTING_TYPE = {
  GENERAL: 'general',
  SCCORING_AND_COMPLETION: 'scoring_and_completion',
  ENROLLMENT: 'enrollment',
  NOTIFICATIONS: 'notifications',
};

export const MIXPANEL_SUB_SETTING_TYPE = {
  NAME: 'name',
  DESCRIPTION: 'description',
  MODULE_RELEVANCE: 'module_relevance',
  SESSION_TIME_ZONE_TOGGLE: 'session_time_zone_toggle',
  LEARNER_ENROLLMENT_TOGGLE: 'learner_enrollment_toggle',
  MULTI_SESSION_TOGGLE: 'multi_session_toggle',
  DEFAULT_SESSION_SCORE_TOGGLE: 'default_session_score_toggle',
  DEFAULT_SESSION_SCORE: 'default_session_score',
  COMPLETION_CRITERIA: 'completion_criteria',
  SESSION_CONFIRMATION_TOGGLE: 'session_confirmation_toggle',
  MINIMUM_ATTENDANCE_REQUIRED_TOGGLE: 'minimum_attendance_required_toggle',
  MINIMUM_ATTENDANCE_REQUIRED_PERCENTAGE: 'minimum_attendance_required_percentage',
  ENROLMENT_THRESHOLD_EMAIL_SETTINGS: 'enrolmentThresholdEmailSettings',
};

export const MIXPANEL_SUB_TYPE_AND_TYPE_MAP = {
  [MIXPANEL_SUB_SETTING_TYPE.NAME]: MIXPANEL_SETTING_TYPE.GENERAL,
  [MIXPANEL_SUB_SETTING_TYPE.DESCRIPTION]: MIXPANEL_SETTING_TYPE.GENERAL,
  [MIXPANEL_SUB_SETTING_TYPE.MODULE_RELEVANCE]: MIXPANEL_SETTING_TYPE.GENERAL,
  [MIXPANEL_SUB_SETTING_TYPE.SESSION_TIME_ZONE_TOGGLE]: MIXPANEL_SETTING_TYPE.GENERAL,
  [MIXPANEL_SUB_SETTING_TYPE.LEARNER_ENROLLMENT_TOGGLE]: MIXPANEL_SETTING_TYPE.ENROLLMENT,
  [MIXPANEL_SUB_SETTING_TYPE.MULTI_SESSION_TOGGLE]: MIXPANEL_SETTING_TYPE.ENROLLMENT,
  [MIXPANEL_SUB_SETTING_TYPE.DEFAULT_SESSION_SCORE_TOGGLE]:
    MIXPANEL_SETTING_TYPE.SCCORING_AND_COMPLETION,
  [MIXPANEL_SUB_SETTING_TYPE.DEFAULT_SESSION_SCORE]: MIXPANEL_SETTING_TYPE.SCCORING_AND_COMPLETION,
  [MIXPANEL_SUB_SETTING_TYPE.COMPLETION_CRITERIA]: MIXPANEL_SETTING_TYPE.SCCORING_AND_COMPLETION,
  [MIXPANEL_SUB_SETTING_TYPE.SESSION_CONFIRMATION_TOGGLE]: MIXPANEL_SETTING_TYPE.NOTIFICATIONS,
  [MIXPANEL_SUB_SETTING_TYPE.MINIMUM_ATTENDANCE_REQUIRED_TOGGLE]:
    MIXPANEL_SETTING_TYPE.SCCORING_AND_COMPLETION,
  [MIXPANEL_SUB_SETTING_TYPE.MINIMUM_ATTENDANCE_REQUIRED_PERCENTAGE]:
    MIXPANEL_SETTING_TYPE.SCCORING_AND_COMPLETION,
  [MIXPANEL_SUB_SETTING_TYPE.ENROLMENT_THRESHOLD_EMAIL_SETTINGS]:
    MIXPANEL_SETTING_TYPE.SCCORING_AND_COMPLETION,
};

const preprocessBooleanValue = (value: any): string | any =>
  typeof value === 'boolean' ? (value === true ? 'on' : 'off') : value;

export const MIXPANEL_SUB_SETTING_VALUE_PREPROCESSOR = {
  [MIXPANEL_SUB_SETTING_TYPE.SESSION_TIME_ZONE_TOGGLE]: preprocessBooleanValue,
  [MIXPANEL_SUB_SETTING_TYPE.MULTI_SESSION_TOGGLE]: preprocessBooleanValue,
  [MIXPANEL_SUB_SETTING_TYPE.LEARNER_ENROLLMENT_TOGGLE]: preprocessBooleanValue,
  [MIXPANEL_SUB_SETTING_TYPE.SESSION_CONFIRMATION_TOGGLE]: preprocessBooleanValue,
  [MIXPANEL_SUB_SETTING_TYPE.DEFAULT_SESSION_SCORE_TOGGLE]: preprocessBooleanValue,
  [MIXPANEL_SUB_SETTING_TYPE.MINIMUM_ATTENDANCE_REQUIRED_TOGGLE]: preprocessBooleanValue,
  [MIXPANEL_SUB_SETTING_TYPE.ENROLMENT_THRESHOLD_EMAIL_SETTINGS]: preprocessBooleanValue,
};
