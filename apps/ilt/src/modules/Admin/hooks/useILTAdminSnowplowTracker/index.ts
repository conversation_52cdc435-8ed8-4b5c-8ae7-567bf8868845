import { useContext } from 'react';

import { useSelector } from 'react-redux';
import { useUserAuth } from 'ui_shell/Auth';
import { useFullstoryHelpers } from 'ui_shell/Fullstory';

import { useSnowplowTracker } from '@mindtickle/snowplow-client';

import SnowplowContext from '~/modules/Admin/contexts/snowplow';

import {
  DISPATCH_TRACK_EVENTS_TO,
  MINDTICKLE_URL,
  PROJECT_TYPE,
  STREAM_NAME,
  SNOWPLOW_FIELD_NAMES,
  MODULE_TYPE,
} from './constants';

import type { TSnowplowTrackerType, TSnowplowEventParams } from './typeDefs';

export type { TSnowplowTrackerType };

const snowplowTracker: TSnowplowTrackerType = {
  trackStructuredEvent: () => {},
};

// FullStory object provided by ui_shell
declare var MTFullStory: any;

export const useILTAdminSnowplowTracker = () => {
  const contexts = useContext(SnowplowContext);
  const tracker = useSnowplowTracker(contexts);
  const integrations = useSelector((state: any) => state.integrations?.data);
  const {
    id: moduleId = '',
    name: moduleName = '',
    lastPublishedVersion,
  } = useSelector((state: any) => state?.ilt?.details && state?.ilt?.details?.staticData) || {};
  const { id: seriesId = '', name: seriesName = '' } =
    useSelector((state: any) => state?.ilt?.details && state?.ilt?.details?.series) || {};

  const fullstoryHelpers = useFullstoryHelpers();

  let fsSessionUrl = '';
  if (typeof MTFullStory !== 'undefined' && MTFullStory) {
    fsSessionUrl = fullstoryHelpers.getCurrentUrl() || 'current session URL API not ready';
  }

  // @ts-ignore
  const zipySessionUrl = window?.zipy?.getCurrentSessionURL?.() || 'zipy url not available';

  const {
    id: userId,
    company: { id: cname, companyType, url: lsUrl, orgId },
    email, // TODO: confirm whether to use primaryemail or email field
    roles,
    superlogin,
  } = useUserAuth();

  snowplowTracker.trackStructuredEvent = (data: TSnowplowEventParams) => {
    const { eventName, ...rest } = data;

    tracker.trackStructuredEvent(eventName, {
      properties: {
        [SNOWPLOW_FIELD_NAMES.STREAM]: STREAM_NAME,
        [SNOWPLOW_FIELD_NAMES.PROJECT_TYPE]: PROJECT_TYPE,
        [SNOWPLOW_FIELD_NAMES.FULLSTORY_SESSION]: fsSessionUrl,
        [SNOWPLOW_FIELD_NAMES.CNAME]: cname,
        [SNOWPLOW_FIELD_NAMES.ORG_ID]: orgId,
        [SNOWPLOW_FIELD_NAMES.LEARNING_SITE_TYPE]: companyType,
        [SNOWPLOW_FIELD_NAMES.LEARNING_SITE]: lsUrl,
        [SNOWPLOW_FIELD_NAMES.ADMIN_ROLES]: roles,
        [SNOWPLOW_FIELD_NAMES.IS_MINDTICKLE_USER]: email.indexOf(MINDTICKLE_URL) > -1,
        [SNOWPLOW_FIELD_NAMES.DISPATCH_TO]: DISPATCH_TRACK_EVENTS_TO,
        [SNOWPLOW_FIELD_NAMES.MODULE_ID]: moduleId,
        [SNOWPLOW_FIELD_NAMES.MODULE_NAME]: moduleName,
        [SNOWPLOW_FIELD_NAMES.MODULE_TYPE]: MODULE_TYPE,
        [SNOWPLOW_FIELD_NAMES.PUBLISH_VERSION]: lastPublishedVersion,
        [SNOWPLOW_FIELD_NAMES.SERIES_ID]: seriesId,
        [SNOWPLOW_FIELD_NAMES.SERIES_NAME]: seriesName,
        [SNOWPLOW_FIELD_NAMES.ZOOM_INTEGRATION_ENABLED]: integrations?.zoom?.enabled || false,
        [SNOWPLOW_FIELD_NAMES.USER_ID]: userId,
        [SNOWPLOW_FIELD_NAMES.SUPERLOGIN]: superlogin,
        [SNOWPLOW_FIELD_NAMES.ZIPY_URL]: zipySessionUrl,
        ...rest,
      },
    });
  };

  return snowplowTracker;
};
