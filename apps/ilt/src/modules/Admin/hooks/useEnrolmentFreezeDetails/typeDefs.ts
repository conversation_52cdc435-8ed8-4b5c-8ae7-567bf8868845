import type { EVENT_ENROLLMENT_ENUM } from '~/modules/Admin/constants/events';
import type { TILTEntities } from '~/modules/Admin/typeDefs';
export interface TUseEnrolmentFreezeDetailsProps {
  entityType: TILTEntities;
  timezone?: any;
  enrollmentFreezeStatus: string;
  enrollmentFreezeEpoch: number;
  enrollmentFreezeTimezone?: any;
  eventEnrollmentType?: (typeof EVENT_ENROLLMENT_ENUM)[keyof typeof EVENT_ENROLLMENT_ENUM];
  enrollmentFreezeDaysBeforeEvent?: number;
  eventStartTime?: number;
}
