import moment from 'moment';

import { ENROLLMENT_FREEZE_STATUSES } from '~/modules/Admin/config/sessions.constants';
import { EVENT_ENROLLMENT_ENUM } from '~/modules/Admin/constants/events';
import { sessionTimeFormatter } from '~/modules/Admin/utils';
import { checkIsEvent } from '~/modules/Admin/utils/checkEntityType';
import { getCurrentTimeZoneAbbreviated } from '~/modules/Admin/utils/timing';
import { offsetRemovedTimestamp } from '~/utils/timezone';

import type { TUseEnrolmentFreezeDetailsProps } from './typeDefs';

export default function useEnrolmentFreezeDetails({
  entityType,
  timezone = {},
  enrollmentFreezeStatus,
  enrollmentFreezeEpoch,
  enrollmentFreezeTimezone = {},
  eventEnrollmentType,
  enrollmentFreezeDaysBeforeEvent,
  eventStartTime,
}: TUseEnrolmentFreezeDetailsProps) {
  const isEvent = checkIsEvent(entityType);
  let isRowEligibleForEnrolmentFreeze = !(
    isEvent && eventEnrollmentType === EVENT_ENROLLMENT_ENUM.SESSIONS
  );
  const enrollmentFreezeEnabled = enrollmentFreezeStatus !== ENROLLMENT_FREEZE_STATUSES.DISABLED;
  let isEnrollmentFrozen: boolean, enrollmentFreezeDateTime: string;
  if (enrollmentFreezeEnabled && eventEnrollmentType === EVENT_ENROLLMENT_ENUM.EVENT) {
    if (enrollmentFreezeStatus === ENROLLMENT_FREEZE_STATUSES.RELATIVE) {
      isRowEligibleForEnrolmentFreeze = !!eventStartTime;
      const eventEnrollmentFreezeTime = moment(eventStartTime)
        .subtract(enrollmentFreezeDaysBeforeEvent, 'days')
        .valueOf();
      isEnrollmentFrozen = Date.now() - eventEnrollmentFreezeTime > 0;
      enrollmentFreezeDateTime =
        sessionTimeFormatter(eventEnrollmentFreezeTime) +
        ' ' +
        getCurrentTimeZoneAbbreviated(eventEnrollmentFreezeTime);
    } else {
      isEnrollmentFrozen =
        Date.now() -
          offsetRemovedTimestamp(enrollmentFreezeEpoch, enrollmentFreezeTimezone.offset) >
        0;
      enrollmentFreezeDateTime =
        sessionTimeFormatter(enrollmentFreezeEpoch) +
        ' ' +
        enrollmentFreezeTimezone.shortDisplayName;
    }
  } else {
    isEnrollmentFrozen =
      Date.now() - offsetRemovedTimestamp(enrollmentFreezeEpoch, timezone.offset) > 0;
    enrollmentFreezeDateTime =
      sessionTimeFormatter(enrollmentFreezeEpoch) + ' ' + timezone.shortDisplayName;
  }

  return {
    enrollmentFreezeEnabled,
    isEnrollmentFrozen,
    enrollmentFreezeDateTime,
    isRowEligibleForEnrolmentFreeze,
  };
}
