import { UPGRADE_STATUSES } from '~/modules/Admin/containers/EntityUpgradeStatus/constants';
import { EMPTY_OBJECT_READONLY } from '~/mt-ui-core/config/global.config';

export const parseUpgradeStatusDetails = (response = { upgradeSummary: EMPTY_OBJECT_READONLY }) => {
  let { upgradeSummary } = response;

  //upgradeSummary may be null
  if (!upgradeSummary) upgradeSummary = EMPTY_OBJECT_READONLY;

  const { timestamp = 0 } = upgradeSummary as { timestamp: undefined };

  let status = UPGRADE_STATUSES.NO_UPGRADE_IN_PROGRESS;
  if (timestamp > 0) status = UPGRADE_STATUSES.UPGRADE_IN_PROGRESS;
  // status = UPGRADE_STATUSES.UPGRADE_ERRORED;  Add Handling Here if Error case needs to be handled

  return {
    data: {
      status: status,
      timestamp: timestamp,
    },
  };
};
