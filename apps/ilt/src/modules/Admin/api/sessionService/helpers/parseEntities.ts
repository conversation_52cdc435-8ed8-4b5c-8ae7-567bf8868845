import moment from 'moment';

import { isString, isUndefined, hoursToMiliseconds } from '@mindtickle/utils';

import {
  WEBINAR_SOURCE,
  SESSION_WEBINAR_URL_VALIDATION_STATUS_TYPES,
  COMPLETION_CRITERIA_ENUM,
} from '~/modules/Admin/config/sessions.constants';
import { DOUBLE_DASH } from '~/modules/Admin/constants';
import { ILT_ENTITIES } from '~/modules/Admin/constants/module';
import {
  MS_TEAMS_MEETING_SETTING_KEY,
  SESSION_TYPES_ENUM,
} from '~/modules/Admin/constants/sessions';
import {
  checkIsEvent,
  checkIsIndependentSession,
  checkIsSessionWithinEvent,
} from '~/modules/Admin/utils/checkEntityType';
import { formatDuration } from '~/modules/Admin/utils/timing';
import {
  getTimezoneDetailsByKey,
  getTimezoneFromString,
  subtractOffsetDiff,
} from '~/utils/timezone';

import {
  TApiEventLocationType,
  TApiEventResponseObject,
  TApiPartialEventResponseObject,
  TApiPartialSessionResponseObject,
  TApiSessionResponseObject,
  TAttachments,
  TEntity,
  TParsedEventObject,
  TParsedPartialEventObject,
  TParsedSessionObject,
  TPartialEntitiesListApiResponse,
  TPartialListEventApiResponse,
} from '../typeDefs/entityDetails';

import type { TTimezone } from '../../../components/SessionEditDrawer/typeDefs';
import type { TParseInstructor, TEntitiesWithBasicInfo, TEntityWithBasicInfo } from '../typeDefs';

export const prepareFullEntityDataRequest = (entities: TEntitiesWithBasicInfo) => {
  const sessionIds: string[] = [];
  const events: { [entityId: string]: string[] } = {};
  let fetchEventDetail = false;
  entities.forEach(entity => {
    if (checkIsEvent(entity.entityType)) {
      fetchEventDetail = true;
    }
    if (checkIsEvent(entity.entityType) && !events[entity.id]) {
      events[entity.id] = [];
    }
    if (checkIsSessionWithinEvent(entity.entityType) && entity.parentId) {
      if (!events[entity.parentId]) {
        events[entity.parentId] = [];
      }
      events[entity.parentId].push(entity.id);
    }
    if (checkIsIndependentSession(entity.entityType)) {
      sessionIds.push(entity.id);
    }
  });
  return {
    sessionIds,
    events: events,
    fetchEventDetail,
  };
};

export const prepareStatsEntityDataRequest = (entities: TEntitiesWithBasicInfo) => {
  const sessionIds: string[] = [];
  const eventIds: string[] = [];
  entities.forEach(entity => {
    if (checkIsEvent(entity.entityType)) {
      eventIds.push(entity.id);
    }
    if (
      checkIsIndependentSession(entity.entityType) ||
      checkIsSessionWithinEvent(entity.entityType)
    ) {
      sessionIds.push(entity.id);
    }
  });
  return {
    sessionIds,
    eventIds,
  };
};

export const prepareDeleteRequest = (entities: TEntitiesWithBasicInfo) => {
  const independentSessions = [];
  const eventChildren: { [eventId: string]: string[] } = {};
  for (const { id, entityType, parentId } of entities) {
    if (checkIsIndependentSession(entityType)) {
      independentSessions.push(id);
    }
    if (checkIsEvent(entityType)) {
      if (!eventChildren[id]) {
        eventChildren[id] = [];
      }
    }
    if (checkIsSessionWithinEvent(entityType) && parentId) {
      if (!eventChildren[parentId]) {
        eventChildren[parentId] = [];
      }
      eventChildren[parentId].push(id);
    }
  }
  return {
    independent_session_ids: independentSessions,
    event: Object.keys(eventChildren).map(eventId => ({
      event_id: eventId,
      session_ids: eventChildren[eventId],
    })),
  };
};

export const parseSessionAttachments = (attachments: TApiSessionResponseObject['attachedMedia']) =>
  (attachments || []).reduce((acc, attachment) => {
    if (attachment.object) {
      acc.push({
        id: attachment.object,
        title: attachment.title,
        type: attachment.type,
      });
    }
    return acc;
  }, [] as TAttachments);

export const parseTimezone = (timezone: TTimezone) =>
  getTimezoneDetailsByKey('displayName', timezone);

const parseInstructors = (instructors: TParseInstructor[] = []) =>
  instructors.map(instructor => ({
    name: instructor.name,
    email: instructor.email,
    description: instructor.details,
  }));

export const parseTimezoneWithTimezoneOffset = (timezone: any) => {
  let timezoneOffset;

  try {
    const { timezone: parsedTimezone, timezoneOffset: parsedTimezoneOffset } =
      getTimezoneFromString(timezone);
    timezoneOffset = parsedTimezoneOffset;
    timezone = parsedTimezone;
  } catch (e) {
    timezone = parseTimezone(timezone);
    timezoneOffset = hoursToMiliseconds(timezone.offset);
  }
  return { timezone, timezoneOffset };
};

const parsePartialSession = (session: TApiPartialSessionResponseObject) => {
  let {
    isPublished,
    id,
    sessionName: name,
    startTimeEpoch: startTime,
    endTimeEpoch: endTime,
    sessionType: type,
    timezone,
    locationType,
    isCancelled = false,
  } = session;
  const { attended = '...', enrolled = '...', waitingList: waiting = '...' } = {};
  const duration = moment(endTime).diff(startTime);

  let { timezoneOffset, timezone: parsedTimezone } = parseTimezoneWithTimezoneOffset(timezone);

  return {
    id,
    name,
    startTime: subtractOffsetDiff(startTime, timezoneOffset),
    endTime: subtractOffsetDiff(endTime, timezoneOffset),
    originalStartTime: startTime,
    originalEndTime: endTime,
    duration: {
      displayValue: formatDuration(duration / 1000),
      value: duration,
    },
    type,
    locationType,
    isCancelled,
    isPublished,
    attended,
    enrolled,
    waiting,
    timezone: parsedTimezone as TTimezone,
    isPartialStaticData: true,
  };
};

const parseSession = (session: TApiSessionResponseObject) => {
  let {
    sessionDescription: description,
    attachedMedia: attachments = [],
    timezone,
    location,
    instructors,
    facilitators,
    maxScore,
    isMediaAvailable: hasRecordings,
    sessionCompletionCriteria = COMPLETION_CRITERIA_ENUM.ATTENDED,
    reminderEnabled,
    reminders = [],
    enrollmentFreezeStatus,
    enrollmentFreezeEpoch,
    isEnrollmentLimited: maxSeatEnabled,
    isWaitingListEnabled: waitingListEnabled,
    isInstructorAvailable,
    isFacilitatorAvailable,
    isAutomaticEnrolmentEnabled: autoEnroll,
    maxEnrollments: maxSeats = 1,
    notifyLearnersOnAutoEnrolment: notifyWaitingList,
    sendInstructorMail = true,
    cancellationReason = '',
    checkInSettings = {},
    webAutoAttendanceSettings = {
      isLinkValidated: false,
      isAutoAttendanceEnabled: false,
      thresholdConfig: {
        isEnabled: false,
        percentageThreshold: 60,
      },
      lastValidityStatus: SESSION_WEBINAR_URL_VALIDATION_STATUS_TYPES.NONE,
      webinarSource: WEBINAR_SOURCE.NONE,
      webMeetingSettings: {},
    },
    enrolmentThresholdEmailSettings = {
      isEnabled: false,
      enrolmentThreshold: 0,
      reminders: [],
    },
    webexMeetingSettings,
    zoomMeetingSettings,
    [MS_TEAMS_MEETING_SETTING_KEY]: msteamsMeetingSettings,
    mtLiveChallengeUniqueCode,
  } = session;

  let { timezoneOffset } = parseTimezoneWithTimezoneOffset(timezone);

  //COMPATIBILITY: where facilitators, instructors, reminders present but flag is off, empty the list
  if (!isFacilitatorAvailable) {
    facilitators = [];
  }
  if (!isInstructorAvailable) {
    instructors = [];
  }
  if (!reminderEnabled) {
    reminders = [];
  }

  const response: TParsedSessionObject = {
    ...parsePartialSession(session),
    isPartialStaticData: false,
    maxSeats,
    enrollmentFreezeStatus,
    enrollmentFreezeEpoch:
      enrollmentFreezeEpoch !== 0
        ? subtractOffsetDiff(enrollmentFreezeEpoch, timezoneOffset)
        : enrollmentFreezeEpoch,
    location,
    maxScore,
    hasRecordings,
    completionCriteria: sessionCompletionCriteria,
    reminders: reminders.map(reminder => reminder.reminderSecondsBeforeSession),
    instructors: parseInstructors(instructors),
    facilitators,
    autoEnroll: isUndefined(autoEnroll) ? waitingListEnabled : autoEnroll,
    description,
    notifyWaitingList,
    maxSeatEnabled,
    reminderEnabled,
    waitingListEnabled,
    isInstructorAvailable,
    isFacilitatorAvailable,
    attachments: parseSessionAttachments(attachments),
    sendInstructorMail,
    cancellationReason: cancellationReason,
    checkInSettings,
    webAutoAttendanceSettings,
    enrolmentThresholdEmailSettings,
    webexMeetingSettings,
    zoomMeetingSettings,
    [MS_TEAMS_MEETING_SETTING_KEY]: msteamsMeetingSettings,
  };

  if (
    mtLiveChallengeUniqueCode &&
    isString(mtLiveChallengeUniqueCode) &&
    mtLiveChallengeUniqueCode !== ''
  ) {
    response.liveChallenge = {
      uniqueCode: mtLiveChallengeUniqueCode,
    };
  }
  return response;
};

export const parseEventType = (eventLocationType: TApiEventLocationType) =>
  eventLocationType === 'VIDEO_CONF' ? SESSION_TYPES_ENUM.WEBINAR : eventLocationType;

const parsePartialEvent = (event: TApiPartialEventResponseObject): TParsedPartialEventObject => {
  const {
    isPublished,
    eventId: id,
    name,
    locationType: _type,
    startTime,
    endTime,
    // timezone,
    isCancelled = false,
  } = event;
  const { attended = '...', enrolled = '...', waitingList: waiting = '...' } = {};
  const duration = moment(endTime).diff(startTime);
  let type: (typeof SESSION_TYPES_ENUM)[keyof typeof SESSION_TYPES_ENUM] = parseEventType(_type);

  // let { timezoneOffset, timezone: parsedTimezone } = parseTimezoneWithTimezoneOffset(timezone);
  // timezone = parsedTimezone;

  return {
    id,
    name,
    startTime, //: subtractOffsetDiff(startTime, timezoneOffset),
    endTime, //: subtractOffsetDiff(endTime, timezoneOffset),
    originalStartTime: startTime,
    originalEndTime: endTime,
    duration: {
      displayValue: duration ? formatDuration(duration / 1000) : DOUBLE_DASH,
      value: duration,
    },
    type,
    isCancelled,
    isPublished,
    attended,
    enrolled,
    waiting,
    isPartialStaticData: true,
  };
};

const parseEvent = (event: TApiEventResponseObject): TParsedEventObject => {
  let {
    description,
    attachments = [],
    score: maxScore,
    completionSettings: { completionPercentage },
    reminderEnabled,
    reminderSettings: reminders = [],
    enrolmentLevel: enrollmentType,
    enrolmentSettings: {
      enrolmentFreezeStatus: enrollmentFreezeStatus,
      enrolmentFreezeTime: enrollmentFreezeEpoch,
      relativeDaysBeforeEnrolment: enrollmentFreezeDaysBeforeEvent,
      enrolmentFreezeTimezone: enrollmentFreezeTimezone,
      hasLimitedEnrolment: maxSeatEnabled,
      maxEnrolment: maxSeats,
      isWaitListEnabled: waitingListEnabled,
      hasAutoEnrolEnabled: autoEnroll,
      notifyOnAutoEnrol: notifyWaitingList,
    },
    cancellationReason = '',
  } = event;

  let { timezoneOffset: enrollmentFreezeTimezoneOffset, timezone: parsedEnrollmentFreezeTimezone } =
    enrollmentFreezeTimezone
      ? parseTimezoneWithTimezoneOffset(enrollmentFreezeTimezone)
      : { timezone: undefined, timezoneOffset: new Date().getTimezoneOffset() };
  enrollmentFreezeTimezone = parsedEnrollmentFreezeTimezone;

  const response: TParsedEventObject = {
    ...parsePartialEvent(event),
    isPartialStaticData: false,
    maxSeats,
    enrollmentFreezeStatus,
    enrollmentFreezeEpoch:
      enrollmentFreezeEpoch !== 0
        ? subtractOffsetDiff(enrollmentFreezeEpoch, enrollmentFreezeTimezoneOffset)
        : enrollmentFreezeEpoch,
    enrollmentFreezeDaysBeforeEvent,
    enrollmentFreezeTimezone,
    maxScore,
    completionCriteria: completionPercentage,
    reminders: reminders,
    autoEnroll: isUndefined(autoEnroll) ? waitingListEnabled : autoEnroll,
    description,
    notifyWaitingList,
    maxSeatEnabled,
    reminderEnabled,
    waitingListEnabled,
    attachments: attachments,
    cancellationReason: cancellationReason,
    enrollmentType,
  };
  return response;
};

const parsePartialIndependentSession = (entityDetails: TApiPartialSessionResponseObject) => {
  const parsedEntity = parsePartialSession(entityDetails);
  return {
    ...parsedEntity,
    entityType: ILT_ENTITIES.SESSION,
  };
};

const parseIndependentSession = (entityDetails: TApiSessionResponseObject) => {
  const parsedEntity = parseSession(entityDetails);
  return {
    ...parsedEntity,
    entityType: ILT_ENTITIES.SESSION,
  };
};

const parsePartialSessionWithinEvent = (entityDetails: TApiPartialSessionResponseObject) => {
  const parsedEntity = parsePartialSession(entityDetails);
  return {
    ...parsedEntity,
    entityType: ILT_ENTITIES.SESSION_WITHIN_EVENT,
  };
};

const parseSessionWithinEvent = (entityDetails: TApiSessionResponseObject) => {
  const parsedEntity = parseSession(entityDetails);
  return {
    ...parsedEntity,
    entityType: ILT_ENTITIES.SESSION_WITHIN_EVENT,
  };
};

const parsePartialEventWrapper = (entityDetails: TApiPartialEventResponseObject) => {
  const parsedEntity = parsePartialEvent(entityDetails);
  return {
    ...parsedEntity,
    entityType: ILT_ENTITIES.EVENT,
  };
};

const parseEventWrapper = (entityDetails: TApiEventResponseObject) => {
  const parsedEntity = parseEvent(entityDetails);
  return {
    ...parsedEntity,
    entityType: ILT_ENTITIES.EVENT,
  };
};

const parsePartialEventSessions = (eventDetails: TPartialListEventApiResponse['event']) => {
  const parsedEvent = parsePartialEventWrapper(eventDetails);
  const parsedEventDetails = { ...parsedEvent };
  let sessionsWithinEvent: { [id: string]: any } = {};
  let sessionWithinEventHidden: { [id: string]: boolean } = {};
  if (eventDetails.filteredSessions && eventDetails.filteredSessions.length) {
    eventDetails.filteredSessions.forEach(session => {
      const parsedSession = parsePartialSessionWithinEvent(session);
      sessionsWithinEvent[parsedSession.id] = { ...parsedSession, parentId: parsedEventDetails.id };
      sessionWithinEventHidden[parsedSession.id] = session.isEligible !== true;
    });
  }
  const sessionWithinEventList = Object.keys(sessionsWithinEvent);
  return {
    sessionsWithinEvent,
    parsedEventDetails,
    sessionWithinEventHidden,
    sessionWithinEventList,
  };
};

export const parsePartialEntities = (entities: TPartialEntitiesListApiResponse = []) => {
  const entityMap: any = {};
  const entityIds: string[] = [];
  const eventSessionListMap: { [id: string]: string[] } = {};
  const entitiesFilterHidden: { [id: string]: boolean } = {};
  entities.forEach(entityWrapper => {
    const { entityType } = entityWrapper;
    if (entityType === ILT_ENTITIES.SESSION) {
      const parsedSession = parsePartialIndependentSession(entityWrapper.session);
      entityMap[parsedSession.id] = parsedSession;
      entityIds.push(parsedSession.id);
      entitiesFilterHidden[parsedSession.id] = false;
    } else {
      const { event: eventDetails } = entityWrapper;
      const {
        parsedEventDetails,
        sessionsWithinEvent,
        sessionWithinEventHidden,
        sessionWithinEventList,
      } = parsePartialEventSessions(eventDetails);
      entityMap[parsedEventDetails.id] = parsedEventDetails;
      Object.assign(entityMap, sessionsWithinEvent);
      entityIds.push(parsedEventDetails.id);
      eventSessionListMap[parsedEventDetails.id] = sessionWithinEventList;
      entitiesFilterHidden[parsedEventDetails.id] = eventDetails.isEligible !== true;
      Object.assign(entitiesFilterHidden, sessionWithinEventHidden);
    }
  });
  return {
    entityMap: entityMap,
    firstLevelEntityIds: entityIds,
    eventSessionListMap,
    entitiesFilterHidden,
  };
};

export const parseDetailedEntities = (
  entities: any = {},
  requestEntities: TEntitiesWithBasicInfo
) => {
  const requestEntitiesMap = requestEntities.reduce((acc, entity) => {
    acc[entity.id] = entity;
    return acc;
  }, {} as { [x: string]: TEntityWithBasicInfo });
  return Object.keys(entities).reduce((acc: any, entityId) => {
    if (!requestEntitiesMap[entityId]) {
      return acc;
    }
    const entity = entities[entityId];
    if (checkIsIndependentSession(requestEntitiesMap[entityId].entityType)) {
      const parsedSession = parseIndependentSession(entity);
      acc[entityId] = parsedSession;
    }
    if (checkIsSessionWithinEvent(requestEntitiesMap[entityId].entityType)) {
      const parsedSession = parseSessionWithinEvent(entity);
      acc[entityId] = Object.assign(parsedSession, {
        parentId: requestEntitiesMap[entityId].parentId,
      });
    }
    if (checkIsEvent(requestEntitiesMap[entityId].entityType)) {
      const parsedEvent = parseEventWrapper(entity);
      acc[entityId] = parsedEvent;
    }
    return acc;
  }, {});
};

export const parseCopyEventResponse = (
  events: { details: TApiEventResponseObject; sessions: string[] }[]
) => {
  const entityMap: Record<string, any> = {};
  const entityIds = [];
  const eventSessionListMap: Record<string, string[]> = {};
  for (const event of events) {
    const { details, sessions } = event;
    const parsedEvent = parseEventWrapper(details);
    entityIds.push(parsedEvent.id);
    entityMap[parsedEvent.id] = parsedEvent;
    eventSessionListMap[parsedEvent.id] = sessions;
  }

  return {
    entityMap,
    firstLevelEntityIds: entityIds,
    eventSessionListMap,
  };
};

export const parseSessionsResponse = (sessions: TApiSessionResponseObject[], parentId?: string) => {
  const entityMap: Record<string, TEntity> = {};
  const entityIds = [];
  for (const session of sessions) {
    const parsedSession: TEntity = parentId
      ? parseSessionWithinEvent(session)
      : parseIndependentSession(session);
    if (parentId) {
      parsedSession.parentId = parentId;
    }
    entityIds.push(parsedSession.id);
    entityMap[parsedSession.id] = parsedSession;
  }

  return {
    entityMap,
    entityIds,
  };
};

export const parseEventsResponse = (events: TApiEventResponseObject[]) => {
  const entityMap: Record<string, TEntity> = {};
  const entityIds = [];
  for (const event of events) {
    const parsedEvent = parseEventWrapper(event);
    entityIds.push(parsedEvent.id);
    entityMap[parsedEvent.id] = parsedEvent;
  }
  return {
    entityMap,
    entityIds,
  };
};
