import type {
  TCheckInSettings,
  TSessionObject,
  TWebAutoAttendanceSettings,
  TWebinarMeetingSettings,
} from '~/modules/Admin/components/SessionEditDrawer/typeDefs';
import type {
  TEntitiesWithBasicInfo,
  TEntityWithBasicInfo,
} from '~/modules/Admin/components/SessionsWrapper/typeDefs';
import type {
  DEFAULT_PAGINATION,
  MS_TEAMS_MEETING_SETTING_KEY,
} from '~/modules/Admin/config/sessions.constants';

import { TParseEventObject } from './entityDetails';

export { TEntitiesWithBasicInfo, TEntityWithBasicInfo, TParseEventObject };

export interface TParseInstructor {
  details: string;
  name: string;
  email: string;
}

export interface TProcessFilter {
  value: string;
  type: string;
}

export interface TExtendedSessionObject extends TSessionObject {
  waiting: number | string;
  hasRecordings: boolean;
  staticId: string;
  attended: number | string;
  enrolled: number | string;
  id: string;
  checkInSettings: any;
}

export interface TSessionApiData extends TParseSessionObject {
  sessionName: string;
  sessionDescription: string;
  attachedMedia: any[];
  sessionType: string;
  startTimeEpoch: number;
  endTimeEpoch: number;
  facilitators: string[];
  isMediaAvailable: boolean;
  sessionCompletionCriteria?: string;
  reminderEnabled: boolean;
  reminders: { reminderSecondsBeforeSession: number }[];
  isEnrollmentLimited: boolean;
  isWaitingListEnabled: boolean;
  isInstructorAvailable: boolean;
  isFacilitatorAvailable: boolean;
  isAutomaticEnrolmentEnabled: boolean;
  maxEnrollments: number;
  notifyLearnersOnAutoEnrolment: boolean;
  sendInstructorMail: boolean;
  isCancelled: boolean;
  cancellationReason: string;
  checkInSettings: TCheckInSettings;
  mtLiveChallengeUniqueCode: string;
}

export interface TSessionData {
  id: string;
  publishedBefore: boolean;
  staticNode: {
    id: string;
    obj: TSessionApiData;
  };
}

export interface TParseSessionObject {
  staticId: string;
  name: string;
  type: string;
  locationType: string;
  location: string;
  description: string;
  attachments: any[];
  completionCriteria: string;
  maxScore: number;
  startTime: number;
  endTime: number;
  timezone: any;
  enrollmentFreezeStatus: string;
  enrollmentFreezeEpoch: number;
  webAutoAttendanceSettings: TWebAutoAttendanceSettings;
  enrolmentThresholdEmailSettings: any;
  webexMeetingSettings: TWebinarMeetingSettings;
  zoomMeetingSettings: TWebinarMeetingSettings;
  [MS_TEAMS_MEETING_SETTING_KEY]: TWebinarMeetingSettings;
  instructors: TParseInstructor[];
}

export interface TSessionStats {
  [sessionId: string]: {
    attended: number;
    enrolled: number;
    waiting: number;
    waitingList?: number;
    enrolledViaThisSeries?: number;
    waitListedViaThisSeries?: number;
    attendedViaThisSeries?: number;
    seriesIds?: string[];
    userAndInvitationFromOtherSeriesMap?: {
      [userId: string]: {
        [seriesId: string]: {
          enrolled?: number;
          waitingList?: number;
          attended?: number;
        };
      };
    };
  };
}

export type TGetSessionDetailsParams = {
  moduleId: string;
  companyId: string;
  seriesId: string;
  filters: any;
  pagination: typeof DEFAULT_PAGINATION;
  sortOrder: string;
  sortField: string;
};

export type TModuleIdSeriesId = { moduleId: string; seriesId: string };
export type TGetSessionDetailsByIdParam = TModuleIdSeriesId & { sessionId: string };
export type TGetEntityStatsPrams = TModuleIdSeriesId & {
  companyId: string;
  entities: TEntitiesWithBasicInfo;
  addMultiPresentStats?: boolean;
};
export type TGetDetailedEntitiesParams = TModuleIdSeriesId & {
  entities: TEntitiesWithBasicInfo;
  companyId: string;
};
export type TCreateSessionsParams = TModuleIdSeriesId & {
  companyId: string;
  sessions: any[];
  parentId?: string;
};
export type TModifySessionParams = TModuleIdSeriesId & {
  companyId: string;
  sessions: [any];
  parentId?: string;
};
export type TDeleteEntitiesParams = TModuleIdSeriesId & {
  companyId: string;
  processIds: TEntitiesWithBasicInfo;
};
export type TCancelSessionParams = TModuleIdSeriesId & {
  parentId?: string;
  companyId: string;
  sessions: any[];
  notifyLearnersOnCancellation?: boolean | any;
};

export type TCreateEventsParams = TModuleIdSeriesId & {
  companyId: string;
  sessions: any[];
};

export type TCopyEventParams = TModuleIdSeriesId & {
  companyId: string;
  sessions: string[];
};

export type TShareCheckInCodeParams = TModuleIdSeriesId & { sessionId: string; emailIds: string[] };
export type TValidateWebinarURLParams = {
  location: string;
  password: string;
  hostEmail?: string;
  webinarSource: string;
  signal: any;
};

export type TCreateVideoConferencingMeetingParams = { webinarSource: string; [x: string]: any };
export type TDeleteVideoConferencingMeetingParams = {
  webinarSource: string;
  meetingId: string;
  hostEmail: string | undefined;
};
