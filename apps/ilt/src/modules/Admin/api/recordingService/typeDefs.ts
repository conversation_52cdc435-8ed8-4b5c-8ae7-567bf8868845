import type {
  TRecordingsUnpublished,
  TCompletionCriteria,
} from '~/modules/Admin/containers/Recordings/typeDefs';

export type TUnpublishedRecordingsParsedResponse = {
  [sessionId: string]: {
    recordingsUnpublished: TRecordingsUnpublished;
    draftSessionCompletionCriteria: TCompletionCriteria | undefined;
  };
};

export type TMediaUnpublished = {
  mediaId: string;
  mediaTitle: string;
  mediaStatus: string;
};

export type TUnpublishedRecordingsResponseRecord = {
  mediaUnpublished?: TMediaUnpublished[];
  draftSessionCompletionCriteria: TCompletionCriteria | undefined;
};

export type TUnpublishedRecordingsResponse = {
  [sessionId: string]: TUnpublishedRecordingsResponseRecord;
};

export type TSaveRecordingsParam = {
  completionCriteria: TCompletionCriteria;
  reEvaluationCriteria?: string;
  recordings: {
    mediaId: string;
    mediaTitle: string;
  }[];
};

export type TGetMediaStatusParams = { companyId: string; mediaId: string };
export type TGetMediaProcessedDataParams = { companyId: string; mediaId: string };
export type TSaveRecordingsServiceParams = {
  companyId: string;
  seriesId: string;
  moduleId: string;
  sessionId: string;
  recordings: {
    mediaId: string;
    mediaTitle: string;
  }[];
  completionCriteria: TCompletionCriteria;
  reEvaluationCriteria?: string;
};
export type TGetUnpublishedRecordingsServiceParams = {
  companyId: string;
  seriesId: string;
  moduleId: string;
  sessionIds: string[];
};
