import { get } from '@mindtickle/api-helpers';

import ApiUrls from '../../config/todosApi.config';

import { parseTodos } from './helper';

const TodoService = {
  getTodos: async ({
    moduleId,
    seriesId,
    companyId,
  }: {
    moduleId: string;
    seriesId: string;
    companyId: string;
  }) => {
    const { todos = {} } = await get(
      ApiUrls.getTodos({
        moduleId,
        companyId,
        query: {
          forSeries: seriesId,
        },
      })
    );
    return parseTodos(todos);
  },
};

export default TodoService;
