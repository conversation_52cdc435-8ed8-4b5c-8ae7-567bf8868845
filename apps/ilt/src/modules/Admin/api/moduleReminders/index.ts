import { get, post } from '@mindtickle/api-helpers';

import ApiUrls from '~/modules/Admin/config/remindersApi.config';
import {
  START,
  SIZE,
} from '~/modules/Admin/containers/Settings/components/Notifications/constants';

import { parseTemplates, parseAutomations, preProcessOperationData } from './helpers';

import type {
  TGetMailTemplateParams,
  TGetAutomationParams,
  TOperateRemindersParams,
} from './typeDefs';

const ReminderService = {
  getMailTemplates: async ({
    companyId,
    seriesId,
    start = START,
    count = SIZE,
  }: TGetMailTemplateParams) => {
    const { templates } = await get(
      ApiUrls.getMailTemplates({
        companyId,
        query: {
          start,
          count,
          forSeries: seriesId,
        },
      })
    );
    return parseTemplates({ templates });
  },
  getReminderAutomations: async ({ companyId, moduleId, seriesId }: TGetAutomationParams) => {
    const response = await get(
      ApiUrls.getReminderAutomations({
        companyId,
        moduleId,
        query: {
          forSeries: seriesId,
        },
      })
    );
    return parseAutomations({ automations: response });
  },
  operateReminders: async ({
    moduleId,
    seriesId,
    companyId,
    operation,
    data,
  }: TOperateRemindersParams) => {
    const response = await post(
      ApiUrls.operateReminders({
        companyId,
        moduleId,
        query: {
          forSeries: seriesId,
        },
      }),
      {
        body: {
          ...preProcessOperationData({ operation, data }),
        },
      }
    );
    return { created: response.create, updated: response.update, deleted: response.delete };
  },
};

export default ReminderService;
