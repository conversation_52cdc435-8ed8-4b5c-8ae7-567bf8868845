import { OPERATIONS } from '~/modules/Admin/containers/Settings/components/Notifications/constants';
import type {
  TDeleteEmailAutomationRequest,
  UpdateEmailAutomationRequest,
  CreateEmailAutomationRequest,
} from '~/modules/Admin/containers/Settings/components/Notifications/typeDefs';
import { EmailAutomationsTypes } from '~/modules/Admin/containers/Settings/components/Notifications/typeDefs';

import type { TOperation, TOperationDataParam } from './typeDefs';

const AUTOMATION_TYPES = Object.values(EmailAutomationsTypes);

const { UPDATE, ADD, REMOVE } = OPERATIONS;

export const parseTemplates = ({
  templates,
}: {
  templates: { [key: string]: { id: string; name: string; description?: string } };
}) => {
  const parsedTemplates = Object.values(templates).reduce((acc, template) => {
    acc.push({
      id: template.id,
      name: template.name,
      description: template.description,
    });
    return acc;
  }, [] as { id: string; name: string; description?: string }[]);
  return parsedTemplates;
};

export const parseAutomations = ({ automations }: any) => {
  const automationMap: { [learnerStatusId: string]: any } = {};
  for (const [learnerStatusId, reminders = []] of Object.entries(automations)) {
    if (!AUTOMATION_TYPES.includes(learnerStatusId as unknown as EmailAutomationsTypes)) {
      continue;
    }
    automationMap[learnerStatusId] = reminders;
  }
  const response = { mailAutomationData: automationMap };
  return response;
};

export const getParams = (data: TOperationDataParam) => {
  const { id, task, templateId, condition, schedule, recurrenceSettings } =
    data as UpdateEmailAutomationRequest;
  return {
    id,
    task,
    templateId,
    condition,
    schedule,
    recurrenceSettings,
  };
};

export const preProcessOperationData = ({
  operation,
  data,
}: {
  operation: TOperation;
  data: TOperationDataParam;
}) => {
  data = data || {
    templateId: '',
    task: '',
  };
  const response = {
    update: [] as UpdateEmailAutomationRequest[],
    create: [] as CreateEmailAutomationRequest[],
    delete: [] as { id: string; mailJobId: string }[],
  };

  switch (operation) {
    case UPDATE:
      response.update.push(getParams(data));
      break;
    case ADD:
      response.create.push(getParams(data) as CreateEmailAutomationRequest);
      break;
    case REMOVE:
      const { id, mailJobId } = data as TDeleteEmailAutomationRequest;
      response.delete.push({ id, mailJobId });
      break;
    default:
  }
  return response;
};
