import { OPERATIONS } from '~/modules/Admin/containers/Settings/components/Notifications/constants';
import type {
  TDeleteEmailAutomationRequest,
  UpdateEmailAutomationRequest,
  CreateEmailAutomationRequest,
} from '~/modules/Admin/containers/Settings/components/Notifications/typeDefs';

export type TOperation = (typeof OPERATIONS)[keyof typeof OPERATIONS];

export type TOperationDataParam =
  | TDeleteEmailAutomationRequest
  | UpdateEmailAutomationRequest
  | CreateEmailAutomationRequest;

export type TGetMailTemplateParams = {
  companyId: string;
  seriesId: string;
  start: number;
  count: number;
};

export type TGetAutomationParams = {
  companyId: string;
  moduleId: string;
  seriesId: string;
};

export type TOperateRemindersParams = {
  companyId: string;
  moduleId: string;
  seriesId: string;
  operation: TOperation;
  data: TOperationDataParam;
};
