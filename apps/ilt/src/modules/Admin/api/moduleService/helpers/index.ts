import { parseScopedPermissions } from 'ui_shell/Permissions';

import { isObject } from '@mindtickle/utils';

import { API_KEY_MAP, DRAFT_STATE } from '~/config/global.config';
import { COMPLETION_CRITERIA_ENUM as SESSION_COMPLETION_CRITERIA_OPTIONS } from '~/modules/Admin/config/sessions.constants';

export const preProcessDetails = (data: any) => {
  const processedData: { [key: string]: any } = {};
  for (let [key, value] of Object.entries(data)) {
    const apiKey = API_KEY_MAP.module_static[key as keyof typeof API_KEY_MAP.module_static];
    if (apiKey) {
      value = isObject(value) ? preProcessDetails(value) : value;
      processedData[apiKey] = value;
    }
  }
  return processedData;
};

export const parseSeries = (
  response: {
    id: any;
    name: any;
    prm: any;
    moduleInvitaionEmailEnabled: any;
    reminderMailEnabled: any;
    sequentialUnlockingEnabled: any;
  },
  userId: string,
  globalPermissions: any
) => ({
  id: response.id,
  name: response.name,
  permissions: parseScopedPermissions({
    permissions: response.prm || {},
    userId,
    globalPermissions,
  }),
  seriesLevelMailSettings: {
    moduleInvitaionEmailEnabled: response.moduleInvitaionEmailEnabled,
    reminderMailEnabled: response.reminderMailEnabled,
  },
  sequentiallyLockedSeries: response.sequentialUnlockingEnabled,
});

export const parseMaxScore = (
  response: { maxScore: number | undefined } = { maxScore: undefined }
) => ({
  maxScore: response.maxScore || 0,
});

export const parseStaticData = (response: { [x: string]: any }) => {
  const {
    id,
    name,
    description,
    scoring,
    score,
    defaultSessionCompletionCriteria,
    hallOfFame,
    showSections,
    companyId,
    mappedSeries,
    sequentialLock,
    createdBy,
    creationTime,
    dueDate,
    dueDateExpiry,
    dueDateType,
    dueDateValue,
    // defaultThumb,
    thumbObj,
    thumbId,
    thumbTitle,
    thumbObjObjNested,
    thumbUrl,
    thumbType,
    isPublished,
    allowMultipleSession,
    learnerCnfSsnEmail,
    restrictLearnerEnroll,
    showLearnerTimezone,
    moduleRelevance,
    attendanceSettings,
    enrolmentThresholdEmailSettings,
    lastPublishedVersion,
  } = API_KEY_MAP.module_static;
  response[dueDate] = response[dueDate] || {};
  return {
    id: response[id],
    name: response[name],
    createdBy: response[createdBy],
    creationTime: response[creationTime],
    description: response[description],
    scoring: !!response[scoring],
    score: response[score] || 0,
    defaultSessionCompletionCriteria:
      response[defaultSessionCompletionCriteria] || SESSION_COMPLETION_CRITERIA_OPTIONS.ATTENDED,
    hallOfFame: !!response[hallOfFame],
    showSections: response[showSections],
    companyId: response[companyId],
    mappedSeries: response[mappedSeries].map(({ series }: { series: any }) => series),
    sequentialLock: response[sequentialLock],
    isPublished: response[isPublished] !== DRAFT_STATE,
    allowMultipleSession: response[allowMultipleSession],
    learnerCnfSsnEmail: response[learnerCnfSsnEmail],
    restrictLearnerEnroll: response[restrictLearnerEnroll],
    showLearnerTimezone: response[showLearnerTimezone],
    moduleRelevance: response[moduleRelevance],
    attendanceSettings: {
      durationBasedAttendanceEnabled: response[attendanceSettings]?.durationBasedAttendanceEnabled,
      percentageThreshold: response[attendanceSettings]?.percentageThreshold,
    },
    enrolmentThresholdEmailSettings: {
      isEnabled: response[enrolmentThresholdEmailSettings]?.isEnabled,
      enrolmentThreshold: response[enrolmentThresholdEmailSettings]?.enrolmentThreshold,
      reminders: response[enrolmentThresholdEmailSettings]?.reminders,
    },
    invitedLearnersCountLoaded: false,
    dueDate: {
      dueDateExpiry: response[dueDate][dueDateExpiry],
      dueDateType: response[dueDate][dueDateType],
      dueDateValue: response[dueDate][dueDateValue],
    },
    // defaultThumb: {
    //   thumbId: response[defaultThumb][thumbId],
    //   thumbTitle: response[defaultThumb][thumbTitle],
    //   thumbUrl: response[defaultThumb][thumbObj][thumbUrl],
    //   thumbType: response[defaultThumb][thumbType],
    // },
    thumbObj: {
      thumbId: response[thumbObj][thumbId],
      thumbTitle: response[thumbObj][thumbTitle],
      thumbUrl: response[thumbObj][thumbObjObjNested][thumbUrl],
      thumbType: response[thumbObj][thumbType],
    },
    lastPublishedVersion: response[lastPublishedVersion],
  };
};
