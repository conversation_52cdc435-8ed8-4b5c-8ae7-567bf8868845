import { get, post } from '@mindtickle/api-helpers';

import { logErrorToIntegrationTools } from '~/modules/Admin/components/ErrorBoundary';
import ApiUrls from '~/modules/Admin/config/moduleApi.config';

import { preProcessDetails, parseSeries, parseMaxScore, parseStaticData } from './helpers';

import type {
  TGetDetailsParams,
  TModifyDetailsParams,
  TGetMaxScoreParams,
  TDiscardModuleParams,
  TArchiveModuleParams,
} from './typeDefs';

const ModuleService = {
  getDetails: async ({
    companyId,
    moduleId,
    seriesId,
    userId,
    globalPermissions,
  }: TGetDetailsParams) => {
    try {
      const response: {
        series: any;
        details: any;
        hasUnpubChanges: boolean;
        companySettings: any;
      } = {
        series: undefined,
        details: undefined,
        hasUnpubChanges: false,
        companySettings: undefined,
      };
      const {
        module_static: moduleDetails,
        series_info: seriesDetails,
        unpublished_changes: hasUnpubChanges,
      } = await get(ApiUrls.getDetails({ companyId, moduleId, query: { forSeries: seriesId } }));
      const getCompanySettingsQuery = {
        operationName: null,
        variables: {},
        query:
          '{\n  ilt_company {\n    getCompanySetting {\n   attendanceSettings {\n        durationBasedAttendanceEnabled\n        allowEditSettings\n        defaultILTSetting\n        attendanceThreshold\n      }\n      enrolmentThresholdEmailSettings {\n        isEnabled\n        defaultILTSetting\n      }\n     }\n  }\n}\n',
      };
      const getCompanySettingsResponse = await post(ApiUrls.getCompanySettings(), {
        body: getCompanySettingsQuery,
      });
      const attendanceCompanySettings =
        getCompanySettingsResponse?.data?.ilt_company?.getCompanySetting?.attendanceSettings;
      const enrolmentThresholdEmailSettings =
        getCompanySettingsResponse?.data?.ilt_company?.getCompanySetting
          ?.enrolmentThresholdEmailSettings;
      response.series = parseSeries(seriesDetails, userId, globalPermissions);
      response.details = parseStaticData(moduleDetails);
      response.hasUnpubChanges = hasUnpubChanges;
      response.companySettings = {
        attendanceSettings: {
          allowEditSettings: attendanceCompanySettings?.allowEditSettings,
          percentageThreshold: attendanceCompanySettings?.attendanceThreshold,
          defaultILTSetting: attendanceCompanySettings?.defaultILTSetting,
          durationBasedAttendanceEnabled: attendanceCompanySettings?.durationBasedAttendanceEnabled,
        },
        enrolmentThresholdEmailSettings: {
          isEnabled: enrolmentThresholdEmailSettings?.isEnabled,
          defaultILTSetting: enrolmentThresholdEmailSettings?.defaultILTSetting,
        },
      };
      return response;
    } catch (error) {
      logErrorToIntegrationTools(error as Error);
      throw error;
    }
  },
  modifyDetails: async ({ companyId, moduleId, seriesId, data }: TModifyDetailsParams) => {
    try {
      const response = await post(
        ApiUrls.modifyDetails({
          moduleId,
          companyId,
          query: {
            forSeries: seriesId,
          },
        }),
        {
          body: preProcessDetails(data),
        }
      );
      return parseStaticData(response);
    } catch (error) {
      logErrorToIntegrationTools(error as Error);
      throw error;
    }
  },
  getMaxScore: async ({ moduleId, seriesId }: TGetMaxScoreParams) => {
    try {
      const { entityData } = await get(
        ApiUrls.getMaxScore({
          moduleId,
          query: {
            forSeries: seriesId,
          },
        })
      );
      return parseMaxScore(entityData);
    } catch (error) {
      logErrorToIntegrationTools(error as Error);
      throw error;
    }
  },
  discardModule: async ({ moduleId, companyId, seriesId }: TDiscardModuleParams) => {
    try {
      const response = await post(
        ApiUrls.discard({
          seriesId,
          moduleId,
          query: {
            programId: seriesId,
          },
        }),
        {
          body: {
            moduleIds: [moduleId],
          },
        }
      );
      return response;
    } catch (error) {
      logErrorToIntegrationTools(error as Error);
      throw error;
    }
  },
  archiveModule: async ({ moduleId, companyId, seriesId }: TArchiveModuleParams) => {
    try {
      const response = await post(
        ApiUrls.archive({
          seriesId,
          moduleId,
          query: {
            programId: seriesId,
          },
        }),
        {
          body: {
            moduleIds: [moduleId],
          },
        }
      );
      return response;
    } catch (error) {
      logErrorToIntegrationTools(error as Error);
      throw error;
    }
  },
};

export default ModuleService;
