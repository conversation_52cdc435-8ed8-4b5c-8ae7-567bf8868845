import {
  DISPLAY_NAME_SUFFIX,
  PUBLISH_CHANGE_TYPE,
} from '~/modules/Admin/containers/PublishHistory/constants';
import type { TEntityModified } from '~/modules/Admin/containers/PublishHistory/typeDefs';

export const parseHistory = ({
  data: history,
  unpublished_changes,
}: {
  data: any;
  unpublished_changes: any;
}) => {
  const parsedHistory = Object.keys(history).map(version => ({
    version,
    time: history[version].publishedTime * 1000,
    userId: history[version].userId,
  }));
  return {
    hasChanges: !!unpublished_changes,
    versions: parsedHistory.sort((a, b) => b.time - a.time),
  };
};

export const parseSummary = ({ content }: { content: any }) => {
  const contentChanges = content.reduce(
    (acc: any[], changes: { name: any; children_edit_summary: any }) => {
      acc.push({
        title: `${DISPLAY_NAME_SUFFIX.CONTENT_ADDED}`,
        changes: [...changes.children_edit_summary.added],
      });
      acc.push({
        title: `${DISPLAY_NAME_SUFFIX.CONTENT_REMOVED}`,
        changes: [...changes.children_edit_summary.deleted],
      });
      return acc;
    },
    []
  );
  // As per new design it is not there and after confirmation within team this object will alwasy be empty
  // const settingChanges = {
  //   title: DISPLAY_NAME_SUFFIX.SETTINGS,
  //   changes: [...settings],
  // };

  return [...contentChanges];
};

const getUnpublishedChanges = (entities: any, operation: string) => {
  const { events, sessions } = entities;

  let contentModifiedChanges: TEntityModified = {
    individualSessions: [],
    existingEventSessions: [],
    newEventSessions: [],
    changesCount: 0,
  };

  for (let event of events) {
    const isEventModified =
      operation === PUBLISH_CHANGE_TYPE.ADDED ? event.is_added : event.is_deleted;
    const eventContent = {
      parentId: event.id,
      parentName: event.name,
      sessions: event.sessions,
    };
    if (isEventModified) {
      contentModifiedChanges.newEventSessions.push(eventContent);
      contentModifiedChanges.changesCount +=
        operation === PUBLISH_CHANGE_TYPE.ADDED ? event.sessions.length + 1 : 1; // For delete, entire event delete will be one operation
    } else {
      contentModifiedChanges.existingEventSessions.push(eventContent);
      contentModifiedChanges.changesCount += event.sessions.length;
    }
  }
  contentModifiedChanges.individualSessions = [...sessions];
  contentModifiedChanges.changesCount += sessions.length;
  return contentModifiedChanges;
};

export const parseDraftSummary = ({ content }: { content: any }) => {
  const { entities_added, entities_deleted } = content;

  const contentAddedChanges = getUnpublishedChanges(entities_added, PUBLISH_CHANGE_TYPE.ADDED);
  const contentRemovedChanges = getUnpublishedChanges(
    entities_deleted,
    PUBLISH_CHANGE_TYPE.REMOVED
  );
  return {
    contentAddedChanges,
    contentRemovedChanges,
  };
};
