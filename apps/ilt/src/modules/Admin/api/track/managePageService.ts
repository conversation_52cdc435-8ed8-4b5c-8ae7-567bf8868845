import { get, post } from '@mindtickle/api-helpers';

import ApiUrls from '~/modules/Admin/config/api.config';
import ManagePageApiUrls from '~/modules/Admin/config/api.managepage.config';
import {
  SUPPORTED_SORTINGS_API_KEY,
  ENROLL_DRAWER_SUPPORTED_SORTING,
  ENROLL_DRAWER_SUPPORTED_FILTERS_API_NAME,
  ENROLL_DRAWER_SUPPORTED_FILTERS,
} from '~/modules/Admin/config/track.constants';
import { SUPPORTED_FILTERS_API_KEY } from '~/modules/Admin/constants/sessions';
import type { TILTEntities } from '~/modules/Admin/typeDefs';

import { parseEventSessionDetails } from './utils/manageEventPanel';
import {
  parseSessionLearners,
  handleOperationResponse,
  prepareAttendanceRequest,
  prepareEventAttendanceRequest,
  prepareChangeEnrollmentStatusRequest,
  handleEventOperationResponse,
  parseEventLearners,
  prepareEventChangeEnrollmentStatusRequest,
} from './utils/managePages';

import type { TEventEnrollmentType } from '../sessionService/typeDefs/entityDetails';

type TCommon = {
  companyId: string;
  moduleId: string;
  seriesId: string;
};

type TGetSessionLearnersParams = TCommon & {
  pagination: { start: number; rows: number };
  sort: {
    type: string;
    order: string;
  };
  sessionId: string;
  eventId?: string;
  filters: object;
};

type TGetEventLearnersParams = TCommon & {
  pagination: { start: number; rows: number };
  sort: {
    type: string;
    order: string;
  };
  eventId: string;
  filters: object;
};

type TGetEventLeftPanelSessionsParam = TCommon & {
  eventId: string;
};

type TGetAllLearnersForSessionParam = TCommon & {
  sessionId: string;
};
type TGetAllLearnersForEventParam = TCommon & {
  eventId: string;
};

type TExportSessionLearnersStatus = TCommon & {
  sessionId: string;
  eventId?: string;
  email: string;
};
type TExportEventLearnersStatus = TCommon & {
  eventId: string;
  email: string;
};

type TMarkAttendance = TCommon & { data: [any]; eventId?: string; sessionId: string };
type TMarkEventAttendance = TCommon & { data: [any]; eventId: string };
type TChangeEnrollmentStatus = TCommon & {
  eventId?: string;
  sessionId: string;
  data: [any];
  entityType: TILTEntities;
  enrollmentType?: TEventEnrollmentType;
};
type TEventChangeEnrollmentStatus = TCommon & {
  eventId: string;
  data: [any];
  entityType: TILTEntities;
  enrollmentType: TEventEnrollmentType;
};
const ManagePageService = {
  getEventLeftPanelSessions: async ({
    eventId,
    companyId,
    moduleId,
    seriesId,
  }: TGetEventLeftPanelSessionsParam) => {
    const { sessions, event } = await post(
      ApiUrls.getEventSessions({
        moduleId,
        companyId,
        eventId,
        query: {
          forSeries: seriesId,
        },
      }),
      {
        body: {
          filters: {
            // TODO on track page all these constants are maintained separattely  but its values are referred from session constants
            [ENROLL_DRAWER_SUPPORTED_FILTERS_API_NAME[
              ENROLL_DRAWER_SUPPORTED_FILTERS.SESSION_STATUS
            ]]: [
              SUPPORTED_FILTERS_API_KEY[ENROLL_DRAWER_SUPPORTED_FILTERS.SESSION_STATUS].PUBLISHED,
              SUPPORTED_FILTERS_API_KEY[ENROLL_DRAWER_SUPPORTED_FILTERS.SESSION_STATUS].CANCELLED,
              SUPPORTED_FILTERS_API_KEY[ENROLL_DRAWER_SUPPORTED_FILTERS.SESSION_STATUS].UNPUBLISHED,
            ],
          },
          sortField: ENROLL_DRAWER_SUPPORTED_SORTING.START_TIME,
          sortOrder: 'ASC',
        },
      }
    );
    return { ...parseEventSessionDetails({ sessions, event, eventId }) };
  },
  getAllLearnersForSession: async ({
    sessionId,
    companyId,
    moduleId,
    seriesId,
  }: TGetAllLearnersForSessionParam) => {
    const { learners = [] } = await get(
      ApiUrls.getAllLearnersForSession({
        moduleId,
        companyId,
        sessionId,
        query: { forSeries: seriesId },
      })
    );
    return learners.map(({ id, name, email }: { id: string; name: string; email: string }) => ({
      id,
      name,
      email,
    }));
  },
  getAllLearnersForEvent: async ({
    eventId,
    companyId,
    moduleId,
    seriesId,
  }: TGetAllLearnersForEventParam) => {
    const { learners = [] } = await get(
      ManagePageApiUrls.getAllLearnersForEvent({
        moduleId,
        companyId,
        eventId,
        query: { forSeries: seriesId },
      })
    );
    return learners.map(({ id, name, email }: { id: string; name: string; email: string }) => ({
      id,
      name,
      email,
    }));
  },

  exportSessionLearnersStatus: async ({
    sessionId,
    eventId,
    email,
    moduleId,
    companyId,
    seriesId,
  }: TExportSessionLearnersStatus) =>
    await post(
      ManagePageApiUrls.exportSessionLearners({
        moduleId,
        companyId,
        query: { forSeries: seriesId },
      }),
      {
        body: {
          sessionId,
          eventId,
          email,
        },
      }
    ),

  exportEventLearnersStatus: async ({
    eventId,
    email,
    moduleId,
    companyId,
    seriesId,
  }: TExportEventLearnersStatus) =>
    await post(
      ManagePageApiUrls.exportEventLearners({
        moduleId,
        companyId,
        query: { forSeries: seriesId },
      }),
      {
        body: {
          eventId,
          email,
        },
      }
    ),

  markAttendance: async ({
    companyId,
    moduleId,
    seriesId,
    eventId,
    sessionId,
    data,
  }: TMarkAttendance) => {
    const { users } = prepareAttendanceRequest(data);
    const { response: learners } = await post(
      ApiUrls.markAttendance({
        companyId,
        moduleId,
        eventId,
        sessionId,
        query: { forSeries: seriesId },
      }),
      {
        body: { users },
      }
    );
    return handleOperationResponse(learners);
  },
  markEventAttendance: async ({
    companyId,
    moduleId,
    seriesId,
    eventId,
    data,
  }: TMarkEventAttendance) => {
    const { users } = prepareEventAttendanceRequest(data);
    const { response: learners } = await post(
      ManagePageApiUrls.markEventAttendance({
        companyId,
        moduleId,
        eventId,
        query: { forSeries: seriesId },
      }),
      {
        body: { users },
      }
    );
    return handleEventOperationResponse(learners);
  },
  getSessionLearners: async ({
    moduleId,
    seriesId,
    companyId,
    pagination,
    sort,
    sessionId,
    eventId,
    filters = {},
  }: TGetSessionLearnersParams) => {
    try {
      const reqBody = {
        filters: filters,
        start: pagination.start,
        size: pagination.rows,
        sort_on: SUPPORTED_SORTINGS_API_KEY[sort.type],
        sort_order: sort.order,
      };
      const { learners: rawLearners, total: totalCount } = await post(
        ManagePageApiUrls.getSessionLearners({
          moduleId,
          companyId,
          sessionId,
          eventId,
          query: {
            forSeries: seriesId,
          },
        }),
        {
          body: reqBody,
        }
      );
      const { learners, learnerDetails } = parseSessionLearners(rawLearners);
      return {
        learners,
        learnerDetails,
        totalCount,
      };
    } catch (error) {
      throw error;
    }
  },
  getEventLearners: async ({
    moduleId,
    seriesId,
    companyId,
    pagination,
    sort,
    eventId,
    filters = {},
  }: TGetEventLearnersParams) => {
    try {
      const reqBody = {
        filters: filters,
        start: pagination.start,
        size: pagination.rows,
        sort_on: SUPPORTED_SORTINGS_API_KEY[sort.type],
        sort_order: sort.order,
      };
      const { learners: rawLearners, total: totalCount } = await post(
        ManagePageApiUrls.getEventLearners({
          moduleId,
          companyId,
          eventId,
          query: {
            forSeries: seriesId,
          },
        }),
        {
          body: reqBody,
        }
      );
      const { learners, learnerDetails } = parseEventLearners(rawLearners);
      return {
        learners,
        learnerDetails,
        totalCount,
      };
    } catch (error) {
      throw error;
    }
  },
  changeEnrollmentStatus: async ({
    moduleId,
    companyId,
    seriesId,
    eventId,
    sessionId,
    entityType,
    enrollmentType,
    data,
  }: TChangeEnrollmentStatus) => {
    try {
      const requestBody = prepareChangeEnrollmentStatusRequest({
        data,
        eventId,
        sessionId,
        entityType,
        enrollmentType,
      });
      const { response: learners } = await post(
        ManagePageApiUrls.changeSessionEnrollmentStatus({
          companyId,
          moduleId,
          eventId,
          sessionId,
          query: { forSeries: seriesId },
        }),
        {
          body: requestBody,
        }
      );
      return handleOperationResponse(learners);
    } catch (error) {
      throw error;
    }
  },
  changeEventEnrollmentStatus: async ({
    moduleId,
    companyId,
    seriesId,
    eventId,
    entityType,
    enrollmentType,
    data,
  }: TEventChangeEnrollmentStatus) => {
    try {
      const requestBody = prepareEventChangeEnrollmentStatusRequest({
        data,
        eventId,
        entityType,
        enrollmentType,
      });
      const { response: learners } = await post(
        ManagePageApiUrls.changeEventEnrollmentStatus({
          companyId,
          moduleId,
          eventId,
          query: { forSeries: seriesId },
        }),
        {
          body: requestBody,
        }
      );
      return handleEventOperationResponse(learners);
    } catch (error) {
      throw error;
    }
  },
};

export default ManagePageService;
