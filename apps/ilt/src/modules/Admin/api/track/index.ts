import isNil from 'lodash/isNil';

import { get, post } from '@mindtickle/api-helpers';
import { isString } from '@mindtickle/utils';

import { POLLING_STATUS } from '~/config/global.config';
import {
  addLocalTimeZoneToSessions,
  parseSession,
  parseSessionStats,
} from '~/modules/Admin/api/sessionService/helpers';
import { logErrorToIntegrationTools } from '~/modules/Admin/components/ErrorBoundary';
import ApiUrls from '~/modules/Admin/config/api.config';
import { DEFAULT_PAGINATION } from '~/modules/Admin/config/sessions.constants';
import {
  SUPPORTED_SORTINGS_API_KEY,
  ENROLL_DRAWER_SUPPORTED_FILTERS,
  ENROLL_DRAWER_SUPPORTED_FILTERS_API_NAME,
} from '~/modules/Admin/config/track.constants';

import { parseLearners, handleOperationResponse, getEntityTypeFromIds } from './helpers';

import type { GetLearnersParamsType } from './typeDefs';
import type { TEntity } from '../sessionService/typeDefs/entityDetails';

const processFilter = (iltFilters: Array<any> = []) =>
  iltFilters
    .filter(iltFilter => iltFilter.value)
    .reduce((prevFilter, filter) => {
      const filterType = filter.type;
      switch (filterType) {
        case ENROLL_DRAWER_SUPPORTED_FILTERS.SESSION_STATE:
        case ENROLL_DRAWER_SUPPORTED_FILTERS.SESSION_TYPE:
        case ENROLL_DRAWER_SUPPORTED_FILTERS.ENTITY_TYPE:
          return {
            ...prevFilter,
            [ENROLL_DRAWER_SUPPORTED_FILTERS_API_NAME[
              filterType as keyof typeof ENROLL_DRAWER_SUPPORTED_FILTERS_API_NAME
            ]]: [filter.value],
          };
        case ENROLL_DRAWER_SUPPORTED_FILTERS.ILT_DATE_RANGE_DD:
          return {
            ...prevFilter,
            startTimeEpoch: filter.value[0],
            endTimeEpoch: filter.value[1],
          };
        case ENROLL_DRAWER_SUPPORTED_FILTERS.ENROLLMENT_STATUS:
        case ENROLL_DRAWER_SUPPORTED_FILTERS.ATTENDANCE_STATUS:
          return { ...prevFilter, [filterType]: filter.value };
        case ENROLL_DRAWER_SUPPORTED_FILTERS.SESSION_STATUS:
          return {
            ...prevFilter,
            [ENROLL_DRAWER_SUPPORTED_FILTERS_API_NAME[
              filterType as keyof typeof ENROLL_DRAWER_SUPPORTED_FILTERS_API_NAME
            ]]: filter.value,
          };
        default:
          // This is something we need to test that in default case we should always get filterType
          // as search by looking at all the conditions I guess htat is the case.
          isString(
            ENROLL_DRAWER_SUPPORTED_FILTERS_API_NAME[
              filterType as typeof ENROLL_DRAWER_SUPPORTED_FILTERS.SEARCH
            ]
          );
          return {
            ...prevFilter,
            [ENROLL_DRAWER_SUPPORTED_FILTERS_API_NAME[
              filterType as typeof ENROLL_DRAWER_SUPPORTED_FILTERS.SEARCH
            ]]: filter.value,
          };
      }
    }, {});

const parsePollingStatus = (response: {
  status: string;
  success: any;
  error: any;
  isNewUM: any;
}) => ({
  status: POLLING_STATUS[response.status as keyof typeof POLLING_STATUS],
  successIds: response.success,
  errorIds: response.error,
  isNewUM: !isNil(response.isNewUM),
});

const TrackService = {
  getAllLearnersForSession: async ({
    sessionId,
    companyId,
    moduleId,
    seriesId,
  }: {
    sessionId: string;
    companyId: string;
    moduleId: string;
    seriesId: string;
  }) => {
    const { learners = [] } = await get(
      ApiUrls.getAllLearnersForSession({
        moduleId,
        companyId,
        sessionId,
        query: { forSeries: seriesId },
      })
    );
    return learners.map(({ id, name, email }: { id: string; name: string; email: string }) => ({
      id,
      name,
      email,
    }));
  },

  exportLearnersStatus: async ({
    sessionId,
    email,
    moduleId,
    seriesId,
  }: {
    sessionId: string;
    email: string;
    moduleId: string;
    seriesId: string;
  }) => {
    await post(
      ApiUrls.exportLearnersStatus({
        moduleId,
        query: { forSeries: seriesId },
      }),
      {
        body: {
          sessionId,
          email,
        },
      }
    );
  },

  pollStatus: async ({ companyId, processId }: any) => {
    try {
      const response = await get(
        ApiUrls.pollStatus({
          companyId,
          processId,
        })
      );
      return parsePollingStatus(response[processId] || {});
    } catch (error) {
      logErrorToIntegrationTools(error as Error);
      throw error;
    }
  },
  getSessionDetails: async ({
    moduleId,
    seriesId,
    sessionIds,
  }: {
    moduleId: string;
    seriesId: string;
    sessionIds: Array<string>[];
  }) => {
    const { sessionDetails = {}, domain } = await post(
      ApiUrls.getMultiSessionDetails({
        moduleId,
        query: { forSeries: seriesId },
      }),
      {
        body: {
          sessionIds,
        },
      }
    );

    const sessionsMap = parseSession(Object.values(sessionDetails));
    const sessionsMapWithLocalTimeZone = await addLocalTimeZoneToSessions(
      sessionsMap as unknown as { [key: string]: TEntity }
    );
    return {
      sessions: sessionsMapWithLocalTimeZone,
      domain: domain || '',
    };
  },
  // Used only in Manage pages
  getSeriesInfo: async ({
    companyId,
    seriesIds = [],
  }: {
    companyId: string;
    seriesIds?: Array<string>[];
  }) => {
    const { seriesNameMap = {}, seriesWithAccess = [] } = await post(
      ApiUrls.getSeriesNameAndAccessBulk({ companyId }),
      {
        body: { seriesIds },
      }
    );

    const seriesInfoMap: { [key: string]: { id: string; name: string; hasAccess: boolean } } = {};
    for (let seriesId in seriesNameMap) {
      if (seriesNameMap.hasOwnProperty(seriesId)) {
        seriesInfoMap[seriesId] = {
          id: seriesId,
          name: seriesNameMap[seriesId],
          hasAccess: seriesWithAccess.includes(seriesId),
        };
      }
    }

    return {
      seriesInfoMap: seriesInfoMap,
    };
  },

  getEntityStats: async ({ companyId, moduleId, seriesId, sessionIds, eventIds }: any) => {
    try {
      const { sessionStats, eventStats } = await post(
        ApiUrls.getEntityStats({
          companyId,
          moduleId,
          query: { forSeries: seriesId },
        }),
        {
          body: {
            sessionIds: sessionIds,
            eventIds: eventIds,
          },
        }
      );
      return {
        entityStatsMap: parseSessionStats(Object.assign({}, sessionStats, eventStats)),
      };
    } catch (error) {
      logErrorToIntegrationTools(error as Error);
      throw error;
    }
  },

  getLearners: async ({
    moduleId,
    seriesId,
    companyId,
    pagination,
    sort,
    sessionId,
    filters = {},
  }: GetLearnersParamsType) => {
    try {
      const { learners: rawLearners, total: totalCount } = await post(
        ApiUrls.getLearners({
          moduleId,
          companyId,
          query: {
            forSeries: seriesId,
          },
        }),
        {
          body: {
            filters: filters,
            start: pagination.start,
            size: pagination.rows,
            sort_on: SUPPORTED_SORTINGS_API_KEY[sort.type],
            sort_order: sort.order,
          },
        }
      );
      const { learners, learnerDetails } = parseLearners(rawLearners);
      return {
        learners,
        learnerDetails,
        totalCount,
      };
    } catch (error) {
      logErrorToIntegrationTools(error as Error);
      throw error;
    }
  },

  changeEnrollmentStatus: async ({ moduleId, companyId, seriesId, data, entity }: any) => {
    try {
      let notifyLearners = true;
      const users = data.map(({ learnerId: userId, operation, notify, attendanceStatus }: any) => {
        notifyLearners = notify;
        return {
          userId,
          operationType: operation,
          attendanceStatus,
        };
      });
      const learners = await post(
        ApiUrls.changeEnrollmentStatus({
          companyId,
          moduleId,
          query: { forSeries: seriesId },
        }),
        {
          body: {
            users,
            notifyLearners,
            sessionId: entity?.sessionId,
            eventId: entity?.eventId,
            entityType: getEntityTypeFromIds(entity?.sessionId, entity?.eventId),
            enrolmentLevel: entity?.enrolmentLevel,
          },
        }
      );
      return handleOperationResponse(learners);
    } catch (error) {
      logErrorToIntegrationTools(error as Error);
      throw error;
    }
  },

  changeRelevance: async ({ moduleId, companyId, seriesId, data, moduleRelevance }: any) => {
    try {
      const uids = data;
      const { processId } = await post(
        ApiUrls.changeRelevance({
          companyId,
          moduleId,
          query: { forSeries: seriesId },
        }),
        {
          body: {
            uids,
            moduleRelevance,
          },
        }
      );
      return processId;
    } catch (error) {
      logErrorToIntegrationTools(error as Error);
      throw error;
    }
  },

  removeLearners: async ({ moduleId, seriesId, companyId, learners }: any) => {
    try {
      const { processId } = await post(
        ApiUrls.removeLearnerFromContext({
          companyId,
        }),
        {
          body: {
            entity: moduleId,
            seriesId: seriesId,
            userReq: {
              ids: learners,
            },
          },
        }
      );
      return processId;
    } catch (error) {
      logErrorToIntegrationTools(error as Error);
      throw error;
    }
  },
  getLearnerSessionsAndEvents: async ({
    companyId,
    moduleId,
    seriesId,
    filters,
    pagination = DEFAULT_PAGINATION,
    sortOrder,
    sortField,
    learnerId,
  }: any) => {
    try {
      const {
        sessionAndEventStatus: learnerEntities,
        sessionAndEventInfoMap: entitiesInfo,
        hasMore,
      } = await post(
        ApiUrls.getLearnerSessionsAndEvents({
          companyId,
          moduleId,
          learnerId,
          query: {
            forSeries: seriesId,
          },
        }),
        {
          body: {
            start: pagination.start,
            size: pagination.rows,
            filters: processFilter(filters),
            sortField,
            sortOrder,
          },
        }
      );

      return {
        entities: learnerEntities,
        entitiesInfo,
        hasMore,
        start: pagination.start + pagination.rows,
        rows: pagination.rows,
      };
    } catch (error) {
      logErrorToIntegrationTools(error as Error);
      throw error;
    }
  },
  getLearnerSessionsWithinEvent: async ({
    companyId,
    moduleId,
    seriesId,
    filters,
    pagination = DEFAULT_PAGINATION,
    sortOrder,
    sortField,
    learnerId,
    eventId,
  }: any) => {
    try {
      const {
        sessionStatus: learnerSessionsWithinEvent,
        sessionInfoMap: sessionsWithinEventInfo,
        hasMore,
      } = await post(
        ApiUrls.getLearnerSessionsWithinEvent({
          companyId,
          moduleId,
          learnerId,
          eventId,
          query: {
            forSeries: seriesId,
          },
        }),
        {
          body: {
            start: pagination.start,
            size: pagination.rows,
            filters: processFilter(filters),
            sortField,
            sortOrder,
          },
        }
      );

      return {
        sessionsWithinEvent: learnerSessionsWithinEvent,
        sessionsWithinEventInfo,
        hasMore,
        start: pagination.start + pagination.rows,
        rows: pagination.rows,
      };
    } catch (error) {
      logErrorToIntegrationTools(error as Error);
      throw error;
    }
  },
};
export default TrackService;
