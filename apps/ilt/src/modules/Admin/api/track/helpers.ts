import { hoursToMiliseconds, prune } from '@mindtickle/utils';

import {
  ATTENDANCE,
  ENROLLMENT_STATUS,
  ENROLLMENT_STATUS_TYPES,
} from '~/modules/Admin/config/track.constants';
import { getStatusByMap } from '~/modules/Admin/utils';
import { getTimezoneDetailsByKey, subtractOffsetDiff } from '~/utils/timezone';

import { ILT_ENTITIES } from '../../constants/module';
import { checkIsEvent, checkIsSessionWithinEvent } from '../../utils/checkEntityType';
import { getCurrentTimeZone, getCurrentTimeZoneAbbreviated } from '../../utils/timing';

const parseTimezone = (timezone: any) => getTimezoneDetailsByKey('displayName', timezone);

export const handleOperationResponse = (learners: { [key: string]: any } = {}) => {
  learners = prune(Object.values(learners.response));
  return parseLearners(learners);
};

export const parseLearners = (learners: any = []) => {
  const learnerDetails: { [key: string]: any } = {};

  learners = learners.map((learner: any) => {
    let {
      sessionDetails: enrolledSessionDetails = {},
      eventDetails: enrolledEventDetails = {},
      addedOn: invitedOn,
      totalAttendedSessionsAndEvents: attendedCount,
      totalEnrolledSessionsAndEvents: enrolledCount,
      totalWaitlistedSessionsAndEvents: waitlistedCount,
      id,
      pic,
      email,
      primaryEmail,
      username,
      name,
      score: maxScore,
      moduleRelevance,
      entityType,
      totalNotEnrolledSessionsInEvent,
      totalEnrolledSessionsInEvent,
    } = learner;
    let isEntityEvent = checkIsEvent(entityType);
    let isEntitySessionWithinEvent = checkIsSessionWithinEvent(entityType);
    let entityDetails = isEntityEvent ? enrolledEventDetails : enrolledSessionDetails;
    // Below timezone calculation for session
    let { timezone = '' } = enrolledSessionDetails;
    let timezoneObj;
    let timezoneOffset;
    try {
      timezoneObj = JSON.parse(timezone);
      timezoneOffset = timezoneObj.offset;
    } catch (err) {
      timezoneObj = parseTimezone(timezone) || {};
      timezoneOffset = hoursToMiliseconds(timezoneObj.offset);
    }

    learnerDetails[id] = {
      enrolledEntityDetails: {
        endTime: isEntityEvent
          ? entityDetails.endTimeEpoch
          : subtractOffsetDiff(entityDetails.endTimeEpoch, timezoneOffset),
        attendedStatus: getStatusByMap(entityDetails.attendanceStatus, ATTENDANCE),
        joinedOn: entityDetails.enrollmentStatusUpdatedAtSec * 1000,
        id: isEntityEvent ? enrolledEventDetails.id : enrolledSessionDetails.sessionId,
        name: isEntityEvent ? enrolledEventDetails.name : enrolledSessionDetails.sessionName,
        enrollmentStatus: entityDetails.enrollmentStatus
          ? getStatusByMap(entityDetails.enrollmentStatus, ENROLLMENT_STATUS)
          : ENROLLMENT_STATUS_TYPES.DID_NOT_ENROLLED,
        startTime: isEntityEvent
          ? entityDetails.startTimeEpoch
          : subtractOffsetDiff(entityDetails.startTimeEpoch, timezoneOffset),
        timezone: isEntityEvent
          ? `(${getCurrentTimeZoneAbbreviated(entityDetails.startTimeEpoch)})`
          : timezoneObj.shortDisplayName && timezoneObj.region
          ? `${timezoneObj.shortDisplayName} ${timezoneObj.region}`
          : timezoneObj.name,
        isPublished: entityDetails.publishedBefore,
      },
      ...(isEntitySessionWithinEvent
        ? {
            enrolledEntityParentDetails: {
              endTime: enrolledEventDetails.endTimeEpoch,
              attendedStatus: getStatusByMap(enrolledEventDetails.attendanceStatus, ATTENDANCE),
              joinedOn: enrolledEventDetails.enrollmentStatusUpdatedAtSec * 1000,
              id: enrolledEventDetails.id,
              name: enrolledEventDetails.name,
              enrollmentStatus: enrolledEventDetails.enrollmentStatus
                ? getStatusByMap(enrolledEventDetails.enrollmentStatus, ENROLLMENT_STATUS)
                : ENROLLMENT_STATUS_TYPES.DID_NOT_ENROLLED,
              startTime: enrolledEventDetails.startTimeEpoch,
              timezone: `(${getCurrentTimeZoneAbbreviated(
                enrolledEventDetails.startTimeEpoch
              )}) ${getCurrentTimeZone()}`,
              isPublished: enrolledEventDetails.publishedBefore,
            },
          }
        : {}),
      id,
      pic,
      name: name || username,
      email,
      username,
      primaryEmail,
      invitedOn: invitedOn * 1000,
      attendedCount,
      enrolledCount,
      waitlistedCount,
      maxScore,
      moduleRelevance,
      totalNotEnrolledSessionsInEvent,
      totalEnrolledSessionsInEvent,
      entityType,
    };
    return id;
  });

  return {
    learnerDetails,
    learners,
  };
};

export const getEntityTypeFromIds = (sessionId?: string, eventId?: string) => {
  if (sessionId && eventId) {
    return ILT_ENTITIES.SESSION_WITHIN_EVENT;
  } else if (eventId) {
    return ILT_ENTITIES.EVENT;
  }
  return ILT_ENTITIES.SESSION;
};
