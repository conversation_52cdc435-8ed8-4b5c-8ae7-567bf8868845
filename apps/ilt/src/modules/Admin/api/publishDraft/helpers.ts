import { prune } from '@mindtickle/utils';

import { ASSIGNED_BY_OPTIONS } from '~/modules/Admin/containers/MTCoreModulePublish/constants';

// learnersCount
export const parseInvitedLearnersCount = (
  { seriesAggr = {} }: { seriesAggr?: { [key: string]: any } } = {},
  { currentSeriesId }: { currentSeriesId: string }
) => {
  const series = Object.keys(seriesAggr) || [];
  let fullCount = 0,
    allCount = 0;

  // filter out private series
  const privateSeries = series.filter(seriesId => !seriesAggr[seriesId].isPublic);
  // check if there is even a single private series
  const numOfPrivateSeries = privateSeries.length;
  const numOfPublicSeries = series.length - numOfPrivateSeries;
  const hasAllPublicSeries = numOfPublicSeries === series.length;
  let isCurrentSeriesPublic = false;
  // calculate all and full count learners
  series.forEach(key => {
    const { FULL = 0, TOTAL = 0, isPublic } = seriesAggr[key];
    fullCount += FULL;
    allCount += TOTAL;
    if (currentSeriesId === key) {
      isCurrentSeriesPublic = isPublic === true;
    }
  });

  const hasPublicSeries = numOfPublicSeries > 0;

  return {
    hasAllPublicSeries,
    hasPublicSeries,
    selectOption: hasPublicSeries && series.length > 1 ? false : true,
    noLearners: !allCount,
    fullCount,
    allCount,
    seriesCount: series.length,
    assignedBy: isCurrentSeriesPublic ? ASSIGNED_BY_OPTIONS.ADMIN : undefined,
    isCurrentSeriesPublic,
  };
};

// learners
const parseLearner = (
  acc: { [x: string]: any },
  { id, name, email, username, groups, entity_count, total_count, pic }: any
) => {
  acc[id] = prune({
    id,
    name: name,
    email: email,
    username: username,
    groups,
    entity_count,
    total_count,
    pic,
  });
  return acc;
};

const addLearnerId = (acc: any, learner: { id: any }) => [...acc, learner.id];

export const parseInvitedLearners = ({ learners, total }: { learners: any[]; total?: number }) => ({
  total,
  data: learners.reduce(addLearnerId, []),
  learners: learners.reduce(parseLearner, {}),
});

// groups
const parseGroup = ({ id, name }: { id: string; name: string }) => ({ value: id, label: name });

export const parseGroups = ({ groups }: { groups: any[] }) => groups.map(parseGroup);

// profile fields
const parseProfileField = ({
  displayName,
  shortKey,
}: {
  displayName: string;
  shortKey: string;
}) => ({
  content: displayName,
  key: shortKey,
});

export const parseProfileFields = ({ profile_fields: profileFields }: { profile_fields: any[] }) =>
  profileFields.map(parseProfileField);

// profileKeys
const parseProfileKey = (profileKey: any) => ({
  label: profileKey,
  value: profileKey,
});
export const parseProfileKeyData = (profileKeys: any[]) => profileKeys.map(parseProfileKey);

export const serializeProfileFields = (profileFields: any[]) => {
  if (profileFields) {
    return profileFields.reduce((acc: { [x: string]: any }, { field, profileKeys }: any) => {
      if (field) {
        acc[field] = profileKeys.join('_#_');
      }
      return acc;
    }, {});
  }
};
