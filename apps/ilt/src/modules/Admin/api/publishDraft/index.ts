import { get, post } from '@mindtickle/api-helpers';

import { logErrorToIntegrationTools } from '~/modules/Admin/components/ErrorBoundary';
import { LEARNER_TYPES } from '~/modules/Admin/containers/MTCoreModulePublish/constants';
import type { TAssigned_By } from '~/modules/Admin/containers/MTCoreModulePublish/typeDefs';

import ApiUrls from '../../config/publishDraftApi.config';

import {
  parseInvitedLearnersCount,
  parseInvitedLearners,
  parseGroups,
  parseProfileFields,
  parseProfileKeyData,
  serializeProfileFields,
} from './helpers';

const PublishService: { [key: string]: Function } = {};

PublishService.getInvitedLearnersCount = async ({
  moduleId,
  seriesId,
}: {
  moduleId: string;
  seriesId: string;
}) => {
  try {
    const response = await get(
      ApiUrls.getInvitedLearnersCount({
        moduleId,
        query: { forSeries: seriesId },
      })
    );

    return parseInvitedLearnersCount(response, { currentSeriesId: seriesId });
  } catch (error) {
    logErrorToIntegrationTools(error as Error);
    throw error;
  }
};
PublishService.getFullLearners = async ({
  moduleId,
  seriesId,
  companyId,
  mappedSeries,
  groupIds,
  profileFields = [],
  assignedBy,
  ...rest
}: {
  moduleId: string;
  seriesId: string;
  companyId: string;
  mappedSeries: string[];
  groupIds: string[];
  profileFields: any[];
  assignedBy?: TAssigned_By;
  [key: string]: any;
}) => {
  try {
    const response = await post(
      ApiUrls.getLearners({
        companyId,
        moduleId,
        query: { ...rest, forSeries: seriesId },
      }),
      {
        body: {
          seriesInviteType: LEARNER_TYPES.FULL,
          seriesIds: mappedSeries,
          groupIds,
          assignedBy,
          filters: {
            profile: serializeProfileFields(profileFields),
          },
        },
      }
    );
    return parseInvitedLearners(response);
  } catch (error) {
    logErrorToIntegrationTools(error as Error);
    throw error;
  }
};

PublishService.getAllLearners = async ({
  mappedSeries,
  seriesId,
  moduleId,
  companyId,
  groupIds,
  profileFields = [],
  assignedBy,
  ...rest
}: {
  moduleId: string;
  seriesId: string;
  companyId: string;
  mappedSeries: string[];
  groupIds: string[];
  profileFields: any[];
  assignedBy?: TAssigned_By;
  [key: string]: any;
}) => {
  try {
    const response = await post(
      ApiUrls.getLearners({
        moduleId,
        companyId,
        query: { ...rest, forSeries: seriesId },
      }),
      {
        body: {
          seriesInviteType: LEARNER_TYPES.ALL,
          seriesIds: mappedSeries,
          groupIds,
          assignedBy,
          filters: {
            profile: serializeProfileFields(profileFields),
          },
        },
      }
    );
    return parseInvitedLearners(response);
  } catch (error) {
    logErrorToIntegrationTools(error as Error);
    throw error;
  }
};

PublishService.getSelectedLearners = async ({
  moduleId,
  seriesId,
  companyId,
  mappedSeries,
  groupIds,
  profileFields = [],
  ...rest
}: {
  moduleId: string;
  seriesId: string;
  companyId: string;
  mappedSeries: string[];
  groupIds: string[];
  profileFields: any[];
  [key: string]: any;
}) => {
  try {
    const response = await post(
      ApiUrls.getSelectedLearners({
        moduleId,
        companyId,
        seriesId,
        query: {
          ...rest,
          forSeries: seriesId,
          sort_on: 'companies.series.added_on',
          sort_order: 'desc',
        },
      }),
      {
        body: {
          seriesIds: mappedSeries,
          groupIds,
          filters: {
            profile: serializeProfileFields(profileFields),
          },
        },
      }
    );
    return parseInvitedLearners(response);
  } catch (error) {
    logErrorToIntegrationTools(error as Error);
    throw error;
  }
};

PublishService.getGroups = async ({
  moduleId,
  seriesId,
  companyId,
  ...rest
}: {
  moduleId: string;
  seriesId: string;
  companyId: string;
  [key: string]: any;
}) => {
  try {
    const response = await get(
      ApiUrls.getGroups({
        moduleId,
        companyId,
        query: { ...rest, forSeries: seriesId },
      })
    );
    return parseGroups(response);
  } catch (error) {
    logErrorToIntegrationTools(error as Error);
    throw error;
  }
};

PublishService.getProfileFields = async ({ companyId }: { companyId: string }) => {
  try {
    const response = await get(
      ApiUrls.getProfileFields({
        companyId,
        query: { exclude_disabled: false },
      })
    );
    return parseProfileFields(response);
  } catch (error) {
    logErrorToIntegrationTools(error as Error);
    throw error;
  }
};

PublishService.getProfileKeyData = async ({
  companyId,
  profileField,
}: {
  companyId: string;
  profileField: any;
}) => {
  try {
    const response = await get(
      ApiUrls.getProfileKeyData({
        companyId,
        query: { profileField },
      })
    );
    return parseProfileKeyData(response.data);
  } catch (error) {
    logErrorToIntegrationTools(error as Error);
    throw error;
  }
};

PublishService.publishData = async ({
  moduleId,
  seriesId,
  companyId,
  ...rest
}: {
  moduleId: string;
  seriesId: string;
  companyId: string;
  [key: string]: any;
}) => {
  try {
    const {
      search: query,
      profileFields,
      groupIds,
      userIds: uids,
      notify,
      type,
      numOfSelectedLearners,
      isPublic,
      assignedBy,
    } = rest;
    const body: { [key: string]: any } = {
      query,
      groupIds,
      uids,
      isPublic,
      profile: serializeProfileFields(profileFields),
      notifyDestSeriesLearners: notify,
      totalInvitingLearners: numOfSelectedLearners,
      assignedBy,
    };

    if (type === LEARNER_TYPES.SELECTED) {
      body.totalSelected = numOfSelectedLearners;
    } else {
      body.inviteTo = type || LEARNER_TYPES.NONE;
    }

    const response = await post(
      ApiUrls.publishData({
        moduleId,
        companyId,
        query: {
          forSeries: seriesId,
          sendEmails: notify,
          upgrade: type,
        },
      }),
      { body: { pubInviteOptions: body } }
    );
    return {
      ...response,
      notify,
      numOfSelectedLearners,
    };
  } catch (error) {
    logErrorToIntegrationTools(error as Error);
    throw error;
  }
};

PublishService.getTodos = async ({
  moduleId,
  seriesId /*, moduleType*/,
  companyId,
}: {
  moduleId: string;
  seriesId: string;
  companyId: string;
}) => {
  const { todos } = await get(
    ApiUrls.getTodos({
      // moduleType,
      moduleId,
      companyId,
      query: {
        forSeries: seriesId,
      },
    })
  );

  return todos.totalErrors;
};

export default PublishService;
