import { get, post } from '@mindtickle/api-helpers';

import ApiUrls from '../../config/api.config';

const ILT_LC_Service = {
  createLiveChallenge: async ({ lcData }: { lcData: any }) =>
    await post(ApiUrls.createLiveChallenge(), {
      body: { payload: lcData },
    }),
  loadLiveChallengeDetails: async ({ lcUniqueCodes }: { lcUniqueCodes: string[] }) => {
    const requests = lcUniqueCodes.map(uniqueCode =>
      get(ApiUrls.loadLiveChallengeDetails(uniqueCode))
    );
    return await Promise.all(requests);
  },
  exportLiveChallengeReport: async ({
    sessionId,
    emailIds,
    uniqueCode,
    companyId,
    moduleId,
  }: {
    sessionId: string;
    emailIds: string[];
    uniqueCode: string;
    companyId: string;
    moduleId: string;
  }) =>
    await post(ApiUrls.exportLiveChallengeReport(uniqueCode), {
      body: {
        body: {
          companyId,
          gameId: moduleId,
          sessionId,
          emailIds,
        },
      },
    }),
  emailLiveChallengeLinks: async ({
    sessionId,
    emailIds,
    moduleId,
    seriesId,
  }: {
    sessionId: string;
    emailIds: string[];
    moduleId: string;
    seriesId: string;
  }) =>
    await post(ApiUrls.emailLiveChallengeLinks({ moduleId, sessionId, seriesId }), {
      body: {
        emailIds,
      },
    }),
};

export default ILT_LC_Service;
