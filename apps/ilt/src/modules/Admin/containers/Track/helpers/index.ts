import moment from 'moment';

import { hoursToMiliseconds } from '@mindtickle/utils';

import { parseTimezone } from '~/modules/Admin/api/sessionService/helpers';
import { ILT_ENTITIES } from '~/modules/Admin/constants/module';
import { SESSION_TYPES_ENUM } from '~/modules/Admin/constants/sessions';
import { getTimezoneFromString, subtractOffsetDiff } from '~/utils/timezone';

import type {
  FiltersType,
  LearnerActionsType,
  TLearnerEntity,
  TLearnerEntityDetails,
  TLearnerSessionsAndEventsInfoMap,
} from '../typeDefs';

const FIlTER_API_VALUE = {
  NONE: {},
  thisMonth: {
    minInvitedOn: moment().startOf('month').unix(),
  },
  prevMonth: {
    minInvitedOn: moment().subtract(1, 'months').startOf('month').unix(),
    maxInvitedOn: moment().startOf('month').unix(),
  },
};

export const flattenFilters = (filters: any) => {
  const groupIds = filters['groups'].map((element: any) => element.id);
  const updatedFilters = {
    ...filters,
    ...FIlTER_API_VALUE[filters['invitedOn'] as keyof typeof FIlTER_API_VALUE],
  };
  updatedFilters['groups'] = groupIds;
  delete updatedFilters['invitedOn'];
  return updatedFilters;
};

export const getLearnersFiltersConfig = ({
  learnerActions,
  filterState,
}: {
  learnerActions: LearnerActionsType;
  filterState: FiltersType;
}) => ({
  INVITED_ON: {
    onChange: (selected: any) => {
      const filterValue = selected.value;
      learnerActions.filter({ filterType: 'invitedOn', data: filterValue });
    },
    placeholder: 'Invited',
    options: [
      { label: 'Invited this month', value: 'thisMonth' },
      { label: 'Invited previous month', value: 'prevMonth' },
    ],
    value: filterState.invitedOn,
  },
  GROUP_FILTER: {
    onChange: (updatedGroups: any) => {
      learnerActions.filter({ filterType: 'groups', data: updatedGroups });
    },
    values: filterState.groups,
  },
  MODULE_RELEVANCE: {
    onChange: (selected: Array<{ value: string }>) => {
      const filterValue = selected.map(element => element.value);
      learnerActions.filter({ filterType: 'moduleRelevance', data: filterValue });
    },
    placeholder: 'Relevance',
    options: [
      { label: 'Optional', value: 'OPT' },
      { label: 'Required', value: 'REQ' },
      { label: 'Unmarked', value: 'NONE' },
    ],
    value: filterState.moduleRelevance,
  },
  SEARCH: {
    placeholder: 'Search learners',
    onSearch: (value: string) => {
      learnerActions.filter({ filterType: 'query', data: (value || '').trim() });
    },
    value: filterState.query,
  },
});

export const processEnrollmentFilters = (filters: any) =>
  Object.keys(filters).map(element => ({ type: element, value: filters[element] }));

const setEntityType = (
  parsedEntity: TLearnerEntityDetails,
  entityData: TLearnerSessionsAndEventsInfoMap,
  entityStats: TLearnerEntity | undefined,
  entitiesStatsParentIds: Set<string | undefined>
) => {
  if (entityStats) {
    parsedEntity.entityType = entityStats.entityType;
    parsedEntity.parentId = entityStats.parentId;
    parsedEntity.type =
      entityStats.entityType === ILT_ENTITIES.EVENT
        ? entityData.locationType === 'VIDEO_CONF'
          ? SESSION_TYPES_ENUM.WEBINAR
          : entityData.locationType!
        : entityData.sessionType!;
  } else if (entitiesStatsParentIds.has(entityData.id)) {
    parsedEntity.entityType = ILT_ENTITIES.EVENT;
    parsedEntity.type =
      entityData.locationType === 'VIDEO_CONF'
        ? SESSION_TYPES_ENUM.WEBINAR
        : entityData.locationType!;
  }
};

export const parseLearnerEntities = (
  entities: Array<TLearnerEntity>,
  entitiesData: {
    [key: string]: TLearnerSessionsAndEventsInfoMap;
  } = {},
  parentId?: string
) => {
  let entitiesStats: {
    [key: string]: TLearnerEntity;
  } = {};
  let entitiesStatsParentIds: Set<string | undefined> = new Set();
  const parsedEntitiesData: {
    [key: string]: TLearnerEntityDetails;
  } = {};
  if (!parentId) {
    entitiesStats = entities.reduce(
      (
        acc: {
          [key: string]: TLearnerEntity;
        },
        obj: TLearnerEntity
      ) => {
        acc[obj.id] = obj;
        return acc;
      },
      {}
    );
    entitiesStatsParentIds = new Set(
      entities.filter(entity => entity.parentId).map(entity => entity.parentId)
    );
  }

  Object.keys(entitiesData).forEach(entityId => {
    const entityData = entitiesData[entityId];
    const entityStats = entitiesStats[entityId];
    let parsedEntity: TLearnerEntityDetails = {
      ...entityData,
      type: SESSION_TYPES_ENUM.CLASSROOM,
      autoEnroll: !!entityData.isAutomaticEnrollmentEnabled,
      waitingListEnabled: !!entityData.isWaitingListEnabled,
      maxSeats: entityData.maxEnrollments,
      maxSeatEnabled: !!entityData.isEnrollmentLimited,
    } as TLearnerEntityDetails;

    if (entityData.timezone && typeof entityData.timezone === 'string') {
      let timezoneOffset;
      let timezone;
      try {
        const { timezone: parsedTimezone, timezoneOffset: parsedTimezoneOffset } =
          getTimezoneFromString(entityData.timezone);
        timezoneOffset = parsedTimezoneOffset;
        timezone = parsedTimezone;
      } catch (e) {
        timezone = parseTimezone(entityData.timezone);
        timezoneOffset = hoursToMiliseconds(timezone.offset);
      }
      parsedEntity = {
        ...parsedEntity,
        timezone,
        originalStartTime: entityData.startTime,
        originalEndTime: entityData.endTime,
        startTime: subtractOffsetDiff(entityData.startTime, timezoneOffset),
        endTime: subtractOffsetDiff(entityData.endTime, timezoneOffset),
      };
    }
    if (!parentId) {
      setEntityType(parsedEntity, entityData, entityStats, entitiesStatsParentIds);
    } else {
      parsedEntity.entityType = ILT_ENTITIES.SESSION_WITHIN_EVENT;
      parsedEntity.parentId = parentId;
      parsedEntity.type = entityData.sessionType!;
    }
    parsedEntitiesData[entityId] = parsedEntity;
  });
  return parsedEntitiesData;
};
