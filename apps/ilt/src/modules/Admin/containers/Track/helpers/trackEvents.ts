import { SNOWPLOW_FIELD_NAMES } from '~/modules/Admin/hooks/useILTAdminSnowplowTracker/constants';
import {
  TRACK_PAGE_OPERATION_EVENT_MAP,
  EVENT_DATA_MAP,
  TRACK_PAGE_EVENTS,
} from '~/modules/Admin/hooks/useILTAdminSnowplowTracker/constants/trackPage';
import type {
  TSnowplowTrackerType,
  TEventDataMap,
} from '~/modules/Admin/hooks/useILTAdminSnowplowTracker/typeDefs';
import { sendEventUsingOperationStatus } from '~/modules/Admin/hooks/useILTAdminSnowplowTracker/utils';

export const sendTrackPageOperation = (
  tracker: TSnowplowTrackerType,
  {
    operationStatus,
    extraTrackingProperties,
  }: { operationStatus: any; extraTrackingProperties?: { [key: string]: any } }
) => {
  try {
    sendEventUsingOperationStatus(
      tracker,
      { operationStatus, extraTrackingProperties },
      {
        eventOperationMap: TRACK_PAGE_OPERATION_EVENT_MAP,
        eventDataMap: EVENT_DATA_MAP as unknown as TEventDataMap,
      }
    );
  } catch (err) {
    // do nothing
  }
};

export const sendExportLearnerClicked = (
  tracker: TSnowplowTrackerType,
  { pageName }: { pageName: string }
) => {
  tracker.trackStructuredEvent({
    eventName: TRACK_PAGE_EVENTS.email_ilt_report_clicked,
    [SNOWPLOW_FIELD_NAMES.PAGE_NAME]: pageName,
  });
};

export const sendEditSessionWithinEventEnrollmentClicked = (
  tracker: TSnowplowTrackerType,
  { pageName }: { pageName: string }
) => {
  tracker.trackStructuredEvent({
    eventName: TRACK_PAGE_EVENTS.edit_session_enrollment_status_clicked,
    [SNOWPLOW_FIELD_NAMES.PAGE_NAME]: pageName,
  });
};
