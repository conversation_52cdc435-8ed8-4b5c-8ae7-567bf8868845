import moment from 'moment';
import {
  put,
  takeEvery,
  takeLatest,
  call,
  all,
  select,
  delay,
  take,
  race,
} from 'redux-saga/effects';

import { getActions, getLoadingActions } from '@mindtickle/medux/Action';
import { reload } from '@mindtickle/utils';

import { POLLING_DELAY, POLLING_STATUS } from '~/config/global.config';
import { getLocalTimeZoneForSessions } from '~/modules/Admin/api/sessionService/helpers';
import TrackService from '~/modules/Admin/api/track';
import {
  DEFAULT_PAGINATION,
  OPERATIONS,
  DEFAULT_SORT,
  UPDATE_ENROLLMENT_STATUS_ACTION_MAP,
  UPDATE_ENROLLMENT_ATTENDANCE_STATUS_ACTION_MAP,
} from '~/modules/Admin/config/track.constants';
import { uniqueMerge } from '~/utils';
import { getUserProfileUrl } from '~/utils/generateUrls';

import {
  TRACK_GET_DATA,
  TRACK_UPDATE_LEARNERS_DETAILS,
  TRACK_UPDATE_ENROLL_OPERATION_STATUS,
  TRACK_ARE_ENTITY_STATS_FETCHED,
  TRACK_FETCH_STATS,
  TRACK_UPDATE_SEARCH_LEARNERS,
  TRACK_MANIPULATE_DATA,
  TRACK_POLLING,
  TRACK_GET_LEARNER_ENTITIES,
  TRACK_ENROLLMENT_DRAWER_CLOSED,
  TRACK_UPDATE_LEARNER_ENTITIES,
  TRACK_UPDATE_LEARNER_ENTITIES_DATA,
  TRACK_GET_LEARNER_SESSIONS_WITHIN_EVENT,
  TRACK_UPDATE_LEARNER_SESSIONS_WITHIN_EVENT,
  TRACK_EVENT_ENROLLMENT_DRAWER_CLOSED,
  TRACK_FETCH_LOCAL_TIMEZONE_NAME,
  TRACK_UPDATE_LOCAL_TIMEZONE_NAME,
} from '../../actionTypes';
import { prepareStatsEntityDataRequest } from '../../api/sessionService/helpers/parseEntities';
import TimezonesService from '../../api/timezonesService';
import { ILT_ENTITIES } from '../../constants/module';
import { getCurrentTimeZoneAbbreviated } from '../../utils/timing';

import { parseLearnerEntities } from './helpers';

import type { LearnerEntitiesType, TLearnerEntity, TLearnerEntityDetails } from './typeDefs';

const {
  GET_UPDATED_LEARNERS,
  SORT_LEARNERS,
  SEARCH_LEARNERS,
  PAGINATE_LEARNERS,
  GET_LEARNERS,
  REMOVE_LEARNERS,
  VIEW_LEARNER_PROFILE,
  GET_POLL_STATUS,
  CHANGE_LEARNER_STATUS,
  CHANGE_RELEVANCE,
  EXPORT_LEARNERS,
  LOAD_MORE,
} = OPERATIONS;

const OPERATIONS_SERVICE_MAP = {
  [GET_LEARNERS]: TrackService.getLearners,
  [SEARCH_LEARNERS]: TrackService.getLearners,
  [SORT_LEARNERS]: TrackService.getLearners,
  [PAGINATE_LEARNERS]: TrackService.getLearners,
  [REMOVE_LEARNERS]: TrackService.removeLearners,
  [GET_POLL_STATUS]: TrackService.pollStatus,
  [CHANGE_LEARNER_STATUS]: TrackService.changeEnrollmentStatus,
  [CHANGE_RELEVANCE]: TrackService.changeRelevance,
  [EXPORT_LEARNERS]: TrackService.exportLearnersStatus,
};

function* poll({ handler, params }: any) {
  while (true) {
    yield delay(POLLING_DELAY);
    yield call(handler, params);
  }
}

function getPollingSaga(actionType: string, handler: Function) {
  return function* () {
    const { SUCCESS, FAIL } = getLoadingActions(actionType);
    yield takeEvery(actionType, function* ({ payload }: any) {
      yield race([call(poll, { handler, params: payload }), take(SUCCESS), take(FAIL)]);
    });
  };
}

const { SUCCESS: POLLING_SUCCESS, FAIL: POLLING_FAIL } = getActions({
  name: TRACK_POLLING,
  options: { async: true },
});

function* checkPollingStatus({ processId, companyId, reqLearnerIds, ...params }: any) {
  var { status, successIds, errorIds, isNewUM } = yield call(
    OPERATIONS_SERVICE_MAP[GET_POLL_STATUS],
    {
      processId,
      companyId,
    }
  );
  switch (status) {
    case POLLING_STATUS.FAILED:
      if (isNewUM) {
        errorIds = reqLearnerIds;
      }
      yield put(POLLING_FAIL({ data: { errorIds, isNewUM } }));
      break;
    case POLLING_STATUS.SUCCESS:
      const { mixpanel } = yield getContext();
      if (isNewUM) {
        successIds = reqLearnerIds;
      }
      yield call(updateLearners, {
        ...params,
        companyId,
        learners: successIds ? successIds : [],
        reqLearnerIds,
      });
      yield put(
        POLLING_SUCCESS({
          data: { successIds, errorIds, response: { successIds }, mixpanel, isNewUM },
        })
      );
      break;
  }
}

function* resetLearnersStore() {
  yield put(
    getActions(TRACK_UPDATE_SEARCH_LEARNERS)({
      data: [],
      rows: DEFAULT_PAGINATION.rows,
      start: DEFAULT_PAGINATION,
    })
  );
}

function* getContext() {
  const {
    ilt: { context, mixpanel },
  } = yield select();
  return { ...context, mixpanel };
}

function* getStoreLearnersData() {
  const {
    ilt: {
      track: {
        searchedLearners: {
          data: learners = [],
          totalCount = 0,
          start = DEFAULT_PAGINATION.start,
          rows = DEFAULT_PAGINATION.rows,
        } = {},
        learnerDetails = {},
        learnersMap = {},
      } = {},
    },
  } = yield select();
  return {
    rows,
    start,
    learners,
    totalCount,
    learnerDetails,
    learnersMap,
  };
}

function calculateEventEnrolledSessionCount({
  learnerEntitiesInfo,
  entity,
  singleLearnerDetail,
}: {
  learnerEntitiesInfo: { [entityId: string]: { totalNumberOfPublishedSessions?: number } };
  entity: { id: string };
  singleLearnerDetail: {
    totalEnrolledSessionsInEvent?: number;
    totalNotEnrolledSessionsInEvent?: number;
  };
}) {
  const { totalNumberOfPublishedSessions } = learnerEntitiesInfo[entity.id] || {};

  return singleLearnerDetail.totalEnrolledSessionsInEvent
    ? singleLearnerDetail.totalEnrolledSessionsInEvent
    : totalNumberOfPublishedSessions &&
      singleLearnerDetail.totalNotEnrolledSessionsInEvent &&
      singleLearnerDetail.totalNotEnrolledSessionsInEvent > 0
    ? totalNumberOfPublishedSessions - singleLearnerDetail.totalNotEnrolledSessionsInEvent
    : 0;
}

// eslint-disable-next-line max-statements
function* updateLearners(params: any): any {
  let {
    learnerDetails = {},
    learners = [],
    operation,
    totalCount,
    pagination: { start, rows },
    reqLearnerIds,
    moduleRelevance,
  } = params;
  const {
    learners: oldLearners,
    totalCount: oldTotalCount,
    learnersMap: oldLearnersMap,
  } = yield call(getStoreLearnersData);
  const updatedLearners: any = {};
  switch (operation) {
    case GET_LEARNERS:
    case SEARCH_LEARNERS:
    case SORT_LEARNERS:
    case GET_UPDATED_LEARNERS:
      updatedLearners.data = learners;
      break;
    case PAGINATE_LEARNERS:
      updatedLearners.data = [...oldLearners, ...learners];
      break;
    case REMOVE_LEARNERS:
      updatedLearners.data = oldLearners.subtract(learners);
      totalCount = oldTotalCount - learners.length;
      break;
    case CHANGE_RELEVANCE:
      updatedLearners.data = oldLearners;
      totalCount = oldTotalCount;
      Object.keys(oldLearnersMap).forEach(userId => {
        learnerDetails[userId] = {
          ...oldLearnersMap[userId],
          moduleRelevance:
            reqLearnerIds.indexOf(userId) > -1
              ? moduleRelevance
              : oldLearnersMap[userId].moduleRelevance,
        };
      });
      break;
    case CHANGE_LEARNER_STATUS:
      updatedLearners.data = oldLearners;
      totalCount = oldTotalCount;

      yield put(getActions(TRACK_ARE_ENTITY_STATS_FETCHED)({ fetched: false }));
      yield put(getActions(TRACK_FETCH_STATS)());
  }

  updatedLearners.start = start;
  updatedLearners.rows = rows;
  updatedLearners.totalCount = totalCount;

  if (operation === CHANGE_LEARNER_STATUS) {
    const userIds = Object.keys(learnerDetails);
    for (let id = 0; id < userIds.length; id++) {
      const userId = userIds[id];
      if (oldLearnersMap[userId]) {
        learnerDetails[userId] = {
          ...oldLearnersMap[userId],
          enrolledCount: learnerDetails[userId].enrolledCount,
          attendedCount: learnerDetails[userId].attendedCount,
          waitlistedCount: learnerDetails[userId].waitlistedCount,
          enrolledEntityDetails: learnerDetails[userId].enrolledEntityDetails,
          enrolledEntityParentDetails: learnerDetails[userId].enrolledEntityParentDetails,
          totalNotEnrolledSessionsInEvent: learnerDetails[userId].totalNotEnrolledSessionsInEvent,
          totalEnrolledSessionsInEvent: learnerDetails[userId].totalEnrolledSessionsInEvent,
        };
      }
    }
    /* This code updates the parent enrollment status based on update response received in action performed on child
 Since for bulk, multiple learners to have same enrolled entity info, can be used based on 1st learner data
 This is for sessionWithinEvent for second drawer only as entities to not contain parentId stats in same list for 1st drawer */
    const {
      ilt: {
        track: {
          learnerEntities: { entities = [] as Array<TLearnerEntity> } = {} as LearnerEntitiesType,
          learnerEntitiesInfo = {},
        } = {} as object,
      },
    } = yield select();
    const singleLearnerDetail = learnerDetails[userIds[0]];
    let updatedLearnerEntities = entities.map((entity: TLearnerEntity) => {
      if (
        singleLearnerDetail.enrolledEntityParentDetails &&
        entity.id === singleLearnerDetail.enrolledEntityParentDetails.id
      ) {
        return {
          ...entity,
          enrollmentStatus:
            UPDATE_ENROLLMENT_STATUS_ACTION_MAP[
              singleLearnerDetail.enrolledEntityParentDetails
                .enrollmentStatus as keyof typeof UPDATE_ENROLLMENT_STATUS_ACTION_MAP
            ],
          attendanceStatus:
            UPDATE_ENROLLMENT_ATTENDANCE_STATUS_ACTION_MAP[
              singleLearnerDetail.enrolledEntityParentDetails
                .attendedStatus as keyof typeof UPDATE_ENROLLMENT_ATTENDANCE_STATUS_ACTION_MAP
            ],
          enrolledSessionsCount: calculateEventEnrolledSessionCount({
            learnerEntitiesInfo,
            entity,
            singleLearnerDetail,
          }),
        };
      }

      // This code will keep the entity redux state updated with latest enrollment status to handle side effects when moving from 2nd drawer to first
      // Also, updates the latest enrolled sessions inside event count for actions performed on event
      // NOTE: This does not update the entity redux state for 2nd drawer session within event
      if (
        singleLearnerDetail.enrolledEntityDetails &&
        entity.id === singleLearnerDetail.enrolledEntityDetails.id
      ) {
        return {
          ...entity,
          enrollmentStatus:
            UPDATE_ENROLLMENT_STATUS_ACTION_MAP[
              singleLearnerDetail.enrolledEntityDetails
                .enrollmentStatus as keyof typeof UPDATE_ENROLLMENT_STATUS_ACTION_MAP
            ],
          attendanceStatus:
            UPDATE_ENROLLMENT_ATTENDANCE_STATUS_ACTION_MAP[
              singleLearnerDetail.enrolledEntityDetails
                .attendedStatus as keyof typeof UPDATE_ENROLLMENT_ATTENDANCE_STATUS_ACTION_MAP
            ],
          ...(entity.entityType === ILT_ENTITIES.EVENT
            ? {
                enrolledSessionsCount: calculateEventEnrolledSessionCount({
                  learnerEntitiesInfo,
                  entity,
                  singleLearnerDetail,
                }),
              }
            : {}),
        };
      }
      return entity;
    });

    yield put(getActions(TRACK_UPDATE_LEARNER_ENTITIES)({ entities: updatedLearnerEntities }));
  } else {
    //TODO: Should be handled at api end
    Object.keys(learnerDetails).forEach(userId => {
      if (oldLearnersMap[userId] && oldLearnersMap[userId].moduleRelevance)
        learnerDetails[userId] = {
          ...learnerDetails[userId],
          moduleRelevance:
            learnerDetails[userId].moduleRelevance || oldLearnersMap[userId].moduleRelevance,
        };
    });

    if (operation === PAGINATE_LEARNERS) {
      learnerDetails = {
        ...oldLearnersMap,
        ...learnerDetails,
      };
    }
  }

  yield put(getActions(TRACK_UPDATE_LEARNERS_DETAILS)(learnerDetails));
  yield put(getActions(TRACK_UPDATE_SEARCH_LEARNERS)(updatedLearners));
}

function* getAsyncLearners(params: any): any {
  const { learnerDetails, learners, totalCount } = yield call(TrackService.getLearners, params);

  yield call(updateLearners, {
    ...params,
    learnerDetails,
    learners,
    totalCount,
  });
}

function* getSessionLocalTimeZone() {
  const {
    ilt: { track: { learnerEntitiesInfo = {} } = {} },
  } = yield select();

  try {
    const filteredLearnerEntitiesInfo = Object.keys(learnerEntitiesInfo).reduce(
      (acc: any, key: string) => {
        const entity = learnerEntitiesInfo[key];
        if (!(entity.localStartTime && entity.localEndTime)) {
          acc[key] = entity;
        }
        return acc;
      },
      {}
    );

    const { localTimeMap } = yield call(getLocalTimeZoneForSessions, {
      sessionsMap: filteredLearnerEntitiesInfo,
    });

    const {
      ilt: { track: { learnerEntitiesInfo: latestEntitiesInfo = {} } = {} },
    } = yield select();

    const entitiesInfoWithLocalTimeZone: {
      [key: string]: any;
    } = {};

    Object.keys(filteredLearnerEntitiesInfo).forEach(entityId => {
      if (latestEntitiesInfo[entityId] && localTimeMap[entityId]) {
        entitiesInfoWithLocalTimeZone[entityId] = {
          ...latestEntitiesInfo[entityId],
          ...(localTimeMap[entityId] || {}),
        };
      }
    });

    yield put(getActions(TRACK_UPDATE_LEARNER_ENTITIES_DATA)(entitiesInfoWithLocalTimeZone));
  } catch (error) {}
}

function* getData() {
  const { SUCCESS, FAIL } = yield call(getActions, {
    name: TRACK_GET_DATA,
    options: {
      async: true,
    },
  });
  try {
    const { moduleId, seriesId, companyId, mixpanel } = yield getContext();
    const params = {
      moduleId,
      seriesId,
      companyId,
      pagination: DEFAULT_PAGINATION,
      sort: DEFAULT_SORT,
      operation: GET_LEARNERS,
    };

    yield call(getAsyncLearners, params);
    yield put(SUCCESS({ mixpanel }));
    yield put(getActions(TRACK_FETCH_LOCAL_TIMEZONE_NAME)());
  } catch (error) {
    yield put(FAIL(error, { globalError: true }));
  }
}

function* removeLearners({ learners, ...rest }: any): any {
  if (learners && learners.length) {
    const processId = yield call(OPERATIONS_SERVICE_MAP[REMOVE_LEARNERS], {
      learners,
      ...rest,
    });
    yield put(getActions(TRACK_POLLING)({ processId, reqLearnerIds: learners, ...rest }));
  }
}

function* updateEnrollmentOperationStatus({ enrollEntityId, isUpdating, hasError, complete }: any) {
  const {
    ilt: {
      track: { enrollOperationStatus: oldStatus = {} },
    },
  } = yield select();
  const updatedStatus = {
    ...oldStatus,
    [enrollEntityId]: {
      isUpdating,
      hasError,
      complete,
    },
  };
  yield put(getActions(TRACK_UPDATE_ENROLL_OPERATION_STATUS)({ ...updatedStatus }));
}

function* changeLearnerStatus({ learners: data = [], entity, ...params }: any) {
  if (!data.length) return;
  const enrollEntityId = entity.sessionId || entity.eventId;

  yield call(updateEnrollmentOperationStatus, {
    enrollEntityId,
    isUpdating: true,
    hasError: false,
    complete: false,
  });

  try {
    const { learners, learnerDetails } = yield call(OPERATIONS_SERVICE_MAP[CHANGE_LEARNER_STATUS], {
      data,
      entity,
      ...params,
    });

    yield call(updateEnrollmentOperationStatus, {
      enrollEntityId,
      isUpdating: false,
      hasError: false,
      complete: true,
    });
    yield call(updateLearners, { ...params, learnerDetails, learners, data, entity });
  } catch (error) {
    yield call(updateEnrollmentOperationStatus, {
      enrollEntityId,
      isUpdating: false,
      hasError: true,
      complete: true,
    });
  }
}

function* exportLearners({ sessionId, email, ...rest }: any) {
  if (!email) return;
  yield call(OPERATIONS_SERVICE_MAP[EXPORT_LEARNERS], {
    sessionId,
    email,
    ...rest,
  });
}

function* changeRelevance({ learners: data, ...params }: any): any {
  if (data && data.length) {
    const processId = yield call(OPERATIONS_SERVICE_MAP[CHANGE_RELEVANCE], {
      data,
      ...params,
    });

    yield put(getActions(TRACK_POLLING)({ processId, reqLearnerIds: data, ...params }));
  }
}

function* manageTrackDetails({
  payload: {
    operation,
    filters = {},
    pagination = DEFAULT_PAGINATION,
    learners = [],
    entity = {},
    learnersMap,
    sessionId,
    sort = DEFAULT_SORT,
    moduleRelevance,
    enrollmentFreezeStatus,
    ...rest
  },
}: any): any {
  const { SUCCESS, FAIL } = yield call(getActions, {
    name: TRACK_MANIPULATE_DATA,
    options: {
      async: true,
    },
  });
  try {
    const { moduleId, seriesId, companyId, mixpanel } = yield call(getContext);
    const params = {
      pagination,
      moduleId,
      seriesId,
      companyId,
      learners,
      entity,
      learnersMap,
      filters,
      sessionId,
      filtered: !!filters.length,
      sort,
      operation,
      moduleRelevance,
      enrollmentFreezeStatus,
    };
    params.pagination =
      operation !== PAGINATE_LEARNERS ? params.pagination || DEFAULT_PAGINATION : params.pagination;
    switch (operation) {
      case GET_UPDATED_LEARNERS:
        yield call(getAsyncLearners, params);
        break;
      case SORT_LEARNERS:
        yield call(resetLearnersStore);
        yield call(getAsyncLearners, params);
        break;
      case GET_LEARNERS:
      case SEARCH_LEARNERS:
      case PAGINATE_LEARNERS:
        yield call(getAsyncLearners, params);
        break;
      case REMOVE_LEARNERS:
        yield call(removeLearners, params);
        break;
      case VIEW_LEARNER_PROFILE:
        yield call(reload, getUserProfileUrl(learners[0]));
        break;
      case CHANGE_LEARNER_STATUS:
        yield call(changeLearnerStatus, params);
        break;
      case CHANGE_RELEVANCE:
        yield call(changeRelevance, params);
        break;
      case EXPORT_LEARNERS:
        yield call(exportLearners, { ...params, ...rest });
        break;
    }
    yield put(SUCCESS({ data: { operation, mixpanel, postData: params } }));
  } catch (error) {
    yield put(FAIL(error, { globalError: false }));
  }
}

function* fetchLearnerEntityStats() {
  const {
    ilt: {
      track: {
        learnerEntitiesInfo = {},
        areEntityStatsFetched: { fetched: entityStatsFetched = false } = {},
      } = {},
    },
  } = yield select();

  if (entityStatsFetched) return;

  const { moduleId, seriesId, companyId } = yield getContext();

  const entitiesWithBasicInfo: any = Object.values(learnerEntitiesInfo);

  const { sessionIds, eventIds } = prepareStatsEntityDataRequest(entitiesWithBasicInfo);

  try {
    const { entityStatsMap } = yield call(TrackService.getEntityStats, {
      sessionIds,
      eventIds,
      moduleId,
      seriesId,
      companyId,
    });

    // To prevent during above api call if redux state is was updated, so to take latest changes from redux
    const {
      ilt: { track: { learnerEntitiesInfo: latestEntitiesInfoList = {} } = {} },
    } = yield select();

    const updatedEntitiesList: {
      [key: string]: TLearnerEntityDetails;
    } = {};
    Object.values(latestEntitiesInfoList).forEach((entity: any) => {
      updatedEntitiesList[entity.id] = {
        ...entity,
        ...(entityStatsMap[entity.id] || {}),
      };
    });

    yield put(getActions(TRACK_UPDATE_LEARNER_ENTITIES_DATA)({ ...updatedEntitiesList }));
    yield put(getActions(TRACK_ARE_ENTITY_STATS_FETCHED)({ fetched: true }));

    // eslint-disable-next-line no-empty
  } catch (error) {}
}

// eslint-disable-next-line max-statements
function* getLearnerSessionsAndEvents({ payload }: any): any {
  const { SUCCESS, FAIL } = yield call(getActions, {
    name: TRACK_GET_LEARNER_ENTITIES,
    options: {
      async: true,
    },
  });

  try {
    const context = yield getContext();

    const {
      ilt: {
        track: {
          learnerEntities: { rows: prevRows, start: prevStart } = {} as object,
        } = {} as object,
      },
    } = yield select();

    const params = {
      ...context,
      ...payload,
    };
    if (payload.operation === LOAD_MORE) {
      params.pagination = { rows: prevRows, start: prevStart };
    }
    const { entities, entitiesInfo, hasMore, rows, start } = yield call(
      TrackService.getLearnerSessionsAndEvents,
      params
    );

    const {
      ilt: {
        track: { learnerEntities: oldEntitiesData = {} },
      },
    } = yield select();

    let updatedSessionsData = { entities, hasMore, rows, start };
    if (payload.operation === LOAD_MORE) {
      updatedSessionsData = {
        ...updatedSessionsData,
        entities: uniqueMerge(oldEntitiesData.entities, entities),
      };
    }
    for (const entityId of Object.keys(entitiesInfo)) {
      const entityInfo = entitiesInfo[entityId];
      if (entityInfo.timezone) {
        continue;
      }
      entityInfo.timezone = {
        displayName: `(${getCurrentTimeZoneAbbreviated(entityInfo.startTime)})`,
      };
    }

    const parsedEntitiesInfo = parseLearnerEntities(entities, entitiesInfo);

    yield put(getActions(TRACK_UPDATE_LEARNER_ENTITIES)({ ...updatedSessionsData }));
    yield put(getActions(TRACK_UPDATE_LEARNER_ENTITIES_DATA)({ ...parsedEntitiesInfo }));
    yield put(getActions(TRACK_ARE_ENTITY_STATS_FETCHED)({ fetched: false }));
    yield put(getActions(TRACK_FETCH_STATS)());
    yield call(getSessionLocalTimeZone);
    yield put(SUCCESS({}));
  } catch (error) {
    yield put(FAIL({ error }));
  }
}

function* getLearnerSessionsWithinEvent({ payload }: any): any {
  const { SUCCESS, FAIL } = yield call(getActions, {
    name: TRACK_GET_LEARNER_SESSIONS_WITHIN_EVENT,
    options: {
      async: true,
    },
  });

  try {
    const context = yield getContext();

    const {
      ilt: {
        track: {
          learnerSessionsWithinEvent: { rows: prevRows, start: prevStart } = {} as object,
        } = {} as object,
      },
    } = yield select();

    const params = {
      ...context,
      ...payload,
    };
    if (payload.operation === LOAD_MORE) {
      params.pagination = { rows: prevRows, start: prevStart };
    }
    const {
      sessionsWithinEvent: sessions,
      sessionsWithinEventInfo,
      hasMore,
      rows,
      start,
    } = yield call(TrackService.getLearnerSessionsWithinEvent, params);

    const {
      ilt: {
        track: {
          learnerSessionsWithinEvent: oldSessionsWithinEvent = {},
          learnerEntitiesInfo: oldEntitiesInfo = {},
        },
      },
    } = yield select();

    let updatedSessionsData = { sessions, hasMore, rows, start };
    if (payload.operation === LOAD_MORE) {
      updatedSessionsData = {
        ...updatedSessionsData,
        sessions: uniqueMerge(oldSessionsWithinEvent.sessions, sessions),
      };
    }

    const parsedEntitiesInfo = parseLearnerEntities(
      sessions,
      sessionsWithinEventInfo,
      params.eventId
    );

    yield put(getActions(TRACK_UPDATE_LEARNER_SESSIONS_WITHIN_EVENT)({ ...updatedSessionsData }));
    yield put(
      getActions(TRACK_UPDATE_LEARNER_ENTITIES_DATA)({
        ...oldEntitiesInfo,
        ...parsedEntitiesInfo,
      })
    );
    yield put(getActions(TRACK_ARE_ENTITY_STATS_FETCHED)({ fetched: false }));
    yield put(getActions(TRACK_FETCH_STATS)());
    yield call(getSessionLocalTimeZone);
    yield put(SUCCESS({}));
  } catch (error) {
    yield put(FAIL({ error }));
  }
}

function* fetchLocalTimezoneForEvent() {
  try {
    const { timezone_records } = yield call(TimezonesService.searchTimezone, {
      epoch: Math.floor(Date.now() / 1000),
      timezoneId: moment.tz.guess(),
    });
    const timezoneDisplayName = timezone_records[0].display_name; // Only taking the display name, offset is calculated locally to save calls
    yield put(getActions(TRACK_UPDATE_LOCAL_TIMEZONE_NAME)(timezoneDisplayName));
  } catch (error) {
    // TODO: Handle error boundary
  }
}

function* clearEnrollmentDrawerData() {
  yield put(getActions(TRACK_UPDATE_ENROLL_OPERATION_STATUS)());
  yield put(getActions(TRACK_UPDATE_LEARNER_ENTITIES)());
  yield put(getActions(TRACK_UPDATE_LEARNER_ENTITIES_DATA)());
}

function* clearEventEnrollmentDrawerData() {
  yield put(getActions(TRACK_UPDATE_ENROLL_OPERATION_STATUS)());
  yield put(getActions(TRACK_UPDATE_LEARNER_SESSIONS_WITHIN_EVENT)());
}

function* handleLoad() {
  yield takeEvery(TRACK_GET_DATA, getData);
}
function* handleStatsFetch() {
  yield takeEvery(TRACK_FETCH_STATS, fetchLearnerEntityStats);
}
function* handleOperations() {
  yield takeLatest(
    (action: any) =>
      action.type === TRACK_MANIPULATE_DATA && action.payload.operation !== CHANGE_LEARNER_STATUS,
    manageTrackDetails
  );
  yield takeEvery(
    (action: any) =>
      action.type === TRACK_MANIPULATE_DATA && action.payload.operation === CHANGE_LEARNER_STATUS,
    manageTrackDetails
  );
}

function* handleGetLearnerEntities() {
  yield takeLatest(TRACK_GET_LEARNER_ENTITIES, getLearnerSessionsAndEvents);
}
function* handleGetLearnerSessionsWithinEvent() {
  yield takeLatest(TRACK_GET_LEARNER_SESSIONS_WITHIN_EVENT, getLearnerSessionsWithinEvent);
}
function* handleCloseEnrollmentDrawer() {
  yield takeLatest(TRACK_ENROLLMENT_DRAWER_CLOSED, clearEnrollmentDrawerData);
}
function* handleCloseEventEnrollmentDrawer() {
  yield takeLatest(TRACK_EVENT_ENROLLMENT_DRAWER_CLOSED, clearEventEnrollmentDrawerData);
}
function* handleFetchLocalTimezoneName() {
  yield takeLatest(TRACK_FETCH_LOCAL_TIMEZONE_NAME, fetchLocalTimezoneForEvent);
}

export default function* () {
  yield all([
    handleLoad(),
    handleOperations(),
    handleStatsFetch(),
    handleGetLearnerEntities(),
    handleFetchLocalTimezoneName(),
    handleGetLearnerSessionsWithinEvent(),
    handleCloseEnrollmentDrawer(),
    handleCloseEventEnrollmentDrawer(),
    getPollingSaga(TRACK_POLLING, checkPollingStatus)(),
  ]);
}
