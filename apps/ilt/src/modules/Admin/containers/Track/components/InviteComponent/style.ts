import styled from 'styled-components';

import mixins, { theme, tokens } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS } from '~/config/constants';

const Wrapper = styled.div<{ placement?: string }>`
  position: relative;
  float: right;
  user-select: none;
  .showcollapse {
    visibility: visible;
    opacity: 1;
    transform: scaleY(1);
    transform-origin: ${props => (props.placement === 'top' ? ` 0% 100%` : ` 100% 0%`)};
    transition: 0.3s transform cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
  .hidecollapse {
    transform: scaleY(0);
    transform-origin: ${props => (props.placement === 'top' ? ` 0% 100%` : ` 100% 0%`)};
    transition: 0.3s transform cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
  .collapse {
    position: absolute;
    overflow: hidden;
    z-index: 2;
    width: 240px;
    right: 0;
    text-align: left;
    position: absolute;
    box-shadow: ${theme.boxShadow.LEVEL2};
    ${props => (props.placement === 'top' ? `bottom:44px` : `top: 44px;`)};
    border: 1px solid ${tokens.borderTokens.COLOR_BORDER_DEFAULT};
  }
  .${THEME_PREFIX_CLS}-collapse {
    ul {
      border: none;
    }
    .${THEME_PREFIX_CLS}-collapse-item {
      .${THEME_PREFIX_CLS}-collapse-header {
        ${mixins.blackLink()};
        padding: 12px 24px;
      }
      :last-child {
        border-bottom: 0;
      }
      .${THEME_PREFIX_CLS}-collapse-content-box {
        padding: 0px 8px;
        .${THEME_PREFIX_CLS}-menu-item {
          ${mixins.greyLink()}
          padding: 0px 16px;
          border-radius: 4px;
          margin: 4px 0px;
          cursor: pointer;
          height: 35px;
          text-align: left;
          .linkIcon {
            padding: 8px 0px 7px;
            font-size: 18px;
            width: 24px;
            float: left;
          }
          .text {
            padding: 8px 0px 7px;
            margin-left: 14px;
            float: left;
          }
          :hover {
            color: ${tokens.textTokens.COLOR_TEXT_INVERSE};
            background-color: ${tokens.bgTokens.COLOR_BG_ACCENT};
          }
        }
      }
    }
  }
`;

export default Wrapper;
