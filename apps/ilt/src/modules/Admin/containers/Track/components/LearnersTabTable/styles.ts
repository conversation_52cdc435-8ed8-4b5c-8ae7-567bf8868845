import styled from 'styled-components';

import { tokens, mixins, theme } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS, ADMIN_NAVBAR_HEIGHT } from '~/config/constants';
import { flexContainer } from '~/modules/Admin/styles/mixins';

export const StyledLearnersTable = styled.div<{ loading: boolean }>`
  position: relative;
  .${THEME_PREFIX_CLS}-table {
    z-index: 0;
  }
  .${THEME_PREFIX_CLS}-table table {
    border-spacing: 0px !important;
  }

  .${THEME_PREFIX_CLS}-table-sticky-header {
    top: ${props => (props.loading ? 0 : ADMIN_NAVBAR_HEIGHT)}px !important;
    position: sticky !important;
    z-index: 1;
  }
  &
    .${THEME_PREFIX_CLS}-table-fixed-header
    .${THEME_PREFIX_CLS}-table-body,
    .${THEME_PREFIX_CLS}-table-sticky-header
    + .${THEME_PREFIX_CLS}-table-body {
    margin-top: -1px; // https://mindtickle.atlassian.net/browse/LA2-1501
  }

  .${THEME_PREFIX_CLS}-table-tbody > tr > td {
    border-bottom: 1px solid ${tokens.borderTokens.COLOR_BORDER_SECONDARY};
    border-radius: 0px !important;
  }

  .${THEME_PREFIX_CLS}-table-thead
    > tr
    > th:not(:last-child):not(.${THEME_PREFIX_CLS}-table-selection-column):not(
      .${THEME_PREFIX_CLS}-table-row-expand-icon-cell
    ):not([colspan])::before {
    content: unset;
  }

  // TODO: Look for a solution without vw unit
  .tableContainer {
    width: calc(100vw - 48px);
    padding-left: 6px;
    padding-right: 6px;
  }
  && .${THEME_PREFIX_CLS}-table-container::before, .${THEME_PREFIX_CLS}-table-container::after {
    box-shadow: none;
    width: 0;
  }

  th.${THEME_PREFIX_CLS}-table-cell {
    ${mixins.greyText()}
    line-height: 17px;
    font-size: 12px;
    padding-left: 10px !important;
    padding-right: 0;

    .${THEME_PREFIX_CLS}-table-column-sorters {
      padding-left: 0;
      justify-content: flex-start;
    }

    .${THEME_PREFIX_CLS}-table-column-sorter {
      visibility: visible !important;
      margin-left: 8px !important;
    }
  }

  .${THEME_PREFIX_CLS}-table-column-title {
    flex: unset !important;
    display: inline-block !important;
  }

  th.${THEME_PREFIX_CLS}-table-column-has-sorters:hover {
    background: ${tokens.bgTokens.COLOR_BG_DEFAULT};
  }

  td.${THEME_PREFIX_CLS}-table-cell {
    padding: 12px 0 12px 10px !important;
  }

  td.${THEME_PREFIX_CLS}-table-cell.${THEME_PREFIX_CLS}-table-selection-column {
    padding-left: 18px !important;
  }

  th.${THEME_PREFIX_CLS}-table-cell.${THEME_PREFIX_CLS}-table-selection-column {
    padding-left: 18px !important;
  }

  td.${THEME_PREFIX_CLS}-table-column-sort {
    background: ${tokens.bgTokens.COLOR_BG_DEFAULT};
  }

  .${THEME_PREFIX_CLS}-table-body {
    min-height: calc(100vh - 490px);
    padding-bottom: 56px;
  }

  .no-data-wrapper {
    padding: 200px 0;
    .no-data-icon {
      margin: 0 auto;
    }
    .no-result-text {
      text-align: center;
      padding: 1px 0 0 0;
      color: ${tokens.textTokens.COLOR_TEXT_DEFAULT};
      font-family: ${theme.fontFamily.DEFAULT};
      font-size: 14px;
      font-weight: 600;
      letter-spacing: 0;
      line-height: 20px;
      margin-top: 20px;
    }
  }
`;

export const TableCellItem = styled.div`
  ${flexContainer()}
  ${mixins.darkText()}
  text-align:left;
  line-height: 22px;

  .enrollment-stat {
    ${mixins.fontStack({
      color: tokens.textTokens.COLOR_TEXT_ACCENT,
      fontWeight: theme.fontWeight.SEMIBOLD,
      fontSize: theme.fontSizes.TEXT,
      lineHeight: theme.lineHeight.TEXT,
    })}

    cursor: pointer;
  }

  .score-cell {
    color: ${tokens.textTokens.COLOR_TEXT_DEFAULT};
  }
`;
