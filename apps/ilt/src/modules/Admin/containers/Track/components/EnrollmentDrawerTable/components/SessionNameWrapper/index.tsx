import { useEffect, useRef } from 'react';

import classnames from 'classnames';
import { useUserAuth } from 'ui_shell/Auth';

import EllipsisTooltip from '@mindtickle/ellipsis-tooltip';
import Icon from '@mindtickle/icon';

import {
  SESSION_STATUS_INFO,
  SESSION_TYPE_INFO,
  ENROLL_DRAWER_ATTENDANCE_STATUS,
  SESSION_STATUS_TYPES,
  ENROLL_DRAWER_ENROLLMENT_STATES,
} from '~/modules/Admin/config/track.constants';
import { checkIsEvent } from '~/modules/Admin/utils/checkEntityType';
import { secondsToHoursAndMinutes } from '~/utils';

import { SessionNameContainer } from './styles';

import type { SessionNameWrapperType } from '../../typeDefs';

export default function SessionNameWrapper({
  entityId,
  entityName,
  entityType,
  parentId,
  parentName,
  sessionType,
  sessionStatus,
  attendanceStatus,
  attendanceDuration,
  enrollmentStatus,
  multipleLearners,
  updateAttendanceMap,
  totalNumberOfPublishedSessions,
}: SessionNameWrapperType) {
  const {
    tempFeatureConfig: { isCaptureAutoAttendanceFeatureEnabled = false },
  } = useUserAuth();
  const prevEnrollmentStatus = useRef('');
  useEffect(() => {
    if (
      prevEnrollmentStatus.current === ENROLL_DRAWER_ENROLLMENT_STATES.ENROLLED &&
      prevEnrollmentStatus.current !== enrollmentStatus
    ) {
      updateAttendanceMap({ entityId, attendanceStatus: '' });
    }
    prevEnrollmentStatus.current = enrollmentStatus;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [enrollmentStatus]);

  const showAttendance =
    !multipleLearners &&
    sessionStatus !== SESSION_STATUS_TYPES.UPCOMING &&
    enrollmentStatus === ENROLL_DRAWER_ENROLLMENT_STATES.ENROLLED;

  const attendanceStatusText =
    attendanceStatus === ENROLL_DRAWER_ATTENDANCE_STATUS.ATTENDED.value
      ? ENROLL_DRAWER_ATTENDANCE_STATUS.ATTENDED.displayText
      : attendanceStatus === ENROLL_DRAWER_ATTENDANCE_STATUS.NOT_ATTENDED.value
      ? ENROLL_DRAWER_ATTENDANCE_STATUS.NOT_ATTENDED.displayText
      : ENROLL_DRAWER_ATTENDANCE_STATUS.UNMARKED_ATTENDANCE.displayText;

  const attendanceDurationText =
    isCaptureAutoAttendanceFeatureEnabled &&
    entityType !== 'EVENT' &&
    attendanceStatusText !== ENROLL_DRAWER_ATTENDANCE_STATUS.UNMARKED_ATTENDANCE.displayText &&
    attendanceDuration !== 0
      ? ` · ` + secondsToHoursAndMinutes(attendanceDuration)
      : '';

  const attendanceText = attendanceStatusText + attendanceDurationText;

  const renderEntityType = () => {
    if (parentId) {
      return (
        <>
          <span>Session in &apos;</span>
          <EllipsisTooltip
            wrapperClassName="session-inside-event-name-tooltip"
            placement="bottom"
            title={parentName}
            showTooltipWhenEllipsis={true}
          >
            {parentName}
          </EllipsisTooltip>
          <span>&apos;</span>
        </>
      );
    } else if (checkIsEvent(entityType)) {
      return <span>Event | {totalNumberOfPublishedSessions} sessions</span>;
    }
    return <span>Session</span>;
  };

  return (
    <SessionNameContainer>
      <EllipsisTooltip
        wrapperClassName="session-name-tooltip"
        placement="bottom"
        title={entityName}
        showTooltipWhenEllipsis={true}
      >
        {entityName}
      </EllipsisTooltip>

      <div className="entity-type">{renderEntityType()}</div>

      <div className="session-details">
        <div>{`${SESSION_TYPE_INFO[sessionType as keyof typeof SESSION_TYPE_INFO]?.displayText} | ${
          SESSION_STATUS_INFO[sessionStatus as keyof typeof SESSION_STATUS_INFO]?.displayText
        }`}</div>
        <div className="left-margin">{showAttendance && `|`}</div>
        <div
          className={classnames('left-margin', {
            attended: attendanceStatus === ENROLL_DRAWER_ATTENDANCE_STATUS.ATTENDED.value,
            'did-not-attend':
              attendanceStatus === ENROLL_DRAWER_ATTENDANCE_STATUS.NOT_ATTENDED.value,
            'unmarked-attendance':
              attendanceStatusText ===
              ENROLL_DRAWER_ATTENDANCE_STATUS.UNMARKED_ATTENDANCE.displayText,
          })}
        >
          {showAttendance ? `${attendanceText} ` : ''}
        </div>
        <div className="green-tick-style">
          {showAttendance &&
            attendanceStatus === ENROLL_DRAWER_ATTENDANCE_STATUS.ATTENDED.value && (
              <Icon type="tickThick" className="tickThick" />
            )}
        </div>
      </div>
    </SessionNameContainer>
  );
}
