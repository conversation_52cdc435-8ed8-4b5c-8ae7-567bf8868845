import { TRIGGER_TYPE } from '@mindtickle/actions-dropdown';
import Icon from '@mindtickle/icon';

import SendEmailsWrapper from '~/modules/Admin/components/SendEmailsWrapper';

import { MENU_ACTIONS } from './constants';
import { MoreActionBtn, StyledActionsDropdown, StyledEmailText } from './styles';

const MoreActionButton = () => (
  <MoreActionBtn className="moreActionBtn">
    <Icon type="more_horizontal2" />
  </MoreActionBtn>
);

export default function LearnerOperations({
  learnersMap,
  moduleRelevanceEnabled,
  learnerActions,
  learnerId,
}: any) {
  const menuConfig = {
    [MENU_ACTIONS.VIEW_PROFILE]: {
      key: MENU_ACTIONS.VIEW_PROFILE,
      content: 'View profile',
      triggerType: TRIGGER_TYPE.REDIRECT,
      url: `ui/user/${learnerId}`,
      disabled: false,
      hide: false,
      displayOrder: 1,
      track: () => learnerActions.trackViewProfileClick(learnerId),
    },
    [MENU_ACTIONS.SEND_EMAILS]: {
      key: MENU_ACTIONS.SEND_EMAILS,
      content: (
        <div>
          <StyledEmailText>
            <SendEmailsWrapper selectedLearners={[learnerId]} />
          </StyledEmailText>
        </div>
      ),
      triggerType: TRIGGER_TYPE.COMPONENT,
      render: () => null,
      disabled: false,
      hide: false,
      displayOrder: 2,
    },
    [MENU_ACTIONS.CHANGE_MODULE_RELEVANCE]: {
      key: MENU_ACTIONS.CHANGE_MODULE_RELEVANCE,
      content: 'Change module relevance',
      triggerType: TRIGGER_TYPE.FUNCTION,
      callFn: () => learnerActions.changeRelevance(learnerId),
      disabled: false,
      hide: !moduleRelevanceEnabled,
      displayOrder: 3,
    },
    [MENU_ACTIONS.REMOVE_FROM_MODULE]: {
      key: MENU_ACTIONS.REMOVE_FROM_MODULE,
      content: 'Remove from ILT',
      triggerType: TRIGGER_TYPE.FUNCTION,
      callFn: () => learnerActions.removeLearners(learnerId),
      disabled: false,
      hide: false,
      displayOrder: 4,
      cautious: true,
    },
  };

  return (
    <StyledActionsDropdown
      placement="bottomLeft"
      actionsConfig={menuConfig}
      TriggerComponent={<MoreActionButton />}
    />
  );
}
