export type EnrollmentDrawerFiltersType = {
  multipleLearners: boolean;
  filtersConfig: {
    ENROLLMENT_STATUS: {
      placeholder: string;
      onChange: Function;
      onClear: Function;
      options: object;
      value: any;
    };
    SESSION_STATE: {
      placeholder: string;
      onChange: Function;
      onClear: Function;
      options: object;
      value: string;
    };
    ATTENDED_STATUS: {
      placeholder: string;
      onChange: Function;
      onClear: Function;
      options: object;
      value: any;
    };
    SESSION_DATE: {
      placeholder: string;
      onChange: Function;
      onClear: Function;
      options: object;
      value: string;
      enableFutureDateSelection: boolean;
      defaultDateFormatter: Function;
    };
    SESSION_TYPE: {
      placeholder: string;
      onChange: Function;
      onClear: Function;
      options: object;
      value: string;
    };
    ENTITY_TYPE: {
      placeholder: string;
      onChange: Function;
      onClear: Function;
      options: object;
      value: string;
    };
    SEARCH: {
      placeholder: string;
      onSearch: Function;
      value: string;
    };
  };
  onResetFiltersClick: Function;
  defaultFilters: {
    [key: string]: Array<string>;
  };
  isEventEnrollDrawerActive: boolean;
};
