export const getManageEnrollTableColumns = ({
  isMultidayEnabled,
  includeRelevance = true,
}: {
  isMultidayEnabled: boolean;
  includeRelevance?: boolean;
}) => {
  let baseColumns = [
    {
      title: 'Learner',
      dataIndex: 'learnerDetails',
      width: includeRelevance ? '24%' : '25%',
      sorter: true,
      sortDirections: ['ascend', 'descend', 'ascend'],
      defaultSortOrder: 'ascend',
    },
    {
      title: 'Invited on',
      dataIndex: 'invitedOn',
      width: includeRelevance ? '9%' : '12%',
      sorter: true,
      sortDirections: ['ascend', 'descend', 'ascend'],
    },
    {
      title: 'Enrolled',
      dataIndex: 'enrolled',
      width: includeRelevance ? '6%' : '7%',
    },
    {
      title: 'Attended',
      dataIndex: 'attended',
      width: includeRelevance ? '6%' : '7%',
    },
    {
      title: 'Waitlisted',
      dataIndex: 'waitlisted',
      width: includeRelevance ? '6%' : '7%',
    },
    {
      title: !isMultidayEnabled
        ? 'Sessions enrolled and waitlisted in'
        : 'Events and sessions enrolled and waitlisted in',
      dataIndex: 'sessionEnrolledWaitlisted',
      width: includeRelevance ? '21%' : '24%',
    },
    {
      title: 'Score',
      dataIndex: 'score',
      width: includeRelevance ? '7%' : '4%',
      ...(!includeRelevance && { sorter: true, sortDirections: ['ascend', 'descend', 'ascend'] }),
    },
    {
      title: '',
      dataIndex: 'manageEnrollment',
      width: includeRelevance ? '8%' : '10%',
    },
    {
      title: '',
      dataIndex: 'learnerOperations',
      width: includeRelevance ? '5%' : '4%',
    },
  ];

  if (includeRelevance) {
    baseColumns.splice(1, 0, {
      title: 'Relevance',
      dataIndex: 'relevance',
      width: '8%',
    });
  }

  return baseColumns;
};

export const TABLE_SORT_KEYS = {
  learnerDetails: 'name',
  invitedOn: 'invitedOn',
  score: 'iltScore',
};
