import styled from 'styled-components';

import { tokens, mixins, theme } from '@mindtickle/styles/lib';

import { flexContainer } from '~/modules/Admin/styles/mixins';

const LearnersTabContainer = styled.div<{ areLearnersInvited: boolean }>`
  ${flexContainer({ flexDirection: 'column' })}
  position: relative;
  & * {
    box-sizing: border-box;
  }

  .filter-row {
    background-color: ${tokens.bgTokens.COLOR_BG_DEFAULT};
    ${flexContainer({
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    })}
  }

  .bottom-border {
    border-bottom: ${props => (props.areLearnersInvited ? 1 : 0)}px solid
      ${tokens.borderTokens.COLOR_BORDER_DEFAULT};
  }

  .page-header {
    padding: 17px 24px 0px 24px;
    .invite-count-text {
      display: flex;
      align-items: center;
      padding-bottom: 4px;
      .invite-count {
        margin-right: 8px;
        ${mixins.fontStack({
          color: tokens.textTokens.COLOR_TEXT_DEFAULT,
          fontWeight: 700,
          fontSize: theme.fontSizes.H2,
          lineHeight: theme.lineHeight.H2,
        })}
      }
      .invite-text {
        ${mixins.fontStack({
          color: tokens.textTokens.COLOR_TEXT_SECONDARY,
          fontWeight: theme.fontWeight.SEMIBOLD,
          fontSize: theme.fontSizes.TEXT,
          lineHeight: theme.lineHeight.TEXT,
        })}
      }
    }
    .header-title {
      .bold-text {
        ${mixins.fontStack({
          fontWeight: theme.fontWeight.SEMIBOLD,
          fontSize: theme.fontSizes.TEXT,
          lineHeight: theme.lineHeight.TEXT,
        })}
      }
      ${mixins.fontStack({
        color: tokens.textTokens.COLOR_TEXT_SECONDARY,
        fontWeight: theme.fontWeight.REGULAR,
        fontSize: theme.fontSizes.TEXT,
        lineHeight: theme.lineHeight.TEXT,
      })}
    }
  }

  .export-report {
    padding: 0px 20px;
    ${mixins.fontStack({
      color: tokens.textTokens.COLOR_TEXT_ACCENT,
      fontWeight: theme.fontWeight.SEMIBOLD,
      fontSize: theme.fontSizes.SMALLTEXT,
      lineHeight: theme.lineHeight.SMALLTEXT,
    })}

    cursor: pointer;
  }

  .analytics-invite-buttons-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .invite-learners {
      padding: 20px 20px 0px 20px;
    }
  }
`;

export default LearnersTabContainer;
