import styled from 'styled-components';

import { THEME_PREFIX_CLS } from '~/config/constants';
import { flexContainer } from '~/modules/Admin/styles/mixins';

export const FiltersContainer = styled.div`
  ${flexContainer({})};
  padding-bottom: 10px;
  .reset-button {
    font-size: 12px;
    padding-left: 14px;
  }

  .${THEME_PREFIX_CLS}-dropdown {
    width: 230px;
  }

  .${THEME_PREFIX_CLS}-input-affix-wrapper {
    padding: 4px 12px 4px 8px;
  }

  & > * {
    margin-right: 10px;
  }
`;
