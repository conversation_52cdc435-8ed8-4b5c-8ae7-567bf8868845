import styled from 'styled-components';

import Drawer from '@mindtickle/drawer';

import { THEME_PREFIX_CLS } from '~/config/constants';

const EnrollmentDrawerContainer = styled(Drawer)`
  .${THEME_PREFIX_CLS}-drawer-body {
    overflow: hidden;
  }
  .${THEME_PREFIX_CLS}-drawer-header-title {
    display: flex;
    flex-direction: row-reverse;
  }
  .${THEME_PREFIX_CLS}-drawer-content {
    border-radius: 0;
  }
`;
export default EnrollmentDrawerContainer;
