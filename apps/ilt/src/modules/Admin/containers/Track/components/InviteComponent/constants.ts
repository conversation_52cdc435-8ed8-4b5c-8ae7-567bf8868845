export const INVITE_TYPE = {
  ADD_INVITE_NEW: 'inviteNew',
  UPLOAD_LIST: 'inviteByUpload',
  INVITE_EXISTING: 'inviteExisting',
  INVITE_GROUP: 'inviteGroup',
};

export const INVITE_SHOW = [
  INVITE_TYPE.INVITE_EXISTING,
  INVITE_TYPE.UPLOAD_LIST,
  INVITE_TYPE.INVITE_GROUP,
];

export const INVITE_TO = {
  MODULE: 'module',
  SERIES: 'series',
};

export const MENU_ITEM = {
  INVITE_EXISTING: 'Individual learners',
  INVITE_GROUP: 'Learner groups',
  UPLOAD_LIST: 'Upload a list',
};

export const INVITE_PERMISSIONS = {
  INVITE_TYPE: {
    [INVITE_TYPE.ADD_INVITE_NEW]: {
      allow: ['LEARNER_MANAGEMENT'],
      deny: [],
    },
    [INVITE_TYPE.UPLOAD_LIST]: {
      allow: ['LEARNER_MANAGEMENT'],
      deny: [],
    },
    [INVITE_TYPE.INVITE_EXISTING]: {
      allow: [],
      deny: [],
    },
    [INVITE_TYPE.INVITE_GROUP]: {
      allow: ['CONTENT_SHARING_VIA_GROUP', 'GROUP_ADMINISTRATION'],
      deny: [],
    },
  },
  INVITE_TO: {
    [INVITE_TO.SERIES]: {
      allow: ['SERIES_MANAGEMENT'],
      deny: [],
    },
    [INVITE_TO.MODULE]: {
      allow: ['SERIES_MANAGEMENT', 'MODULE_SHARING'],
      deny: [],
    },
  },
};

export const ICON_TYPE = {
  [MENU_ITEM.INVITE_EXISTING]: 'InviteIndividualLearners',
  [MENU_ITEM.INVITE_GROUP]: 'InviteLearnerGroups',
  [MENU_ITEM.UPLOAD_LIST]: 'UploadNewList',
};

export const INVITE_TO_OPTIONS = (moduleType: string): Record<string, string> => ({
  [INVITE_TO.SERIES]: 'To series',
  [INVITE_TO.MODULE]: `To ${moduleType} only`,
});

export const INVITE_TYPE_DETAILS = {
  [INVITE_TYPE.ADD_INVITE_NEW]: {
    title: 'Add & invite new',
  },
  [INVITE_TYPE.UPLOAD_LIST]: {
    title: 'Upload a list',
  },
  [INVITE_TYPE.INVITE_EXISTING]: {
    title: 'Individual learners',
  },
  [INVITE_TYPE.INVITE_GROUP]: {
    title: 'Learner groups',
  },
};

export const CB_MESSAGE = {
  SUCCESS: 'SUCCESS',
  FAILURE: 'FAILURE',
  TRIGGER: 'TRIGGER',
};
