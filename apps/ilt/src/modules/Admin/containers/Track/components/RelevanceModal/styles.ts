import styled from 'styled-components';

import { tokens } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS } from '~/config/constants';
import { flexContainer } from '~/modules/Admin/styles/mixins';

export const radioStyle = {
  display: 'block',
  height: '30px',
  lineHeight: '30px',
};

export const ContentContainer = styled.div`
  ${flexContainer({ flexDirection: 'column' })}

  .${THEME_PREFIX_CLS}-radio-group > label > span {
    vertical-align: baseline;
  }

  && {
    .content-wrapper-style {
      padding: 24px 32px;
    }

    .footer {
      padding: 16px 32px;
      background-color: ${tokens.bgTokens.COLOR_BG_TERTIARY};
      text-align: right;
      border-radius: 0 0 4px 4px;
      border-top: 1px solid ${tokens.borderTokens.COLOR_BORDER_SECONDARY};
    }

    .cancel-button {
      margin-right: 16px;
    }
  }
`;
