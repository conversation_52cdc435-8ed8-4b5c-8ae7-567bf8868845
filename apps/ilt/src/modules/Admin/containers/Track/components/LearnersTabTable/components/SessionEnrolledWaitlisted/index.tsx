import React from 'react';

import EllipsisTooltip from '@mindtickle/ellipsis-tooltip';

import { DOUBLE_DASH } from '~/modules/Admin/constants';
import { ILT_ENTITIES } from '~/modules/Admin/constants/module';
import { HIDE_ENROLL_ATTEND_FILTERS_AS_COULD_NOT_ADD_BE_SUPPORT_IN_MULTIDAY as HIDE_ENROL_ATTEND_FILTERS } from '~/modules/Admin/containers/Track/constants';
import { sessionTimeFormatter } from '~/modules/Admin/utils';

import { SessionContainer, EmptySessionContainer } from './styles';

export default function SessionEnrolledWaitlisted({
  learnerDetails,
  toggleSessionsDrawer,
  localTimezoneName,
}: {
  learnerDetails: any;
  toggleSessionsDrawer: Function;
  localTimezoneName?: string;
}) {
  const session = learnerDetails.enrolledEntityDetails ? learnerDetails.enrolledEntityDetails : {};
  const totalEnrolledAndWaitlisted = learnerDetails.enrolledCount + learnerDetails.waitlistedCount;

  return (
    <React.Fragment>
      {session.name ? (
        <SessionContainer>
          <EllipsisTooltip
            wrapperClassName="session-name"
            placement="bottom"
            title={session.name}
            showTooltipWhenEllipsis={true}
          >
            {session.name}
          </EllipsisTooltip>
          <div className="session-date-time">{sessionTimeFormatter(session.startTime)}</div>
          <div className="session-date-time">
            {learnerDetails.entityType === ILT_ENTITIES.EVENT
              ? session.timezone + ' ' + (localTimezoneName || '')
              : session.timezone}
          </div>
          {totalEnrolledAndWaitlisted > 1 && (
            <span
              className="total-text"
              onClick={event => {
                event.stopPropagation();
                toggleSessionsDrawer({
                  show: true,
                  learnerId: learnerDetails.id,
                  defaultFilters: HIDE_ENROL_ATTEND_FILTERS
                    ? {}
                    : { ENROLLMENT: ['ENROLLED', 'WAITLISTED'] },
                });
              }}
            >
              {totalEnrolledAndWaitlisted > 1 ? `+ ${totalEnrolledAndWaitlisted - 1} more` : ``}
            </span>
          )}
        </SessionContainer>
      ) : (
        <EmptySessionContainer>
          <div>{DOUBLE_DASH}</div>
        </EmptySessionContainer>
      )}
    </React.Fragment>
  );
}
