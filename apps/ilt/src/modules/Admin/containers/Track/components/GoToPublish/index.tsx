import { Link } from 'react-router-dom';
import { LIFECYCLE_STAGES } from 'ui_shell/GlobalConstants';

import Button from '@mindtickle/button';
import IconWithGradient from '@mindtickle/icon-with-gradient';
import Loader from '@mindtickle/loader';

import ModuleRoutes from '~/modules/Admin/config/routes';

import { EmptyStateContainer } from './styles';

const GoToPublish = ({
  isPublished,
  areLearnersInvited,
  invitedLearnersCountLoaded,
}: {
  isPublished: boolean;
  areLearnersInvited: boolean;
  invitedLearnersCountLoaded: boolean;
}) => {
  const publishRoute = `../../../${ModuleRoutes.lifecycle[LIFECYCLE_STAGES.PUBLISH]}`;
  return invitedLearnersCountLoaded ? (
    <EmptyStateContainer>
      <IconWithGradient type="emptyState" />
      <div className="message-wrapper">
        {areLearnersInvited && <div className="no-learners-text">{'No learners added yet'}</div>}
        <div className="go-to-publish-text">
          {isPublished
            ? 'You can invite learners and track their progress here.'
            : 'Once you publish this ILT, you can invite learners and track their progress here.'}
        </div>
      </div>
      {!isPublished && (
        <Link to={publishRoute} className="">
          <Button type="primary">{'Go to publish'}</Button>
        </Link>
      )}
    </EmptyStateContainer>
  ) : (
    <Loader size="sizeSmall" />
  );
};
export default GoToPublish;
