import { ILT_ENTITIES } from '~/modules/Admin/constants/module';
import { sessionTimeFormatter } from '~/modules/Admin/utils';

import { SessionDateTimeContainer } from './styles';

import type { SessionDateTimeWrapperType } from '../../typeDefs';

export default function SessionDateTimeWrapper({
  startTimeEpoch,
  sessionTimezone,
  localStartTime,
  localTimezoneName,
  entityType,
}: SessionDateTimeWrapperType) {
  let timeAndDateString = sessionTimeFormatter(localStartTime || startTimeEpoch);

  return (
    <SessionDateTimeContainer className="session-date-time-wrapper">
      <div className="date-time-text">{timeAndDateString}</div>
      <div className="timezone-text">
        {entityType === ILT_ENTITIES.EVENT
          ? sessionTimezone + ' ' + (localTimezoneName || '')
          : sessionTimezone}
      </div>
    </SessionDateTimeContainer>
  );
}
