import { FormattedMessage } from 'react-intl';

import EllipsisTooltip from '@mindtickle/ellipsis-tooltip';
import Icon, { ICON_MAP } from '@mindtickle/icon';
import { ActionPill } from '@mindtickle/pill';
import Switch from '@mindtickle/switch';
import Tooltip from '@mindtickle/tooltip';

import useMultidayEnabled from '~/modules/Admin/hooks/useMultidayEnabled';
import ENROLL_DRAWER_MESSAGES from '~/modules/Admin/messages/track/enrollDrawer';

import { TOOLTIP_Z_INDEX } from './constants';
import { EnrollmentDrawerTitleContainer } from './styles';

import type { EnrollmentDrawerTitleType, TMappedSeries } from './typeDefs';

const MultipresentInfo = ({ mappedSeries }: TMappedSeries) => (
  <div className="multipresent-container">
    <div className="multipresent-text">{`This ILT is multipresent in ${
      mappedSeries.length - 1
    } other series`}</div>
    <Tooltip
      key={`multipresent`}
      overlayStyle={TOOLTIP_Z_INDEX}
      title={<FormattedMessage {...ENROLL_DRAWER_MESSAGES.MULTIPRESENT_INFO_TOOLTIP} />}
      placement={'top'}
      trigger={'hover'}
    >
      <Icon className="notify-icon" type={ICON_MAP.INFO2}></Icon>
    </Tooltip>
  </div>
);

const EnrollmentDrawerTitle = ({
  learnerName,
  toggleModifyMode,
  drawerModifyEnroll,
  multipleLearners,
  notifyLearners,
  flipNotifyToggle,
  learnerIds,
  isCalendarAutoSyncEnabled,
  mappedSeries,
}: EnrollmentDrawerTitleType) => {
  const isMultiDayEnabled = useMultidayEnabled();
  const showMappedSeries = mappedSeries.length > 1;

  return drawerModifyEnroll ? (
    <EnrollmentDrawerTitleContainer>
      <div className="learner-name">
        <span>{'Manage enrollment for'}&nbsp;</span>
        {multipleLearners ? (
          <span>{learnerIds.length + ' learners'}</span>
        ) : (
          <EllipsisTooltip placement="bottom" title={learnerName} showTooltipWhenEllipsis={true}>
            {learnerName}
          </EllipsisTooltip>
        )}
      </div>
      {showMappedSeries && <MultipresentInfo mappedSeries={mappedSeries} />}
      <div className="toggle-container">
        <Switch checked={notifyLearners} onChange={() => flipNotifyToggle()} />
        <div className="notify-text">{`Notify learners via email`}</div>
        <Tooltip
          key={`notifyLearnersTooltip`}
          overlayStyle={TOOLTIP_Z_INDEX}
          title={<FormattedMessage {...ENROLL_DRAWER_MESSAGES.NOTIFY_LEARNERS_TOOLTIP} />}
          placement={'top'}
          trigger={'hover'}
        >
          <Icon className="notify-icon" type={ICON_MAP.INFO2}></Icon>
        </Tooltip>
      </div>
      {!notifyLearners && isCalendarAutoSyncEnabled && (
        <div className="info-container">
          <span className="info-container-text">
            <FormattedMessage {...ENROLL_DRAWER_MESSAGES.LEARNER_CALENDAR_AUTO_SYNC_INFO} />
          </span>
        </div>
      )}
    </EnrollmentDrawerTitleContainer>
  ) : (
    <EnrollmentDrawerTitleContainer>
      <div className="learner-name">
        <EllipsisTooltip placement="bottom" title={learnerName} showTooltipWhenEllipsis={true}>
          {learnerName}
        </EllipsisTooltip>
        <span>{`'s${isMultiDayEnabled ? ' events and' : ''} sessions`}</span>
        <ActionPill className="title-button" onClick={() => toggleModifyMode(true)}>
          {`Modify enrollment status`}
        </ActionPill>
      </div>
      {showMappedSeries && <MultipresentInfo mappedSeries={mappedSeries} />}
    </EnrollmentDrawerTitleContainer>
  );
};
export default EnrollmentDrawerTitle;
