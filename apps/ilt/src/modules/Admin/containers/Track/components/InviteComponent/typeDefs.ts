import React from 'react';

type ModuleType = string;

export type InviteOptionType = {
  moduleType: string;
  globalPermissions: any;
  isSiteOwner: any;
};

export type InviteOptions = {
  title: string;
  value: string;
};

export type GetInviteOptionsReturnType = {
  children: Array<InviteOptions>;
} & InviteOptions;

export type OptionsType = {
  header: string;
  value: string;
  content?: React.ReactNode;
};

export type CallbackProps = {
  onComplete: Function;
};

export type InviteLearnersProps = {
  moduleType: ModuleType;
  placement?: string;
  onComplete: Function;
  moduleId: string;
  seriesId: string;
  defaultModuleRelevance?: string;
  moduleRelevanceEnabled: boolean;
  moduleName?: string;
};

export type InviteModalProps = {
  moduleId: string;
  seriesId: string;
  moduleType: ModuleType;
  moduleName?: string;
  callback: Function;
  companyId: string;
  moduleRelevanceEnabled: boolean;
  isSeries: boolean;
  addNewLearnerAllowed: boolean;
  defaultModuleRelevance?: string;
  setExistingVisible: (visible: boolean) => void;
  showInviteExisting: boolean;
  uploadVisible: boolean;
  setUploadVisible: (visible: boolean) => void;
  groupVisible: boolean;
  setGroupVisible: (visible: boolean) => void;
};
