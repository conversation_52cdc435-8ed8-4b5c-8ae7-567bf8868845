import { injectIntl } from 'react-intl';

import { TRIGGER_TYPE } from '@mindtickle/actions-dropdown';
import Icon from '@mindtickle/icon';

import messages from '~/modules/Admin/messages/track/eventEnrollDrawer';

import { TRACK_EVENT_OPERATIONS } from './constants';
import { StyledActionsDropdown, MoreActionBtn } from './styles';

import type { TEventOperationsWrapper } from '../../typeDefs';
import type { InjectedIntlProps } from 'react-intl';

const EventOperationsWrapper = (props: TEventOperationsWrapper & InjectedIntlProps) => {
  const { actions, eventId, intl } = props;
  const menuConfig = {
    [TRACK_EVENT_OPERATIONS.EDIT_ENROLL_STATUS]: {
      key: TRACK_EVENT_OPERATIONS.EDIT_ENROLL_STATUS,
      content: intl.formatMessage(messages.EDIT_EVENT_ENROLLMENT),
      triggerType: TRIGGER_TYPE.FUNCTION,
      callFn: () => actions.editEventEnrollment({ id: eventId }),
      displayOrder: 1,
    },
  };
  return (
    <StyledActionsDropdown
      placement="bottomLeft"
      actionsConfig={menuConfig}
      TriggerComponent={
        <MoreActionBtn className="more-action-btn">
          <Icon type="more_horizontal2" />
        </MoreActionBtn>
      }
    />
  );
};

export default injectIntl(EventOperationsWrapper);
