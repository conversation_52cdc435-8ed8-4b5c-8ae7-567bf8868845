import styled from 'styled-components';

import { tokens, mixins, theme } from '@mindtickle/styles/lib';

export const SuccessContainer = styled.div`
  ${mixins.fontStack({
    color: tokens.textTokens.COLOR_TEXT_SECONDARY,
    fontWeight: theme.fontWeight.REGULAR,
    fontSize: theme.fontSizes.SMALLTEXT,
    lineHeight: theme.lineHeight.SMALLTEXT,
  })}
  float: left;
  .feedback-not-enrolled {
    margin-left: 15px;
  }
  .feedback-enrolled {
    margin-left: 90px;
  }
  .feedback-waitlisted {
    margin-left: 178px;
  }
`;

export const FailureContainer = styled.div`
  ${mixins.fontStack({
    color: tokens.textTokens.COLOR_TEXT_DANGER,
    fontWeight: theme.fontWeight.REGULAR,
    fontSize: theme.fontSizes.SMALLTEXT,
    lineHeight: theme.lineHeight.SMALLTEXT,
  })}
  float: left;
  .feedback-not-enrolled {
    margin-left: 28px;
  }
  .feedback-enrolled {
    margin-left: 111px;
  }
  .feedback-enrolled-extra {
    margin-left: 142px;
  }
  .feedback-waitlisted {
    margin-left: 206px;
  }
  .feedback-waitlisted-extra {
    margin-left: 224px;
  }
`;
