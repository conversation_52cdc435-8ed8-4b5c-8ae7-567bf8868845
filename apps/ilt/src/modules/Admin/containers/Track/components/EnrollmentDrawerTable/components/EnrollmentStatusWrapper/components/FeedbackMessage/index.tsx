import classnames from 'classnames';

import {
  DRAWER_ENROLL_STATUS_ACTIONS,
  DRAWER_ENROLL_STATUS_ACTION_MAP,
  EMAIL_SCHEDULED_TEXT,
  FAILED_TEXT,
} from '~/modules/Admin/config/track.constants';

import { SuccessContainer, FailureContainer } from './styles';

import type { TFeedbackMessage } from './typeDefs';

const RenderFeedbackMessage = ({
  enrollmentType,
  checkOperationFailed,
  checkOperationSucceeded,
  activeButtonObj,
  enrollmentStatus,
}: TFeedbackMessage) => {
  const isOperationSucceeded = checkOperationSucceeded({ enrollmentType });
  const isOperationFailed = checkOperationFailed({ enrollmentType });
  const showFeedback = activeButtonObj.showFeedback;

  const res =
    isOperationSucceeded && showFeedback ? (
      <SuccessContainer>
        <div
          className={classnames({
            'feedback-not-enrolled': enrollmentType === DRAWER_ENROLL_STATUS_ACTIONS.UN_ENROLL,
            'feedback-enrolled': enrollmentType === DRAWER_ENROLL_STATUS_ACTIONS.ENROLL,
            'feedback-waitlisted': enrollmentType === DRAWER_ENROLL_STATUS_ACTIONS.MOVE_TO_WAITING,
          })}
        >
          {EMAIL_SCHEDULED_TEXT}
        </div>
      </SuccessContainer>
    ) : isOperationFailed ? (
      <FailureContainer>
        <div
          className={classnames({
            'feedback-not-enrolled': enrollmentType === DRAWER_ENROLL_STATUS_ACTIONS.UN_ENROLL,
            'feedback-enrolled': enrollmentType === DRAWER_ENROLL_STATUS_ACTIONS.ENROLL,
            'feedback-enrolled-extra':
              enrollmentType === DRAWER_ENROLL_STATUS_ACTIONS.ENROLL &&
              DRAWER_ENROLL_STATUS_ACTION_MAP[
                enrollmentStatus as keyof typeof DRAWER_ENROLL_STATUS_ACTION_MAP
              ] === DRAWER_ENROLL_STATUS_ACTIONS.UN_ENROLL,
            'feedback-waitlisted': enrollmentType === DRAWER_ENROLL_STATUS_ACTIONS.MOVE_TO_WAITING,
            'feedback-waitlisted-extra':
              enrollmentType === DRAWER_ENROLL_STATUS_ACTIONS.MOVE_TO_WAITING &&
              DRAWER_ENROLL_STATUS_ACTION_MAP[
                enrollmentStatus as keyof typeof DRAWER_ENROLL_STATUS_ACTION_MAP
              ] === DRAWER_ENROLL_STATUS_ACTIONS.UN_ENROLL,
          })}
        >
          {FAILED_TEXT}
        </div>
      </FailureContainer>
    ) : (
      <SuccessContainer></SuccessContainer>
    );

  return res;
};

export default RenderFeedbackMessage;
