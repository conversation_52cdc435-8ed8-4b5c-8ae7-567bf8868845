import styled from 'styled-components';

import Table from '@mindtickle/table';

import { THEME_PREFIX_CLS } from '~/config/constants';

export const CustomizedTable = styled(Table)`
  .${THEME_PREFIX_CLS}-table-column-title {
    flex: unset !important;
    display: inline-block !important;
  }
  && .${THEME_PREFIX_CLS}-table-tbody > tr > td:first-child.name-column,
  .name-column > .${THEME_PREFIX_CLS}-table-column-sorters {
    padding-left: 12px; // https://mindtickle.atlassian.net/browse/LA2-1500 oral discussion me 12px karne kaha hai to avoid hover issue
  }

  th.${THEME_PREFIX_CLS}-table-cell {
    .${THEME_PREFIX_CLS}-table-column-sorters {
      justify-content: flex-start;
    }
  }

  .${THEME_PREFIX_CLS}-table-tbody > tr > td:nth-child(3) {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  .${THEME_PREFIX_CLS}-table-thead > tr > th {
    border-top: 0;
  }
  .${THEME_PREFIX_CLS}-table-body {
    min-height: 500px;
  }
  .${THEME_PREFIX_CLS}-table-placeholder {
    margin-top: -500px;
  }
  .no-data-wrapper {
    padding: 200px 0;
    .no-data-icon {
      margin: 0 auto;
    }
  }
  .learner-entities-table-row .more-action-btn,
  .learner-entities-table-row .${THEME_PREFIX_CLS}-dropdown-menu {
    display: none;
  }

  .learner-entities-table-row:hover .more-action-btn,
  .learner-entities-table-row:hover .${THEME_PREFIX_CLS}-dropdown-menu {
    display: block;
  }
`;
