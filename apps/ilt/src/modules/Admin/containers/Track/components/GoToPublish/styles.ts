import styled from 'styled-components';

import { tokens, mixins, theme } from '@mindtickle/styles/lib';

export const EmptyStateContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30px;
  padding: 100px 0px;

  .message-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    .no-learners-text {
      ${mixins.fontStack({
        color: tokens.textTokens.COLOR_TEXT_DEFAULT,
        fontWeight: theme.fontWeight.SEMIBOLD,
        fontSize: theme.fontSizes.TEXT,
        lineHeight: theme.lineHeight.TEXT,
      })}
    }
    .go-to-publish-text {
      ${mixins.fontStack({
        color: tokens.textTokens.COLOR_TEXT_SECONDARY,
        fontWeight: theme.fontWeight.REGULAR,
        fontSize: theme.fontSizes.TEXT,
        lineHeight: theme.lineHeight.TEXT,
      })}
    }
  }
`;
