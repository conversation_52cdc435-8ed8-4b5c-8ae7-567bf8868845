import ActionBar from '@mindtickle/action-bar';
import { ActionPill, DestructivePill } from '@mindtickle/pill';

import SendEmailsWrapper from '~/modules/Admin/components/SendEmailsWrapper';
import { MANAGE_ENROLLMENT_TEXT } from '~/modules/Admin/config/track.constants';

import { TTrackActionBar } from './typeDefs';

export default function TrackActionBar({
  learnersMap,
  learnerActions,
  moduleRelevanceEnabled,
  toggleSessionsDrawer,
  selectedLearners,
  onCloseActionBar,
}: TTrackActionBar) {
  return (
    <ActionBar
      countText={`${selectedLearners.length} learners selected`}
      onCloseHandler={onCloseActionBar}
    >
      <ActionPill
        onClick={() =>
          toggleSessionsDrawer({
            show: true,
            modify: true,
            learnerIds: selectedLearners,
            defaultFilters: { SESSION_STATE: 'UPCOMING' },
            fromActionBar: true,
          })
        }
      >
        {MANAGE_ENROLLMENT_TEXT}
      </ActionPill>
      <ActionPill>
        <SendEmailsWrapper selectedLearners={selectedLearners} />
      </ActionPill>
      {moduleRelevanceEnabled && (
        <ActionPill onClick={() => learnerActions.changeRelevance(selectedLearners)}>
          {'Change relevance'}
        </ActionPill>
      )}
      <DestructivePill onClick={() => learnerActions.removeLearners(selectedLearners)}>
        {'Remove from ILT'}
      </DestructivePill>
    </ActionBar>
  );
}
