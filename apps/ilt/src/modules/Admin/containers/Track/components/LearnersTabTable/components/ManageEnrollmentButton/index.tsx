import Pill from '@mindtickle/pill';

export default function ManageEnrollmentButton({
  toggleSessionsDrawer,
  learnerId,
}: {
  toggleSessionsDrawer: Function;
  learnerId: string;
}) {
  return (
    <Pill
      onClick={(event: any) => {
        event.stopPropagation();
        toggleSessionsDrawer({
          show: true,
          modify: true,
          learnerId,
          defaultFilters: { SESSION_STATE: 'UPCOMING' },
        });
      }}
    >
      Manage
    </Pill>
  );
}
