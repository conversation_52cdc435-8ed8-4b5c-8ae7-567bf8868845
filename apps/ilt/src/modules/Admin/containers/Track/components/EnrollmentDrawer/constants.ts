export const ENROLL_DRAWER_ENROLLMENT_FILTER_OPTIONS = [
  { value: 'NOT_ENROLLED', label: 'Not enrolled' },
  { value: 'ENROLLED', label: 'Enrolled' },
  { value: 'WAITLISTED', label: 'Waitlisted' },
];

export const ENROLL_DRAWER_ATTENDANCE_FILTER_OPTIONS = [
  { value: 'NOT_ATTENDED', label: 'Did not attend' },
  { value: 'ATTENDED', label: 'Attended' },
  { value: 'ATTENDANCE_NOT_MARKED', label: 'Unmarked' },
];

export const ENROLL_DRAWER_SESSION_STATE_FILTER_OPTIONS = [
  { value: 'UPCOMING', label: 'Upcoming' },
  { value: 'LIVE', label: 'Live' },
  { value: 'PAST', label: 'Past' },
];

export const ENROLL_DRAWER_SESSION_TYPE_FILTER_OPTIONS = (isEventEnrollDrawerActive: boolean) => [
  { value: 'CLASSROOM', label: 'Classroom' },
  { value: 'WEBINAR', label: 'Video conferencing' },
  ...(!isEventEnrollDrawerActive ? [{ value: 'HYBRID', label: 'Hybrid' }] : []),
];

export const ENROLL_DRAWER_ENTITY_TYPE_FILTER_OPTIONS = [
  { value: 'EVENT', label: 'Event' },
  { value: 'INDEPENDENT_SESSION', label: 'Independent session' },
  { value: 'SESSION_WITHIN_EVENT', label: 'Session within event' },
];

export const FILTER_TITLES = {
  ENROLLMENT_STATUS: 'Enrollment',
  ATTENDANCE_STATUS: 'Attendance',
  SESSION_STATE: 'Time',
  ILT_DATE_RANGE_DD: 'Date',
  SESSION_TYPE: 'Format',
  SEARCH: 'Search',
  ENTITY_TYPE: 'Type',
} as const;

export const ENROLL_DRAWER_TYPES = {
  ENTITY: 'entity',
  EVENT: 'event',
};
