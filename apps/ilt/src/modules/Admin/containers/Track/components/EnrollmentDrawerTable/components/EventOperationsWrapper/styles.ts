import styled from 'styled-components';

import ActionsDropdown from '@mindtickle/actions-dropdown';
import Button from '@mindtickle/button';
import { tokens } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS } from '~/config/constants';

export const MoreActionBtn = styled(Button)`
  &&.more-action-btn {
    border-radius: 16px;
    width: 40px;
    height: 24px;
    padding: 0;
    border: 1px solid ${tokens.borderTokens.COLOR_BORDER_DEFAULT};
    background-color: ${tokens.bgTokens.COLOR_BG_DEFAULT};
    box-shadow: none;

    &:hover {
      border: 1px solid ${tokens.borderTokens.COLOR_BORDER_DEFAULT};
      background-color: ${tokens.bgTokens.COLOR_BG_DEFAULT};
    }

    &:focus {
      border: 1px solid ${tokens.borderTokens.COLOR_BORDER_ACCENT};
      background-color: ${tokens.bgTokens.COLOR_BG_DEFAULT};
      color: ${tokens.textTokens.COLOR_TEXT_ACCENT};

      .more-action-icon {
        color: ${tokens.iconTokens.COLOR_ICON_ACCENT};
      }
    }

    .more-action-icon {
      font-size: 14px;
      line-height: 24px;
      color: ${tokens.iconTokens.COLOR_ICON_DEFAULT};
      vertical-align: middle;
    }
  }
`;

export const StyledActionsDropdown = styled(ActionsDropdown)`
  .${THEME_PREFIX_CLS}-dropdown {
    width: 250px;
  }
`;
