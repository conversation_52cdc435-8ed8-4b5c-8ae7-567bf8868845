import { FormattedMessage } from 'react-intl';

import Button from '@mindtickle/button';
import DateFilter from '@mindtickle/date-filter';
import Search from '@mindtickle/search';
import SelectWithSearch from '@mindtickle/select-with-search';
import Tooltip from '@mindtickle/tooltip';

import { HIDE_ENROLL_ATTEND_FILTERS_AS_COULD_NOT_ADD_BE_SUPPORT_IN_MULTIDAY as HIDE_ENROL_ATTEND_FILTERS } from '~/modules/Admin/containers/Track/constants';
import useMultidayEnabled from '~/modules/Admin/hooks/useMultidayEnabled';
import ENROLL_DRAWER_MESSAGES from '~/modules/Admin/messages/track/enrollDrawer';

import { FiltersContainer } from './styles';

import type { EnrollmentDrawerFiltersType } from './typeDefs';

export default function EnrollmentDrawerFilters({
  multipleLearners,
  filtersConfig,
  onResetFiltersClick,
  defaultFilters,
  isEventEnrollDrawerActive,
}: EnrollmentDrawerFiltersType) {
  const { isMultidayEnabled } = useMultidayEnabled();
  return (
    <FiltersContainer>
      {!isEventEnrollDrawerActive && (
        <SelectWithSearch
          buttonLabel={filtersConfig.ENTITY_TYPE.placeholder}
          onChange={filtersConfig.ENTITY_TYPE.onChange}
          isMulti={false}
          showSearch={false}
          noneLabel={'All'}
          placeholder={filtersConfig.ENTITY_TYPE.placeholder}
          isButton={true}
          options={filtersConfig.ENTITY_TYPE.options}
          value={filtersConfig.ENTITY_TYPE.value}
        />
      )}

      {!HIDE_ENROL_ATTEND_FILTERS && !multipleLearners && (
        <SelectWithSearch
          buttonLabel={filtersConfig.ENROLLMENT_STATUS.placeholder}
          onChange={filtersConfig.ENROLLMENT_STATUS.onChange}
          isMulti={true}
          showSearch={false}
          isButton={true}
          options={filtersConfig.ENROLLMENT_STATUS.options}
          defaultValue={defaultFilters['ENROLLMENT']}
          onClear={filtersConfig.ENROLLMENT_STATUS.onClear}
          value={filtersConfig.ENROLLMENT_STATUS.value}
        />
      )}
      {!HIDE_ENROL_ATTEND_FILTERS && !multipleLearners && (
        <SelectWithSearch
          buttonLabel={filtersConfig.ATTENDED_STATUS.placeholder}
          onChange={filtersConfig.ATTENDED_STATUS.onChange}
          isMulti={true}
          showSearch={false}
          isButton={true}
          options={filtersConfig.ATTENDED_STATUS.options}
          defaultValue={defaultFilters['ATTENDANCE']}
          onClear={filtersConfig.ATTENDED_STATUS.onClear}
          value={filtersConfig.ATTENDED_STATUS.value}
        />
      )}
      <SelectWithSearch
        buttonLabel={filtersConfig.SESSION_STATE.placeholder}
        onChange={filtersConfig.SESSION_STATE.onChange}
        isMulti={false}
        showSearch={false}
        noneLabel={'All'}
        isButton={true}
        options={filtersConfig.SESSION_STATE.options}
        value={filtersConfig.SESSION_STATE.value}
        defaultValue={defaultFilters['SESSION_STATE']}
      />
      <DateFilter
        placeholder={filtersConfig.SESSION_DATE.placeholder}
        onChange={filtersConfig.SESSION_DATE.onChange}
        options={filtersConfig.SESSION_DATE.options}
        value={filtersConfig.SESSION_DATE.value}
        enableFutureDateSelection={filtersConfig.SESSION_DATE.enableFutureDateSelection}
      />
      <SelectWithSearch
        buttonLabel={filtersConfig.SESSION_TYPE.placeholder}
        onChange={filtersConfig.SESSION_TYPE.onChange}
        isMulti={false}
        showSearch={false}
        noneLabel={'All'}
        placeholder={filtersConfig.SESSION_TYPE.placeholder}
        isButton={true}
        options={filtersConfig.SESSION_TYPE.options}
        value={filtersConfig.SESSION_TYPE.value}
      />
      <Tooltip
        key={`enrollDrawerSearchTooltip`}
        overlayStyle={{ zIndex: 100000 }}
        title={
          <FormattedMessage
            {...ENROLL_DRAWER_MESSAGES.SEARCH_TOOLTIP}
            values={{
              entityType:
                !isMultidayEnabled || isEventEnrollDrawerActive ? 'session' : 'event or session',
            }}
          />
        }
        placement={'topRight'}
        trigger={'hover'}
      >
        <Search
          key={`enrollDrawerSearch`}
          className="multifilterDropdown"
          placeholder={filtersConfig.SEARCH.placeholder}
          onSearch={filtersConfig.SEARCH.onSearch}
          style={{ width: '140px' }}
          value={filtersConfig.SEARCH.value}
        />
      </Tooltip>
      <Button className="reset-button" type="text" onClick={() => onResetFiltersClick()}>
        Reset filters
      </Button>
    </FiltersContainer>
  );
}
