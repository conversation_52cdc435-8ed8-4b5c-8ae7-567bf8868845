import React, { Fragment, useCallback } from 'react';

import RemoteInviteExisting from '~/components/RemoteInviteExisting';
import RemoteInviteGroups from '~/components/RemoteInviteGroups';
import RemoteInviteUpload from '~/components/RemoteInviteUpload';

import type { InviteModalProps } from '../../typeDefs';

export const InviteModals: React.FC<InviteModalProps> = ({
  addNewLearnerAllowed,
  callback,
  companyId,
  defaultModuleRelevance,
  groupVisible,
  isSeries,
  moduleId,
  moduleRelevanceEnabled,
  seriesId,
  setExistingVisible,
  setGroupVisible,
  setUploadVisible,
  showInviteExisting,
  uploadVisible,
}) => {
  const selectableMetaKeys = !isSeries && moduleRelevanceEnabled ? '["moduleRelevance"]' : '[]';
  const getBulkModulesData = useCallback(
    () => [
      {
        id: moduleId,
        isPublished: true,
      },
    ],
    [moduleId]
  );

  return (
    <Fragment>
      {showInviteExisting && (
        <RemoteInviteExisting
          moduleId={moduleId}
          seriesId={seriesId}
          callback={callback}
          defaultModuleRelevance={defaultModuleRelevance}
          isSeriesScoped={isSeries ? 'true' : 'false'}
          withTrigger={false}
          selectableMetaKeys={selectableMetaKeys}
          addNewLearnerAllowed={addNewLearnerAllowed ? 'true' : 'false'}
          disableUrl={'true'}
          onClose={setExistingVisible.bind(null, false)}
        />
      )}
      {uploadVisible && (
        <RemoteInviteUpload
          companyId={companyId}
          modules={[]}
          getBulkModulesData={getBulkModulesData}
          moduleId={moduleId}
          seriesId={seriesId}
          callback={callback}
          isSeriesScoped={isSeries}
          moduleRelevanceEnabled={moduleRelevanceEnabled}
          isSchedule={false}
          defaultModuleRelevance={defaultModuleRelevance}
          onClose={setUploadVisible.bind(null, false)}
        />
      )}
      {groupVisible && (
        <RemoteInviteGroups
          moduleId={moduleId}
          modules={[]}
          seriesId={seriesId}
          callback={callback}
          hideModuleNames
          getBulkModulesData={getBulkModulesData}
          isBulkModulesInvite={!isSeries}
          isSchedule={false}
          isSeriesScoped={isSeries}
          defaultModuleRelevance={defaultModuleRelevance}
          moduleRelevanceEnabled={moduleRelevanceEnabled}
          groupVisible={groupVisible}
          setGroupVisible={setGroupVisible}
          close={setGroupVisible.bind(null, false)}
        />
      )}
    </Fragment>
  );
};
