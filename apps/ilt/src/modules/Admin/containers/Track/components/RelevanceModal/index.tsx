import { useState } from 'react';

import Button, { BUTTON_TYPES } from '@mindtickle/button';
import Modal from '@mindtickle/modal';
import Radio from '@mindtickle/radio';

import { RELEVANCE_TYPE, DEFAULT_TEXT } from './constants';
import { ContentContainer, radioStyle } from './styles';

import type { TRelevanceModalRadioGroup, TRelevanceModal } from './typeDefs';

const VerticalGroupRadio = ({
  cancelButtonText,
  okButtonText,
  onCancel,
  onOk,
  value,
  setValue,
  defaultModuleRelevance,
}: TRelevanceModalRadioGroup) => {
  const onChange = (e: any) => {
    setValue(e.target.value);
  };

  return (
    <ContentContainer>
      <Radio.Group onChange={onChange} value={value} className="content-wrapper-style">
        <Radio style={radioStyle} value={RELEVANCE_TYPE.REQUIRED.value}>
          {RELEVANCE_TYPE.REQUIRED.label}
          {defaultModuleRelevance === RELEVANCE_TYPE.REQUIRED.value && <span>{DEFAULT_TEXT}</span>}
        </Radio>
        <Radio style={radioStyle} value={RELEVANCE_TYPE.OPTIONAL.value}>
          {RELEVANCE_TYPE.OPTIONAL.label}
          {defaultModuleRelevance === RELEVANCE_TYPE.OPTIONAL.value && <span>{DEFAULT_TEXT}</span>}
        </Radio>
        <Radio style={radioStyle} value={RELEVANCE_TYPE.UNMARKED.value}>
          {RELEVANCE_TYPE.UNMARKED.label}
          {defaultModuleRelevance === RELEVANCE_TYPE.UNMARKED.value && <span>{DEFAULT_TEXT}</span>}
        </Radio>
      </Radio.Group>
      <div className="footer">
        <Button className="cancel-button" type={BUTTON_TYPES.TEXT} onClick={onCancel}>
          {cancelButtonText}
        </Button>
        <Button type={BUTTON_TYPES.PRIMARY} disabled={!Boolean(value)} onClick={onOk}>
          {okButtonText}
        </Button>
      </div>
    </ContentContainer>
  );
};

const RelevanceModal = ({
  onModuleRelevanceSelection,
  onModuleRelevanceClose,
  learnersForModuleRelevanceChange,
  learnersMap,
  defaultModuleRelevance,
}: TRelevanceModal) => {
  const [selectedRelevance, setRelevance] = useState<string>('');

  const title =
    learnersForModuleRelevanceChange.length > 1
      ? `Change relevance of ${learnersForModuleRelevanceChange.length} selected learners`
      : `Change the relevance of ${learnersMap[learnersForModuleRelevanceChange[0]]?.name}`;
  return (
    <Modal
      visible={true}
      title={title}
      content={''}
      size={'small'}
      onCancel={onModuleRelevanceClose}
      bodyStyle={{ padding: 0 }}
    >
      <VerticalGroupRadio
        cancelButtonText={'Cancel'}
        okButtonText={'Change'}
        onOk={() => onModuleRelevanceSelection(selectedRelevance)}
        value={selectedRelevance}
        setValue={selected => setRelevance(selected)}
        onCancel={onModuleRelevanceClose}
        defaultModuleRelevance={defaultModuleRelevance}
      />
    </Modal>
  );
};

export default RelevanceModal;
