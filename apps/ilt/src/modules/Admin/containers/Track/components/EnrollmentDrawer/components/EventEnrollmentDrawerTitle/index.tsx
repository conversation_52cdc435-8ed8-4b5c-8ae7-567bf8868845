import { FormattedMessage } from 'react-intl';

import Divider from '@mindtickle/divider';
import EllipsisTooltip from '@mindtickle/ellipsis-tooltip';
import Icon, { ICON_MAP } from '@mindtickle/icon';
import Switch from '@mindtickle/switch';
import Tooltip from '@mindtickle/tooltip';

import {
  SESSION_STATUS_INFO,
  SESSION_STATUS_TYPES,
  SESSION_TYPE_INFO,
  SESSION_TYPE_TYPES,
} from '~/modules/Admin/config/track.constants';
import ENROLL_DRAWER_MESSAGES from '~/modules/Admin/messages/track/enrollDrawer';
import { sessionTimeFormatter, updateSessionStatusDetails } from '~/modules/Admin/utils';
import { getCurrentTimeZoneAbbreviated } from '~/modules/Admin/utils/timing';

import { TOOLTIP_Z_INDEX } from '../EnrollmentDrawerTitle/constants';

import {
  EventEnrollmentDrawerTitleContainer,
  StyledEventDescriptionContainer,
  StyledEventNameContainer,
} from './styles';

import type { TEventEnrollmentDrawerTitle } from './typeDefs';

const EventEnrollmentDrawerTitle = ({
  notifyLearners,
  flipNotifyToggle,
  isCalendarAutoSyncEnabled,
  eventInfo,
  switchToEnrollDrawer,
}: TEventEnrollmentDrawerTitle) => {
  const entityInfo: any = updateSessionStatusDetails(eventInfo);
  let timeAndDateString = sessionTimeFormatter(eventInfo.localStartTime || eventInfo.startTime);

  const sessionStatus = entityInfo.isUpcoming
    ? SESSION_STATUS_TYPES.UPCOMING
    : entityInfo.isOver
    ? SESSION_STATUS_TYPES.PAST
    : SESSION_STATUS_TYPES.LIVE;

  const sessionType =
    entityInfo.type === SESSION_TYPE_TYPES.CLASSROOM
      ? SESSION_TYPE_TYPES.CLASSROOM
      : entityInfo.type === SESSION_TYPE_TYPES.HYBRID
      ? SESSION_TYPE_TYPES.HYBRID
      : SESSION_TYPE_TYPES.WEBINAR;

  return (
    <EventEnrollmentDrawerTitleContainer>
      <StyledEventNameContainer>
        <Icon type={ICON_MAP.LEFT_ARROW} className="go-back-icon" onClick={switchToEnrollDrawer} />
        <EllipsisTooltip
          wrapperClassName="event-name event-name-tooltip"
          placement="bottom"
          title={eventInfo.name}
          showTooltipWhenEllipsis={true}
        >
          {eventInfo.name}
        </EllipsisTooltip>
        <div className="event-tag">Event</div>
      </StyledEventNameContainer>

      <StyledEventDescriptionContainer>
        <Icon
          type={ICON_MAP.COHORTSMALL}
          className="calendar-icon"
          onClick={switchToEnrollDrawer}
        />
        <div className="event-name">
          {timeAndDateString + `(${getCurrentTimeZoneAbbreviated(entityInfo.startTime)})`}
        </div>
        <Divider className={'divider'} type="vertical" />
        <div className="event-name">
          {SESSION_TYPE_INFO[sessionType as keyof typeof SESSION_TYPE_INFO]?.displayText}
        </div>
        <Divider className={'divider'} type="vertical" />
        <div className="event-name">
          {SESSION_STATUS_INFO[sessionStatus as keyof typeof SESSION_STATUS_INFO]?.displayText}
        </div>
      </StyledEventDescriptionContainer>

      <div className="toggle-container">
        <Switch checked={notifyLearners} onChange={() => flipNotifyToggle()} />
        <div className="notify-text">{`Notify learners via email`}</div>
        <Tooltip
          key={`notifyLearnersTooltip`}
          overlayStyle={TOOLTIP_Z_INDEX}
          title={<FormattedMessage {...ENROLL_DRAWER_MESSAGES.NOTIFY_LEARNERS_TOOLTIP} />}
          placement={'top'}
          trigger={'hover'}
        >
          <Icon className="notify-icon" type={ICON_MAP.INFO2}></Icon>
        </Tooltip>
      </div>
      {!notifyLearners && isCalendarAutoSyncEnabled && (
        <div className="info-container">
          <span className="info-container-text">
            <FormattedMessage {...ENROLL_DRAWER_MESSAGES.LEARNER_CALENDAR_AUTO_SYNC_INFO} />
          </span>
        </div>
      )}
    </EventEnrollmentDrawerTitleContainer>
  );
};
export default EventEnrollmentDrawerTitle;
