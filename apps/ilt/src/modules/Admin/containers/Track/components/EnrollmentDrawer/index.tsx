/* eslint-disable max-statements */
import { useState, useEffect, useCallback } from 'react';

import type { TEventEnrollmentType } from '~/modules/Admin/api/sessionService/typeDefs/entityDetails';
import {
  DATE_FILTER_OPTIONS,
  DRAWER_ENROLL_STATUS_ACTION_MAP,
  ENROLL_DRAWER_SUPPORTED_FILTERS,
  ENROLL_DRAWER_SUPPORTED_SORTING,
  OPERATIONS,
} from '~/modules/Admin/config/track.constants';
import { SUPPORTED_FILTERS_API_KEY } from '~/modules/Admin/constants/sessions';
import { useILTAdminSnowplowTracker } from '~/modules/Admin/hooks/useILTAdminSnowplowTracker';
import { getDatesInEpochForDateFilter } from '~/utils';

import { sendEditSessionWithinEventEnrollmentClicked } from '../../helpers/trackEvents';
import EnrollmentDrawerFilters from '../EnrollmentDrawerFilters';
import EnrollmentDrawerTable from '../EnrollmentDrawerTable';
import { ENR<PERSON>LMENT_DEFAULT_FILTERS } from '../LearnersTab/constants';

import EnrollmentDrawerTitle from './components/EnrollmentDrawerTitle';
import EventEnrollmentDrawerTitle from './components/EventEnrollmentDrawerTitle';
import {
  ENROLL_DRAWER_ENROLLMENT_FILTER_OPTIONS,
  ENROLL_DRAWER_ATTENDANCE_FILTER_OPTIONS,
  ENROLL_DRAWER_SESSION_STATE_FILTER_OPTIONS,
  ENROLL_DRAWER_SESSION_TYPE_FILTER_OPTIONS,
  FILTER_TITLES,
  ENROLL_DRAWER_ENTITY_TYPE_FILTER_OPTIONS,
  ENROLL_DRAWER_TYPES,
} from './constants';
import EnrollmentDrawerContainer from './styles';

import type { EnrollmentDrawerType } from './typeDefs';
import type { EnrollmentDrawerFiltersType } from '../EnrollmentDrawerFilters/typeDefs';

const { LOAD_MORE } = OPERATIONS;

export default function EnrollmentDrawer({
  learnerEntitiesDataStatus = {
    loaded: false,
    isLoading: false,
    loadingData: {
      operation: '',
    },
  },
  learnerSessionsWithinEventDataStatus = {
    loaded: false,
    isLoading: false,
    loadingData: {
      operation: '',
    },
  },
  drawerModifyEnroll,
  learnersMap,
  learnerIds,
  activeLearnerId = '',
  learnerEntities = {
    entities: [],
    hasMore: false,
    start: 0,
    rows: 0,
  },
  learnerSessionsWithinEvent = {
    sessions: [],
    hasMore: false,
    start: 0,
    rows: 0,
  },
  toggleSessionsDrawer,
  toggleModifyMode,
  defaultFilters,
  multipleLearners = false,
  changeEnrollStatus,
  fetchDrawerData,
  fetchEventEnrollDrawerData,
  enrollOperationStatus,
  learnerEntitiesInfo,
  isCalendarAutoSyncEnabled,
  setEnrollmentDrawerDefaultFilters,
  mappedSeries = [],
  clearEventEnrollmentDrawerData,
  localTimezoneName,
  trackerPageName,
}: EnrollmentDrawerType) {
  const initialFilters = {
    [ENROLL_DRAWER_SUPPORTED_FILTERS.ENROLLMENT_STATUS]: defaultFilters['ENROLLMENT'],
    [ENROLL_DRAWER_SUPPORTED_FILTERS.ATTENDANCE_STATUS]: defaultFilters['ATTENDANCE'],
    [ENROLL_DRAWER_SUPPORTED_FILTERS.SESSION_STATE]: defaultFilters['SESSION_STATE'],
    [ENROLL_DRAWER_SUPPORTED_FILTERS.SESSION_STATUS]: [
      SUPPORTED_FILTERS_API_KEY[ENROLL_DRAWER_SUPPORTED_FILTERS.SESSION_STATUS].PUBLISHED,
    ],
    [ENROLL_DRAWER_SUPPORTED_FILTERS.SESSION_TYPE]: '',
    [ENROLL_DRAWER_SUPPORTED_FILTERS.ILT_DATE_RANGE_DD]: '',
    [ENROLL_DRAWER_SUPPORTED_FILTERS.SEARCH]: '',
  };

  const [resetCount, setResetCount] = useState<number>(0);
  const [eventDrawerResetCount, setEventDrawerResetCount] = useState<number>(0);
  const [notifyLearners, setNotifyLearners] = useState<boolean>(true);
  const [selectedFilters, setSelectedFilters] = useState<{ [key: string]: any }>(initialFilters);
  const [selectedEventEnrollFilters, setSelectedEventEnrollFilters] = useState<{
    [key: string]: any;
  }>(initialFilters);
  const [sortOrder, setSortOrder] = useState<string>('DESC');
  const [sortField, setSortField] = useState<string>(ENROLL_DRAWER_SUPPORTED_SORTING.START_TIME);
  const [activeDrawer, setActiveDrawer] = useState<string>(ENROLL_DRAWER_TYPES.ENTITY);
  const [eventDrawerParentId, setEventDrawerParentId] = useState<string>();
  const [enrollmentMap, setEnrollmentMap] = useState<object>({}); //TODO: Check whether map state can be avoided and directly consumed from redux
  const [attendanceMap, setAttendanceMap] = useState<object>({}); //TODO: Check whether map state can be avoided and directly consumed from redux
  const [activeButtonMap, setActiveButtonMap] = useState<object>({}); //TODO: Check whether map state can be avoided and directly consumed from redux
  const isEventEnrollDrawerActive = activeDrawer === ENROLL_DRAWER_TYPES.EVENT;
  const [eventEnrollDrawerDefaultFilters, setEventEnrollDrawerDefaultFilters] = useState<any>(
    ENROLLMENT_DEFAULT_FILTERS
  );

  const [dateValueState, setDateValueState] = useState<Array<object>>([]);
  const [eventEnrollDrawerDateValueState, setEventEnrollDrawerDateValueState] = useState<
    Array<object>
  >([]);

  const tracker = useILTAdminSnowplowTracker();

  const updateLearnerStatsMap = (prevMap: any, idsToRemove: string[]) =>
    Object.keys(prevMap).reduce((acc: any, key: string) => {
      if (!idsToRemove.includes(key)) {
        acc[key] = prevMap[key];
      }
      return acc;
    }, {});

  const switchToEventEnrollDrawer = () => {
    setEventEnrollDrawerDefaultFilters(ENROLLMENT_DEFAULT_FILTERS);
    setActiveDrawer(ENROLL_DRAWER_TYPES.EVENT);
  };
  const switchToEnrollDrawer = () => {
    const { entities } = learnerEntities;
    const entityStats = entities.find(entity => entity.id === eventDrawerParentId);
    setActiveDrawer(ENROLL_DRAWER_TYPES.ENTITY);
    const idsToRemove = learnerSessionsWithinEvent.sessions.map(session => session.id);
    // This updates the parent enroll and attendance status maps to latest once child drawer closes and removed the child drawer sessions from map once it closes
    setEnrollmentMap(prevEnrollmentMap => {
      const updatedMap = {
        ...prevEnrollmentMap,
        [eventDrawerParentId as keyof typeof enrollmentMap]: entityStats?.enrollmentStatus,
      };
      return updateLearnerStatsMap(updatedMap, idsToRemove);
    });
    setActiveButtonMap(prevActiveButtonMap => {
      const updatedMap = {
        ...prevActiveButtonMap,
        [eventDrawerParentId as keyof typeof activeButtonMap]: {
          enrollmentType:
            DRAWER_ENROLL_STATUS_ACTION_MAP[
              entityStats?.enrollmentStatus as keyof typeof DRAWER_ENROLL_STATUS_ACTION_MAP
            ],
          showFeedback: false,
        },
      };
      return updateLearnerStatsMap(updatedMap, idsToRemove);
    });
    setAttendanceMap(prevAttendanceMap => {
      const updatedMap = {
        ...prevAttendanceMap,
        [eventDrawerParentId as keyof typeof attendanceMap]: entityStats?.attendanceStatus,
      };
      return updateLearnerStatsMap(updatedMap, idsToRemove);
    });
    setEventEnrollDrawerDateValueState([]);
    setSelectedEventEnrollFilters(initialFilters);
    setEventDrawerResetCount(eventDrawerResetCount + 1);
    setEventEnrollDrawerDefaultFilters(ENROLLMENT_DEFAULT_FILTERS);
    clearEventEnrollmentDrawerData();
  };

  useEffect(() => {
    fetchDrawerData({
      learnerId: activeLearnerId,
      filters: selectedFilters,
      sortOrder: sortOrder,
      sortField: sortField,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const flipNotifyToggle = () => {
    const curr = notifyLearners;
    setNotifyLearners(!curr);
  };

  const onFetchMore = (eventId?: string) => {
    isEventEnrollDrawerActive
      ? fetchEventEnrollDrawerData({
          learnerId: activeLearnerId,
          operation: LOAD_MORE,
          filters: selectedEventEnrollFilters,
          sortOrder,
          sortField,
          eventId,
        })
      : fetchDrawerData({
          learnerId: activeLearnerId,
          operation: LOAD_MORE,
          filters: selectedFilters,
          sortOrder,
          sortField,
        });
  };

  const handleEnrollmentChange = ({
    enrollmentType,
    sessionId,
    eventId,
    enrolmentLevel,
  }: {
    enrollmentType: Array<String>;
    sessionId?: string;
    eventId?: string;
    enrolmentLevel?: TEventEnrollmentType;
  }) => {
    const enrollmentFreezeStatus =
      learnerEntitiesInfo[sessionId as keyof typeof learnerEntitiesInfo]?.enrollmentFreezeStatus;

    const learnerEnrollmentData = learnerIds.map(learnerId => ({
      enrollmentFreezeStatus: enrollmentFreezeStatus,
      learnerId: learnerId,
      notify: notifyLearners,
      operation: enrollmentType,
    }));
    const entityData = {
      sessionId: sessionId,
      eventId: eventId,
      enrolmentLevel,
    };

    changeEnrollStatus(learnerEnrollmentData, entityData);
  };

  const onSort = ({
    sortOrder,
    sortField,
    eventId,
  }: {
    sortOrder: string;
    sortField: string;
    eventId?: string;
  }) => {
    setSortOrder(sortOrder);
    setSortField(sortField);
    isEventEnrollDrawerActive
      ? fetchEventEnrollDrawerData({
          learnerId: activeLearnerId,
          filters: selectedEventEnrollFilters,
          sortOrder,
          sortField,
          eventId,
        })
      : fetchDrawerData({
          learnerId: activeLearnerId,
          sortOrder,
          sortField,
          filters: selectedFilters,
        });
  };

  const filterOnChange = ({
    filterType,
    value,
  }: {
    filterType: string;
    value?: string | Array<String>;
  }) => {
    if (isEventEnrollDrawerActive) {
      let newSelectedFilters = {
        ...selectedEventEnrollFilters,
        [filterType]: value,
      };
      setSelectedEventEnrollFilters(newSelectedFilters);
      fetchEventEnrollDrawerData({
        learnerId: activeLearnerId,
        sortOrder,
        sortField,
        filters: newSelectedFilters,
        eventId: eventDrawerParentId,
      });
    } else {
      let newSelectedFilters = {
        ...selectedFilters,
        [filterType]: value,
      };

      setSelectedFilters(newSelectedFilters);

      fetchDrawerData({
        learnerId: activeLearnerId,
        sortOrder,
        sortField,
        filters: newSelectedFilters,
      });
    }
  };

  const onResetFiltersClick = () => {
    const updatedSelectedFilters = {
      [ENROLL_DRAWER_SUPPORTED_FILTERS.ENROLLMENT_STATUS]: [],
      [ENROLL_DRAWER_SUPPORTED_FILTERS.ATTENDANCE_STATUS]: [],
      [ENROLL_DRAWER_SUPPORTED_FILTERS.SESSION_STATE]: '',
      [ENROLL_DRAWER_SUPPORTED_FILTERS.SESSION_STATUS]: [
        SUPPORTED_FILTERS_API_KEY[ENROLL_DRAWER_SUPPORTED_FILTERS.SESSION_STATUS].PUBLISHED,
      ],
      [ENROLL_DRAWER_SUPPORTED_FILTERS.SESSION_TYPE]: '',
      [ENROLL_DRAWER_SUPPORTED_FILTERS.ILT_DATE_RANGE_DD]: '',
      [ENROLL_DRAWER_SUPPORTED_FILTERS.SEARCH]: '',
      ...(isEventEnrollDrawerActive ? {} : { [ENROLL_DRAWER_SUPPORTED_FILTERS.ENTITY_TYPE]: '' }),
    };

    if (isEventEnrollDrawerActive) {
      setEventEnrollDrawerDateValueState([]);
      setEventEnrollDrawerDefaultFilters({ ENROLLMENT: [], ATTENDANCE: [] });
      setSelectedEventEnrollFilters(updatedSelectedFilters);
      fetchEventEnrollDrawerData({
        learnerId: activeLearnerId,
        sortOrder,
        sortField,
        filters: updatedSelectedFilters,
        eventId: eventDrawerParentId,
      });
      setEventDrawerResetCount(eventDrawerResetCount + 1);
    } else {
      setDateValueState([]);
      setEnrollmentDrawerDefaultFilters({ ENROLLMENT: [], ATTENDANCE: [] });
      setSelectedFilters(updatedSelectedFilters);
      fetchDrawerData({
        learnerId: activeLearnerId,
        sortOrder,
        sortField,
        filters: updatedSelectedFilters,
      });
      setResetCount(resetCount + 1);
    }
  };

  const filtersConfig = {
    ENROLLMENT_STATUS: {
      onChange: (selected: Array<{ value: String }>) => {
        const filterValue = selected.map(element => element.value);
        filterOnChange({
          filterType: ENROLL_DRAWER_SUPPORTED_FILTERS.ENROLLMENT_STATUS,
          value: filterValue,
        });
      },
      onClear: () =>
        filterOnChange({ filterType: ENROLL_DRAWER_SUPPORTED_FILTERS.ENROLLMENT_STATUS }),
      placeholder: FILTER_TITLES.ENROLLMENT_STATUS,
      options: ENROLL_DRAWER_ENROLLMENT_FILTER_OPTIONS,
      value: isEventEnrollDrawerActive
        ? selectedEventEnrollFilters[ENROLL_DRAWER_SUPPORTED_FILTERS.ENROLLMENT_STATUS as string]
        : selectedFilters[ENROLL_DRAWER_SUPPORTED_FILTERS.ENROLLMENT_STATUS as string],
    },
    ATTENDED_STATUS: {
      onChange: (selected: Array<{ value: String }>) => {
        const filterValue = selected.map(element => element.value);
        filterOnChange({
          filterType: ENROLL_DRAWER_SUPPORTED_FILTERS.ATTENDANCE_STATUS,
          value: filterValue,
        });
      },
      onClear: () =>
        filterOnChange({ filterType: ENROLL_DRAWER_SUPPORTED_FILTERS.ATTENDANCE_STATUS }),
      placeholder: FILTER_TITLES.ATTENDANCE_STATUS,
      options: ENROLL_DRAWER_ATTENDANCE_FILTER_OPTIONS,
      value: isEventEnrollDrawerActive
        ? selectedEventEnrollFilters[ENROLL_DRAWER_SUPPORTED_FILTERS.ATTENDANCE_STATUS as string]
        : selectedFilters[ENROLL_DRAWER_SUPPORTED_FILTERS.ATTENDANCE_STATUS as string],
    },
    SESSION_STATE: {
      onChange: (selected: { filterType: string; value: string }) =>
        filterOnChange({
          filterType: ENROLL_DRAWER_SUPPORTED_FILTERS.SESSION_STATE,
          value: selected.value === 'None' ? '' : selected.value,
        }),
      showSearch: false,
      isMulti: false,
      placeholder: FILTER_TITLES.SESSION_STATE,
      options: ENROLL_DRAWER_SESSION_STATE_FILTER_OPTIONS,
      value: isEventEnrollDrawerActive
        ? selectedEventEnrollFilters[ENROLL_DRAWER_SUPPORTED_FILTERS.SESSION_STATE as string]
        : selectedFilters[ENROLL_DRAWER_SUPPORTED_FILTERS.SESSION_STATE as string],
    },
    SESSION_DATE: {
      placeholder: FILTER_TITLES.ILT_DATE_RANGE_DD,
      onChange: (from: any, to: any) => {
        isEventEnrollDrawerActive
          ? setEventEnrollDrawerDateValueState([from, to])
          : setDateValueState([from, to]);
        const fromDate = from ? from._d : null;
        const toDate = to ? to._d : null;
        filterOnChange({
          filterType: ENROLL_DRAWER_SUPPORTED_FILTERS.ILT_DATE_RANGE_DD,
          value: getDatesInEpochForDateFilter(fromDate, toDate),
        });
      },
      value: isEventEnrollDrawerActive ? eventEnrollDrawerDateValueState : dateValueState,
      options: Object.keys(DATE_FILTER_OPTIONS).map(
        v => DATE_FILTER_OPTIONS[v as keyof typeof DATE_FILTER_OPTIONS]
      ),
      enableFutureDateSelection: true,
    },
    SESSION_TYPE: {
      onChange: (selected: { filterType: string; value: string }) =>
        filterOnChange({
          filterType: ENROLL_DRAWER_SUPPORTED_FILTERS.SESSION_TYPE,
          value: selected.value === 'None' ? '' : selected.value,
        }),
      showSearch: false,
      isMulti: false,
      placeholder: FILTER_TITLES.SESSION_TYPE,
      options: ENROLL_DRAWER_SESSION_TYPE_FILTER_OPTIONS(isEventEnrollDrawerActive),
      value: isEventEnrollDrawerActive
        ? selectedEventEnrollFilters[ENROLL_DRAWER_SUPPORTED_FILTERS.SESSION_TYPE as string]
        : selectedFilters[ENROLL_DRAWER_SUPPORTED_FILTERS.SESSION_TYPE],
    },
    SEARCH: {
      placeholder: FILTER_TITLES.SEARCH,
      onSearch: (value: string) =>
        filterOnChange({ filterType: ENROLL_DRAWER_SUPPORTED_FILTERS.SEARCH, value }),
      value: isEventEnrollDrawerActive
        ? selectedEventEnrollFilters[ENROLL_DRAWER_SUPPORTED_FILTERS.SEARCH as string]
        : selectedFilters[ENROLL_DRAWER_SUPPORTED_FILTERS.SEARCH],
    },
    ENTITY_TYPE: {
      onChange: (selected: { filterType: string; value: string }) =>
        filterOnChange({
          filterType: ENROLL_DRAWER_SUPPORTED_FILTERS.ENTITY_TYPE,
          value: selected.value === 'None' ? '' : selected.value,
        }),
      showSearch: false,
      isMulti: false,
      placeholder: FILTER_TITLES.ENTITY_TYPE,
      options: ENROLL_DRAWER_ENTITY_TYPE_FILTER_OPTIONS,
      value: selectedFilters[ENROLL_DRAWER_SUPPORTED_FILTERS.ENTITY_TYPE],
    },
  };

  const renderDrawerFilters = () => (
    <EnrollmentDrawerFilters
      key={isEventEnrollDrawerActive ? eventDrawerResetCount : resetCount}
      multipleLearners={multipleLearners}
      filtersConfig={filtersConfig as unknown as EnrollmentDrawerFiltersType['filtersConfig']}
      onResetFiltersClick={onResetFiltersClick}
      defaultFilters={isEventEnrollDrawerActive ? eventEnrollDrawerDefaultFilters : defaultFilters}
      isEventEnrollDrawerActive={isEventEnrollDrawerActive}
    ></EnrollmentDrawerFilters>
  );

  const tableActions = {
    editEventEnrollment: ({ id }: { id: string }) => {
      fetchEventEnrollDrawerData({
        learnerId: activeLearnerId,
        filters: selectedEventEnrollFilters,
        sortOrder: sortOrder,
        sortField: sortField,
        eventId: id,
      });
      setEventDrawerParentId(id);
      switchToEventEnrollDrawer();
      sendEditSessionWithinEventEnrollmentClicked(tracker, { pageName: trackerPageName });
    },
  };

  const renderDrawerTable = () => {
    const learnerName = (learnersMap as { [key: string]: any })[
      activeLearnerId as keyof typeof learnersMap
    ]?.name;

    const { entities, hasMore } = learnerEntities;
    const { sessions: sessionsWithinEvent, hasMore: hasMoreSessionsWithinEvent } =
      learnerSessionsWithinEvent;

    return (
      <EnrollmentDrawerTable
        learnerEntities={isEventEnrollDrawerActive ? sessionsWithinEvent : entities}
        drawerModifyEnroll={drawerModifyEnroll}
        multipleLearners={multipleLearners}
        handleEnrollmentChange={handleEnrollmentChange}
        enrollOperationStatus={enrollOperationStatus}
        onSort={onSort}
        onFetchMore={onFetchMore}
        hasMore={isEventEnrollDrawerActive ? hasMoreSessionsWithinEvent : hasMore}
        learnerEntitiesDataStatus={
          isEventEnrollDrawerActive
            ? learnerSessionsWithinEventDataStatus
            : learnerEntitiesDataStatus
        }
        selectedLearnersCount={learnerIds.length}
        learnerEntitiesInfo={learnerEntitiesInfo}
        notifyLearners={notifyLearners}
        learnerName={learnerName}
        mappedSeries={mappedSeries}
        tableActions={tableActions}
        isEventEnrollDrawerActive={isEventEnrollDrawerActive}
        enrollmentMap={enrollmentMap}
        setEnrollmentMap={setEnrollmentMap}
        attendanceMap={attendanceMap}
        setAttendanceMap={setAttendanceMap}
        activeButtonMap={activeButtonMap}
        setActiveButtonMap={setActiveButtonMap}
        eventEnrollDrawerId={eventDrawerParentId}
        localTimezoneName={localTimezoneName}
      />
    );
  };

  const getLearnerName = useCallback(() => {
    const learnerId = learnerIds.length === 1 ? learnerIds[0] : null;
    if (!learnerId) {
      return '';
    }
    const learnerName = learnersMap[learnerId as keyof typeof learnersMap]
      ? (learnersMap as { [key: string]: any })[learnerId as keyof typeof learnersMap].name
      : '';

    return learnerName;
  }, [learnerIds, learnersMap]);

  return (
    <EnrollmentDrawerContainer
      title={
        !isEventEnrollDrawerActive ? (
          <EnrollmentDrawerTitle
            toggleModifyMode={toggleModifyMode}
            drawerModifyEnroll={drawerModifyEnroll}
            multipleLearners={multipleLearners}
            learnerName={getLearnerName()}
            notifyLearners={notifyLearners}
            flipNotifyToggle={flipNotifyToggle}
            learnerIds={learnerIds}
            isCalendarAutoSyncEnabled={isCalendarAutoSyncEnabled}
            mappedSeries={mappedSeries}
          />
        ) : (
          <EventEnrollmentDrawerTitle
            notifyLearners={notifyLearners}
            flipNotifyToggle={flipNotifyToggle}
            isCalendarAutoSyncEnabled={isCalendarAutoSyncEnabled}
            eventInfo={learnerEntitiesInfo[eventDrawerParentId as keyof typeof learnerEntitiesInfo]}
            switchToEnrollDrawer={switchToEnrollDrawer}
          />
        )
      }
      placement="right"
      width={1150}
      bodyStyle={{
        padding: '10px 28px',
      }}
      onClose={() => {
        toggleSessionsDrawer({ show: false, isEventEnrollDrawerActive });
      }}
      visible={true}
      headerStyle={{ borderBottom: 0, paddingBottom: '10px' }}
      zIndex={9912}
      rootClassName="enrollDrawer"
    >
      {renderDrawerFilters()}
      {renderDrawerTable()}
    </EnrollmentDrawerContainer>
  );
}
