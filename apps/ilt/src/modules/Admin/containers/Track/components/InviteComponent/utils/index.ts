import { checkAccessByPermissionsMap } from 'ui_shell/Permissions';

import { MT_MODULES } from '~/config/global.config';

import {
  CB_MESSAGE,
  INVITE_PERMISSIONS,
  INVITE_SHOW,
  INVITE_TO_OPTIONS,
  INVITE_TYPE,
  INVITE_TYPE_DETAILS,
} from '../constants';
import messages from '../messages';

import { ErrorToast, LoadingToast, SuccessToast } from './toast';

import type {
  InviteOptionType,
  GetInviteOptionsReturnType,
  InviteOptions,
  CallbackProps,
} from '../typeDefs';

type ModuleType = string;

export const getInviteOptions = ({
  moduleType,
  globalPermissions,
  isSiteOwner,
}: InviteOptionType): ReadonlyArray<GetInviteOptionsReturnType> => {
  const inviteTo = INVITE_TO_OPTIONS(moduleType);
  const inviteOptions: Array<InviteOptions> = [];
  INVITE_SHOW.forEach(type => {
    if (
      checkAccessByPermissionsMap({
        permissions: INVITE_PERMISSIONS.INVITE_TYPE[type],
        globalPermissions,
        isSiteOwner,
      })
    ) {
      inviteOptions.push({
        value: type,
        title: INVITE_TYPE_DETAILS[type].title,
      });
    }
  });
  const options = [];
  for (const [value, title] of Object.entries(inviteTo)) {
    if (
      checkAccessByPermissionsMap({
        permissions: INVITE_PERMISSIONS.INVITE_TO[value],
        globalPermissions,
        isSiteOwner,
      })
    ) {
      options.push({
        title,
        value,
        children: inviteOptions,
      });
    }
  }
  return options;
};

export const canAddNewLearners = ({
  globalPermissions,
  isSiteOwner,
}: Omit<InviteOptionType, 'moduleType'>): boolean =>
  !!checkAccessByPermissionsMap({
    permissions: INVITE_PERMISSIONS.INVITE_TYPE[INVITE_TYPE.ADD_INVITE_NEW],
    globalPermissions,
    isSiteOwner,
  });

export const getCallback =
  ({ onComplete }: CallbackProps) =>
  (msg: string) => {
    switch (msg) {
      case CB_MESSAGE.SUCCESS:
        SuccessToast({
          message: messages.invited,
        });
        onComplete();
        break;
      case CB_MESSAGE.FAILURE:
        ErrorToast({
          message: messages.invitationFailed,
        });
        break;
      case CB_MESSAGE.TRIGGER:
        LoadingToast({
          message: messages.inviting,
        });
    }
  };

export const getModuleNameFromModuleType = (type: ModuleType): string => MT_MODULES.ILT;

export const getModuleIdFromModuleType = (type: ModuleType): number => 7;
