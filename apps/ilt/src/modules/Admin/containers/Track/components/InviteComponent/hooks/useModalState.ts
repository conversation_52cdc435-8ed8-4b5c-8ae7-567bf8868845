import { useState } from 'react';

import { INVITE_TO, INVITE_TYPE } from '../constants';

const useModalState = () => {
  const [showInviteExisting, setExistingVisible] = useState(false);
  const [uploadVisible, setUploadVisible] = useState(false);
  const [groupVisible, setGroupVisible] = useState(false);
  const [isSeries, setIsSeries] = useState(false);
  const onSelect = (inviteToType: string, value: string) => {
    if (value === INVITE_TYPE.INVITE_EXISTING) {
      setExistingVisible(true);
    } else if (value === INVITE_TYPE.UPLOAD_LIST) {
      setUploadVisible(true);
    } else if (value === INVITE_TYPE.INVITE_GROUP) {
      setGroupVisible(true);
    }
    setIsSeries(inviteToType === INVITE_TO.SERIES);
  };
  return {
    showInviteExisting,
    setExistingVisible,
    uploadVisible,
    setUploadVisible,
    groupVisible,
    setGroupVisible,
    isSeries,
    onSelect,
  };
};

export default useModalState;
