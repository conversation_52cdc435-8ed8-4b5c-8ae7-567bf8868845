import Button from '@mindtickle/button';
import Search from '@mindtickle/search';
import SelectWithSearch from '@mindtickle/select-with-search';

import RemoteGroupFilter from '~/components/RemoteGroupFilter';

import { FiltersContainer } from './styles';

type LearnersTabFiltersPropType = {
  filtersConfig: any;
  moduleRelevanceEnabled: boolean;
  onReset: Function;
};

export default function LearnersTabFilters({
  filtersConfig,
  moduleRelevanceEnabled,
  onReset,
}: LearnersTabFiltersPropType) {
  return (
    <FiltersContainer>
      <SelectWithSearch
        buttonLabel={filtersConfig.INVITED_ON.placeholder}
        value={filtersConfig.INVITED_ON.value}
        isButton
        noneLabel={'All invited'}
        showSearch={false}
        onChange={filtersConfig.INVITED_ON.onChange}
        options={filtersConfig.INVITED_ON.options}
        className="invited-on"
      />
      <RemoteGroupFilter
        values={filtersConfig.GROUP_FILTER.values}
        onChange={filtersConfig.GROUP_FILTER.onChange}
      />
      {moduleRelevanceEnabled && (
        <SelectWithSearch
          buttonLabel={filtersConfig.MODULE_RELEVANCE.placeholder}
          value={filtersConfig.MODULE_RELEVANCE.value}
          isMulti
          isButton
          showSearch={false}
          onChange={filtersConfig.MODULE_RELEVANCE.onChange}
          options={filtersConfig.MODULE_RELEVANCE.options}
          className="module-relevance"
        />
      )}
      <Search
        placeholder={filtersConfig.SEARCH.placeholder}
        value={filtersConfig.SEARCH.value}
        onSearch={filtersConfig.SEARCH.onSearch}
      />
      <Button className="reset-button" type="text" onClick={onReset}>
        Reset filters
      </Button>
    </FiltersContainer>
  );
}
