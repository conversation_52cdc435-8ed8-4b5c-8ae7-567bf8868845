import styled from 'styled-components';

import { tokens, mixins, theme } from '@mindtickle/styles/lib';
export const StyledEventNameContainer = styled.div`
  display: flex;
  align-items: center;
  .go-back-icon {
    margin-right: 8px;
    cursor: pointer;
  }
  .event-name {
    ${mixins.h2()}
    max-width: 90%;
  }
  .event-tag {
    margin-left: 8px;
    padding: 2px 8px;
    border-radius: 4px;
    background: ${tokens.bgTokens.COLOR_BG_TERTIARY};
    ${mixins.activeBlackLink}
  }
`;

export const StyledEventDescriptionContainer = styled.div`
  display: flex;
  align-items: center;
  margin-left: 24px;
  margin-top: 12px;
  ${mixins.smallBlackLink()}
  .calendar-icon {
    margin-right: 8px;
  }
  .divider {
    border-left-color: ${tokens.textTokens.COLOR_TEXT_DISABLED};
  }
`;

export const EventEnrollmentDrawerTitleContainer = styled.div`
  display: flex;
  flex-direction: column;

  .toggle-container {
    display: flex;
    margin-top: 20px;
    .notify-text {
      ${mixins.fontStack({
        color: tokens.textTokens.COLOR_TEXT_DEFAULT,
        fontWeight: theme.fontWeight.SEMIBOLD,
        fontSize: theme.fontSizes.TEXT,
        lineHeight: theme.lineHeight.TEXT,
      })}
      margin-left: 10px;
    }
    .notify-icon {
      margin-left: 10px;
      padding-top: 3px;
      color: ${tokens.iconTokens.COLOR_ICON_DEFAULT};
      font-size: 14px;
    }
  }
  .info-container {
    background-color: ${tokens.bgTokens.COLOR_BG_WARNING};
    padding: 8px 8px;
    margin: 15px 510px 0px 0px;
    border-radius: 4px;

    .info-container-text {
      ${mixins.fontStack({
        color: tokens.textTokens.COLOR_TEXT_DEFAULT,
        fontWeight: theme.fontWeight.REGULAR,
        fontSize: theme.fontSizes.SMALLTEXT,
        lineHeight: theme.lineHeight.SMALLTEXT,
      })}
      margin-left: 10px;
    }
  }
`;
