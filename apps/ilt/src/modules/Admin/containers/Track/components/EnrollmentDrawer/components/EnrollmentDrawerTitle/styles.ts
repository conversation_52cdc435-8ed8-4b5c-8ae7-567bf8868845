import styled from 'styled-components';

import { tokens, mixins, theme } from '@mindtickle/styles/lib';

export const EnrollmentDrawerTitleContainer = styled.div`
  display: flex;
  flex-direction: column;

  .title-button {
    height: 25px;
    margin-left: 20px;
  }

  .manage-button-font {
    ${mixins.fontStack({
      color: tokens.textTokens.COLOR_TEXT_ACCENT,
      fontWeight: theme.fontWeight.SEMIBOLD,
      fontSize: theme.fontSizes.SMALLTEXT,
      lineHeight: theme.lineHeight.SMALLTEXT,
    })}
    padding: 2px 0;
  }

  .learner-name {
    display: flex;
    align-items: center;
    ${mixins.fontStack({
      color: tokens.textTokens.COLOR_TEXT_DEFAULT,
      fontWeight: theme.fontWeight.SEMIBOLD,
      fontSize: theme.fontSizes.H2,
      lineHeight: theme.lineHeight.H2,
    })}
    white-space: nowrap;
    margin-right: 20px;
  }

  .multipresent-container {
    display: flex;
    margin-top: 8px;
    .multipresent-text {
      ${mixins.fontStack({
        color: tokens.textTokens.COLOR_TEXT_SECONDARY,
        fontWeight: theme.fontWeight.REGULAR,
        fontSize: theme.fontSizes.TEXT,
        lineHeight: theme.lineHeight.TEXT,
      })}
    }
    .notify-icon {
      margin-left: 10px;
      padding-top: 3px;
      color: ${tokens.iconTokens.COLOR_ICON_DEFAULT};
      font-size: 14px;
    }
  }
  .toggle-container {
    display: flex;
    margin-top: 20px;
    .notify-text {
      ${mixins.fontStack({
        color: tokens.textTokens.COLOR_TEXT_DEFAULT,
        fontWeight: theme.fontWeight.SEMIBOLD,
        fontSize: theme.fontSizes.TEXT,
        lineHeight: theme.lineHeight.TEXT,
      })}
      margin-left: 10px;
    }
    .notify-icon {
      margin-left: 10px;
      padding-top: 3px;
      color: ${tokens.iconTokens.COLOR_ICON_DEFAULT};
      font-size: 14px;
    }
  }
  .info-container {
    background-color: ${tokens.bgTokens.COLOR_BG_WARNING};
    padding: 8px 8px;
    margin: 15px 510px 0px 0px;
    border-radius: 4px;

    .info-container-text {
      ${mixins.fontStack({
        color: tokens.textTokens.COLOR_TEXT_DEFAULT,
        fontWeight: theme.fontWeight.REGULAR,
        fontSize: theme.fontSizes.SMALLTEXT,
        lineHeight: theme.lineHeight.SMALLTEXT,
      })}
      margin-left: 10px;
    }
  }
`;
