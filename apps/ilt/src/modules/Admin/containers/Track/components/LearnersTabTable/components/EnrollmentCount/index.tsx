import { HIDE_ENROLL_ATTEND_FILTERS_AS_COULD_NOT_ADD_BE_SUPPORT_IN_MULTIDAY as HIDE_ENROL_ATTEND_FILTERS } from '~/modules/Admin/containers/Track/constants';

import { TableCellItem } from '../../styles';

import { TYPE_TO_DEFAULT_FILTERS_MAP } from './constants';

export default function EnrollmentCount({
  defaultFilterType,
  count,
  toggleSessionsDrawer,
  learnerId,
}: {
  defaultFilterType: number;
  count: number;
  toggleSessionsDrawer: Function;
  learnerId: string;
}) {
  return (
    <TableCellItem>
      <div
        className="enrollment-stat"
        onClick={event => {
          event.stopPropagation();
          toggleSessionsDrawer({
            show: true,
            learnerId,
            defaultFilters: HIDE_ENROL_ATTEND_FILTERS
              ? {}
              : TYPE_TO_DEFAULT_FILTERS_MAP[defaultFilterType],
          });
        }}
      >
        {count}
      </div>
    </TableCellItem>
  );
}
