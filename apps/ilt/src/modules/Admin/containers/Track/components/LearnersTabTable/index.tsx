import { useMemo } from 'react';

import IconWithGradient from '@mindtickle/icon-with-gradient';
import Table from '@mindtickle/table';

import { SORTING_ORDER } from '~/modules/Admin/config/track.constants';
import {
  LearnersTabTablePropType,
  LearnersTableDataType,
} from '~/modules/Admin/containers/Track/typeDefs';
import useMultidayEnabled from '~/modules/Admin/hooks/useMultidayEnabled';

import TrackActionBar from '../TrackActionBar';

import EnrollmentCount from './components/EnrollmentCount';
import InvitedOn from './components/InvitedOn';
import LearnerDetails from './components/LearnerDetails';
import LearnerOperations from './components/LearnerOperations';
import ManageEnrollmentButton from './components/ManageEnrollmentButton';
import Relevance from './components/Relevance';
import Score from './components/Score';
import SessionEnrolledWaitlisted from './components/SessionEnrolledWaitlisted';
import { TABLE_SORT_KEYS, getManageEnrollTableColumns } from './constants';
import { StyledLearnersTable } from './styles';

export default function LearnersTabTable({
  learnerActions,
  learnersMap,
  searchedLearners,
  isLearnersDataLoading,
  isMoreLearnersDataLoading,
  toggleSessionsDrawer,
  onSelect,
  onSelectAll,
  selectedLearners,
  onCloseActionBar,
  moduleRelevanceEnabled,
  localTimezoneName,
}: LearnersTabTablePropType) {
  const tableData: Array<LearnersTableDataType> = [];
  const { isMultidayEnabled } = useMultidayEnabled();
  const learnersList = searchedLearners ? searchedLearners.data : [];
  const hasMore = searchedLearners
    ? searchedLearners.start + searchedLearners.rows < searchedLearners.totalCount
    : false;
  const nextPagination = searchedLearners && {
    start: searchedLearners.start + searchedLearners.rows,
    rows: searchedLearners.rows,
  };
  const tableColumns = useMemo(
    () =>
      getManageEnrollTableColumns({
        isMultidayEnabled,
        includeRelevance: moduleRelevanceEnabled,
      }),
    [isMultidayEnabled, moduleRelevanceEnabled]
  );

  for (let index = 0; index < learnersList.length; index++) {
    const learnerId = learnersList[index];

    const learnerDetails = learnersMap[learnerId];

    tableData.push({
      key: `${learnerId}`,
      learnerDetails: (
        <LearnerDetails
          learner={{
            name: learnerDetails.name,
            email: learnerDetails.email,
            pic: learnerDetails?.pic,
          }}
        />
      ),
      relevance: <Relevance moduleRelevance={learnerDetails.moduleRelevance} />,
      invitedOn: <InvitedOn timeEpoch={learnerDetails.invitedOn} />,
      enrolled: (
        <EnrollmentCount
          defaultFilterType={0}
          count={learnerDetails.enrolledCount}
          toggleSessionsDrawer={toggleSessionsDrawer}
          learnerId={learnerId}
        />
      ),
      attended: (
        <EnrollmentCount
          defaultFilterType={1}
          count={learnerDetails.attendedCount}
          toggleSessionsDrawer={toggleSessionsDrawer}
          learnerId={learnerId}
        />
      ),
      waitlisted: (
        <EnrollmentCount
          defaultFilterType={2}
          count={learnerDetails.waitlistedCount}
          toggleSessionsDrawer={toggleSessionsDrawer}
          learnerId={learnerId}
        />
      ),
      sessionEnrolledWaitlisted: (
        <SessionEnrolledWaitlisted
          learnerDetails={learnerDetails}
          toggleSessionsDrawer={toggleSessionsDrawer}
          localTimezoneName={localTimezoneName}
        />
      ),
      score: <Score learnerScore={learnerDetails.maxScore} />,
      manageEnrollment: (
        <ManageEnrollmentButton toggleSessionsDrawer={toggleSessionsDrawer} learnerId={learnerId} />
      ),
      learnerOperations: (
        <LearnerOperations
          learnersMap={learnersMap}
          moduleRelevanceEnabled={moduleRelevanceEnabled}
          learnerActions={learnerActions}
          learnerId={learnerId}
        />
      ),
    });
  }

  const rowSelection = {
    onSelect: onSelect,
    onSelectAll: onSelectAll,
    selectedRowKeys: selectedLearners,
  };

  return (
    <StyledLearnersTable loading={isLearnersDataLoading}>
      <Table
        dataSource={!isLearnersDataLoading && tableData}
        columns={tableColumns}
        emptyTableData={
          <div className="no-data-wrapper">
            {!isLearnersDataLoading && (
              <div>
                <IconWithGradient gradient={true} type="noSearchResults" className="no-data-icon" />
                <div className="no-result-text">{'No results found'}</div>
              </div>
            )}
          </div>
        }
        onRow={(record: any) => ({
          onClick: () => {
            toggleSessionsDrawer({
              show: true,
              learnerId: record.key,
              defaultFilters: { SESSION_STATE: 'UPCOMING' },
            });
          },
        })}
        rowSelection={rowSelection}
        loaderType={isMoreLearnersDataLoading ? 'skeleton' : 'spin'}
        infiniteScroll={true}
        windowScroll
        isMultiSelect={true}
        fetchData={() => {
          learnerActions.pagination(nextPagination);
        }}
        loading={isLearnersDataLoading}
        loadingMore={isMoreLearnersDataLoading}
        hasMore={hasMore}
        threshold={0.9}
        pagination={false}
        pageSize={11}
        onChange={(_1: any, _2: any, { field, order }: { field: string; order: string }) => {
          learnerActions.sort({
            sort: {
              order: order === 'ascend' ? SORTING_ORDER.ASC : SORTING_ORDER.DESC,
              type: TABLE_SORT_KEYS[field as keyof typeof TABLE_SORT_KEYS],
            },
          });
        }}
        footer={
          <TrackActionBar
            learnersMap={learnersMap}
            moduleRelevanceEnabled={moduleRelevanceEnabled}
            learnerActions={learnerActions}
            toggleSessionsDrawer={toggleSessionsDrawer}
            selectedLearners={selectedLearners}
            onCloseActionBar={onCloseActionBar}
          />
        }
      />
    </StyledLearnersTable>
  );
}
