import React, { useCallback, useEffect, useMemo, useRef, useState, Fragment } from 'react';

import classnames from 'classnames';
import { useUserAuth } from 'ui_shell/Auth';

import Button from '@mindtickle/button';
import Collapse from '@mindtickle/collapse';
import Icon from '@mindtickle/icon';
import Menu from '@mindtickle/menu';

import { InviteModals } from './components/InviteModals';
import { ICON_TYPE, INVITE_TO } from './constants';
import useModalState from './hooks/useModalState';
import Wrapper from './style';
import {
  canAddNewLearners,
  getCallback,
  getInviteOptions,
  getModuleNameFromModuleType,
} from './utils';

import type {
  GetInviteOptionsReturnType,
  InviteLearnersProps,
  InviteOptions,
  OptionsType,
} from './typeDefs';

type MenuCallbackType = {
  key: string;
};

const parseOptionChildren = (subOptions: ReadonlyArray<InviteOptions>) =>
  subOptions.map(({ title, value }) => {
    let type = ICON_TYPE[title];
    return (
      <Menu.Item key={value} className="clearfix">
        <Icon type={type} className="linkIcon" />
        <div className="text">{title}</div>
      </Menu.Item>
    );
  });

const getOptions = ({
  options,
  onMenuSelection,
}: {
  options: ReadonlyArray<GetInviteOptionsReturnType>;
  onMenuSelection: ({ key }: MenuCallbackType) => void;
}): ReadonlyArray<OptionsType> =>
  options.map(option => ({
    header: option.title,
    value: option.value,
    content: option.children && (
      <Menu
        mode={'vertical'}
        selectable={false}
        onClick={({ key }: MenuCallbackType) => {
          onMenuSelection({ key });
          return { key: option.value, type: key };
        }}
      >
        {parseOptionChildren(option.children)}
      </Menu>
    ),
  }));

const InviteLearners: React.FC<InviteLearnersProps> = ({
  moduleType,
  placement,
  moduleId,
  seriesId,
  moduleRelevanceEnabled,
  defaultModuleRelevance,
  onComplete,
  moduleName,
}) => {
  const wrapperRef = useRef(null);
  const [activeKey, setActiveKey] = useState(INVITE_TO.MODULE);
  const [open, setOpen] = useState(false);
  const { permissions, isSiteOwner } = useUserAuth();
  const {
    onSelect,
    groupVisible,
    isSeries,
    setExistingVisible,
    setGroupVisible,
    setUploadVisible,
    showInviteExisting,
    uploadVisible,
  } = useModalState();

  const {
    // @ts-ignore
    company: { id },
  } = useUserAuth();

  const callback = getCallback({
    onComplete,
  });

  const btnClick = useCallback(() => {
    setOpen(!open);
    setActiveKey(INVITE_TO.MODULE);
  }, [open]);

  const onMenuSelection = useCallback(
    ({ key }: MenuCallbackType) => {
      if (activeKey) {
        onSelect(activeKey, key);
        setOpen(false);
      }
    },
    [activeKey, onSelect]
  );
  const options = useMemo(
    () =>
      getInviteOptions({
        moduleType: getModuleNameFromModuleType(moduleType),
        globalPermissions: permissions,
        isSiteOwner,
      }),
    [isSiteOwner, moduleType, permissions]
  );
  const canInviteNewLearners = useMemo(
    () => canAddNewLearners({ globalPermissions: permissions, isSiteOwner }),
    [isSiteOwner, permissions]
  );
  useEffect(() => {
    document.addEventListener('click', handleClickOutside, false);
    return () => {
      document.removeEventListener('click', handleClickOutside, false);
    };
  }, []);

  const handleClickOutside = (event: MouseEvent) => {
    // @ts-ignore
    if (wrapperRef.current && !wrapperRef.current.contains(event.target)) {
      setOpen(false);
    }
  };

  return (
    <Fragment>
      <Wrapper placement={placement} ref={wrapperRef}>
        <Button type="primary" onClick={btnClick}>
          Invite learners
        </Button>
        <Collapse
          className={classnames(
            'invite-track-collapse',
            'collapse',
            open ? 'showcollapse' : 'hidecollapse'
          )}
          onChange={(key: string) => {
            setActiveKey(key);
            return key;
          }}
          accordion={true}
          defaultActiveKey={activeKey}
          activeKey={activeKey}
          options={getOptions({ options, onMenuSelection })}
        />
      </Wrapper>
      <InviteModals
        moduleId={moduleId}
        seriesId={seriesId}
        moduleType={moduleType}
        moduleName={moduleName}
        callback={callback}
        companyId={id}
        moduleRelevanceEnabled={moduleRelevanceEnabled}
        isSeries={isSeries}
        addNewLearnerAllowed={canInviteNewLearners}
        defaultModuleRelevance={defaultModuleRelevance}
        setExistingVisible={setExistingVisible}
        showInviteExisting={showInviteExisting}
        uploadVisible={uploadVisible}
        setUploadVisible={setUploadVisible}
        groupVisible={groupVisible}
        setGroupVisible={setGroupVisible}
      />
    </Fragment>
  );
};

export default InviteLearners;
