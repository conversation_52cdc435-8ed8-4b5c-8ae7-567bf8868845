import UserThumbnail from '@mindtickle/user-thumbnail';

import { StyledUserNode } from './styles';

const LearnerDetails = ({
  learner,
}: {
  learner: {
    name: string;
    email: string;
    pic: string;
  };
}) => (
  <StyledUserNode>
    <UserThumbnail
      avatarProps={{ size: 'large' }}
      name={learner.name}
      email={learner.email}
      src={learner?.pic}
      className="user-node"
    />
  </StyledUserNode>
);

export default LearnerDetails;
