import styled from 'styled-components';

import { tokens, mixins, theme } from '@mindtickle/styles/lib';

export const EnrollmentStatusTextContainer = styled.div`
  .waitlisted {
    width: 59px;
    height: 16px;

    ${mixins.fontStack({
      color: tokens.textTokens.COLOR_TEXT_WARNING,
      fontWeight: theme.fontWeight.SEMIBOLD,
      fontSize: theme.fontSizes.SMALLTEXT,
      lineHeight: theme.lineHeight.SMALLTEXT,
    })}
    cursor: default;
  }
  .enrolled {
    width: 48px;
    height: 16px;

    ${mixins.fontStack({
      color: tokens.chartTokens.COLOR_CHART_HUNTER,
      fontWeight: theme.fontWeight.SEMIBOLD,
      fontSize: theme.fontSizes.SMALLTEXT,
      lineHeight: theme.lineHeight.SMALLTEXT,
    })}
    cursor: default;
  }

  .not-enrolled {
    width: 73px;
    height: 16px;

    ${mixins.fontStack({
      color: tokens.textTokens.COLOR_TEXT_DEFAULT,
      fontWeight: theme.fontWeight.SEMIBOLD,
      fontSize: theme.fontSizes.SMALLTEXT,
      lineHeight: theme.lineHeight.SMALLTEXT,
    })}
    cursor: default;
  }
`;

export const EnrollmentStatusButtonsContainer = styled.div`
  display: flex;
  flex-direction: column-reverse;
  justify-content: center;
  position: relative;
  height: 68px;

  .enrollment-status-wrapper {
    padding: 2px 0;
    display: flex;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    .default-button {
      ${mixins.fontStack({
        fontWeight: theme.fontWeight.SEMIBOLD,
        fontSize: theme.fontSizes.SMALLTEXT,
        lineHeight: theme.lineHeight.SMALLTEXT,
      })}
      height: 24px;
      margin: 0px 4px;
    }

    .inactive-button {
      width: 79px;
      cursor: pointer;

      color: ${tokens.textTokens.COLOR_TEXT_TERTIARY};
      background: #ffffff;
      border: 1px solid #e8eaed;
      border-radius: 24px;
    }

    .not-enrolled-active {
      width: 110px;
      cursor: pointer;

      color: ${tokens.iconTokens.COLOR_ICON_DEFAULT} !important;
      background: ${tokens.bgTokens.COLOR_BG_TERTIARY} !important;
      border: 1px solid ${tokens.borderTokens.COLOR_BORDER_STRONG};
      border-radius: 24px;

      .tick-color {
        color: ${tokens.iconTokens.COLOR_ICON_DEFAULT};
        transform: scale(0.8);
        margin-left: 4px;
        padding-top: 2px;
      }
    }

    .enrolled-active {
      width: 92px;
      cursor: pointer;

      color: ${tokens.textTokens.COLOR_TEXT_SUCCESS} !important;
      background: ${tokens.bgTokens.COLOR_BG_SUCCESS} !important;
      border: 1px solid ${tokens.borderTokens.COLOR_BORDER_SUCCESS};
      border-radius: 24px;

      .tick-color {
        color: ${tokens.iconTokens.COLOR_ICON_SUCCESS};
        transform: scale(0.8);
        margin-left: 4px;
        padding-top: 2px;
      }
    }

    .waitlisted-active {
      width: 100px;
      cursor: pointer;

      color: ${tokens.textTokens.COLOR_TEXT_WARNING} !important;
      background: ${tokens.bgTokens.COLOR_BG_WARNING} !important;
      border: 1px solid ${tokens.bgTokens.COLOR_BG_WARNING_STRONG};
      border-radius: 24px;

      .tick-color {
        color: ${tokens.iconTokens.COLOR_ICON_WARNING};
        transform: scale(0.8);
        margin-left: 4px;
        padding-top: 2px;
      }
    }

    .updating {
      cursor: pointer;
      color: ${tokens.textTokens.COLOR_TEXT_TERTIARY} !important;
      background: ${tokens.bgTokens.COLOR_BG_DEFAULT};
      border: 1px solid ${tokens.borderTokens.COLOR_BORDER_SECONDARY};
      border-radius: 24px;
    }

    .not-enrolled-updating {
      width: 110px;
    }

    .enrolled-updating {
      width: 95px;
    }

    .waitlisted-updating {
      width: 105px;
    }

    .failed-button {
      width: 79px;
      cursor: pointer;

      color: ${tokens.textTokens.COLOR_TEXT_TERTIARY};
      background: #ffffff;
      border: 1px solid ${tokens.borderTokens.COLOR_BORDER_DANGER};
      border-radius: 24px;
    }

    .disabled-button {
      color: #bbbbbb;
      cursor: default;
    }

    .button-with-tooltip {
      background-color: ${tokens.bgTokens.COLOR_BG_SECONDARY};
    }

    .loader-wrapper {
      transform: scale(0.6);
    }

    .loader-scale {
      width: 15px;
      height: 15px;
      margin: -7.5px;
    }

    .message-wrapper-updating {
      display: flex;
      justify-content: flex-start;

      .updating-text {
        margin-left: 12px;
        margin-right: 8px;
      }
    }

    .message-wrapper {
      display: flex;
      justify-content: center;
    }

    .tick-thick {
      color: ${tokens.iconTokens.COLOR_ICON_SUCCESS};
    }
  }

  .no-data-wrapper {
    padding: 200px 0;
    .noDataIcon {
      margin: 0 auto;
    }
  }

  .feedback-text-wrapper {
    padding: 2px 0;
    width: 320px;
    margin-top: 16%;
  }
  .feedback-text-success {
    ${mixins.fontStack({
      color: tokens.textTokens.COLOR_TEXT_SECONDARY,
      fontWeight: theme.fontWeight.REGULAR,
      fontSize: theme.fontSizes.SMALLTEXT,
      lineHeight: theme.lineHeight.SMALLTEXT,
    })}
    float: left;
    .feedback-not-enrolled {
      margin-left: 15px;
    }
    .feedback-enrolled {
      margin-left: 90px;
    }
    .feedback-waitlisted {
      margin-left: 178px;
    }
  }
  .feedback-text-failed {
    ${mixins.fontStack({
      color: tokens.textTokens.COLOR_TEXT_DANGER,
      fontWeight: theme.fontWeight.REGULAR,
      fontSize: theme.fontSizes.SMALLTEXT,
      lineHeight: theme.lineHeight.SMALLTEXT,
    })}
    float: left;
    .feedback-not-enrolled {
      margin-left: 28px;
    }
    .feedback-enrolled {
      margin-left: 111px;
    }
    .feedback-enrolled-extra {
      margin-left: 142px;
    }
    .feedback-waitlisted {
      margin-left: 206px;
    }
    .feedback-waitlisted-extra {
      margin-left: 224px;
    }
  }

  .unenroll-reset-text {
    ${mixins.fontStack({
      color: tokens.textTokens.COLOR_TEXT_SECONDARY,
      fontWeight: theme.fontWeight.REGULAR,
      fontSize: theme.fontSizes.TEXT,
      lineHeight: theme.lineHeight.TEXT,
    })}
    margin-top: 10px;
  }
`;

export const StyledEnrollButtonContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  .event-enroll-count {
    ${mixins.smallDarkLink()}
  }
`;
