import styled from 'styled-components';

import { flexContainer } from '../../../../styles/mixins';

export const FiltersContainer = styled.div`
  ${flexContainer({})};
  padding: 24px 24px;

  .invited-on {
    .css-xu0k1y-menu {
      width: 240px;
    }
  }
  .reset-button {
    font-size: 12px;
    padding-left: 14px;
  }

  .search-bar {
    font-size: 12px !important;
  }

  .footer-btn-wrp {
    padding-top: 0 !important;
    border-top: unset !important;
  }

  .checkbox-wrp {
    padding: 6px 16px !important;
  }

  & > * {
    margin-right: 10px;
  }
`;
