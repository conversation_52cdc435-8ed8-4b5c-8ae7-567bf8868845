import { LearnersMapType } from '../../typeDefs';

export type TRelevanceModalRadioGroup = {
  cancelButtonText: string;
  okButtonText: string;
  onOk: Function;
  value: any;
  setValue: (value: any) => void;
  onCancel: Function;
  defaultModuleRelevance: string;
};

export type TRelevanceModal = {
  onModuleRelevanceSelection: Function;
  onModuleRelevanceClose: Function;
  learnersForModuleRelevanceChange: Array<string>;
  learnersMap: LearnersMapType;
  defaultModuleRelevance: string;
};
