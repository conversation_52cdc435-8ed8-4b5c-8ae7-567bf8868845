import { useRef, useEffect, useCallback } from 'react';

import classnames from 'classnames';
import { FormattedMessage } from 'react-intl';
import { injectIntl } from 'react-intl';

import Modal, { MODAL_SIZE } from '@mindtickle/modal';
import { isUndefined } from '@mindtickle/utils';

import { MASKED_MODAL_INDEX } from '~/config/constants';
import {
  ENROLL_DRAWER_ENROLL_STATUS,
  DRAWER_ENROLL_STATUS_ACTION_MAP,
  DRAWER_ENROLL_STATUS_ACTIONS,
  ENROLLMENT_BUTTON_TYPES,
  ENROLL_DRAWER_ATTENDANCE_STATUS,
  SESSION_STATUS_TYPES,
  ENROLL_DRAWER_ENROLLMENT_MAP,
  ENTITY_TYPE_FOR_MESSAGE,
} from '~/modules/Admin/config/track.constants';
import { EVENT_ENROLLMENT_ENUM } from '~/modules/Admin/constants/events';
import ENR<PERSON><PERSON>_DRAWER_MESSAGES from '~/modules/Admin/messages/track/enrollDrawer';
import messages from '~/modules/Admin/messages/track/eventEnrollDrawer';
import { checkIsEvent, checkIsSessionWithinEvent } from '~/modules/Admin/utils/checkEntityType';

import EnrollmentButton from './components/EnrollmentButton';
import FeedbackMessage from './components/FeedbackMessage';
import {
  EnrollmentStatusTextContainer,
  EnrollmentStatusButtonsContainer,
  StyledEnrollButtonContainer,
} from './styles';

import type { EnrollmentStatusWrapperType } from '../../typeDefs';
import type { InjectedIntlProps } from 'react-intl';

function EnrollmentStatusWrapper({
  sessionId,
  parentId,
  entityType,
  enrollmentStatus = '',
  drawerModifyEnroll,
  sessionStatus = '',
  maxSeatEnabled,
  waitingListEnabled,
  autoEnroll,
  maxSeats,
  enrolledCount,
  selectedLearnersCount,
  attendanceStatus = '',
  multipleLearners,
  handleEnrollmentChange,
  sessionEnrollOperationStatus,
  notifyLearners,
  activeButtonObj = { enrollmentType: '', showFeedback: false },
  updateActiveButtonMap,
  updateEnrollmentMap,
  learnerName,
  enrolledSessionsCount,
  totalNumberOfPublishedSessions,
  isEventEnrollDrawerActive,
  isSessionWithinEventOperationInProgress,
  setSessionWithinEventOperationInProgress,
  intl,
}: EnrollmentStatusWrapperType & InjectedIntlProps) {
  const rowRef = useRef(null);
  const prevEnrollOperationStatus = useRef({
    complete: true,
    hasError: false,
    isUpdating: false,
  });

  useEffect(() => {
    if (
      !prevEnrollOperationStatus.current.complete &&
      !sessionEnrollOperationStatus.hasError &&
      sessionEnrollOperationStatus.complete
    ) {
      const prevEnrollmentStatus = enrollmentStatus;
      const updatedEnrollmentStatus =
        ENROLL_DRAWER_ENROLLMENT_MAP[
          activeButtonObj.enrollmentType as keyof typeof ENROLL_DRAWER_ENROLLMENT_MAP
        ];
      updateEnrollmentMap({
        sessionId,
        enrollmentStatus: updatedEnrollmentStatus,
        prevEnrollmentStatus,
      });
    }
    prevEnrollOperationStatus.current = sessionEnrollOperationStatus;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sessionEnrollOperationStatus]);

  useEffect(() => {
    const isCurrentRowUpdating =
      isEventEnrollDrawerActive && sessionEnrollOperationStatus.isUpdating;
    if (isCurrentRowUpdating) {
      setSessionWithinEventOperationInProgress(true);
    }
    return () => {
      if (isCurrentRowUpdating) {
        setSessionWithinEventOperationInProgress(false);
      }
    };
  }, [
    isEventEnrollDrawerActive,
    sessionEnrollOperationStatus,
    setSessionWithinEventOperationInProgress,
  ]);

  const getTooltipText = useCallback(
    ({ enrollmentType }: { enrollmentType: string }) => {
      let tooltipText = '';
      if (enrollmentType === activeButtonObj.enrollmentType) return tooltipText;

      if (maxSeatEnabled && waitingListEnabled && autoEnroll) {
        switch (enrollmentType) {
          case DRAWER_ENROLL_STATUS_ACTIONS.ENROLL:
            tooltipText = intl.formatMessage(ENROLL_DRAWER_MESSAGES.AUTO_ENROLL_ENROLL, {
              entityType:
                ENTITY_TYPE_FOR_MESSAGE[entityType as keyof typeof ENTITY_TYPE_FOR_MESSAGE],
            });
            break;
          case DRAWER_ENROLL_STATUS_ACTIONS.UN_ENROLL:
            tooltipText = intl.formatMessage(ENROLL_DRAWER_MESSAGES.AUTO_ENROLL_UN_ENROLL, {
              entityType:
                ENTITY_TYPE_FOR_MESSAGE[entityType as keyof typeof ENTITY_TYPE_FOR_MESSAGE],
            });
            break;
          case DRAWER_ENROLL_STATUS_ACTIONS.MOVE_TO_WAITING:
            tooltipText = intl.formatMessage(ENROLL_DRAWER_MESSAGES.AUTO_ENROLL_MOVE_TO_WAITING, {
              entityType:
                ENTITY_TYPE_FOR_MESSAGE[entityType as keyof typeof ENTITY_TYPE_FOR_MESSAGE],
            });
            break;
        }
      } else if (
        enrollmentType === DRAWER_ENROLL_STATUS_ACTIONS.ENROLL &&
        maxSeats &&
        maxSeatEnabled
      ) {
        if (maxSeats <= enrolledCount) {
          tooltipText = intl.formatMessage(ENROLL_DRAWER_MESSAGES.SEATS_FULL);
        } else if (selectedLearnersCount > maxSeats - enrolledCount) {
          tooltipText = `Available seats: ${
            maxSeats - enrolledCount
          }. Increase seat limit to enroll more learners.`;
        }
      } else if (DRAWER_ENROLL_STATUS_ACTIONS.MOVE_TO_WAITING === enrollmentType) {
        if (!waitingListEnabled) {
          tooltipText = intl.formatMessage(ENROLL_DRAWER_MESSAGES.WAITING_LIST_DISABLED, {
            entityType: ENTITY_TYPE_FOR_MESSAGE[entityType as keyof typeof ENTITY_TYPE_FOR_MESSAGE],
          });
        }
      }
      return tooltipText;
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps -- excluded intl
    [
      activeButtonObj,
      maxSeats,
      maxSeatEnabled,
      enrolledCount,
      waitingListEnabled,
      autoEnroll,
      selectedLearnersCount,
      entityType,
    ]
  );

  const handleButtonClick = ({ enrollmentType }: { enrollmentType: string }) => {
    const isEvent = checkIsEvent(entityType);
    const isSessionWithinEvent = checkIsSessionWithinEvent(entityType);
    const enrollTypeText =
      ENROLLMENT_BUTTON_TYPES[enrollmentType as keyof typeof ENROLLMENT_BUTTON_TYPES].initialText;
    const enrolmentLevel = isEventEnrollDrawerActive
      ? EVENT_ENROLLMENT_ENUM.EVENT
      : isSessionWithinEvent
      ? EVENT_ENROLLMENT_ENUM.SESSIONS
      : undefined;
    if (isEvent) {
      handleEnrollmentChange({ enrollmentType, eventId: sessionId });
      updateActiveButtonMap({ sessionId, enrollmentType, showFeedback: notifyLearners });
    } else {
      if (
        isEventEnrollDrawerActive &&
        enrollmentStatus === ENROLL_DRAWER_ENROLLMENT_MAP.MOVE_TO_WAITING
      ) {
        Modal.confirm({
          centered: true,
          content: (
            <>
              <FormattedMessage
                {...messages.REMOVE_FROM_WAITING_LIST_WARNING_ONE}
                values={{
                  enrollmentType: enrollTypeText.toLowerCase(),
                }}
              />
              <br />
              <br />
              <FormattedMessage
                {...messages.REMOVE_FROM_WAITING_LIST_WARNING_TWO}
                values={{
                  enrollmentType: enrollTypeText.toLowerCase(),
                }}
              />
            </>
          ),
          onOk: () => {
            handleEnrollmentChange({
              enrollmentType,
              sessionId,
              eventId: parentId,
              enrolmentLevel,
            });
            updateActiveButtonMap({ sessionId, enrollmentType, showFeedback: notifyLearners });
          },
          okButtonText: enrollTypeText,
          title: <FormattedMessage {...messages.CHANGE_ENROLLMENT_STATUS_MODAL_TITLE} />,
          zIndex: MASKED_MODAL_INDEX,
          size: MODAL_SIZE.SMALL,
        });
      } else {
        handleEnrollmentChange({ enrollmentType, sessionId, eventId: parentId, enrolmentLevel });
        updateActiveButtonMap({ sessionId, enrollmentType, showFeedback: notifyLearners });
      }
    }
  };

  const checkOperationSucceeded = useCallback(
    ({ enrollmentType }: { enrollmentType: string }) =>
      activeButtonObj.enrollmentType === enrollmentType &&
      sessionEnrollOperationStatus.complete &&
      !sessionEnrollOperationStatus.hasError,
    [activeButtonObj, sessionEnrollOperationStatus]
  );

  const checkOperationRunning = useCallback(
    ({ enrollmentType }: { enrollmentType: string }) =>
      activeButtonObj.enrollmentType === enrollmentType && sessionEnrollOperationStatus.isUpdating,
    [activeButtonObj, sessionEnrollOperationStatus]
  );

  const checkOperationFailed = useCallback(
    ({ enrollmentType }: { enrollmentType: string }) =>
      activeButtonObj.enrollmentType === enrollmentType && sessionEnrollOperationStatus.hasError,
    [activeButtonObj, sessionEnrollOperationStatus]
  );

  const checkPopConfirmEnabled = useCallback(
    ({ enrollmentType }: { enrollmentType: string }) =>
      !getTooltipText({ enrollmentType }) &&
      !multipleLearners &&
      enrollmentType === DRAWER_ENROLL_STATUS_ACTIONS.UN_ENROLL &&
      sessionStatus === SESSION_STATUS_TYPES.PAST &&
      (attendanceStatus === ENROLL_DRAWER_ATTENDANCE_STATUS.ATTENDED.value ||
        attendanceStatus === ENROLL_DRAWER_ATTENDANCE_STATUS.NOT_ATTENDED.value),
    [getTooltipText, multipleLearners, sessionStatus, attendanceStatus]
  );

  const checkButtonDisabled = useCallback(
    ({ enrollmentType }: { enrollmentType: string }) => {
      if (isEventEnrollDrawerActive && isSessionWithinEventOperationInProgress) {
        return true;
      }

      return (
        sessionEnrollOperationStatus.isUpdating ||
        (enrollmentType === activeButtonObj.enrollmentType &&
          !checkOperationFailed({ enrollmentType })) ||
        !!getTooltipText({ enrollmentType })
      );
    },
    [
      isEventEnrollDrawerActive,
      isSessionWithinEventOperationInProgress,
      sessionEnrollOperationStatus.isUpdating,
      activeButtonObj.enrollmentType,
      checkOperationFailed,
      getTooltipText,
    ]
  );

  const enrollStatusType =
    DRAWER_ENROLL_STATUS_ACTION_MAP[
      enrollmentStatus as keyof typeof DRAWER_ENROLL_STATUS_ACTION_MAP
    ];
  const displayTextSingle =
    ENROLL_DRAWER_ENROLL_STATUS[enrollStatusType as keyof typeof ENROLL_DRAWER_ENROLL_STATUS]
      ?.displayText;

  const enrollmentButtonTypes = Object.keys(ENROLLMENT_BUTTON_TYPES);

  return multipleLearners || drawerModifyEnroll ? (
    <EnrollmentStatusButtonsContainer ref={rowRef}>
      <div className="feedback-text-wrapper">
        {enrollmentButtonTypes.map(enrollmentType => (
          <FeedbackMessage
            key={`${sessionId}-feedbackMessage-${enrollmentType}`}
            enrollmentType={enrollmentType}
            checkOperationFailed={checkOperationFailed}
            checkOperationSucceeded={checkOperationSucceeded}
            activeButtonObj={activeButtonObj}
            enrollmentStatus={enrollmentStatus}
          />
        ))}
      </div>
      <div className={classnames(`${sessionId}-enrollmentButton`, 'enrollment-status-wrapper')}>
        {enrollmentButtonTypes.map(enrollmentType => (
          <StyledEnrollButtonContainer key={`${sessionId}-enrollmentButton-${enrollmentType}`}>
            {(!isEventEnrollDrawerActive ||
              enrollmentType !== DRAWER_ENROLL_STATUS_ACTIONS.MOVE_TO_WAITING ||
              enrollmentStatus === ENROLL_DRAWER_ENROLLMENT_MAP.MOVE_TO_WAITING) && (
              <EnrollmentButton
                enrollmentType={enrollmentType}
                initialText={
                  ENROLLMENT_BUTTON_TYPES[enrollmentType as keyof typeof ENROLLMENT_BUTTON_TYPES]
                    .initialText
                }
                updatingText={
                  ENROLLMENT_BUTTON_TYPES[enrollmentType as keyof typeof ENROLLMENT_BUTTON_TYPES]
                    .updatingText
                }
                updatedText={
                  ENROLLMENT_BUTTON_TYPES[enrollmentType as keyof typeof ENROLLMENT_BUTTON_TYPES]
                    .updatedText
                }
                checkOperationSucceeded={checkOperationSucceeded}
                checkOperationRunning={checkOperationRunning}
                checkOperationFailed={checkOperationFailed}
                getTooltipText={getTooltipText}
                checkButtonDisabled={checkButtonDisabled}
                checkPopConfirmEnabled={checkPopConfirmEnabled}
                handleButtonClick={handleButtonClick}
                sessionId={sessionId}
                activeButtonObj={activeButtonObj}
                enrollmentStatus={enrollmentStatus}
                learnerName={learnerName}
                rowRef={rowRef}
              />
            )}
            {enrollmentType === DRAWER_ENROLL_STATUS_ACTION_MAP.ENROLLED &&
              !isUndefined(enrolledSessionsCount) &&
              enrolledSessionsCount !== 0 &&
              enrolledSessionsCount !== totalNumberOfPublishedSessions && (
                <span className="event-enroll-count">{`in ${enrolledSessionsCount}/${totalNumberOfPublishedSessions} sessions`}</span>
              )}
          </StyledEnrollButtonContainer>
        ))}
      </div>
    </EnrollmentStatusButtonsContainer>
  ) : (
    <EnrollmentStatusTextContainer ref={rowRef} className="enrollment-status-wrapper">
      <div>
        <div className={classnames('floatL', 'marginR10')}>
          <div
            className={classnames({
              'not-enrolled': enrollStatusType === DRAWER_ENROLL_STATUS_ACTIONS.UN_ENROLL,
              enrolled: enrollStatusType === DRAWER_ENROLL_STATUS_ACTIONS.ENROLL,
              waitlisted: enrollStatusType === DRAWER_ENROLL_STATUS_ACTIONS.MOVE_TO_WAITING,
            })}
          >
            {displayTextSingle}
          </div>
        </div>
      </div>
    </EnrollmentStatusTextContainer>
  );
}

export default injectIntl(EnrollmentStatusWrapper);
