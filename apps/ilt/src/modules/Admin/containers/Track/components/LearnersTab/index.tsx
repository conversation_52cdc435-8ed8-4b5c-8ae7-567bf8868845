import React, { useState, useMemo } from 'react';

import classnames from 'classnames';
import { FormattedMessage } from 'react-intl';

import Modal from '@mindtickle/modal';
import Tooltip from '@mindtickle/tooltip';

import ExportReportModalContent from '~/modules/Admin/components/ExportReportModalContent';
import { ILT_CALENDAR_AUTO_SYNC_CONFIG_KEY } from '~/modules/Admin/config/sessions.constants';
import {
  ENROLL_DRAWER_BOTTOM_MENU,
  ENROLL_DRAWER_LISTING_MENU,
} from '~/modules/Admin/constants/track';
import { useILTAdminSnowplowTracker } from '~/modules/Admin/hooks/useILTAdminSnowplowTracker';
import { MIXPANEL_UI_EVENTS } from '~/modules/Admin/hooks/useILTAdminSnowplowTracker/constants';
import messages from '~/modules/Admin/messages/track';

import EnrollmentDrawer from '../EnrollmentDrawer';
import GoToPublish from '../GoToPublish';
import InviteLearners from '../InviteComponent';
import LearnersTabFilters from '../LearnersTabFilters';
import LearnersTabTable from '../LearnersTabTable';

import { ENROLLMENT_DEFAULT_FILTERS } from './constants';
import LearnersTabContainer from './styles';

import type { THandleEnrollmentMixpanel } from './typeDefs';
import type { LearnersTabPropType } from '../../typeDefs';

export default function LearnersTab({
  learnerActions,
  learnersMap,
  searchedLearners,
  onResetFilters,
  filtersConfig,
  isLearnersDataLoading,
  isMoreLearnersDataLoading,
  context,
  staticData,
  learnerEntities,
  learnerSessionsWithinEvent,
  learnerEntitiesDataStatus,
  learnerSessionsWithinEventDataStatus,
  enrollOperationStatus,
  learnerEntitiesInfo,
  extraConfig = {},
  selectedLearners,
  onCloseActionBar,
  moduleRelevanceEnabled,
  trackerPageName,
  onInviteComplete,
  localTimezoneName,
}: LearnersTabPropType) {
  const [enrollmentDrawerStates, setEnrollmentDrawerStates] = useState<any>({
    showEnrollDrawer: false,
    drawerModifyEnroll: false,
    activeLearnerId: null,
  });

  const [enrollmentDrawerDefaultFilters, setEnrollmentDrawerDefaultFilters] = useState<any>(
    ENROLLMENT_DEFAULT_FILTERS
  );

  const [exportEmail, setExportEmail] = useState<string>('');
  const [exportModalVisible, setExportModalVisible] = useState<boolean>(false);

  const tracker = useILTAdminSnowplowTracker();

  const handleEnrollmentDrawerMixpanel = ({
    show: isExpanded,
    modify,
    learnerId,
    fromActionBar = false,
  }: THandleEnrollmentMixpanel) => {
    const location = fromActionBar ? ENROLL_DRAWER_BOTTOM_MENU : ENROLL_DRAWER_LISTING_MENU;
    const learnerIds = learnerId ? [learnerId] : selectedLearners;

    const mixpanelData = {
      learner_id: learnerIds,
      count_learners: learnerIds.length,
      location,
    };

    if (isExpanded) {
      tracker.trackStructuredEvent({
        eventName: MIXPANEL_UI_EVENTS.MODULE_ILT_LEARNER_ENROLLMENT_SECTION_EXPANDED,
        ...mixpanelData,
      });
    } else {
      tracker.trackStructuredEvent({
        eventName: MIXPANEL_UI_EVENTS.MODULE_ILT_LEARNER_ENROLLMENT_SECTION_COLLAPSED,
      });
    }

    if (modify) {
      tracker.trackStructuredEvent({
        eventName: MIXPANEL_UI_EVENTS.MODULE_ILT_LEARNER_MODIFY_ENROLLMENT_STATUS_CLICKED,
        ...mixpanelData,
      });
    }
  };

  const areLearnersInvited = useMemo(
    () => staticData.invitedLearnersCountLoaded && staticData.invitedLearnersCount > 0,
    [staticData.invitedLearnersCount, staticData.invitedLearnersCountLoaded]
  );

  const { moduleId, seriesId, moduleType } = context;

  const headingWrapper = () => (
    <div className="filter-row">
      <div className="page-header">
        {areLearnersInvited && (
          <React.Fragment>
            <div className="invite-count-text">
              <span className="invite-count">{staticData.invitedLearnersCount}</span>
              <span className="invite-text">{`Invited learner${
                staticData.invitedLearnersCount > 1 ? 's' : ''
              }`}</span>
            </div>

            <div className="header-title">
              <span>{'Click'}</span>
              <span className="bold-text">{' manage '}</span>
              <span>{`to enroll, unenroll or waitlist learners individually or perform bulk actions by selecting multiple learners.`}</span>
            </div>
          </React.Fragment>
        )}
      </div>

      <div className="analytics-invite-buttons-wrapper">
        <div className="invite-learners">
          <InviteLearners
            moduleRelevanceEnabled={moduleRelevanceEnabled}
            moduleType={moduleType}
            moduleId={moduleId}
            seriesId={seriesId}
            defaultModuleRelevance={staticData?.moduleRelevance}
            onComplete={onInviteComplete}
            moduleName={staticData?.name}
          />
        </div>
      </div>
    </div>
  );

  const filtersWrapper = () => (
    <div className={classnames('filter-row', 'bottom-border')}>
      <LearnersTabFilters
        filtersConfig={filtersConfig}
        moduleRelevanceEnabled={moduleRelevanceEnabled}
        onReset={onResetFilters}
      />
      <Tooltip
        key={`export-report-tooltip`}
        overlayStyle={{ zIndex: 10, width: '200px' }}
        title={<FormattedMessage {...messages.EXPORT_REPORT_TOOLTIP_TEXT} />}
        placement={'topRight'}
        trigger={'hover'}
      >
        <div
          className="export-report"
          onClick={() => {
            setExportModalVisible(true);
            learnerActions.onExportLearnersModalOpen();
          }}
        >
          {'Email report'}
        </div>
      </Tooltip>
      <Modal
        visible={exportModalVisible}
        title={'Email report of invited learners'}
        size={'small'}
        onCancel={() => setExportModalVisible(false)}
        bodyStyle={{ padding: 0 }}
      >
        <ExportReportModalContent
          cancelButtonText={'Cancel'}
          okButtonText={'Email report'}
          onOk={() => {
            learnerActions.exportLearners({ email: exportEmail });
            setExportModalVisible(false);
          }}
          onCancel={() => setExportModalVisible(false)}
          email={exportEmail}
          setEmail={setExportEmail}
        />
      </Modal>
    </div>
  );

  const tableWrapper = () => (
    <LearnersTabTable
      learnerActions={learnerActions}
      learnersMap={learnersMap}
      searchedLearners={searchedLearners}
      isLearnersDataLoading={isLearnersDataLoading}
      isMoreLearnersDataLoading={isMoreLearnersDataLoading}
      toggleSessionsDrawer={toggleSessionsDrawer}
      onSelect={learnerActions.onSelect}
      onSelectAll={learnerActions.onSelectAll}
      selectedLearners={selectedLearners}
      onCloseActionBar={onCloseActionBar}
      moduleRelevanceEnabled={moduleRelevanceEnabled}
      localTimezoneName={localTimezoneName}
    />
  );

  const toggleSessionsDrawer = ({
    show,
    modify,
    learnerId,
    defaultFilters,
    fromActionBar,
    isEventEnrollDrawerActive,
  }: any) => {
    if (!show) {
      learnerActions.getUpdatedLearners();
      if (isEventEnrollDrawerActive) {
        learnerActions.clearEventEnrollmentDrawerData();
      }
      learnerActions.clearEnrollmentDrawerData();
      setEnrollmentDrawerDefaultFilters(ENROLLMENT_DEFAULT_FILTERS);
    } else {
      setEnrollmentDrawerDefaultFilters({
        ...enrollmentDrawerDefaultFilters,
        ...defaultFilters,
      });
    }

    setEnrollmentDrawerStates({
      showEnrollDrawer: show,
      drawerModifyEnroll: modify,
      activeLearnerId: learnerId ? learnerId : null,
    });

    handleEnrollmentDrawerMixpanel({ show, modify, learnerId, fromActionBar });
  };

  const toggleModifyMode = (modify: boolean) => {
    setEnrollmentDrawerStates({ ...enrollmentDrawerStates, drawerModifyEnroll: modify });
  };

  const enrollmentDrawer = () => {
    const { showEnrollDrawer, activeLearnerId, drawerModifyEnroll } = enrollmentDrawerStates;

    const isCalendarAutoSyncEnabled =
      ILT_CALENDAR_AUTO_SYNC_CONFIG_KEY in extraConfig
        ? extraConfig.isILTSessionCalendarAutoSyncEnabled
        : true;

    return (
      showEnrollDrawer && (
        <EnrollmentDrawer
          drawerModifyEnroll={drawerModifyEnroll}
          learnersMap={learnersMap}
          learnerIds={activeLearnerId ? [activeLearnerId] : selectedLearners}
          activeLearnerId={selectedLearners.length === 1 ? selectedLearners[0] : activeLearnerId}
          learnerEntities={learnerEntities}
          learnerSessionsWithinEvent={learnerSessionsWithinEvent}
          toggleSessionsDrawer={toggleSessionsDrawer}
          toggleModifyMode={toggleModifyMode}
          defaultFilters={enrollmentDrawerDefaultFilters}
          multipleLearners={drawerModifyEnroll && !activeLearnerId && selectedLearners.length > 1}
          changeEnrollStatus={learnerActions.changeEnrollStatus}
          fetchDrawerData={learnerActions.fetchDrawerData}
          fetchEventEnrollDrawerData={learnerActions.fetchEventEnrollDrawerData}
          learnerEntitiesDataStatus={learnerEntitiesDataStatus}
          learnerSessionsWithinEventDataStatus={learnerSessionsWithinEventDataStatus}
          enrollOperationStatus={enrollOperationStatus}
          learnerEntitiesInfo={learnerEntitiesInfo}
          isCalendarAutoSyncEnabled={!!isCalendarAutoSyncEnabled}
          setEnrollmentDrawerDefaultFilters={setEnrollmentDrawerDefaultFilters}
          mappedSeries={staticData.mappedSeries}
          trackerPageName={trackerPageName}
          clearEventEnrollmentDrawerData={learnerActions.clearEventEnrollmentDrawerData}
          localTimezoneName={localTimezoneName}
        />
      )
    );
  };

  return (
    <LearnersTabContainer areLearnersInvited={areLearnersInvited}>
      {staticData.isPublished && headingWrapper()}
      {staticData.isPublished && areLearnersInvited ? (
        <React.Fragment>
          {filtersWrapper()}
          {tableWrapper()}
          {enrollmentDrawer()}
        </React.Fragment>
      ) : (
        <GoToPublish
          isPublished={staticData.isPublished}
          areLearnersInvited={areLearnersInvited}
          invitedLearnersCountLoaded={staticData.invitedLearnersCountLoaded}
        />
      )}
    </LearnersTabContainer>
  );
}
