import { successToast, loadingToast, errorToast, hideToast } from '@mindtickle/toast';

type ToastProps = {
  message: string;
};

export const SuccessToast = ({ message }: ToastProps) => {
  successToast({
    message,
    autoHide: true,
    freeze: false,
    timeout: 3000,
  });
};
export const LoadingToast = ({ message }: ToastProps) => {
  loadingToast({
    message: message,
    autoHide: false,
    freeze: true,
    timeout: 3000,
  });
};

export const ErrorToast = ({ message }: ToastProps) => {
  hideToast();
  errorToast({ message, autoHide: true, freeze: false, timeout: 3000 });
};
