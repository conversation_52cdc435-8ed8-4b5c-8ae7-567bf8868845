import { useCallback, useEffect, useMemo, useState } from 'react';

import IconWithGradient from '@mindtickle/icon-with-gradient';

import {
  ENROLL_DRAWER_SUPPORTED_SORTING,
  getEnrollDrawerTableColumns,
  SESSION_STATUS_TYPES,
  SESSION_TYPE_TYPES,
  OPERATIONS,
  NO_RESULTS_FOUND_TEXT,
  ADJUST_FILTER_TEXT,
  DRAWER_ENROLL_STATUS_ACTION_MAP,
  ENROLL_DRAWER_ENROLLMENT_MAP,
} from '~/modules/Admin/config/track.constants';
import useMultidayEnabled from '~/modules/Admin/hooks/useMultidayEnabled';
import { updateSessionStatusDetails } from '~/modules/Admin/utils';

import EnrollmentStatusWrapper from './components/EnrollmentStatusWrapper';
import EventOperationsWrapper from './components/EventOperationsWrapper';
import SessionDateTimeWrapper from './components/SessionDateTimeWrapper';
import SessionNameWrapper from './components/SessionNameWrapper';
import { CustomizedTable } from './styles';

import type { EnrollmentDrawerTableType, TableDataType, RowDataType } from './typeDefs';

export default function EnrollmentDrawerTable({
  learnerEntities = [],
  drawerModifyEnroll,
  multipleLearners = false,
  handleEnrollmentChange,
  enrollOperationStatus,
  onSort,
  onFetchMore,
  hasMore,
  learnerEntitiesDataStatus,
  selectedLearnersCount,
  learnerEntitiesInfo = {},
  notifyLearners,
  learnerName = '',
  mappedSeries,
  tableActions,
  isEventEnrollDrawerActive,
  enrollmentMap,
  setEnrollmentMap,
  attendanceMap,
  setAttendanceMap,
  activeButtonMap,
  setActiveButtonMap,
  eventEnrollDrawerId,
  localTimezoneName,
}: EnrollmentDrawerTableType) {
  const [scrollHeight, setScrollHeightState] = useState<number>(700);
  const [isSessionWithinEventOperationInProgress, setSessionWithinEventOperationInProgress] =
    useState(false);
  const { isMultidayEnabled } = useMultidayEnabled();

  const setScrollHeight = useCallback(() => {
    let headerOffset = drawerModifyEnroll ? (notifyLearners ? 100 : 150) : 50;
    if (mappedSeries.length > 1) headerOffset += 20;
    const scrollHeight = window.innerHeight - headerOffset - 130;
    setScrollHeightState(scrollHeight);
  }, [drawerModifyEnroll, notifyLearners, mappedSeries]);

  const enrollDrawerTableColumns = useMemo(
    () =>
      getEnrollDrawerTableColumns({
        drawerModifyEnroll,
        isEventEnrollDrawerActive,
        isMultidayEnabled,
      }),
    [drawerModifyEnroll, isEventEnrollDrawerActive, isMultidayEnabled]
  );

  useEffect(() => {
    setScrollHeight();
    window.addEventListener('resize', setScrollHeight);
    return () => {
      window.removeEventListener('resize', setScrollHeight);
    };
  }, [setScrollHeight]);

  useEffect(() => {
    if (multipleLearners) return;

    setActiveButtonMap((activeButtonMap: any) => {
      let updatedActiveButtonMap = { ...activeButtonMap };
      learnerEntities.forEach(element => {
        if (!updatedActiveButtonMap[element.id as keyof typeof updatedActiveButtonMap]) {
          updatedActiveButtonMap = {
            ...updatedActiveButtonMap,
            [element.id as keyof typeof updatedActiveButtonMap]: {
              enrollmentType:
                DRAWER_ENROLL_STATUS_ACTION_MAP[
                  element.enrollmentStatus as keyof typeof DRAWER_ENROLL_STATUS_ACTION_MAP
                ],
              showFeedback: false,
            },
          };
        }
      });
      return updatedActiveButtonMap;
    });

    setEnrollmentMap((enrollmentMap: any) => {
      let updatedEnrollmentMap = { ...enrollmentMap };
      learnerEntities.forEach(element => {
        if (!updatedEnrollmentMap[element.id as keyof typeof updatedEnrollmentMap]) {
          updatedEnrollmentMap = {
            ...updatedEnrollmentMap,
            [element.id as keyof typeof updatedEnrollmentMap]: element.enrollmentStatus,
          };
        }
      });
      return updatedEnrollmentMap;
    });

    setAttendanceMap((attendanceMap: any) => {
      let updatedAttendanceMap = { ...attendanceMap };
      learnerEntities.forEach(element => {
        if (!updatedAttendanceMap[element.id as keyof typeof updatedAttendanceMap]) {
          updatedAttendanceMap = {
            ...updatedAttendanceMap,
            [element.id as keyof typeof updatedAttendanceMap]: element.attendanceStatus,
          };
        }
      });
      return updatedAttendanceMap;
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [learnerEntitiesDataStatus.loaded, learnerEntities, multipleLearners]);

  const updateActiveButtonMap = ({
    sessionId,
    enrollmentType,
    showFeedback,
  }: {
    sessionId: string;
    enrollmentType: string;
    showFeedback: boolean;
  }) => {
    setActiveButtonMap({
      ...activeButtonMap,
      [sessionId as keyof typeof activeButtonMap]: {
        enrollmentType,
        showFeedback,
      },
    });
  };

  const updateEnrollmentMap = ({
    sessionId,
    enrollmentStatus,
    prevEnrollmentStatus,
  }: {
    sessionId: string;
    enrollmentStatus: string;
    prevEnrollmentStatus: string;
  }) => {
    if (
      isEventEnrollDrawerActive &&
      prevEnrollmentStatus === ENROLL_DRAWER_ENROLLMENT_MAP.MOVE_TO_WAITING
    ) {
      const restSessionIds = learnerEntities
        .map(entity => entity.id)
        .filter(id => id !== sessionId);
      const remainingUpdates = Object.fromEntries(
        restSessionIds.map(sessionId => [sessionId, ENROLL_DRAWER_ENROLLMENT_MAP.UN_ENROLL])
      );
      setEnrollmentMap({
        ...enrollmentMap,
        ...remainingUpdates,
        [sessionId as keyof typeof enrollmentMap]: enrollmentStatus,
      });
    } else {
      setEnrollmentMap({
        ...enrollmentMap,
        [sessionId as keyof typeof enrollmentMap]: enrollmentStatus,
      });
    }
  };

  const updateAttendanceMap = ({
    entityId,
    attendanceStatus,
  }: {
    entityId: string;
    attendanceStatus: string;
  }) => {
    setAttendanceMap({
      ...attendanceMap,
      [entityId as keyof typeof attendanceMap]: attendanceStatus,
    });
  };

  const getRowData = ({
    index,
    entityStatusData = {},
    sessionType,
    sessionStatus,
    entityInfo = {},
    entityParentInfo = {},
    enrollOperationStatus,
    handleEnrollmentChange,
    multipleLearners,
    drawerModifyEnroll,
    selectedLearnersCount,
    notifyLearners,
  }: RowDataType) => {
    const entityDetails = isEventEnrollDrawerActive ? entityParentInfo : entityInfo;
    const {
      enrolled: enrolledCount,
      maxSeats,
      autoEnroll,
      waitingListEnabled,
      maxSeatEnabled,
    } = entityDetails;

    const sessionEnrollOperationStatus =
      enrollOperationStatus &&
      enrollOperationStatus[entityStatusData.id as keyof typeof enrollOperationStatus]
        ? enrollOperationStatus[entityStatusData.id as keyof typeof enrollOperationStatus]
        : {
            isUpdating: false,
            hasError: false,
            complete: true,
          };

    const enrollmentStatus = enrollmentMap[entityStatusData.id as keyof typeof enrollmentMap];
    const attendanceStatus = attendanceMap[entityStatusData.id as keyof typeof attendanceMap];
    const activeButtonObj = activeButtonMap[entityStatusData.id as keyof typeof activeButtonMap];
    const attendanceDuration =
      learnerEntities.find(item => item.id === entityStatusData.id)?.durationAttended || 0;

    const rowData = {
      key: `${index}-${entityStatusData.id}`,
      entityType: entityStatusData.entityType,
      sessionName: (
        <SessionNameWrapper
          entityId={entityStatusData.id}
          entityType={entityStatusData.entityType}
          parentId={entityInfo?.parentId}
          entityName={entityInfo.name}
          parentName={entityParentInfo?.name}
          sessionType={sessionType}
          sessionStatus={sessionStatus}
          attendanceStatus={attendanceStatus}
          attendanceDuration={attendanceDuration}
          enrollmentStatus={enrollmentStatus}
          multipleLearners={multipleLearners}
          updateAttendanceMap={updateAttendanceMap}
          totalNumberOfPublishedSessions={entityInfo?.totalNumberOfPublishedSessions}
        />
      ),
      sessionDateTime: (
        <SessionDateTimeWrapper
          startTimeEpoch={entityInfo.startTime}
          sessionTimezone={entityInfo.timezone ? entityInfo.timezone.displayName : ''}
          localStartTime={entityInfo.localStartTime}
          localTimezoneName={localTimezoneName}
          entityType={entityStatusData.entityType}
        />
      ),
      sessionEnrollStatus: (
        <EnrollmentStatusWrapper
          sessionId={entityStatusData.id}
          parentId={entityInfo?.parentId}
          entityType={entityStatusData.entityType}
          enrollmentStatus={enrollmentStatus}
          attendanceStatus={attendanceStatus}
          sessionStatus={sessionStatus}
          maxSeatEnabled={maxSeatEnabled}
          waitingListEnabled={waitingListEnabled}
          autoEnroll={autoEnroll}
          maxSeats={maxSeats}
          enrolledCount={enrolledCount}
          selectedLearnersCount={selectedLearnersCount}
          drawerModifyEnroll={drawerModifyEnroll}
          multipleLearners={multipleLearners}
          handleEnrollmentChange={handleEnrollmentChange}
          sessionEnrollOperationStatus={sessionEnrollOperationStatus}
          notifyLearners={notifyLearners}
          activeButtonObj={activeButtonObj}
          updateActiveButtonMap={updateActiveButtonMap}
          updateEnrollmentMap={updateEnrollmentMap}
          learnerName={learnerName}
          totalNumberOfPublishedSessions={entityInfo?.totalNumberOfPublishedSessions}
          enrolledSessionsCount={entityStatusData?.enrolledSessionsCount}
          isEventEnrollDrawerActive={isEventEnrollDrawerActive}
          isSessionWithinEventOperationInProgress={isSessionWithinEventOperationInProgress}
          setSessionWithinEventOperationInProgress={setSessionWithinEventOperationInProgress}
        />
      ),
      eventEnrollOperations: (
        <EventOperationsWrapper
          actions={tableActions}
          eventId={entityStatusData.id}
          entityType={entityStatusData.entityType}
          drawerModifyEnroll={drawerModifyEnroll}
        />
      ),
    };
    return rowData;
  };

  const getTableData = ({
    learnerEntities,
    drawerModifyEnroll,
    handleEnrollmentChange,
    enrollOperationStatus,
    selectedLearnersCount,
    learnerEntitiesInfo,
    multipleLearners,
    notifyLearners,
  }: TableDataType) => {
    const dataSource = [];
    for (let index = 0; index < learnerEntities.length; index++) {
      const entityStatusData = learnerEntities[index];

      const entityInfoUnprocessed =
        learnerEntitiesInfo[entityStatusData.id as keyof typeof learnerEntitiesInfo];

      const entityInfo: any = updateSessionStatusDetails(entityInfoUnprocessed);
      const entityParentInfo =
        learnerEntitiesInfo[entityInfo.parentId as keyof typeof learnerEntitiesInfo];

      const sessionStatus = entityInfo.isUpcoming
        ? SESSION_STATUS_TYPES.UPCOMING
        : entityInfo.isOver
        ? SESSION_STATUS_TYPES.PAST
        : SESSION_STATUS_TYPES.LIVE;

      const sessionType =
        entityInfo.type === SESSION_TYPE_TYPES.CLASSROOM
          ? SESSION_TYPE_TYPES.CLASSROOM
          : entityInfo.type === SESSION_TYPE_TYPES.HYBRID
          ? SESSION_TYPE_TYPES.HYBRID
          : SESSION_TYPE_TYPES.WEBINAR;

      const rowData = getRowData({
        index,
        entityStatusData,
        sessionType,
        sessionStatus,
        entityInfo,
        entityParentInfo,
        enrollOperationStatus,
        handleEnrollmentChange,
        multipleLearners,
        drawerModifyEnroll,
        selectedLearnersCount,
        notifyLearners,
      });

      dataSource.push(rowData);
    }

    return dataSource;
  };

  const isDataLoading =
    learnerEntitiesDataStatus.isLoading &&
    learnerEntitiesDataStatus.loadingData.operation === OPERATIONS.GET;

  const isLoadingMore =
    learnerEntitiesDataStatus.isLoading &&
    learnerEntitiesDataStatus.loadingData.operation !== OPERATIONS.GET;

  const dataSource = getTableData({
    learnerEntities,
    drawerModifyEnroll,
    handleEnrollmentChange,
    enrollOperationStatus,
    selectedLearnersCount,
    learnerEntitiesInfo,
    multipleLearners,
    notifyLearners,
  });

  return (
    <CustomizedTable
      hasSeparators={true}
      dataSource={!isDataLoading && dataSource}
      columns={enrollDrawerTableColumns}
      emptyTableData={
        <div className="no-data-wrapper">
          {!isDataLoading && (
            <div>
              <IconWithGradient type="noSearchResults" className="no-data-icon" />
              <div className="tableStyling">{NO_RESULTS_FOUND_TEXT}</div>
              <div className="adjustMsg">{ADJUST_FILTER_TEXT}</div>
            </div>
          )}
        </div>
      }
      loaderType={'spin'}
      scroll={{ y: scrollHeight }}
      infiniteScroll={true}
      fetchData={() => onFetchMore(eventEnrollDrawerId)}
      loadingMore={isLoadingMore}
      hasMore={hasMore}
      pagination={false}
      threshold={0.9}
      loading={isDataLoading}
      pageSize={11}
      onChange={(_1: any, _2: any, { field, order }: { field: string; order: string }) => {
        onSort({
          sortOrder: order === 'ascend' ? 'ASC' : 'DESC',
          sortField:
            field === 'sessionName'
              ? ENROLL_DRAWER_SUPPORTED_SORTING.SESSION_NAME
              : ENROLL_DRAWER_SUPPORTED_SORTING.START_TIME,
          eventId: eventEnrollDrawerId,
        });
      }}
      rowClassName="learner-entities-table-row"
    />
  );
}
