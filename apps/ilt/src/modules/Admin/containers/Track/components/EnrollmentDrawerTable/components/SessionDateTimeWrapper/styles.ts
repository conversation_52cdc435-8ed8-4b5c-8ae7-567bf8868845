import styled from 'styled-components';

import { tokens, mixins, theme } from '@mindtickle/styles/lib';

export const SessionDateTimeContainer = styled.div`
  .session-date-time-wrapper {
    padding: 2px 0;
  }
  .date-time-text {
    ${mixins.fontStack({
      color: tokens.textTokens.COLOR_TEXT_DEFAULT,
      fontWeight: theme.fontWeight.REGULAR,
      fontSize: theme.fontSizes.TEXT,
      lineHeight: theme.lineHeight.TEXT,
    })}
    margin-bottom: 3px;
    cursor: default;
  }

  .timezone-text {
    ${mixins.fontStack({
      color: tokens.textTokens.COLOR_TEXT_DEFAULT,
      fontWeight: theme.fontWeight.REGULAR,
      fontSize: theme.fontSizes.SMALLTEXT,
      lineHeight: theme.lineHeight.SMALLTEXT,
    })}
    cursor: default;
  }
`;
