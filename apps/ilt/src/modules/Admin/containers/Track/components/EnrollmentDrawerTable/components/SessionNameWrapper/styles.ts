import styled from 'styled-components';

import { tokens, mixins, theme } from '@mindtickle/styles/lib';

export const SessionNameContainer = styled.div`
  display: flex;
  flex-direction: column;
  padding: 2px 0;
  .session-name-tooltip {
    width: 100%;
    height: 20px;

    ${mixins.blackLink()}
    cursor: default;
    margin-bottom: 4px;
  }

  .session-inside-event-name-tooltip {
    max-width: 80%;
  }

  .session-details,
  .entity-type {
    width: 100%;
    height: 16px;
    display: flex;
    justify-content: flex-start;

    ${mixins.fontStack({
      color: tokens.textTokens.COLOR_TEXT_TERTIARY,
      fontWeight: theme.fontWeight.SEMIBOLD,
      fontSize: theme.fontSizes.SMALLTEXT,
      lineHeight: theme.lineHeight.SMALLTEXT,
    })}
    margin-bottom: 4px;

    cursor: default;
    .left-margin {
      margin-left: 3px;
    }
  }

  .did-not-attend {
    height: 16px;

    ${mixins.fontStack({
      color: tokens.textTokens.COLOR_TEXT_DANGER,
      fontWeight: theme.fontWeight.SEMIBOLD,
      fontSize: theme.fontSizes.SMALLTEXT,
      lineHeight: theme.lineHeight.SMALLTEXT,
    })}

    cursor: default;
  }
  .attended {
    height: 16px;

    ${mixins.fontStack({
      color: tokens.textTokens.COLOR_TEXT_SUCCESS,
      fontWeight: theme.fontWeight.SEMIBOLD,
      fontSize: theme.fontSizes.SMALLTEXT,
      lineHeight: theme.lineHeight.SMALLTEXT,
    })}

    cursor: default;
  }

  .green-tick-style {
    margin-left: 4px;
    cursor: default;
    color: ${tokens.textTokens.COLOR_TEXT_SUCCESS};
  }

  .unmarked-attendance {
    height: 16px;

    ${mixins.fontStack({
      color: tokens.textTokens.COLOR_TEXT_DEFAULT,
      fontWeight: theme.fontWeight.SEMIBOLD,
      fontSize: theme.fontSizes.SMALLTEXT,
      lineHeight: theme.lineHeight.SMALLTEXT,
    })}

    cursor: default;
  }
`;
