import styled from 'styled-components';

import { tokens, mixins, theme } from '@mindtickle/styles/lib';

export const SessionContainer = styled.div`
  min-height: 70px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  row-gap: 2px;
  .session-name {
    ${mixins.fontStack({
      color: tokens.textTokens.COLOR_TEXT_DEFAULT,
      fontWeight: theme.fontWeight.REGULAR,
      fontSize: theme.fontSizes.TEXT,
      lineHeight: theme.lineHeight.TEXT,
    })}
  }
  .session-date-time {
    ${mixins.fontStack({
      fontWeight: theme.fontWeight.REGULAR,
      fontSize: theme.fontSizes.SMALLTEXT,
      lineHeight: theme.lineHeight.SMALLTEXT,
    })}
    color: #999999;
  }
  .total-text {
    ${mixins.activeBlackLink()}
    color: ${tokens.textTokens.COLOR_TEXT_ACCENT};
    cursor: pointer;
  }
`;

export const EmptySessionContainer = styled.div`
  height: 70px;
  display: flex;
  align-items: center;
`;
