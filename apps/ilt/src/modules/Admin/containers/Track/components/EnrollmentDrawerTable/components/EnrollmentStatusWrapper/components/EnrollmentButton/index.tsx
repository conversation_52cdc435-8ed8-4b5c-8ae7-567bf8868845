import classnames from 'classnames';

import Icon from '@mindtickle/icon';
import Loader from '@mindtickle/loader';
import PopConfirm from '@mindtickle/popconfirm';
import Tooltip from '@mindtickle/tooltip';

import {
  DRAWER_ENROLL_STATUS_ACTIONS,
  DRAWER_ENROLL_STATUS_ACTION_MAP,
} from '~/modules/Admin/config/track.constants';

import MANAGE_MESSAGES from '../../messages';
import PopConfirmTitle from '../PopConfirmTitle';

import { EnrollmentTag, popConfirmStyle } from './styles';

import type { TEnrollmentButton } from './typeDefs';

const EnrollmentButton = ({
  enrollmentType,
  initialText,
  updatingText,
  updatedText,
  checkOperationSucceeded,
  checkOperationRunning,
  checkOperationFailed,
  getTooltipText,
  checkButtonDisabled,
  checkPopConfirmEnabled,
  handleButtonClick,
  sessionId,
  activeButtonObj,
  enrollmentStatus,
  learnerName,
  rowRef,
}: TEnrollmentButton) => {
  const isOperationSucceeded = checkOperationSucceeded({ enrollmentType });
  const isOperationRunning = checkOperationRunning({ enrollmentType });
  const isOperationFailed = checkOperationFailed({ enrollmentType });

  const tooltipText = getTooltipText({ enrollmentType });

  const buttonJsx = (
    <EnrollmentTag
      key={`${sessionId}-${enrollmentType}`}
      onClick={() =>
        !checkButtonDisabled({ enrollmentType }) &&
        !checkPopConfirmEnabled({ enrollmentType }) &&
        handleButtonClick({ enrollmentType })
      }
      className={classnames('default-button', {
        'inactive-button': activeButtonObj.enrollmentType !== enrollmentType,
        'enrolled-active':
          (isOperationSucceeded ||
            DRAWER_ENROLL_STATUS_ACTION_MAP[
              enrollmentStatus as keyof typeof DRAWER_ENROLL_STATUS_ACTION_MAP
            ] === DRAWER_ENROLL_STATUS_ACTIONS.ENROLL) &&
          enrollmentType === DRAWER_ENROLL_STATUS_ACTIONS.ENROLL,
        'not-enrolled-active':
          (isOperationSucceeded ||
            DRAWER_ENROLL_STATUS_ACTION_MAP[
              enrollmentStatus as keyof typeof DRAWER_ENROLL_STATUS_ACTION_MAP
            ] === DRAWER_ENROLL_STATUS_ACTIONS.UN_ENROLL) &&
          enrollmentType === DRAWER_ENROLL_STATUS_ACTIONS.UN_ENROLL,
        'waitlisted-active':
          (isOperationSucceeded ||
            DRAWER_ENROLL_STATUS_ACTION_MAP[
              enrollmentStatus as keyof typeof DRAWER_ENROLL_STATUS_ACTION_MAP
            ] === DRAWER_ENROLL_STATUS_ACTIONS.MOVE_TO_WAITING) &&
          enrollmentType === DRAWER_ENROLL_STATUS_ACTIONS.MOVE_TO_WAITING,
        updating: isOperationRunning,
        'not-enrolled-updating':
          isOperationRunning && enrollmentType === DRAWER_ENROLL_STATUS_ACTIONS.UN_ENROLL,
        'enrolled-updating':
          isOperationRunning && enrollmentType === DRAWER_ENROLL_STATUS_ACTIONS.ENROLL,
        'waitlisted-updating':
          isOperationRunning && enrollmentType === DRAWER_ENROLL_STATUS_ACTIONS.MOVE_TO_WAITING,
        'failed-button': isOperationFailed,
        'disabled-button': checkButtonDisabled({ enrollmentType }),
        'button-with-tooltip': !!tooltipText,
      })}
    >
      {isOperationRunning ? (
        <div className="message-wrapper-updating">
          <div className="updating-text">{updatingText}</div>
          <div className="loader-wrapper">
            <div className="loader-scale">
              <Loader size="sizeXSmall" type="Small" />
            </div>
          </div>
        </div>
      ) : isOperationSucceeded ||
        (enrollmentType === DRAWER_ENROLL_STATUS_ACTIONS.ENROLL &&
          DRAWER_ENROLL_STATUS_ACTION_MAP[
            enrollmentStatus as keyof typeof DRAWER_ENROLL_STATUS_ACTION_MAP
          ] === DRAWER_ENROLL_STATUS_ACTIONS.ENROLL) ||
        (enrollmentType === DRAWER_ENROLL_STATUS_ACTIONS.UN_ENROLL &&
          DRAWER_ENROLL_STATUS_ACTION_MAP[
            enrollmentStatus as keyof typeof DRAWER_ENROLL_STATUS_ACTION_MAP
          ] === DRAWER_ENROLL_STATUS_ACTIONS.UN_ENROLL) ||
        (enrollmentType === DRAWER_ENROLL_STATUS_ACTIONS.MOVE_TO_WAITING &&
          DRAWER_ENROLL_STATUS_ACTION_MAP[
            enrollmentStatus as keyof typeof DRAWER_ENROLL_STATUS_ACTION_MAP
          ] === DRAWER_ENROLL_STATUS_ACTIONS.MOVE_TO_WAITING) ? (
        <div className="message-wrapper">
          <div>{updatedText}</div>
          <Icon type={'tick'} className="tick-color" />
        </div>
      ) : (
        <div>{initialText}</div>
      )}
    </EnrollmentTag>
  );

  if (checkPopConfirmEnabled({ enrollmentType })) {
    return (
      <PopConfirm
        placement="top"
        overlayStyle={popConfirmStyle}
        title={<PopConfirmTitle learnerName={learnerName} />}
        onConfirm={() => handleButtonClick({ enrollmentType })}
        okText={MANAGE_MESSAGES.UNENROLL_OK_TXT}
        cancelText={'Cancel'}
        getPopupContainer={() => rowRef.current}
      >
        {buttonJsx}
      </PopConfirm>
    );
  } else {
    return (
      <Tooltip
        key={`${sessionId}-${enrollmentType}`}
        overlayStyle={{ zIndex: 9913 }}
        title={tooltipText}
        placement={'bottomLeft'}
        trigger={'hover'}
        getPopupContainer={() => rowRef.current}
      >
        {buttonJsx}
      </Tooltip>
    );
  }
};

export default EnrollmentButton;
