import styled from 'styled-components';

import { tokens } from '@mindtickle/styles/lib';

import { flexContainer } from '~/modules/Admin/styles/mixins';

export const StyledUserNode = styled.div`
  ${flexContainer({ flexDirection: 'column', alignItems: 'flex-start' })}

  .info-email {
    font-size: 12px !important;
    line-height: 16px !important;
    font-weight: normal !important;
    color: ${tokens.textTokens.COLOR_TEXT_SECONDARY} !important;
  }

  .user-node {
    padding-right: 16px;
  }
`;
