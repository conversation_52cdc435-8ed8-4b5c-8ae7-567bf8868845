export type TEnrollmentButton = {
  enrollmentType: string;
  initialText: string;
  updatingText: string;
  updatedText: string;
  checkOperationSucceeded: Function;
  checkOperationRunning: Function;
  checkOperationFailed: Function;
  getTooltipText: Function;
  checkButtonDisabled: Function;
  checkPopConfirmEnabled: Function;
  handleButtonClick: Function;
  sessionId: string;
  activeButtonObj: any;
  enrollmentStatus: string;
  learnerName: string;
  rowRef: any;
};
