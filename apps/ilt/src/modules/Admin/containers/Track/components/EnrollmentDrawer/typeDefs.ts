import type {
  LearnerEntitiesType,
  LearnerEntitiesDataStatusType,
  TLearnerEntityInfo,
  LearnerSessionsWithinEventType,
} from '~/modules/Admin/containers/Track/typeDefs';

export type EnrollmentDrawerType = {
  learnerEntitiesDataStatus: LearnerEntitiesDataStatusType;
  learnerSessionsWithinEventDataStatus: LearnerEntitiesDataStatusType;
  drawerModifyEnroll: boolean;
  learnersMap: object;
  learnerIds: Array<string>;
  activeLearnerId: string;
  learnerEntities: LearnerEntitiesType;
  learnerSessionsWithinEvent: LearnerSessionsWithinEventType;
  toggleSessionsDrawer: Function;
  toggleModifyMode: Function;
  defaultFilters: {
    [key: string]: Array<string>;
  };
  multipleLearners: boolean;
  changeEnrollStatus: Function;
  fetchDrawerData: Function;
  fetchEventEnrollDrawerData: Function;
  enrollOperationStatus: object;
  learnerEntitiesInfo: {
    [key: string]: TLearnerEntityInfo;
  };
  isCalendarAutoSyncEnabled: any;
  setEnrollmentDrawerDefaultFilters: Function;
  mappedSeries: Array<string>;
  trackerPageName: string;
  clearEventEnrollmentDrawerData: Function;
  localTimezoneName?: string;
};
