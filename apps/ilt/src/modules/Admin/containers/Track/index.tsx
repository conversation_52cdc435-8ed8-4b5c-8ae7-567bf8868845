import React, { useEffect, useState, useMemo, useRef } from 'react';

import { FormattedMessage } from 'react-intl';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { useUserAuth } from 'ui_shell/Auth';
import { usePerformanceData as getPerformanceData } from 'ui_shell/PerformanceMetrics';

import Loader from '@mindtickle/loader';
import { getActions } from '@mindtickle/medux/Action';
import { injectSaga } from '@mindtickle/medux/Saga';
import { successToast, loadingToast, errorToast } from '@mindtickle/toast';
import { deepEqual } from '@mindtickle/utils';

import {
  TRACK_GET_DATA,
  TRACK_MANIPULATE_DATA,
  TRACK_GET_LEARNER_ENTITIES,
  TRACK_ENROLLMENT_DRAWER_CLOSED,
  TRACK_RESET,
  TRACK_GET_LEARNER_SESSIONS_WITHIN_EVENT,
  TRACK_EVENT_ENROLLMENT_DRAWER_CLOSED,
} from '~/modules/Admin/actionTypes';
import { PAGE_LOAD_DATA } from '~/modules/Admin/config/constants';
import GET_ERROR_MESSAGES from '~/modules/Admin/config/error.messages';
import {
  OPERATIONS,
  MESSAGES,
  DEFAULT_LEARNERS_FILTER,
  DEFAULT_LEARNERS_SORT,
} from '~/modules/Admin/config/track.constants';
import createWithPageTimeSpent from '~/modules/Admin/hocs/withPageTimeSpent';
import { useILTAdminSnowplowTracker } from '~/modules/Admin/hooks/useILTAdminSnowplowTracker';
import {
  EVENT_NAME,
  SNOWPLOW_FIELD_NAMES,
} from '~/modules/Admin/hooks/useILTAdminSnowplowTracker/constants';
import { getLearnerIds, getErrorCode, checkDataLoading } from '~/modules/Admin/utils/learners';

import LearnersTab from './components/LearnersTab';
import RelevanceModal from './components/RelevanceModal';
import { getLearnersFiltersConfig } from './helpers';
import { sendTrackPageOperation } from './helpers/trackEvents';
import useGetParams from './hooks/useGetParams';
import useLearnerActions from './hooks/useLearnerActions';
import saga from './saga';

import type {
  TTrackOwnProps,
  TrackPropsType,
  TrackDispatchPropsType,
  TrackStatePropsType,
  FiltersType,
  TGetLearnerEntities,
  TOperationStatus,
  TGetLearnerSessionsWithinEvent,
} from './typeDefs';

const {
  SORT_LEARNERS,
  SEARCH_LEARNERS,
  PAGINATE_LEARNERS,
  REMOVE_LEARNERS,
  CHANGE_RELEVANCE,
  EXPORT_LEARNERS,
} = OPERATIONS;

const trackerPageName = PAGE_LOAD_DATA.ILT_INVITE_AND_TRACK_PAGE.PAGE_NAME;

// eslint-disable-next-line max-statements
const Track = ({
  context,
  staticData,
  onLearnerRemoval,
  extraConfig,
  operationStatus = {
    isLoading: false,
    loaded: false,
    hasError: false,
    partial: false,
    data: {
      operation: '',
      postData: undefined,
      mixpanel: undefined,
    },
    loadingData: undefined,
  },
  isTrackLoading,
  isTrackLoaded,
  poll = {
    isLoading: false,
    loaded: false,
    hasError: false,
    partial: false,
    data: {
      isNewUM: false,
      successIds: [],
      response: {
        successIds: [],
      },
      mixpanel: undefined,
    },
    error: {
      errorCode: '',
    },
  },
  actions,
  learnersMap,
  searchedLearners,
  learnerEntitiesInfo,
  learnerEntities,
  learnerSessionsWithinEvent,
  learnerEntitiesDataStatus,
  learnerSessionsWithinEventDataStatus,
  enrollOperationStatus,
  onInvite,
  localTimezoneName,
}: TrackPropsType) => {
  const [sortState, setSort] = useState<object>(DEFAULT_LEARNERS_SORT);
  const [filterState, setFilters] = useState<FiltersType>(DEFAULT_LEARNERS_FILTER);
  const [showRelevanceSelectionModal, setShowRelevanceSelectionModal] = useState<boolean>(false);
  const [learnersForModuleRelevanceChange, setLearnersForModuleRelevanceChange] = useState<
    Array<string>
  >([]);

  const [selectedLearners, setSelectedLearners] = useState<Set<string>>(new Set([]));

  const {
    features: { moduleRelevanceEnabled, newUserProfileAndGroupPageUIEnabled },
  } = useUserAuth();

  const tracker = useILTAdminSnowplowTracker();

  const isLearnersDataLoading = useMemo(
    () =>
      isTrackLoading ||
      checkDataLoading({ action: [SEARCH_LEARNERS, SORT_LEARNERS], operationStatus }),
    [isTrackLoading, operationStatus]
  );

  const isMoreLearnersDataLoading = useMemo(
    () =>
      operationStatus?.isLoading && operationStatus?.loadingData?.operation === PAGINATE_LEARNERS,
    [operationStatus]
  );

  const prevPoll = useRef({ isLoading: false, loaded: false, hasError: false });
  const prevOperationStatus = useRef<TOperationStatus>();

  useEffect(() => {
    if (staticData.isPublished) {
      actions.getData();
    }
    return () => {
      actions.resetTrack();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (deepEqual(prevPoll.current, poll)) return;

    const {
      loadingData: { operation: loadingOperationType = '' } = { operation: '' },
      data: { operation: dataOperationType = '' } = {},
    } = operationStatus;

    const operationType: string = (loadingOperationType as string) || dataOperationType || '';

    const { data: { successIds = [], errorIds = [], isNewUM = false } = {} } = poll;

    if (poll.isLoading) {
      const loadingMessageFunc = MESSAGES[operationType]?.LOADING;
      loadingToast({
        message: typeof loadingMessageFunc === 'function' ? loadingMessageFunc() : '',
        freeze: true,
      });
    } else if (poll.hasError) {
      errorToast({
        message: (
          <FormattedMessage
            {...GET_ERROR_MESSAGES(
              getErrorCode({
                error: poll.error,
                isNewUM,
              })
            )}
          />
        ),
      });
    } else if (poll.loaded && operationType) {
      const successMessageFunc = MESSAGES[operationType]?.SUCCESS;
      successToast({
        message:
          typeof successMessageFunc === 'function'
            ? successMessageFunc({
                successCount: successIds.length,
                failCount: errorIds.length,
              })
            : '',
      });
      if (operationType === REMOVE_LEARNERS) {
        onLearnerRemoval();
        sendTrackPageOperation(tracker, {
          operationStatus,
          extraTrackingProperties: {
            [SNOWPLOW_FIELD_NAMES.PAGE_NAME]: trackerPageName,
          },
        });
      }
      reloadTab();
    }

    prevPoll.current = poll;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [poll, operationStatus, onLearnerRemoval]);

  useEffect(() => {
    if (deepEqual(prevOperationStatus.current, operationStatus)) return;
    const { loadingData: prevLoadingData = {} } = prevOperationStatus.current || {};

    const { loaded, hasError, data: { operation } = {} } = operationStatus;
    if (operation === EXPORT_LEARNERS && loaded && !hasError) {
      successToast({
        message: 'Preparing the report, the email would be sent in some time.',
        timeout: 3000,
        hideBtn: true,
      });
    } else if (prevLoadingData.operation === EXPORT_LEARNERS && loaded && hasError) {
      errorToast({
        message: 'Failed to send report. Please try again.',
        timeout: 3000,
        hideBtn: true,
      });
    }
    if (
      [SEARCH_LEARNERS, OPERATIONS.CHANGE_LEARNER_STATUS, EXPORT_LEARNERS].includes(
        operation || ''
      ) &&
      loaded &&
      loaded !== prevOperationStatus.current?.loaded &&
      !hasError
    ) {
      sendTrackPageOperation(tracker, {
        operationStatus,
        extraTrackingProperties: {
          [SNOWPLOW_FIELD_NAMES.PAGE_NAME]: trackerPageName,
        },
      });
    }
    prevOperationStatus.current = operationStatus;
    // eslint-disable-next-line react-hooks/exhaustive-deps -- no need to add tracker
  }, [operationStatus]);

  useEffect(() => {
    if (isTrackLoaded) {
      const metricPageLoad = getPerformanceData();

      metricPageLoad &&
        tracker.trackStructuredEvent({
          eventName: EVENT_NAME.PAGE_LOAD_VIEW,
          [SNOWPLOW_FIELD_NAMES.PAGE_NAME]: trackerPageName,
          ...metricPageLoad,
        });
    }
  }, [isTrackLoaded, tracker]);

  const getParams = useGetParams({
    sortState,
    setSort,
    filterState,
    setFilters,
    newUserProfileAndGroupPageUIEnabled,
    learnersMap,
  });

  const learnerActions = useLearnerActions({
    selectedLearners,
    setSelectedLearners,
    setLearnersForModuleRelevanceChange,
    setShowRelevanceSelectionModal,
    searchedLearners,
    actions,
    getParams,
    learnersMap,
    tracker,
    trackerPageName,
  });

  const reloadTab = () => {
    actions.getData();
    setSort(DEFAULT_LEARNERS_SORT);
    setFilters(DEFAULT_LEARNERS_FILTER);
    setShowRelevanceSelectionModal(false);
    setLearnersForModuleRelevanceChange([]);
    setSelectedLearners(new Set([]));
  };

  const onInviteComplete = () => {
    onInvite();
    reloadTab();
  };

  const onResetFilters = () => {
    const updatedFilters = DEFAULT_LEARNERS_FILTER;
    if (!deepEqual(updatedFilters, filterState)) {
      learnerActions.filter({ filterType: 'reset' });
    }
  };

  const onModuleRelevanceSelection = (moduleRelevanceSelection: string) => {
    actions.manipulateData(
      getParams(CHANGE_RELEVANCE, {
        learners: learnersForModuleRelevanceChange,
        moduleRelevanceSelection,
      })
    );
    setShowRelevanceSelectionModal(false);
  };

  return !isTrackLoading ? (
    <React.Fragment>
      <LearnersTab
        learnerActions={learnerActions}
        learnersMap={learnersMap}
        searchedLearners={searchedLearners}
        onResetFilters={onResetFilters}
        filtersConfig={getLearnersFiltersConfig({ learnerActions, filterState })}
        isLearnersDataLoading={isLearnersDataLoading}
        isMoreLearnersDataLoading={isMoreLearnersDataLoading}
        context={context}
        staticData={staticData}
        learnerEntities={learnerEntities}
        learnerSessionsWithinEvent={learnerSessionsWithinEvent}
        learnerEntitiesDataStatus={learnerEntitiesDataStatus}
        learnerSessionsWithinEventDataStatus={learnerSessionsWithinEventDataStatus}
        enrollOperationStatus={enrollOperationStatus}
        learnerEntitiesInfo={learnerEntitiesInfo}
        extraConfig={extraConfig}
        selectedLearners={[...selectedLearners]}
        onCloseActionBar={() => {
          setSelectedLearners(new Set([]));
        }}
        moduleRelevanceEnabled={moduleRelevanceEnabled}
        trackerPageName={trackerPageName}
        onInviteComplete={onInviteComplete}
        localTimezoneName={localTimezoneName}
      />
      {showRelevanceSelectionModal && (
        <RelevanceModal
          learnersMap={learnersMap}
          learnersForModuleRelevanceChange={learnersForModuleRelevanceChange}
          onModuleRelevanceSelection={onModuleRelevanceSelection}
          onModuleRelevanceClose={() => {
            setShowRelevanceSelectionModal(false);
          }}
          defaultModuleRelevance={staticData.moduleRelevance}
        />
      )}
    </React.Fragment>
  ) : (
    <Loader size="sizeSmall" />
  );
};

const mapDispatchToProps = (dispatch: any) => ({
  getData: (params: any) => dispatch(getActions(TRACK_GET_DATA)(params)),
  manipulateData: (data: any) => {
    const { learnerIds, emailIds } = getLearnerIds(data.learners);
    dispatch(
      getActions(TRACK_MANIPULATE_DATA)(data, {
        loadingData: { operation: data.operation, learnerIds, emailIds },
      })
    );
  },
  resetTrack: () => dispatch(getActions(TRACK_RESET)()),
  getLearnerEntities: (data: TGetLearnerEntities) => {
    dispatch(
      getActions(TRACK_GET_LEARNER_ENTITIES)(data, {
        loadingData: { operation: data.operation ? data.operation : OPERATIONS.GET },
      })
    );
  },
  getLearnerSessionsWithinEvent: (data: TGetLearnerSessionsWithinEvent) => {
    dispatch(
      getActions(TRACK_GET_LEARNER_SESSIONS_WITHIN_EVENT)(data, {
        loadingData: { operation: data.operation ? data.operation : OPERATIONS.GET },
      })
    );
  },
  clearEnrollmentDrawerData: () => dispatch(getActions(TRACK_ENROLLMENT_DRAWER_CLOSED)()),
  clearEventEnrollmentDrawerData: () =>
    dispatch(getActions(TRACK_EVENT_ENROLLMENT_DRAWER_CLOSED)()),
});

const mapStateToProps = (state: any) => {
  const {
    track: {
      poll,
      isLoading: isTrackLoading,
      loaded: isTrackLoaded,
      learnersMap,
      searchedLearners,
      operationStatus,
      learnerEntities,
      learnerSessionsWithinEvent,
      learnerEntitiesDataStatus,
      learnerSessionsWithinEventDataStatus,
      enrollOperationStatus,
      learnerEntitiesInfo = {} as object,
      localTimezoneName,
    },
    context,
    details: { staticData } = {} as object,
  } = state.ilt;

  return {
    poll,
    learnersMap,
    searchedLearners,
    operationStatus,
    isTrackLoading,
    isTrackLoaded,
    context,
    staticData,
    learnerEntities,
    learnerSessionsWithinEvent,
    learnerEntitiesDataStatus,
    learnerSessionsWithinEventDataStatus,
    enrollOperationStatus,
    learnerEntitiesInfo,
    localTimezoneName,
  };
};

const mergeProps = (
  stateProps: TrackStatePropsType,
  dispatchProps: TrackDispatchPropsType,
  ownProps: TTrackOwnProps
) => ({
  poll: stateProps.poll,
  learnersMap: stateProps.learnersMap,
  searchedLearners: stateProps.searchedLearners,
  operationStatus: stateProps.operationStatus,
  isTrackLoading: stateProps.isTrackLoading,
  isTrackLoaded: stateProps.isTrackLoaded,
  context: stateProps.context,
  staticData: stateProps.staticData,
  learnerEntities: stateProps.learnerEntities,
  learnerSessionsWithinEvent: stateProps.learnerSessionsWithinEvent,
  learnerEntitiesDataStatus: stateProps.learnerEntitiesDataStatus,
  learnerSessionsWithinEventDataStatus: stateProps.learnerSessionsWithinEventDataStatus,
  enrollOperationStatus: stateProps.enrollOperationStatus,
  learnerEntitiesInfo: stateProps.learnerEntitiesInfo,
  localTimezoneName: stateProps.localTimezoneName,
  actions: {
    getData: dispatchProps.getData,
    manipulateData: dispatchProps.manipulateData,
    resetTrack: dispatchProps.resetTrack,
    getLearnerEntities: dispatchProps.getLearnerEntities,
    getLearnerSessionsWithinEvent: dispatchProps.getLearnerSessionsWithinEvent,
    clearEnrollmentDrawerData: dispatchProps.clearEnrollmentDrawerData,
    clearEventEnrollmentDrawerData: dispatchProps.clearEventEnrollmentDrawerData,
  },
  ...ownProps,
});
const withPageTimeSpent = createWithPageTimeSpent({
  pageName: trackerPageName,
});
const withConnect = connect(mapStateToProps, mapDispatchToProps, mergeProps);
const withSaga = injectSaga({ name: 'iltTrack', saga: saga });
export default compose<React.ComponentType<TTrackOwnProps>>(
  withSaga,
  withConnect,
  withPageTimeSpent
)(Track);
