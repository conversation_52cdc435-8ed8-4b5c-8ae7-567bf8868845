import { hasValue } from '@mindtickle/utils';

import WithRemoveConfirmation from '~/modules/Admin/components/WithRemoveConfirmation';
import { OPERATIONS } from '~/modules/Admin/config/track.constants';
import { processEnrollmentFilters } from '~/modules/Admin/containers/Track/helpers';
import type { TChangeEnrollLearner } from '~/modules/Admin/containers/Track/typeDefs';
import { SNOWPLOW_FIELD_NAMES } from '~/modules/Admin/hooks/useILTAdminSnowplowTracker/constants';

import { sendTrackPageOperation, sendExportLearnerClicked } from '../helpers/trackEvents';

import type { TUseLearnerActions } from './typeDefs';

const {
  GET_UPDATED_LEARNERS,
  SORT_LEARNERS,
  SEARCH_LEARNERS,
  PAGINATE_LEARNERS,
  REMOVE_LEARNERS,
  CHANGE_LEARNER_STATUS,
  VIEW_LEARNER_PROFILE,
  EXPORT_LEARNERS,
} = OPERATIONS;

const useLearnerActions = ({
  selectedLearners,
  setSelectedLearners,
  setLearnersForModuleRelevanceChange,
  setShowRelevanceSelectionModal,
  searchedLearners,
  actions,
  getParams,
  learnersMap,
  tracker,
  trackerPageName,
}: TUseLearnerActions) => {
  const learnerActions = {
    onSelect: (record: any) => {
      const updatedSelectedLearners = new Set([...selectedLearners]);
      if (selectedLearners.has(record.key)) {
        updatedSelectedLearners.delete(record.key);
      } else {
        updatedSelectedLearners.add(record.key);
      }
      setSelectedLearners(updatedSelectedLearners);
    },
    onSelectAll: (selected: boolean) => {
      const updatedSelectedLearners = new Set([...selectedLearners]);
      const learnerIds = searchedLearners ? searchedLearners.data : [];

      if (selected) {
        learnerIds.forEach((id: string) => updatedSelectedLearners.add(id));
      } else {
        learnerIds.forEach((id: string) => updatedSelectedLearners.delete(id));
      }
      setSelectedLearners(updatedSelectedLearners);
    },
    getUpdatedLearners: () => {
      actions.manipulateData(getParams(GET_UPDATED_LEARNERS, {}));
    },
    pagination: (pagination: { start: number; rows: number }) => {
      actions.manipulateData(getParams(PAGINATE_LEARNERS, { pagination }));
    },
    removeLearners: (ids: Array<string>) => {
      if (!hasValue(ids)) return;
      ids = Array.isArray(ids) ? ids : [ids];
      const operation = REMOVE_LEARNERS;
      const callback = () => {
        actions.manipulateData(getParams(operation, { learners: ids }));
      };
      WithRemoveConfirmation({ callback, learners: ids, learnersMap });
    },
    filter: ({ filterType, data }: { filterType: string; data?: any }) => {
      actions.manipulateData(getParams(SEARCH_LEARNERS, { filters: { type: filterType, data } }));
    },
    changeEnrollStatus: (
      learners: TChangeEnrollLearner | Array<TChangeEnrollLearner>,
      entity: {
        sessionId?: string;
        eventId?: string;
      }
    ) => {
      if (!hasValue(learners)) return;
      learners = Array.isArray(learners) ? learners : [learners];
      actions.manipulateData(getParams(CHANGE_LEARNER_STATUS, { learners, entity }));
    },
    fetchDrawerData: ({
      learnerId,
      operation,
      filters,
      sortOrder,
      sortField,
    }: {
      learnerId?: string;
      operation?: string;
      filters: object;
      sortOrder: string;
      sortField: string;
    }) => {
      const processedFilters = processEnrollmentFilters(filters);
      actions.getLearnerEntities({
        learnerId,
        operation,
        filters: processedFilters,
        sortOrder,
        sortField,
      });
    },
    fetchEventEnrollDrawerData: ({
      learnerId,
      operation,
      filters,
      sortOrder,
      sortField,
      eventId,
    }: {
      learnerId?: string;
      operation?: string;
      filters: object;
      sortOrder: string;
      sortField: string;
      eventId: string;
    }) => {
      const processedFilters = processEnrollmentFilters(filters);
      actions.getLearnerSessionsWithinEvent({
        learnerId,
        operation,
        filters: processedFilters,
        sortOrder,
        sortField,
        eventId,
      });
    },
    clearEnrollmentDrawerData: actions.clearEnrollmentDrawerData,
    clearEventEnrollmentDrawerData: actions.clearEventEnrollmentDrawerData,

    changeRelevance: (learners: string | string[]) => {
      if (!hasValue(learners)) return;
      learners = Array.isArray(learners) ? learners : [learners];
      if (learners) {
        setLearnersForModuleRelevanceChange(learners as string[]);
        setShowRelevanceSelectionModal(true);
      }
    },
    onRelevanceModalClose: () => {
      setShowRelevanceSelectionModal(false);
    },
    trackViewProfileClick: (learnerId: string) => {
      sendTrackPageOperation(tracker, {
        operationStatus: {
          data: { postData: { learners: [learnerId] }, operation: VIEW_LEARNER_PROFILE },
        },
        extraTrackingProperties: {
          [SNOWPLOW_FIELD_NAMES.PAGE_NAME]: trackerPageName,
        },
      });
    },
    sort: ({ sort }: any) => {
      actions.manipulateData(getParams(SORT_LEARNERS, { sort }));
    },
    exportLearners: ({ email }: { email: string }) => {
      actions.manipulateData(getParams(EXPORT_LEARNERS, { email }));
    },
    onExportLearnersModalOpen: () => {
      sendExportLearnerClicked(tracker, { pageName: trackerPageName });
    },
  };

  return learnerActions;
};

export default useLearnerActions;
