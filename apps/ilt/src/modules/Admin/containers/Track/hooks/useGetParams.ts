import {
  OPERATIONS,
  DEFAULT_PAGINATION,
  DEFAULT_LEARNERS_FILTER,
} from '~/modules/Admin/config/track.constants';
import { flattenFilters } from '~/modules/Admin/containers/Track/helpers';
import type { FiltersType } from '~/modules/Admin/containers/Track/typeDefs';

import type { TUseGetParams } from './typeDefs';

const {
  GET_UPDATED_LEARNERS,
  SORT_LEARNERS,
  SEARCH_LEARNERS,
  PAGINATE_LEARNERS,
  REMOVE_LEARNERS,
  CHANGE_LEARNER_STATUS,
  VIEW_LEARNER_PROFILE,
  CHANGE_RELEVANCE,
  EXPORT_LEARNERS,
} = OPERATIONS;

const useGetParams = ({
  sortState,
  setSort,
  filterState,
  setFilters,
  newUserProfileAndGroupPageUIEnabled,
  learnersMap,
}: TUseGetParams) => {
  const getParams = (operation: string, data: any = {}) => {
    let params = {};
    switch (operation) {
      case REMOVE_LEARNERS:
        params = {
          sort: sortState,
          learners: data.learners,
          pagination: DEFAULT_PAGINATION,
          filters: flattenFilters(filterState),
        };
        break;
      case SEARCH_LEARNERS:
        let updatedFilters = DEFAULT_LEARNERS_FILTER;
        setFilters((prevFilters: FiltersType) => {
          if (data.filters.type !== 'reset') {
            updatedFilters = {
              ...prevFilters,
              [data.filters.type]: data.filters.data,
            };
          }
          return updatedFilters;
        });

        params = {
          sort: sortState,
          pagination: DEFAULT_PAGINATION,
          filters: flattenFilters(updatedFilters),
        };
        break;
      case CHANGE_LEARNER_STATUS:
        params = {
          learners: data.learners,
          enrollmentFreezeStatus:
            data.learners[0].enrollmentFreezeStatus !== undefined
              ? data.learners[0].enrollmentFreezeStatus
              : {},
          entity: data.entity,
        };
        break;
      case VIEW_LEARNER_PROFILE:
        params = {
          learners: data.learners,
          newUserProfileEnabled: newUserProfileAndGroupPageUIEnabled,
        };
        break;
      case GET_UPDATED_LEARNERS:
        params = {
          sort: sortState,
          pagination: DEFAULT_PAGINATION,
          filters: flattenFilters(filterState),
        };
        break;
      case SORT_LEARNERS:
        setSort(data.sort);
        params = {
          sort: data.sort,
          pagination: DEFAULT_PAGINATION,
          filters: flattenFilters(filterState),
        };
        break;
      case PAGINATE_LEARNERS:
        params = {
          sort: sortState,
          pagination: data.pagination,
          filters: flattenFilters(filterState),
        };
        break;
      case EXPORT_LEARNERS:
        params = data;
        break;
      case CHANGE_RELEVANCE:
        params = {
          learners: data.learners,
          moduleRelevance: data.moduleRelevanceSelection,
        };
        break;
    }

    return {
      ...params,
      operation,
      learnersMap,
    };
  };

  return getParams;
};

export default useGetParams;
