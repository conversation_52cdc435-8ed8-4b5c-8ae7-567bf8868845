import type {
  LearnersMapType,
  FiltersType,
  SearchedLearnersType,
  TrackActionsType,
} from '~/modules/Admin/containers/Track/typeDefs';
import type { TSnowplowTrackerType } from '~/modules/Admin/hooks/useILTAdminSnowplowTracker/typeDefs';

export type TUseGetParams = {
  sortState: object;
  setSort: Function;
  filterState: FiltersType;
  setFilters: Function;
  newUserProfileAndGroupPageUIEnabled: boolean;
  learnersMap: LearnersMapType;
};

export type TUseLearnerActions = {
  selectedLearners: Set<string>;
  setSelectedLearners: Function;
  setLearnersForModuleRelevanceChange: Function;
  setShowRelevanceSelectionModal: Function;
  searchedLearners: SearchedLearnersType;
  actions: TrackActionsType;
  getParams: Function;
  learnersMap: LearnersMapType;
  tracker: TSnowplowTrackerType;
  trackerPageName: string;
};
