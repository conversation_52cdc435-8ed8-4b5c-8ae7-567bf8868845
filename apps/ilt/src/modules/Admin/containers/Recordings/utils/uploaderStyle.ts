import { tokens } from '@mindtickle/styles/lib';

export default () => {
  const uploaderStyle = document.createElement('style');
  uploaderStyle.textContent = getUploaderStyleOverride();
  return uploaderStyle;
};

export const getUploaderStyleOverride = () => `
  div#simplemodal-container {
    height: 100% !important;
    width: 100% !important;
    top: 0 !important;
    left: 0 !important;
    min-height: 424px;
    border-radius: 8px;
    box-shadow: unset;
  }

  div#simplemodal-data.boxArea.simplemodal-data {
    position: relative;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  .textalign_C.fileDropperClass {
    font-size: 14px;
    font-weight: 600;
    color: ${tokens.textTokens.COLOR_TEXT_DEFAULT};
  }

  [ng-app=learnginBoardApp] #supportingDocUploader .selectArea.uploaderCousin .uploadIcon,
  .upload_bg_crop {
    height: 230px;
    margin: unset;
  }

  div#simplemodal-container .simplemodal-data.whiteBg_skipPopUp {
    height: unset;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translate(-50%, -50%);
    top: 50%;
    left: 50%;
    position: relative;
  }

  div#simplemodal-container .simplemodal-data.whiteBg_skipPopUp .colorBg_skipPopUp .errMsg {	
    margin-top: 40px !important;	
  }

  .uploadArea.uploadAreaPopup .uploadBox {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  
  .js-supported-file-msg{
    line-height: 16px;
  }
  .js-supported-file-msg::after{
    color: ${tokens.textTokens.COLOR_TEXT_SECONDARY};
  }

  #supportingDocUploader,
  .uploaderChild,
  .uploaderCousin,
  .uploaderGrandChild,
  .uploadAreaPopup  {
    height: 100%;
  }
`;
