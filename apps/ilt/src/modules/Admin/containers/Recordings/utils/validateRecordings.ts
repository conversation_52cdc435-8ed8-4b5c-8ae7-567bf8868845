/* eslint-disable max-statements */ // this disable is intentional
import {
  MEDIA_STATUS,
  UPLOADING_STATUS,
  VALIDATION_STATUS,
} from '~/modules/Admin/config/recordings.constants';

import { checkAllSavedRecordingsDeleted, searchRecording } from './recordingsPureUtils';

import type { TValidadateRecordingsParams } from '../typeDefs';

export default ({ recordingsUnpublished, recordings, mediaMap }: TValidadateRecordingsParams) => {
  if (
    checkAllSavedRecordingsDeleted({
      originalRecordings: recordingsUnpublished,
      recordings,
    })
  ) {
    return { status: VALIDATION_STATUS.SUCCESS };
  }
  if (!recordingsUnpublished.length && recordings.length === 1) {
    const { mediaId, uploading } = recordings[0];
    if (!mediaId && !uploading) {
      return { status: VALIDATION_STATUS.SUCCESS };
    }
  }
  for (const item of recordings) {
    const { mediaId, uploading } = item;
    if (UPLOADING_STATUS.FAILED === uploading) {
      return {
        status: VALIDATION_STATUS.FAILED,
        _key: item._key,
        message: 'Upload failed',
      };
    }
    if (!mediaId && !uploading) {
      return {
        status: VALIDATION_STATUS.FAILED,
        _key: item._key,
        message: 'Either upload or delete',
      };
    }
    const serverRecordingStatus = searchRecording(recordingsUnpublished, mediaId);
    const currentInfoOfMedia = mediaMap[mediaId || ''];
    if (currentInfoOfMedia) {
      const { mediaStatus } = currentInfoOfMedia;
      if (mediaStatus === MEDIA_STATUS.FAILED) {
        return {
          status: VALIDATION_STATUS.FAILED,
          _key: item._key,
          message: 'Processing failed',
        };
      }
    }
    if (serverRecordingStatus) {
      const { mediaStatus } = serverRecordingStatus;
      if (mediaStatus === MEDIA_STATUS.FAILED) {
        return {
          status: VALIDATION_STATUS.FAILED,
          _key: item._key,
          message: 'Processing failed',
        };
      }
    }
  }
  return { status: VALIDATION_STATUS.SUCCESS };
};
