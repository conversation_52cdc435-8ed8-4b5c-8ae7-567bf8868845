import { useEffect, useState, useRef } from 'react';

import { connect } from 'react-redux';
import { compose } from 'redux';
import { usePerformanceData as getPerformanceData } from 'ui_shell/PerformanceMetrics';

import Loader from '@mindtickle/loader';
import { getActions } from '@mindtickle/medux/Action';
import { injectSaga } from '@mindtickle/medux/Saga';
import { deepEqual } from '@mindtickle/utils';

import usePrevious from '~/hooks/usePrevious';
import { createWithUseURLAsState } from '~/hooks/useURLAsState';
import createWithPageTimeSpent from '~/modules/Admin/hocs/withPageTimeSpent';
import { useILTAdminSnowplowTracker } from '~/modules/Admin/hooks/useILTAdminSnowplowTracker';
import {
  EVENT_NAME,
  SNOWPLOW_FIELD_NAMES,
} from '~/modules/Admin/hooks/useILTAdminSnowplowTracker/constants';
import { prepareFilters } from '~/utils';

import {
  MANIPULATE_BUILD_DATA,
  GET_BUILD_DATA,
  UPDATE_LIVE_CHALLENGE,
  RECORDINGS_MANIPULATE_DATA,
} from '../../actionTypes';
import {
  SUPPORTED_SORTING as SUPPORTED_TABLE_SORTING,
  SORTING_ORDER as TABLE_SORTING_ORDER,
} from '../../components/SessionsTable/constants';
import SessionsWrapper from '../../components/SessionsWrapper';
import { sendBulkCreateIltSessionEvent } from '../../components/SessionsWrapper/utils/trackEvents';
import { PAGE_LOAD_DATA } from '../../config/constants';
import { OPERATIONS as RECORDINGS_OPERATIONS } from '../../config/recordings.constants';
import {
  OPERATIONS,
  SUPPORTED_FILTERS,
  SUPPORTED_FILTERS_API_KEY,
  ILT_CALENDAR_AUTO_SYNC_CONFIG_KEY,
} from '../../config/sessions.constants';

import saga from './saga';

import type {
  TBuild,
  TContext,
  TEntityFilterHiddenMap,
  TEventSessionListMap,
  TGetDataFunc,
  TLiveChallenge,
  TManipulateDataFunc,
  TModuleDetails,
  TOperationStatus,
  TOwnProps,
  TSessionStatusFilter,
  TSessions,
  TSessionsMap,
  TEntityStatsMap,
} from './typeDefs';
import type {
  TSortingOrder,
  TSupportedSortingField,
} from '../../components/SessionsTable/typeDefs';
import type { Dispatch } from 'redux';

const { SEARCH } = OPERATIONS;

const trackerPageName = PAGE_LOAD_DATA.ILT_BUILD_PAGE.PAGE_NAME;

const Build = (props: TBuild) => {
  const {
    status: { loaded },
    actions,
    ILTFilters,
    sessions,
    sessionsMap,
    entityFilterHiddenMap,
    eventSessionListMap,
    entityStatsMap,
    operationStatus,
    moduleDetails,
    companySettings,
    context,
    haveTrackAccess,
    haveBuildAccess,
    companyData,
    integrations,
    updateILTFilters,
    liveChallenge,
    extraConfig,
  } = props;
  const isCalendarAutoSyncEnabled =
    ILT_CALENDAR_AUTO_SYNC_CONFIG_KEY in extraConfig!
      ? (extraConfig.isILTSessionCalendarAutoSyncEnabled as boolean)
      : true;
  const [colSortOrder, setColSortOrder] = useState<TSortingOrder>(TABLE_SORTING_ORDER.ASC);
  const [colSortField, setColSortField] = useState<TSupportedSortingField>(
    SUPPORTED_TABLE_SORTING.START_TIME
  );
  const mounted = useRef<boolean>();
  const prevILTFilters = usePrevious(ILTFilters);
  const tracker = useILTAdminSnowplowTracker();

  useEffect(() => {
    actions.getData({ filters: ILTFilters, sortOrder: colSortOrder, sortField: colSortField });
    return () => {
      actions.resetData();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (!mounted.current) {
      // do componentDidMount logic
      mounted.current = true;
    } else {
      // do componentDidUpdate logic
      if (!deepEqual(prevILTFilters, ILTFilters)) {
        actions.manipulateData({
          operation: SEARCH,
          filters: prepareFilters(ILTFilters),
          sortOrder: colSortOrder,
          sortField: colSortField,
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps -- dependant props already included
  }, [ILTFilters, prevILTFilters, actions.manipulateData]);

  useEffect(() => {
    if (loaded) {
      const metricPageLoad = getPerformanceData();

      metricPageLoad &&
        tracker.trackStructuredEvent({
          eventName: EVENT_NAME.PAGE_LOAD_VIEW,
          [SNOWPLOW_FIELD_NAMES.PAGE_NAME]: trackerPageName,
          ...metricPageLoad,
        });
    }
  }, [loaded, tracker]);

  useEffect(() => {
    const { loaded: newLoaded, data: { operation = '' } = {}, hasError } = operationStatus || {};
    if (newLoaded && !hasError && OPERATIONS.ADD_BULK === operation) {
      sendBulkCreateIltSessionEvent(tracker, {
        operationStatus: props.operationStatus,
        extraTrackingProperties: {
          [SNOWPLOW_FIELD_NAMES.PAGE_NAME]: trackerPageName,
        },
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps -- need to execute only on change of operationstatus loaded
  }, [operationStatus?.loaded]);

  if (!loaded) return <Loader size="sizeSmall" />;

  return (
    <SessionsWrapper
      trackerPageName={trackerPageName}
      actions={actions}
      sessions={sessions}
      sessionsMap={sessionsMap}
      entityFilterHiddenMap={entityFilterHiddenMap}
      eventSessionListMap={eventSessionListMap}
      entityStatsMap={entityStatsMap}
      operationStatus={operationStatus}
      moduleDetails={moduleDetails}
      context={context}
      haveTrackAccess={haveTrackAccess}
      haveBuildAccess={haveBuildAccess}
      defaultModuleRelevance={moduleDetails.moduleRelevance}
      companyData={companyData}
      integrations={integrations}
      ILTFilters={ILTFilters}
      colSortOrder={colSortOrder}
      setColSortOrder={setColSortOrder}
      colSortField={colSortField}
      setColSortField={setColSortField}
      updateILTFilters={updateILTFilters}
      liveChallenge={liveChallenge}
      companySettings={companySettings}
      isILTSessionCalendarAutoSyncEnabled={isCalendarAutoSyncEnabled}
    />
  );
};

Build.defaultProps = {
  sessions: {
    data: [],
    hasMore: false,
  },
  sessionsMap: {},
  entityFilterHiddenMap: {},
  eventSessionListMap: {},
  entityStatsMap: {},
};

const mapDispatchToProps = (dispatch: Dispatch) => ({
  manipulateData: (data: any) => {
    dispatch(
      getActions(MANIPULATE_BUILD_DATA)(data, {
        loadingData: { operation: data.operation },
      })
    );
  },
  resetData: () => {
    dispatch(
      getActions({
        name: GET_BUILD_DATA,
        options: {
          async: true,
        },
      }).RESET()
    );
  },
  getData: (payload: any) => {
    dispatch(getActions(GET_BUILD_DATA)(payload));
  },
  resetNewLiveChallengeData: () => {
    dispatch(
      getActions({
        name: UPDATE_LIVE_CHALLENGE,
        options: {
          async: true,
        },
      }).RESET()
    );
  },
  openUploadRecordings: (session: any) => {
    dispatch(
      getActions(RECORDINGS_MANIPULATE_DATA)({
        operation: RECORDINGS_OPERATIONS.ADD_SESSION,
        session,
      })
    );
  },
});

const mapStateToProps = (state: {
  ilt: { build?: any; details?: any; context?: any; liveChallenge?: any };
}) => {
  const {
    isLoading,
    loaded,
    hasError,
    operationStatus,
    sessions,
    sessionsMap,
    eventSessionListMap,
    entityFilterHiddenMap,
    entityStatsMap,
  } = state.ilt.build;
  const { staticData: moduleDetails } = state.ilt.details;
  const { companySettings } = state.ilt.details;
  const context = state.ilt.context;
  const { liveChallenge } = state.ilt;
  return {
    loaded,
    context,
    hasError,
    sessions,
    isLoading,
    sessionsMap,
    eventSessionListMap,
    entityFilterHiddenMap,
    moduleDetails,
    companySettings,
    operationStatus,
    liveChallenge,
    entityStatsMap,
  };
};

const mergeProps = (
  stateProps: {
    loaded: boolean;
    isLoading: boolean;
    hasError: boolean;
    context: TContext;
    operationStatus: TOperationStatus;
    sessions: TSessions;
    sessionsMap: TSessionsMap;
    eventSessionListMap: TEventSessionListMap;
    entityFilterHiddenMap: TEntityFilterHiddenMap;
    entityStatsMap: TEntityStatsMap;
    moduleDetails: TModuleDetails;
    companySettings: any;
    liveChallenge: TLiveChallenge;
  },
  dispatchProps: {
    manipulateData: TManipulateDataFunc;
    getData: TGetDataFunc;
    resetData: () => void;
    resetNewLiveChallengeData: () => void;
    openUploadRecordings: (session: any) => void;
  },
  ownProps: TOwnProps
) => ({
  status: {
    loaded: stateProps.loaded,
    isLoading: stateProps.isLoading,
    hasError: stateProps.hasError,
  },
  context: stateProps.context,
  operationStatus: stateProps.operationStatus,
  sessions: stateProps.sessions,
  sessionsMap: stateProps.sessionsMap,
  eventSessionListMap: stateProps.eventSessionListMap,
  entityFilterHiddenMap: stateProps.entityFilterHiddenMap,
  entityStatsMap: stateProps.entityStatsMap,
  moduleDetails: stateProps.moduleDetails,
  // @ts-ignore
  companySettings: stateProps.companySettings,
  ...ownProps,
  actions: {
    manipulateData: dispatchProps.manipulateData,
    getData: dispatchProps.getData,
    resetData: dispatchProps.resetData,
    resetNewLiveChallengeData: dispatchProps.resetNewLiveChallengeData,
    openUploadRecordings: dispatchProps.openUploadRecordings,
  },
  liveChallenge: stateProps.liveChallenge,
});

const withConnect = connect(mapStateToProps, mapDispatchToProps, mergeProps);
const withSaga = injectSaga({ name: 'build', saga: saga });

const sessionStatusFilter = SUPPORTED_FILTERS_API_KEY[
  SUPPORTED_FILTERS.SESSION_STATUS
] as TSessionStatusFilter;

const withFiltersAsState = createWithUseURLAsState({
  defaultValue: {
    [SUPPORTED_FILTERS.SESSION_STATE]: 'ALL',
    [SUPPORTED_FILTERS.SEARCH]: '',
    [SUPPORTED_FILTERS.SESSION_TYPE]: 'ALL',
    [SUPPORTED_FILTERS.ILT_DATE_RANGE_DD]: [],
    [SUPPORTED_FILTERS.SESSION_STATUS]: [
      sessionStatusFilter.PUBLISHED,
      sessionStatusFilter.UNPUBLISHED,
    ],
  },
  key: 'ILTFilters',
});
const withPageTimeSpent = createWithPageTimeSpent({
  pageName: trackerPageName,
});
export default compose(withSaga, withConnect, withPageTimeSpent, withFiltersAsState)(Build);
