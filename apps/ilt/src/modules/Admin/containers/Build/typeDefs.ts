import type { TAuthPermissionsDefinition, TSeriesPermissionsDefinition } from '~/typeDefs';

import { STAGES } from '../../config/constants';
import ModuleRoutes from '../../config/routes';

import type { TSessionObject } from '../../components/SessionEditDrawer/typeDefs';
import type {
  TSortingOrder,
  TSupportedSortingField,
} from '../../components/SessionsTable/typeDefs';

export interface TBuild {
  status: {
    isLoading: boolean;
    loaded: boolean;
    hasError: boolean;
  };
  context: TContext;
  companyData: TCompanyData;
  seriesData: TSeriesData;
  integrations: object;
  moduleData: TModuleData;
  onInvite: () => void;
  haveTrackAccess: boolean;
  haveBuildAccess: boolean;
  operationStatus: TOperationStatus & { operation?: string };
  sessions: TSessions;
  sessionsMap: TSessionsMap;
  entityFilterHiddenMap: TEntityFilterHiddenMap;
  eventSessionListMap: TEventSessionListMap;
  entityStatsMap: TEntityStatsMap;
  moduleDetails: TModuleDetails;
  companySettings: any;
  actions: {
    manipulateData: TManipulateDataFunc;
    getData: TGetDataFunc;
    onInvite?: () => void;
    resetData: () => void;
    resetNewLiveChallengeData: () => void;
    openUploadRecordings: (sessionDetail: TSessionObject & { id: string }) => void;
  };
  ILTFilters: TILTFilters;
  updateILTFilters?: (filters: TILTFilters) => void;
  liveChallenge?: TLiveChallenge;
  extraConfig?: object;
}

export type TContext = {
  companyId: string;
  companyUrl: string;
  globalPermissions: TAuthPermissionsDefinition;
  isSiteOwner: boolean;
  moduleId: string;
  moduleType: string;
  seriesId: string;
  seriesPermissions: TSeriesPermissionsDefinition;
  userId: string;
};

export type TOperationStatus = {
  isLoading: boolean;
  hasError: boolean;
  loaded: boolean;
  error: any;
  data: any;
  loadingData?: { operation: string };
};

export type TSessions = {
  data: string[];
  hasMore: boolean;
  start: number;
  rows: number;
};

export type TSessionsMap = Record<string, TSessionObject>;
export type TEventSessionListMap = Record<string, string[]>;
export type TEntityFilterHiddenMap = Record<string, boolean>;
export type TEntityStats = {
  loaded?: boolean;
  attended: number;
  enrolled: number;
  waiting: number;
};
export type TEntityStatsMap = Record<string, TEntityStats>;

export type TModuleDetails = {
  invitedLearnersCount: number;
  isPublished: boolean;
  invitedLearnersCountLoaded: boolean;
  name: string;
  mappedSeries: string[];
  scoring: boolean;
  score: number;
  moduleRelevance: string;
};

export type TLiveChallenge = {
  data?: object;
  operationStatus?: {
    isLoading: boolean;
    loaded: boolean;
    hasError: boolean;
    partial: boolean;
    data?: object;
  };
};

export type TManipulateDataFunc = (data: { operation: string; [key: string]: any }) => void;
export type TGetDataFunc = ({
  filters,
  sortOrder,
  sortField,
}: {
  filters: TILTFilters;
  sortOrder: TSortingOrder;
  sortField: TSupportedSortingField;
}) => void;
export type TILTFilters = {
  dateRange?: number[];
  search?: string;
  sessionStatus?: string[];
  sessionType?: string;
  sessionState?: string;
};

export type TOwnProps = TCommonProps &
  TSessionsProps & {
    haveTrackAccess: boolean;
    haveBuildAccess: boolean;
  };

export type TSessionsProps = {
  moduleData: TModuleData;
  seriesData: TSeriesData;
  companyData: TCompanyData;
  onInvite: () => void;
  enabledFeatures: TEnabledFeatures;
  companySettings: any;
};

export type TModuleData = {
  id: string;
  type: string;
  name: string;
  isPublished: boolean;
  mappedSeries: string[];
  moduleRelevance: string;
};

export type TSeriesData = {
  id: string;
  name: string;
  permissions: any;
  seriesLevelMailSettings: any;
  sequentiallyLockedSeries: boolean;
};

export type TCompanyData = {
  id: string;
  url: string;
  isNewDashboard: boolean;
};

export type TEnabledFeatures = {
  moduleRelevanceEnabled: boolean;
  isUIDEnabled: boolean;
};

export type TStages = typeof STAGES;
export type TLifecycleRoutes = typeof ModuleRoutes.lifecycle;

export type TCommonProps = {
  stages: TStages;
  stageRoutes: TLifecycleRoutes;
  moduleId: string;
  seriesId: string;
  companyId: string;
  moduleName: string;
  moduleType: string;
  tabType: string;
  isPublished: boolean;
  integrations: object;
};

export interface TSessionStatusFilter {
  PUBLISHED: string;
  UNPUBLISHED: string;
  CANCELLED: string;
}
