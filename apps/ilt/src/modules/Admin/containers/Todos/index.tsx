import { useEffect } from 'react';

import { connect } from 'react-redux';
import { compose } from 'redux';

import { getActions } from '@mindtickle/medux/Action';
import { injectReducer } from '@mindtickle/medux/Reducer';
import { injectSaga } from '@mindtickle/medux/Saga';

import usePrevious from '~/hooks/usePrevious';

import { GET_TODOS, TOGGLE_TODOS } from './actionTypes';
import TodoPopover from './components/TodosPopover';
import TodoWrapper from './components/TodosWrapper';
import reducer from './reducer';
import saga from './saga';

import type { TTodosOwnProps, TTodosProps } from './typeDefs';

function Todos(props: TTodosProps) {
  const prevProps = usePrevious(props);
  const { todos, loaded, show, toggleShow } = props;
  useEffect(() => {
    if (prevProps) {
      const { show: oldShowState } = prevProps;
      const { show: newShowState, moduleId, seriesId, getTodos, toggleShow } = props;
      if (oldShowState !== newShowState) {
        if (oldShowState) {
          toggleShow(false);
        } else {
          getTodos({ moduleId, seriesId });
        }
      }
    }
  }, [prevProps, props]);

  return (
    <TodoPopover
      onVisibleChange={toggleShow}
      content={<TodoWrapper loading={!!show && !loaded} todos={todos} />}
      isVisible={!!show}
    />
  );
}

Todos.defaultProps = {
  todos: [],
  show: false,
};

const mapStateToProps = (
  state: {
    moduleTodos: {
      loaded: boolean;
      isLoading: boolean;
      hasError: boolean;
      data?: [
        {
          id: string;
          type: string;
          errors: string[];
        }
      ];
      show?: boolean;
    };
  },
  ownProps: TTodosOwnProps
) => {
  const { loaded, isLoading, hasError, data: todos, show } = state.moduleTodos;
  const { moduleId, seriesId, className } = ownProps;
  return {
    className,
    loaded,
    isLoading,
    hasError,
    todos,
    show,
    moduleId,
    seriesId,
  };
};
const mapDispatchToProps = (dispatch: (arg0: any) => void) => ({
  getTodos: ({ moduleId, seriesId }: { moduleId: string; seriesId: string }) =>
    dispatch(getActions(GET_TODOS)({ moduleId, seriesId })),
  toggleShow: (show: boolean) => {
    dispatch(getActions(TOGGLE_TODOS)({ show }));
    if (!show) {
      const { RESET } = getActions({
        name: GET_TODOS,
        options: { async: true },
      });
      dispatch(RESET());
    }
  },
});

const withConnect = connect(mapStateToProps, mapDispatchToProps);
const withReducer = injectReducer({ name: 'moduleTodos', reducer });
const withSaga = injectSaga({ name: 'moduleTodos', saga });

export const showTodos = () => getActions(TOGGLE_TODOS)({ show: true });
export const hideTodos = () => getActions(TOGGLE_TODOS)({ show: false });

export default compose<React.ComponentType<TTodosOwnProps>>(
  withReducer,
  withSaga,
  withConnect
)(Todos);
