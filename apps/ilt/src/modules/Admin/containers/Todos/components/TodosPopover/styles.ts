import styled from 'styled-components';

import Icon from '@mindtickle/icon';
import Popover from '@mindtickle/popover';
import { tokens } from '@mindtickle/styles/lib';

export const StyledPopover = styled(Popover)`
  margin-bottom: 24px;
`;

export const StyledIcon = styled(Icon)`
  font-size: 16px;
  color: ${tokens.iconTokens.COLOR_ICON_DEFAULT};
  cursor: pointer;
  padding: 6px 8px;
  border: 1px solid ${tokens.borderTokens.COLOR_BORDER_DEFAULT};
  border-radius: 4px;

  &.active {
    color: ${tokens.textTokens.COLOR_TEXT_ACCENT};
  }
`;
