import { useEffect, useState } from 'react';

import { ICON_MAP } from '@mindtickle/icon';

import { StyledPopover, StyledIcon } from './styles';

import type { TTodoProps } from './typeDefs';

const TodosPopover = (props: TTodoProps) => {
  const { content, onVisibleChange, isVisible } = props;
  const [visible, setVisible] = useState(isVisible);

  useEffect(() => {
    if (isVisible) {
      setVisible(isVisible);
    }
  }, [isVisible]);

  const handleVisibleChange = (popoverState: boolean) => {
    setVisible(popoverState);
    if (popoverState) {
      onVisibleChange(popoverState);
    } else {
      setTimeout(() => onVisibleChange(popoverState), 200);
    }
  };

  return (
    <StyledPopover
      content={content}
      trigger="click"
      overlayStyle={{ width: 320 }}
      onVisibleChange={handleVisibleChange}
      visible={visible}
      placement="leftBottom" // as in ilt only tooltip present
    >
      <StyledIcon type={ICON_MAP.TO_DOS} />
    </StyledPopover>
  );
};

export default TodosPopover;
