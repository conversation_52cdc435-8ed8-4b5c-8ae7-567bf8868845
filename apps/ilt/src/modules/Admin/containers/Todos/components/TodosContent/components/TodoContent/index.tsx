import { useNavigate } from 'react-router-dom';

import EllipsisTooltip from '@mindtickle/ellipsis-tooltip';

import { messages } from '../../messages';
import Empty from '../Empty';

import {
  StyledTodoContent,
  StyledSubTitle,
  StyledTodosType,
  StyledTodoList,
  StyledScrollingContent,
  StyledEllipsisTooltip,
  StyleLoader,
} from './styles';

import type { TodoProps, TodoContentProps } from './typeDefs';
import type { TodoItem } from '../../typeDefs';

const TodoList = ({ todos, title, todosClickHandler = () => {} }: TodoProps) => {
  let navigate = useNavigate();
  const onTodoItemSelect = (url: string, text: string, loId: string) => {
    if (url) {
      if (typeof todosClickHandler === 'function') {
        todosClickHandler(text, loId);
      }
      navigate(url, { replace: true });
    }
  };

  return (
    <StyledTodosType>
      {title && <div className="todo-title">{title}</div>}
      {todos &&
        todos.map(item => (
          <StyledTodoList key={item.id}>
            <div className="todo-bullet-icon" />
            <StyledEllipsisTooltip>
              <EllipsisTooltip
                placement="bottomLeft"
                title={item.text}
                onClick={() => onTodoItemSelect(item.url, item.text, item.id)}
              >
                {item.text}
              </EllipsisTooltip>
            </StyledEllipsisTooltip>
          </StyledTodoList>
        ))}
    </StyledTodosType>
  );
};

const TodoContent = (props: TodoContentProps) => {
  const { data, isLoading, todosClickHandler = () => {}, todosLength, ...args } = props;
  const keys = Object.keys(data);
  const getTodosCountLabel = () => `Todos - (${todosLength})`;

  if (isLoading) {
    return <StyleLoader type={'Small'} {...args} />;
  } else if (keys.length === 0) {
    return (
      <StyledTodoContent>
        <div className="count-with-title ">{getTodosCountLabel()}</div>
        <Empty />
      </StyledTodoContent>
    );
  }
  return (
    <StyledTodoContent>
      <div className="count-with-title ">{getTodosCountLabel()}</div>
      <StyledSubTitle>{messages.TODO_SUB_TITLE}</StyledSubTitle>
      {keys.length > 1 ? (
        <StyledScrollingContent>
          {keys.map((objKey: string) => {
            const item: Array<TodoItem> = data[objKey];
            return (
              <TodoList
                key={objKey}
                todos={item}
                title={objKey}
                todosClickHandler={todosClickHandler}
              />
            );
          })}
        </StyledScrollingContent>
      ) : (
        <StyledScrollingContent>
          <TodoList
            key={keys[0]}
            todos={data[keys[0]]}
            title=""
            todosClickHandler={todosClickHandler}
          />
        </StyledScrollingContent>
      )}
    </StyledTodoContent>
  );
};

export default TodoContent;
