import TodoContent from './components/TodoContent';
import { getFormattedTodosData } from './utils';

import type { TProps } from './typeDefs';

const Todos = (props: TProps) => {
  const { todos, config, isLoading, todosClickHandler = () => {} } = props;
  const { finalConfig, todosLength } = getFormattedTodosData(config, todos);
  return (
    <TodoContent
      data={finalConfig}
      todosLength={todosLength}
      isLoading={isLoading}
      todosClickHandler={todosClickHandler}
    />
  );
};

export default Todos;
