import type { Todos, Config, FinalConfig } from './typeDefs';

export function getFormattedTodosData(config: Config, todos: Todos) {
  const { todoMessages } = config;
  const finalConfig: FinalConfig = {};
  let todosLength: number = 0;
  todos &&
    Array.isArray(todos) &&
    todos.forEach(({ errors = [], type, name }) => {
      todosLength++;
      errors.forEach(todo => {
        const node = todoMessages[type]?.[todo];
        if (!node) {
          return;
        }
        const nodeGroup = node.category;
        if (!finalConfig[nodeGroup]) {
          finalConfig[nodeGroup] = [];
        }
        finalConfig[nodeGroup].push({
          nodeGroup: nodeGroup,
          id: todo,
          text: node.text(),
          url: node.getNodeUrl(type, { name }),
        });
      });
    });

  return { finalConfig, todosLength };
}
