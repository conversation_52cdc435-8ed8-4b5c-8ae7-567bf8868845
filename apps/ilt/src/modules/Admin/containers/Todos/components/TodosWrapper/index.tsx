import useMultidayEnabled from '~/modules/Admin/hooks/useMultidayEnabled';

import TodosContent from '../TodosContent';

import { NODE_GROUPS, TODO_TYPES } from './constants';
import todoMessages from './todoMessages';

import type { TProps } from './typeDefs';

const TodosWrapper = (props: TProps) => {
  const { loading, todosClickHandler = () => {}, todos = null } = props;
  const { isMultidayEnabled } = useMultidayEnabled();
  const todosConfig = todoMessages(isMultidayEnabled);
  const config = { NODE_GROUPS, TODO_TYPES, todoMessages: todosConfig };
  return (
    <TodosContent
      isLoading={loading}
      todos={loading ? null : todos}
      config={config}
      todosClickHandler={todosClickHandler}
    />
  );
};

export default TodosWrapper;
