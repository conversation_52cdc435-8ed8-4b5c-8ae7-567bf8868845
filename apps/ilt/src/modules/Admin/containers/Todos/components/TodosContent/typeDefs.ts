export interface Config {
  NODE_GROUPS: Record<string, string>;
  TODO_TYPES: Record<string, string>;
  todoMessages: {
    [domainLevel: string]: {
      [errorType: string]: {
        text: Function;
        category: string;
        getNodeUrl: Function;
      };
    };
  };
}

export type Todos =
  | [
      {
        id: string;
        name?: string;
        type: string;
        errors: string[];
      }
    ]
  | null;

export type TProps = {
  todos: Todos;
  isLoading: boolean;
  config: Config;
  todosClickHandler?: Function;
};

export interface TodoItem {
  nodeGroup: string;
  id: string;
  text: string;
  url: string;
}

export type FinalConfig = Record<string, Array<TodoItem>>;
