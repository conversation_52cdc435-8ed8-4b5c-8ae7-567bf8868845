import styled from 'styled-components';

import Loader from '@mindtickle/loader';
import { tokens, mixins } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS } from '~/config/constants';

export const StyledTodoContent = styled.div`
  .count-with-title {
    ${mixins.blackLink()};
  }
`;

export const StyledSubTitle = styled.div`
  padding: 4px 0 0 0;
  ${mixins.smallGreyLink()};
`;

export const StyledTodosType = styled.div`
  cursor: pointer;
  padding-bottom: 20px;

  &:last-child {
    padding-bottom: 0px;
  }

  .todo-title {
    ${mixins.smallBlackText()};
    padding-bottom: 8px;
  }
`;

export const StyledTodoList = styled.div`
  margin-bottom: 8px;

  .todo-bullet-icon {
    width: 6px;
    height: 6px;
    border-radius: 6px;
    background: ${tokens.bgTokens.COLOR_BG_ACCENT};
    display: inline-block;
    vertical-align: middle;
    margin-right: 8px;
  }
`;

export const StyleLoader = styled(Loader)`
  margin-top: 65px;
  margin-bottom: 65px;
`;

export const StyledEllipsisTooltip = styled.div`
  display: inline-block;
  vertical-align: middle;
  ${mixins.smallActiveLink()};
  ${mixins.truncate(`235px`)};
  font-weight: 600;

  .${THEME_PREFIX_CLS}-tooltip-inner {
    white-space: break-spaces;
  }
`;

export const StyledTodoEmptyState = styled.div`
  text-align: center;
  padding: 85px 0;

  .empty-state {
    display: inline-block;
    ${mixins.h3Grey()};

    .empty-text {
      margin-top: 4px;
    }

    .empty-icon {
      font-size: 20px;
    }
  }
`;

export const StyledScrollingContent = styled.div`
  max-height: calc(100vh - 350px);
  overflow: auto;
  margin-top: 20px;
  width: 268px;
`;
