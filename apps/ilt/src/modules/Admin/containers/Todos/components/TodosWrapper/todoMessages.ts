import { LIFECYCLE_STAGES } from 'ui_shell/GlobalConstants';

import { addQuery } from '@mindtickle/utils/url';

import ModuleRoutes from '~/modules/Admin/config/routes';
import {
  SESSION_STATE,
  SESSION_STATUS,
  SESSION_TYPES_FILTER_OPTIONS,
  SUPPORTED_FILTERS,
} from '~/modules/Admin/config/sessions.constants';

import { NODE_GROUPS, TODO_DOMAIN_LEVELS, TODO_TYPES } from './constants';

function getLearningObjectTodos(
  CATEGORY_TO_URL_GENERATOR: Record<string, Function>,
  isMultidayEnabled?: boolean
) {
  return {
    [TODO_DOMAIN_LEVELS.ILT]: {
      [TODO_TYPES.INSUFFICIENT_CHILDREN]: {
        text: () =>
          isMultidayEnabled
            ? 'Add at least 1 session or event in the ILT'
            : 'Add at least 1 session in the ILT',
        category: NODE_GROUPS.BUILD_SECTION,
        getNodeUrl: CATEGORY_TO_URL_GENERATOR[NODE_GROUPS.BUILD_SECTION],
      },
    },
    [TODO_DOMAIN_LEVELS.EVENT]: {
      [TODO_TYPES.INSUFFICIENT_CHILDREN]: {
        text: () => 'Add at least 1 session in event',
        category: NODE_GROUPS.BUILD_SECTION,
        getNodeUrl: CATEGORY_TO_URL_GENERATOR[NODE_GROUPS.BUILD_SECTION],
      },
    },
  };
}

export default function getTodoMessages(isMultidayEnabled?: boolean) {
  const getURLForBuildSection = (domainLevel: string, { name }: { name: string }) => {
    switch (domainLevel) {
      case TODO_DOMAIN_LEVELS.ILT:
        return `${ModuleRoutes.lifecycle[LIFECYCLE_STAGES.BUILD]}`;
      case TODO_DOMAIN_LEVELS.EVENT:
        const newUrl = addQuery(ModuleRoutes.lifecycle[LIFECYCLE_STAGES.BUILD], {
          ILTFilters: JSON.stringify({
            [SUPPORTED_FILTERS.SESSION_STATE]: SESSION_STATE.ALL.filterValue,
            [SUPPORTED_FILTERS.SEARCH]: name,
            [SUPPORTED_FILTERS.SESSION_TYPE]: SESSION_TYPES_FILTER_OPTIONS.ALL.value,
            [SUPPORTED_FILTERS.ILT_DATE_RANGE_DD]: [],
            [SUPPORTED_FILTERS.SESSION_STATUS]: [
              SESSION_STATUS.PUBLISHED.filterValue,
              SESSION_STATUS.UNPUBLISHED.filterValue,
            ],
          }),
        });
        return newUrl;
      default:
        return `${ModuleRoutes.lifecycle[LIFECYCLE_STAGES.BUILD]}`;
    }
  };

  const CATEGORY_TO_URL_GENERATOR = {
    [NODE_GROUPS.BUILD_SECTION]: getURLForBuildSection,
  };

  const BUILD_SECTION_TODOS = getLearningObjectTodos(CATEGORY_TO_URL_GENERATOR, isMultidayEnabled);

  return BUILD_SECTION_TODOS;
}
