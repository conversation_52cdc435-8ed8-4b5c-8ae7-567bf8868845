export type TTodosOwnProps = { moduleId: string; seriesId: string; className?: string };

export type TTodosProps = TTodosOwnProps & {
  todos?: [
    {
      id: string;
      type: string;
      errors: string[];
    }
  ];
  show?: boolean;
  toggleShow: (show: boolean) => void;
  loaded: boolean;
  isLoading: boolean;
  hasError: boolean;
  getTodos: (param: { moduleId: string; seriesId: string }) => void;
};
