import { put, takeEvery, call, select } from 'redux-saga/effects';

import { getActions } from '@mindtickle/medux/Action';

import TodoService from '~/modules/Admin/api/todos';
import { ILT_ENTITIES } from '~/modules/Admin/constants/module';

import ILTSessionService from '../../api/sessionService';

import { GET_TODOS } from './actionTypes';
import { TODO_DOMAIN_LEVELS } from './components/TodosWrapper/constants';

function* getTodos({ payload: { moduleId, seriesId } }) {
  const { SUCCESS, FAIL } = getActions({
    name: GET_TODOS,
    options: { async: true },
  });
  const {
    ilt: {
      context: { companyId },
    },
  } = yield select();
  const params = { moduleId, seriesId, companyId };
  try {
    const todos = yield call(TodoService.getTodos, params);
    // Extract eventIds and sessionIds from the fetched data
    const eventIds = todos
      .filter(todo => todo.type === TODO_DOMAIN_LEVELS.EVENT)
      .map(todo => ({
        id: todo.id,
        entityType: ILT_ENTITIES.EVENT,
      }));
    //TODO: This is for sessions within event domain level, need to introduce for independent sessions if in future comes from backend
    const sessionIds = todos
      .filter(todo => todo.type === TODO_DOMAIN_LEVELS.SESSION)
      .map(todo => ({
        id: todo.id,
        entityType: ILT_ENTITIES.SESSION_WITHIN_EVENT,
      }));
    const entities = [...eventIds, ...sessionIds];

    let sessionsMap;
    try {
      ({ sessionsMap } = yield call(ILTSessionService.getEntitiesFullData, {
        moduleId,
        seriesId,
        companyId,
        entities,
      }));
    } catch (error) {
      sessionsMap = {};
    }

    const data = todos.map(todo => {
      const name = sessionsMap[todo.id]?.name || undefined;
      return { ...todo, name };
    });
    yield put(SUCCESS({ data }));
  } catch (error) {
    yield put(FAIL(error));
  }
}

export default function* handleGetTodos() {
  yield takeEvery(GET_TODOS, getTodos);
}
