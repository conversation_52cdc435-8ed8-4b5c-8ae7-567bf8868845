import EllipsisTooltip from '@mindtickle/ellipsis-tooltip';
import Icon, { ICON_MAP } from '@mindtickle/icon';
import Tooltip from '@mindtickle/tooltip';

import { StyledAddIcon, StyledTagWrapper, StyledTagCell } from '../../styles';

import type { TProps } from './typeDefs';

export default function AppliedTags({
  tagsMap,
  tagsListToShow,
  removeTag: removeTagOutsideModal,
  showAllTagsButton,
  preSelectedTags,
  onShowAllTagsClick: handleOnClick,
  canApplyTags,
}: TProps) {
  return (
    <StyledTagWrapper>
      {tagsListToShow &&
        tagsListToShow.map((id: string) => {
          let value = tagsMap[id];
          return (
            <StyledTagCell
              canApplyTags={canApplyTags}
              key={value.id}
              className="ellipses-tootip-tag"
            >
              <EllipsisTooltip title={value.name}>{value.name}</EllipsisTooltip>
              {canApplyTags && (
                <Icon
                  type={ICON_MAP.CLOSE}
                  className="close-icon"
                  onClick={() => removeTagOutsideModal(value.id)}
                />
              )}
            </StyledTagCell>
          );
        })}
      {showAllTagsButton && (
        <StyledTagCell canApplyTags={canApplyTags} className="show-all" onClick={handleOnClick}>
          See all {preSelectedTags.length}
        </StyledTagCell>
      )}
      {canApplyTags && (
        <Tooltip title="Add new tags">
          <StyledAddIcon type={ICON_MAP.ADD} onClick={handleOnClick} />
        </Tooltip>
      )}
    </StyledTagWrapper>
  );
}
