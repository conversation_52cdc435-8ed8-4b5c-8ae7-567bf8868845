import { useMemo, useRef, useEffect, useState } from 'react';

import { useUserAuth } from 'ui_shell/Auth';

import { ICON_MAP } from '@mindtickle/icon';
import { errorToast } from '@mindtickle/toast';
import Tooltip from '@mindtickle/tooltip';

import RemoteListAndApplyTags from '~/components/RemoteListApplyTags';

import { OPERATIONS } from '../../constants';
import {
  StyledTagIcon,
  StyledAddIcon,
  StyledModuleTagsModalWrapper,
  TagsText,
  TagsTextRegular,
  TagsUIWrapper,
} from '../../styles';
import AppliedTags from '../AppliedTags';
import TagsModal from '../TagsModal';

import type { TTagsWrapperProps } from './typeDefs';
import type { TTag } from '../../typeDefs';

const MAX_TAGS = 5;

export default function TagsWrapper({
  onShowAllTagsClick,
  appliedTags,
  hasInitialAppliedTagLoaded,
  operationStatus,
  removeTag: removeTagOutsideModal,
  showModal,
  onModalCancel: handleCancel,
  onModalApply: handleApply,
  categoriesMap,
  tagsMap,
  tags,
  canCreateTags,
  canApplyTags,
  suggestedTags,
  creating,
  onTagCreate,
}: TTagsWrapperProps) {
  const tagsInfoRef = useRef<Record<string, TTag>>(tagsMap || {});
  const [localSelected, setLocalSelected] = useState<TTag[]>([]);

  const preSelectedTags = useMemo(
    () => Object.keys(appliedTags || {}).filter(id => (appliedTags || {})[id]),
    [appliedTags]
  );
  const {
    tempFeatureConfig: { isNewApplyTagsModalEnabled = false },
  } = useUserAuth();
  const showAllTagsBtn = preSelectedTags.length > MAX_TAGS;
  const noOfTagsToShow = Math.min(MAX_TAGS, preSelectedTags.length || 0);
  const tagsListToShow = preSelectedTags.slice(0, noOfTagsToShow);

  const onRemoveTag = (
    tags: TTag[],
    _: TTag[],
    extraInfo?: { areRemovedTagsSubmitted?: boolean }
  ) => {
    const { areRemovedTagsSubmitted = false } = extraInfo || {};
    if (!areRemovedTagsSubmitted && tags.length) {
      setLocalSelected(locallySelected => locallySelected.filter(({ id }) => id !== tags[0].id));
      removeTagOutsideModal(tags[0].id);
    }
  };

  const onApplyTags = (tags: TTag[]) => {
    for (const tag of tags) {
      tagsInfoRef.current[tag.id] = tag;
    }
    setLocalSelected(tags);
    handleApply(tags.map(tag => tag.id));
  };

  useEffect(() => {
    if (isNewApplyTagsModalEnabled && hasInitialAppliedTagLoaded) {
      setLocalSelected(() =>
        Object.keys(appliedTags || {})
          .filter(id => (appliedTags || {})[id])
          .map(tagId => tagsInfoRef.current[tagId] || (tagsMap[tagId] as TTag))
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps -- should be executed only when applied tags loaded for the first time
  }, [hasInitialAppliedTagLoaded]);

  useEffect(() => {
    if (isNewApplyTagsModalEnabled && operationStatus && operationStatus.loaded) {
      const { data, error } = operationStatus;
      const operation =
        operationStatus.hasError && error && error.data
          ? error.data.operation
          : data
          ? data.operation
          : '';
      if (operation === OPERATIONS.UPDATE || operation === OPERATIONS.REMOVE) {
        setLocalSelected(() =>
          Object.keys(appliedTags || {})
            .filter(id => (appliedTags || {})[id])
            .map(tagId => tagsInfoRef.current[tagId] || (tagsMap[tagId] as TTag))
        );
        operationStatus.hasError &&
          errorToast({
            message: `Failed to ${
              operation === OPERATIONS.REMOVE ? 'remove' : 'save'
            } tags. Please try again`,
            timeout: 3000,
          });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps -- should be executed only when operationstatus changes
  }, [operationStatus]);

  return (
    <TagsUIWrapper>
      <TagsText>
        <StyledTagIcon type={ICON_MAP.TAG} />
        <span>Tags on module: </span>
      </TagsText>
      {isNewApplyTagsModalEnabled ? (
        <StyledModuleTagsModalWrapper>
          <RemoteListAndApplyTags
            appliedTags={localSelected}
            addTags={onApplyTags}
            removeTags={onRemoveTag}
            isEditAllowed={canCreateTags}
          />
        </StyledModuleTagsModalWrapper>
      ) : !preSelectedTags || preSelectedTags.length === 0 ? (
        <>
          {canApplyTags ? (
            <Tooltip title="Add new tags">
              <StyledAddIcon type={ICON_MAP.ADD} onClick={onShowAllTagsClick} />
            </Tooltip>
          ) : (
            <TagsTextRegular>No tags applied</TagsTextRegular>
          )}
        </>
      ) : (
        <AppliedTags
          tagsListToShow={tagsListToShow}
          onShowAllTagsClick={onShowAllTagsClick}
          preSelectedTags={preSelectedTags}
          removeTag={removeTagOutsideModal}
          showAllTagsButton={showAllTagsBtn}
          tagsMap={tagsMap}
          canApplyTags={canApplyTags}
        />
      )}
      {showModal && (
        <TagsModal
          visible={true}
          onCancel={handleCancel}
          onApply={handleApply}
          categoriesMap={categoriesMap}
          tagsMap={tagsMap}
          tags={tags}
          preSelectedTags={preSelectedTags}
          canCreateTags={canCreateTags}
          canApplyTags={canApplyTags}
          suggestedTags={suggestedTags}
          creating={creating}
          onTagCreate={onTagCreate}
        />
      )}
    </TagsUIWrapper>
  );
}
