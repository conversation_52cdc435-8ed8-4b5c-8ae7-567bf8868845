import type { TTagsContainerProps, TTagWithCategoryId, TCategoryInfo } from '../../typeDefs';

export type TTagsWrapperProps = {
  hasInitialAppliedTagLoaded: boolean;
  onShowAllTagsClick: () => void;
  appliedTags?: Record<string, any>;
  removeTag: (id: string) => void;
  showModal: boolean;
  onModalCancel: () => void;
  onModalApply: (ids: string[]) => void;
  categoriesMap?: Record<string, TCategoryInfo>;
  tagsMap: Record<string, TTagWithCategoryId>;
  tags?: Record<string, any>;
  canCreateTags: boolean;
  canApplyTags: boolean;
  suggestedTags?: string[];
  creating: boolean;
  onTagCreate: (categoryId: string, tagName: string) => void;
} & Pick<TTagsContainerProps, 'operationStatus'>;
