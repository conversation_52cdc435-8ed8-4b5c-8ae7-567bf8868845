import { useEffect, useMemo, useState } from 'react';

import TagsWithModal from '~/modules/Admin/components/TagWithModal';

import {
  parseTagsByCategoryInArray,
  removeTag,
  getNewTagsListWithTagInfo,
  getDeletedTagsIds,
} from '../../utils';

import type { TProps } from './typeDefs';

const EMPTY_OBJECT = {};
const EMPTY_ARRAY: string[] = [];

function TagsModal({
  visible,
  onCancel: handleCancelButton,
  onApply,
  categoriesMap = EMPTY_OBJECT,
  tagsMap = EMPTY_OBJECT,
  tags = EMPTY_OBJECT,
  preSelectedTags,
  canCreateTags,
  canApplyTags,
  suggestedTags = EMPTY_ARRAY,
  creating = false,
  onTagCreate,
}: TProps) {
  const [selectedTags, setSelectedTags] = useState<Array<string>>([]);

  const categoryConfig = useMemo(
    () =>
      parseTagsByCategoryInArray({
        tags,
        categoriesMap,
        tagsMap,
      }),
    [tags, categoriesMap, tagsMap]
  );

  const suggestedTagsList = useMemo(
    () => suggestedTags.map((id: string) => (tagsMap || {})[id]),
    [tagsMap, suggestedTags]
  );

  const handleTagSelect = (selected: boolean, id: string) => {
    if (canApplyTags) {
      setSelectedTags(selectedTags => {
        selectedTags = [...selectedTags];
        if (selected) {
          selectedTags.push(id);
        } else {
          selectedTags = removeTag(id, selectedTags);
        }
        return selectedTags;
      });
    }
  };

  const handleTagClose = (id: string) => {
    handleTagSelect(false, id);
  };
  const handleApplyButton = () => {
    onApply && onApply(selectedTags);
  };

  const newTagsList = getNewTagsListWithTagInfo(tagsMap, preSelectedTags, selectedTags);
  const deletedTagsList = getDeletedTagsIds(preSelectedTags, selectedTags);

  useEffect(() => {
    setSelectedTags(preSelectedTags);
    // this should be onetime operation;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <TagsWithModal
      visible={visible}
      onCancel={handleCancelButton}
      onApply={handleApplyButton}
      newTagsList={newTagsList}
      deletedTagsList={deletedTagsList}
      onTagClose={handleTagClose}
      selectedTags={selectedTags}
      categoryConfig={categoryConfig}
      suggestedTags={suggestedTagsList}
      onTagSelect={handleTagSelect}
      onTagCreate={onTagCreate}
      creating={creating}
      canCreateTags={canCreateTags}
    />
  );
}

export default function TagsModalWrapper(props: TProps) {
  const { visible } = props;
  return visible ? <TagsModal {...props} /> : null;
}
