import type { TTagWithCategoryId, TCategoryInfo } from '../../typeDefs';

export type TProps = {
  visible: boolean;
  onCancel: () => void;
  onApply: (ids: string[]) => void;
  categoriesMap?: Record<string, TCategoryInfo>;
  tagsMap?: Record<string, TTagWithCategoryId>;
  tags?: Record<string, any>;
  preSelectedTags: string[];
  canCreateTags: boolean;
  canApplyTags: boolean;
  suggestedTags?: string[];
  creating: boolean;
  onTagCreate: (categoryId: string, tagName: string) => void;
};
