import { PureComponent } from 'react';

import { connect } from 'react-redux';
import { compose } from 'redux';
import { haveAccess } from 'ui_shell/Permissions';

import { getActions } from '@mindtickle/medux/Action';
import { injectReducer } from '@mindtickle/medux/Reducer';
import { injectSaga } from '@mindtickle/medux/Saga';

import withILTAdminSnowplowTracker from '~/modules/Admin/hocs/withILTAdminSnowplowTracker';
import { EMPTY_ARRAY_READONLY } from '~/mt-ui-core/config/global.config';

import { GET_APPLIED_TAGS, MANIPULATE_DATA, GET_SUGGESTED_TAGS } from './actionTypes';
import TagsWrapper from './components/TagsWrapper';
import { OPERATIONS, PERMISSIONS } from './constants';
import reducer from './reducer';
import saga from './saga';
import { createUpdateTagsEventProperties, createApplyTagsClickedEventProperties } from './utils';

import type { TTagsContainerOwnProps, TTagsContainerProps, TTagsContainerState } from './typeDefs';

const { UPDATE, REMOVE, ADD, SEARCH } = OPERATIONS;

class Tags extends PureComponent<TTagsContainerProps, TTagsContainerState> {
  static defaultProps = {
    appliedTags: {},
    searchedTags: {},
    tags: {},
    categories: {},
    operationStatus: {},
  };

  state = {
    isShowModalTriggered: false,
    showModal: false,
    creating: false,
  };

  showModal = (value = true) => {
    this.setState({ showModal: value });
  };

  closeModal = () => this.setState({ showModal: false, isShowModalTriggered: false });

  searchTags = (query = undefined) => {
    this.setState({ isShowModalTriggered: true });
    this.handleOperations({ operation: SEARCH, query });
  };

  removeTag = (id: any) => {
    this.handleOperations({ operation: REMOVE, processIds: [id] });
  };

  applyTags = (tags: any) => {
    this.handleOperations({ operation: UPDATE, processIds: tags });
  };

  createTag = (categoryId: string, tagName: string) => {
    this.setState({ creating: true });
    this.handleOperations({ operation: ADD, name: tagName, categoryId });
  };

  handleOperations = ({ operation, ...data }: any) => {
    const { moduleId, seriesId, companyId, manipulateData } = this.props;
    const context = { moduleId, seriesId, companyId, operation };
    manipulateData({ ...context, ...data });
  };

  componentDidMount() {
    const { loaded, getAppliedTags, moduleId, seriesId, companyId } = this.props;
    !loaded && getAppliedTags({ moduleId, seriesId, companyId });
  }

  componentDidUpdate(prevProps: TTagsContainerProps, prevState: TTagsContainerState) {
    const { operationStatus: { loaded, isLoading } = {} } = prevProps;
    const {
      operationStatus: {
        loaded: newLoadedState,
        isLoading: newIsLoading,
        data: { operation } = { operation: '' },
      } = {},
      snowplowTracker,
    } = this.props;
    if (
      newLoadedState &&
      !this.state.showModal &&
      this.state.isShowModalTriggered &&
      operation === SEARCH
    ) {
      this.showModal();
    }
    if (newLoadedState !== loaded && operation === UPDATE) {
      this.closeModal();

      try {
        const { appliedTags, tags } = this.props;
        const { eventName, eventDetails } = createUpdateTagsEventProperties({ appliedTags, tags });
        snowplowTracker.trackStructuredEvent({
          eventName: eventName,
          ...eventDetails,
        });
      } catch (err) {
        // Do nothing
      }
    }

    if (!newIsLoading && newIsLoading !== isLoading && operation === ADD) {
      this.setState({ creating: false });
    }

    const { showModal: prevShowModal } = prevState;
    const { showModal } = this.state;
    const { suggestedTagsLoaded, getSuggestedTags, companyId } = this.props;
    if (showModal && showModal !== prevShowModal) {
      !suggestedTagsLoaded && getSuggestedTags({ companyId });
      const { eventName, eventDetails } = createApplyTagsClickedEventProperties();
      snowplowTracker.trackStructuredEvent({
        eventName: eventName,
        ...eventDetails,
      });
    }
  }

  render() {
    const { showModal, creating } = this.state;
    let {
      canApplyTags,
      appliedTags,
      tags: tagsMap,
      categories,
      searchedTags,
      canCreateTags,
      suggestedTags,
      loaded,
      operationStatus,
    } = this.props;

    return (
      <TagsWrapper
        showModal={showModal}
        tagsMap={tagsMap}
        operationStatus={operationStatus}
        hasInitialAppliedTagLoaded={loaded || false}
        appliedTags={appliedTags}
        onShowAllTagsClick={() => this.searchTags()}
        removeTag={this.removeTag}
        onModalCancel={this.closeModal}
        onModalApply={this.applyTags}
        categoriesMap={categories}
        tags={searchedTags?.data || {}}
        canCreateTags={canCreateTags}
        canApplyTags={canApplyTags}
        suggestedTags={suggestedTags}
        onTagCreate={this.createTag}
        creating={creating}
      />
    );
  }
}

const mapStateToProps = (
  state: { moduleTags?: any; auth?: any },
  ownProps: TTagsContainerOwnProps
) => {
  const {
    loaded,
    isLoading,
    hasError,
    appliedTags,
    tags,
    categories,
    searchedTags,
    operationStatus,
    suggestedTags: {
      loaded: suggestedTagsLoaded = false,
      hasError: suggestedTagshasError = false,
      tagList: suggestedTagsList = EMPTY_ARRAY_READONLY,
    } = {},
  } = state.moduleTags;
  const {
    auth: { permissions },
  } = state;
  const canApplyTags = haveAccess([PERMISSIONS.APPLY_TAGS], permissions);
  const canCreateTags = haveAccess([PERMISSIONS.CREATE_EDIT_TAGS], permissions);
  const { moduleId, seriesId, companyId } = ownProps;
  const suggestedTags =
    suggestedTagsLoaded && !suggestedTagshasError ? suggestedTagsList : EMPTY_ARRAY_READONLY;
  return {
    loaded,
    isLoading,
    hasError,
    appliedTags,
    tags,
    categories,
    searchedTags,
    moduleId,
    seriesId,
    companyId,
    operationStatus,
    suggestedTagsLoaded,
    suggestedTags,
    canApplyTags,
    canCreateTags,
  };
};
const mapDispatchToProps = (dispatch: (arg0: any) => void) => ({
  getSuggestedTags: ({ companyId }: { companyId: string }) =>
    dispatch(getActions(GET_SUGGESTED_TAGS)({ companyId })),
  getAppliedTags: ({
    moduleId,
    seriesId,
    companyId,
  }: {
    moduleId: string;
    seriesId: string;
    companyId: string;
  }) => dispatch(getActions(GET_APPLIED_TAGS)({ moduleId, seriesId, companyId })),
  manipulateData: (data: any) => {
    dispatch(getActions(MANIPULATE_DATA)(data));
  },
});

const withConnect = connect(mapStateToProps, mapDispatchToProps);
const withReducer = injectReducer({ name: 'moduleTags', reducer });
const withSaga = injectSaga({ name: 'moduleTags', saga });

export default compose<React.ComponentType<TTagsContainerOwnProps>>(
  withILTAdminSnowplowTracker,
  withReducer,
  withSaga,
  withConnect
)(Tags);
