import type { TSnowplowTrackerType } from '~/modules/Admin/hooks/useILTAdminSnowplowTracker';

export interface TTag {
  id: string;
  name: string;
}

export interface TTagWithCategoryId extends TTag {
  categoryId: string;
}
export type TCategoryInfo = {
  id: string;
  name: string;
  description: string;
};

export type TCategoryAndTags = TCategoryInfo & { tags: TTagWithCategoryId[] };

export type TTagsContainerOwnProps = {
  moduleId: string;
  seriesId: string;
  companyId: string;
  moduleName: string;
  moduleType: string;
  className?: string | string[];
};

export type TTagsContainerProps = {
  className?: string;
  loaded?: boolean;
  isLoading?: boolean;
  hasError: boolean;
  appliedTags: Record<string, boolean>;
  getAppliedTags: (param: { moduleId: string; seriesId: string; companyId: string }) => void;
  canCreateTags: boolean;
  canApplyTags: boolean;
  suggestedTagsLoaded?: boolean;
  suggestedTags?: string[];
  getSuggestedTags: (param: { companyId: string }) => void;
  searchedTags: {
    data?: Record<string, any>;
    hasMore: boolean;
  };
  tags: Record<string, TTagWithCategoryId>;
  categories: Record<string, TCategoryInfo>;
  manipulateData: (data: any) => void;
  operationStatus: {
    loaded: boolean;
    isLoading: boolean;
    hasError: boolean;
    data?: any;
    error?: any;
  };
  snowplowTracker: TSnowplowTrackerType;
} & TTagsContainerOwnProps;

export type TTagsContainerState = {
  showModal: boolean;
  creating: boolean;
  isShowModalTriggered: boolean;
};
