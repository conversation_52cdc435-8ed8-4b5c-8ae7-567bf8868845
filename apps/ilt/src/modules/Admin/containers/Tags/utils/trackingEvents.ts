import { PAGE_LOAD_DATA } from '~/modules/Admin/config/constants';
import { TAGS_EVENT_NAME } from '~/modules/Admin/hooks/useILTAdminSnowplowTracker/constants';

import type { TTagsContainerProps } from '../typeDefs';

function convertToEventDetailsProperties(eventName: string, eventDetails: Record<string, any>) {
  return {
    eventName,
    eventDetails,
  };
}

export function createUpdateTagsEventProperties({
  appliedTags,
  tags,
}: {
  appliedTags: TTagsContainerProps['appliedTags'];
  tags: TTagsContainerProps['tags'];
}) {
  return convertToEventDetailsProperties(TAGS_EVENT_NAME.MODULE_CONTENT_TAGS_APPLIED, {
    tag_applied_to: 'module',
    tags_applied_to: 'module',
    number_of_tags: (value => Object.keys(value).filter(key => value[key]).length)(appliedTags),
    tags_applied: (() => {
      const tagIds = Object.keys(appliedTags).filter(key => appliedTags[key]);
      return tagIds.map(id => tags[id].name);
    })(),
    page_name: PAGE_LOAD_DATA.ILT_SETTINGS_GENERAL.PAGE_NAME,
  });
}

export function createApplyTagsClickedEventProperties() {
  return convertToEventDetailsProperties(TAGS_EVENT_NAME.MODULE_APPLY_TAGS_CLICK, {
    location: 'module_setting_popup',
  });
}
