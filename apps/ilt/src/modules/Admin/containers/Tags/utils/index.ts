import type { TTag, TCategoryInfo, TCategoryAndTags, TTagWithCategoryId } from '../typeDefs';

export {
  createUpdateTagsEventProperties,
  createApplyTagsClickedEventProperties,
} from './trackingEvents';

type TParseTagsByCategory = {
  tags: Record<string, any>;
  categoriesMap: Record<string, TCategoryInfo>;
  tagsMap: Record<string, TTagWithCategoryId>;
};

export const parseTagsByCatgegory = ({ tags, categoriesMap, tagsMap }: TParseTagsByCategory) => {
  const parsedData: Record<string, TCategoryAndTags> = {};
  Object.keys(tags).forEach(tagId => {
    const tag = { ...(tagsMap[tagId] || {}) };
    const category = categoriesMap[tag.categoryId] || {};
    parsedData[tag.categoryId] = parsedData[tag.categoryId] || { tags: [] };
    parsedData[tag.categoryId].name = category.name;
    parsedData[tag.categoryId].id = tag.categoryId;
    parsedData[tag.categoryId].description = category.description;
    parsedData[tag.categoryId].tags.push(tag);
  });
  return parsedData;
};

export const parseTagsByCategoryInArray = ({
  tags,
  categoriesMap,
  tagsMap,
}: TParseTagsByCategory) => Object.values(parseTagsByCatgegory({ tags, categoriesMap, tagsMap }));

export const removeTag = (id: string, tags: string[]) => {
  const updatedTags = [...tags];
  const tagIndex = updatedTags.indexOf(id);
  if (tagIndex !== -1) {
    updatedTags.splice(tagIndex, 1);
  }

  return updatedTags;
};

export const getNewTagsListWithTagInfo = (
  tagsMap: Record<string, any>,
  originalTagsList: Array<string>,
  newTagsList: Array<string>
) => {
  let tagsIdsWithName: Array<TTag> = [];
  const tagsIds = getNewTagsList(originalTagsList, newTagsList);

  if (tagsMap && tagsIds.length > 0) {
    tagsIds.forEach(item => {
      tagsIdsWithName.push({ id: item, name: tagsMap[item].name });
    });
  }

  return tagsIdsWithName;
};

export const getDeletedTagsIds = (originalTagsList: Array<string>, newTagsList: Array<string>) => {
  let tagsIds: Array<string> = [];
  if (!newTagsList || newTagsList.length === 0) {
    tagsIds = [...originalTagsList];
  } else if (!originalTagsList || originalTagsList.length === 0) {
    tagsIds = [];
  } else {
    originalTagsList.forEach(item => {
      if (newTagsList.indexOf(item) === -1) {
        tagsIds.push(item);
      }
    });
  }

  return tagsIds;
};

export const getNewTagsList = (originalTagsList: Array<string>, newTagsList: Array<string>) => {
  let tagsIds: Array<string> = [];
  if (!originalTagsList || originalTagsList.length === 0) {
    tagsIds = newTagsList;
  } else if (newTagsList && newTagsList.length > 0) {
    newTagsList.forEach(item => {
      if (originalTagsList.indexOf(item) === -1) {
        tagsIds.push(item);
      }
    });
  }

  return tagsIds;
};
