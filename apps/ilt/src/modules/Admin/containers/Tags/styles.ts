import styled, { css } from 'styled-components';

import Icon from '@mindtickle/icon';
import { tokens, theme, mixins } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS } from '~/config/constants';
const { fontSizes, fontWeight, lineHeight } = theme;

export const StyledTagWrapper = styled.div`
  .${THEME_PREFIX_CLS}-tooltip-inner {
    white-space: break-spaces;
  }
`;

export const TagsUIWrapper = styled.div`
  display: flex;
  align-items: center;
`;
export const StyledModuleTagsModalWrapper = styled.div`
  z-index: 2;
`;

export const TagsText = styled.div`
  display: flex;
  color: ${tokens.textTokens.COLOR_TEXT_SECONDARY};
  margin-right: 8px;
  font-style: normal;
  font-weight: ${fontWeight.SEMIBOLD};
  font-size: ${fontSizes.SMALLTEXT};
  line-height: ${lineHeight.LABEL};
`;
export const TagsTextRegular = styled(TagsText)`
  font-weight: ${fontWeight.REGULAR};
`;

export const StyleTagsBox = styled.div`
  background: ${tokens.bgTokens.COLOR_BG_TERTIARY};
  border: 1px solid ${tokens.borderTokens.COLOR_BORDER_SECONDARY};
  box-sizing: border-box;
  border-radius: 24px;
  padding: 2px 12px;
  margin-right: 12px;
  font-size: ${fontSizes.SMALLTEXT};
  height: 24px;
`;

export const StyledTagIcon = styled(Icon)`
  color: ${tokens.iconTokens.COLOR_ICON_DISABLED};
  margin-right: 8px;
  font-size: 16px;
`;

export const StyledAddIcon = styled(Icon)`
  cursor: pointer;
  background: ${tokens.bgTokens.COLOR_BG_DEFAULT};
  border: 1px solid ${tokens.borderTokens.COLOR_BORDER_ACCENT};
  box-sizing: border-box;
  border-radius: 24px;
  padding: 6px 6px;
  margin-right: 24px;
  font-size: 10px;
  color: ${tokens.textTokens.COLOR_TEXT_ACCENT};
  position: relative;
`;

export const StyledAppliedTagsWrapper = styled.div`
  text-align: left;
  float: left;
  width: 74%;

  .apply-tags-count {
    ${mixins.smallGreyLink()};
    float: left;
    line-height: 26px;
    margin-right: 10px;
    width: 110px;
  }

  .applied-tags-list-added {
    max-height: 100px;
    overflow-y: auto;
  }

  && {
    .${THEME_PREFIX_CLS}-tag {
      background-color: ${tokens.bgTokens.COLOR_BG_ACCENT_SELECTED};
      border-color: ${tokens.borderTokens.COLOR_BORDER_ACCENT};
      color: ${tokens.textTokens.COLOR_TEXT_ACCENT};
    }
  }
`;

export const StyledTagCell = styled.span<{ canApplyTags: boolean }>`
  background: ${tokens.bgTokens.COLOR_BG_TERTIARY};
  border: 1px solid ${tokens.borderTokens.COLOR_BORDER_SECONDARY};
  border-radius: 20px;
  position: relative;
  height: 24px;
  display: inline-block;
  padding: 0 30px 0 14px;
  ${props =>
    !props.canApplyTags &&
    css`
      padding-right: 14px;
    `}
  vertical-align: middle;
  margin-right: 6px;
  ${mixins.smallGreyLink()};
  font-weight: 600;
  line-height: 22px;

  &.ellipses-tootip-tag {
    max-width: 200px;
    background: ${tokens.bgTokens.COLOR_BG_DEFAULT};
  }

  &.show-all {
    cursor: pointer;
  }

  .close-icon {
    color: ${tokens.iconTokens.COLOR_ICON_DEFAULT};
    position: absolute;
    right: 14px;
    top: 8px;
    font-size: 8px;
    margin-left: 10px;
    cursor: pointer;
    font-weight: 600;
  }
`;
