import { useEffect, useState } from 'react';

import { FormattedMessage } from 'react-intl';
import { useLocation } from 'react-router-dom';

import { ActionButtons } from '@mindtickle/error-page';
import { ICON_MAP } from '@mindtickle/icon-with-gradient';

import {
  IntegrationAuthChannelDataStatus,
  integrationAuthChannelPostMessage,
} from '~/utils/broadcastChannel';

import messages from './messages';
import { StyledLoader, StyledMessagePage } from './styles';

function AuthRoutes() {
  const location = useLocation();
  const [isLoading, setIsLoading] = useState(true);
  const search = location.search;
  const urlParams = new URLSearchParams(search);
  const hostEmail = urlParams.get('hostEmail') ?? '';
  const status = urlParams.get('status') ?? '';

  useEffect(() => {
    if (status) {
      integrationAuthChannelPostMessage({
        type: 'auth',
        payload: {
          status:
            status === 'success'
              ? IntegrationAuthChannelDataStatus.SUCCESS
              : IntegrationAuthChannelDataStatus.ERROR,
          hostEmail,
        },
      });
    }
    const timeOutRef = setTimeout(() => setIsLoading(false), 1150);
    return () => {
      clearTimeout(timeOutRef);
    };
  }, [hostEmail, status]);

  const isSuccess = status === 'success';

  return isLoading ? (
    <StyledLoader size="sizeBig" type="Full" />
  ) : (
    <StyledMessagePage
      pageType={
        isSuccess
          ? {
              image: ICON_MAP.SUCCESS_HAND_THUMB,
              heading: <FormattedMessage {...messages.SUCCESS_HEADING} />,
              description: <FormattedMessage {...messages.SUCCESS_DESCRIPTION} />,
              buttons: (props: any) => <ActionButtons {...props} />,
            }
          : {
              image: ICON_MAP.ALARM_CLOCK,
              heading: <FormattedMessage {...messages.ERROR_HEADING} />,
              description: (
                <FormattedMessage {...messages.ERROR_DESCRIPTION} values={{ br: <br /> }} />
              ),
              buttons: (props: any) => <ActionButtons {...props} />,
            }
      }
      showGoToHome={true}
      size={''}
      styleIcon={{ height: isSuccess ? 100 : 190 }}
      showLogo={false}
      centered={true}
      styleHeading={'page-heading'}
    />
  );
}

export default AuthRoutes;
