import styled from 'styled-components';

import ErrorPage from '@mindtickle/error-page';
import Loader from '@mindtickle/loader';
import { tokens, mixins } from '@mindtickle/styles/lib';

export const StyledLoader = styled(Loader)`
  &.fullPageloadingScreen {
    position: fixed;
    background-color: #ffffffb3;
    .loaderMessage {
      color: ${tokens.textTokens.COLOR_TEXT_DEFAULT};
    }
  }
`;

export const StyledMessagePage = styled(ErrorPage)`
  .page-heading {
    ${mixins.h3()}
  }
`;
