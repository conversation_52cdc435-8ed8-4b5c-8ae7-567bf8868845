import styled, { css } from 'styled-components';

import Affix from '@mindtickle/affix';
import Card from '@mindtickle/card';
import { tokens, mixins } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS } from '~/config/constants';

import { flexContainer } from '../../styles/mixins';

export const settingDetailsColStyle = { backgroundColor: tokens.bgTokens.COLOR_BG_SECONDARY };

export const settingsContainerRowStyle = { width: '100%', height: '100%' };

export const StyleWrapper = styled.div`
  display: flex;
  flex-direction: column;
  margin-bottom: 0;
  background: white;
  border-radius: 8px 8px 0px 0px;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.08);
`;
export const StyledTabSettingContentWrapper = styled.div`
  height: 100%;
  width: 100%;
`;

export const LeftBarSettingAffix = styled(Affix)`
  .${THEME_PREFIX_CLS}-affix ul {
    height: calc(100vh - 120px);
  }
`;

export const StyledSettingsCard = styled(Card)`
  && {
    padding: 0;
  }

  .${THEME_PREFIX_CLS}-card-grid {
    width: 100%;
  }

  .vertical-radio-style {
    display: flex;
    margin-top: 24px;
    margin-bottom: 24px;
    row-gap: 12px;
    flex-direction: column;
    justify-content: space-evenly;
  }

  .${THEME_PREFIX_CLS}-card {
    box-shadow: rgb(0 0 0 / 8%) 0px 0px 4px 0px;
  }

  .${THEME_PREFIX_CLS}-card-grid {
    border-bottom: 1px solid ${tokens.borderTokens.COLOR_BORDER_SECONDARY};

    &:last-child {
      border-bottom: 0px;
    }
  }

  .${THEME_PREFIX_CLS}-card-grid, .${THEME_PREFIX_CLS}-card-grid-hoverable:hover {
    box-shadow: none;
  }
`;

export const StyledToggleDiv = styled.div<{
  toggle: boolean;
  display: string;
}>`
  display: ${props => (props.toggle ? props.display : 'none')};

  &.default-max-score-block {
    justify-content: flex-start;
    align-items: center;
    margin-top: 24px;
  }

  .default-max-score-text {
    margin-left: 8px;
  }

  .reattempt-text {
    ${mixins.smallDarkLink()}
    margin-top: 18px;
    margin-bottom: 12px;
  }
`;

export const StyledInputParaContainer = styled.div`
  display: flex;
  align-items: center;
`;

export const StyledNote = styled.span`
  font-weight: 600;
`;

export const StyledRadioButtonSettingTab = styled.div`
  width: 100%;

  .${THEME_PREFIX_CLS}-card-grid {
    border-bottom: 1px solid ${tokens.borderTokens.COLOR_BORDER_SECONDARY} !important;
  }
`;

export const StyledSettingHeaderWithToolTip = styled.div`
  & {
    display: flex;
    justify-content: flex-start;
    align-items: baseline;
    position: relative;

    .module-info-icon {
      margin-left: 9px;
      color: ${tokens.iconTokens.COLOR_ICON_DEFAULT};
      font-size: 12px;
    }
  }
`;

export const StyledDivider = styled.div`
  margin: 24px -24px;
  height: 1px;
  background: ${tokens.bgTokens.COLOR_BG_DISABLED};
`;

export const StyledCard = styled(Card)`
  &.${THEME_PREFIX_CLS}-card {
    margin: 24px;
  }
`;

export const StyledToggleHeader = styled.div`
  display: flex;
  justify-content: space-between;

  .randomization-heading {
    display: inline-block;
    ${mixins.blackLink()}
  }
`;

export const StyledRandomizationNoteSmall = styled.div<{ toggleDisabled?: boolean }>`
  margin-top: 4px;
  ${mixins.darkText()};
  ${({ toggleDisabled }) =>
    toggleDisabled &&
    css`
      color: ${tokens.textTokens.COLOR_TEXT_TERTIARY};
    `}
`;

export const StyledRandomizationNoteSmallWithWarning = styled(StyledRandomizationNoteSmall)`
  ${mixins.smallBlackLink()}
  background-color: ${tokens.bgTokens.COLOR_BG_WARNING};
  padding: 8px 16px;
  border-radius: 8px;
`;

export const StyledSectionWithTooltip = styled.div<{ toggleDisabled?: boolean }>`
  & {
    display: flex;
    justify-content: flex-start;
    align-items: baseline;

    .randomization-not-eligible {
      font-size: 12px;
      font-style: italic;
      margin-left: 8px;
    }

    .module-info-icon {
      margin-left: 9px;
      color: ${tokens.iconTokens.COLOR_ICON_DEFAULT};
      font-size: 12px;
    }
    ${({ toggleDisabled }) =>
      toggleDisabled &&
      css`
        h4.${THEME_PREFIX_CLS}-typography {
          color: ${tokens.textTokens.COLOR_TEXT_TERTIARY};
        }
      `}
  }
`;

export const StyledTimeLimitNote = styled.div`
  margin-top: 8px;
  ${mixins.smallDarkLink()};
  font-style: italic;
`;

export const StyledHeaderRow = styled.div`
  ${flexContainer({ alignItems: 'center' })};
  padding: 14px 0;
  border-bottom: 1px solid ${tokens.borderTokens.COLOR_BORDER_SECONDARY};
  &:last-child {
    border-bottom: none;
  }
`;

export const StyledStatusItem = styled.div`
  width: 20%;
  margin-left: 16px;
`;

export const StyledSendItem = styled.div`
  width: 60%;
  margin-left: 16px;
`;

export const StyledEmailItem = styled.div``;

export const StyledReminderDateItem = styled.div`
  width: 60%;
  margin-left: 16px;
  display: flex;
  align-items: center;
`;

export const StyledEnrolmentReminderDateItem = styled.div`
  width: 60%;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
`;

export const StyledTemplateItem = styled.div`
  display: flex;
  align-items: center;
`;

export const HeaderItem = styled.div`
  ${mixins.smallDarkLink()}
`;

export const StyledEmailSection = styled.div`
  width: 80%;
  display: flex;
  align-items: center;
`;

export const TableContainer = styled.div``;

export const StyledGridwithTootip = styled.div`
  display: flex;
  justify-content: space-between;
  position: relative;

  .module-info-icon {
    margin-left: 9px;
    color: ${tokens.iconTokens.COLOR_ICON_DEFAULT};
    font-size: 12px;
  }
`;

export const StyledInputContainer = styled.div`
  margin-top: 24px;
`;

export const StyledInput = styled.div`
  width: 100px;
  margin-top: 4px;
  display: flex;

  .default-max-score-text {
    margin-top: 3px;
    margin-left: 8px;
  }
`;

export const StyledText = styled.text`
  font-size: 12px;
  font-color: ${mixins.blackLink};
  .module-info-icon {
    margin-left: 9px;
    color: ${tokens.iconTokens.COLOR_ICON_DEFAULT};
    font-size: 12px;
  }
`;

export const StyledErrorText = styled.text`
  font-size: 12px;
  width: 100px;
  color: ${tokens.iconTokens.COLOR_ICON_DANGER};
`;

export const StyledErrorNote = styled.div`
  margin-top: 8px;
  ${mixins.smallDarkLink()};
  color: ${tokens.textTokens.COLOR_TEXT_DANGER};
`;
