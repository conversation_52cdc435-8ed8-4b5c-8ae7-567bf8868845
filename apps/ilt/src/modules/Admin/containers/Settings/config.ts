export const SETTINGS_TYPE = {
  GENERAL: 'GENERAL',
  ENROLMENT: 'ENROLMENT',
  SCORE_AND_COMPLETION: 'SCORE_AND_COMPLETION',
  NOTIFICATIONS: 'NOTIFICATIONS',
  EMAILS_FOR_LEARNER: 'EMAILS_FOR_LEARNER',
};

export const EMAIL_CATEGORY = {
  NOT_ENROLLED: 'Not Enrolled',
};

export const URL_PATH_MAP: Record<string, string> = {
  general: SETTINGS_TYPE.GENERAL,
  enrollment: SETTINGS_TYPE.ENROLMENT,
  'score-and-completion': SETTINGS_TYPE.SCORE_AND_COMPLETION,
  notifications: SETTINGS_TYPE.NOTIFICATIONS,
};

export const settingsTab = [
  { id: SETTINGS_TYPE.GENERAL, name: 'General', urlPath: 'general' },
  { id: SETTINGS_TYPE.ENROLMENT, name: 'Enrollment', urlPath: 'enrollment' },
  {
    id: SETTINGS_TYPE.SCORE_AND_COMPLETION,
    name: 'Scoring and completion',
    urlPath: 'score-and-completion',
  },
  { id: SETTINGS_TYPE.NOTIFICATIONS, name: 'Emails and notifications', urlPath: 'notifications' },
];

export const SCROLL_PROPS = {
  autoHide: true,
  autoHideTimeout: 1000,
  autoHideDuration: 200,
  style: { height: 'calc(100vh - 52px)' },
};
