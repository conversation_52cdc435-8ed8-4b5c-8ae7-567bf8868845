import { useEffect, useState, useCallback } from 'react';

import { connect } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import { compose } from 'redux';

import Grid from '@mindtickle/grid';
import { getActions } from '@mindtickle/medux/Action';
import { injectReducer } from '@mindtickle/medux/Reducer';
import { injectSaga } from '@mindtickle/medux/Saga';
import Scrollbar from '@mindtickle/scrollbar';

import { ADMIN_NAVBAR_HEIGHT } from '~/config/constants';
import { PAGE_LOAD_DATA } from '~/modules/Admin/config/constants';
import createWithPageTimeSpent from '~/modules/Admin/hocs/withPageTimeSpent';
import { useILTAdminSnowplowTracker } from '~/modules/Admin/hooks/useILTAdminSnowplowTracker';

import { MANIPULATE_SETTINGS } from './actionTypes';
import EmailReminders from './components/EmailReminders';
import Enrolment from './components/Enrolment';
import GeneralContent from './components/GeneralContent';
import ScoreAndCompletion from './components/ScoreAndCompletion';
import SettingLeftMenuTab from './components/SettingLeftMenuTab';
import { SETTINGS_TYPE, URL_PATH_MAP, settingsTab, SCROLL_PROPS } from './config';
import { OPERATIONS } from './constants';
import reducer from './reducer';
import saga from './saga';
import {
  settingsContainerRowStyle,
  settingDetailsColStyle,
  LeftBarSettingAffix,
  StyleWrapper,
  StyledTabSettingContentWrapper,
} from './styles';
import { sendSettingUpdateTrackEvent, sendSettingTabClickTrackEvent } from './utils/trackEvents';

import type { SETTINGS_TAB_TYPES, TSettingsProps, TSettingsOwnProps } from './typeDefs';

const { UPDATE } = OPERATIONS;

const { Row, Col } = Grid;

const tabComponentMap = {
  [SETTINGS_TYPE.GENERAL]: GeneralContent,
  [SETTINGS_TYPE.ENROLMENT]: Enrolment,
  [SETTINGS_TYPE.SCORE_AND_COMPLETION]: ScoreAndCompletion,
  [SETTINGS_TYPE.NOTIFICATIONS]: EmailReminders,
};

const trackerPageName = PAGE_LOAD_DATA.ILT_SETTINGS_PAGE.PAGE_NAME;

const Settings = ({
  moduleData,
  moduleUpdater,
  manipulateData,
  companySettings,
}: TSettingsProps) => {
  const [activeSettingsTab, setActiveSettingsTab] = useState('');
  const changeRoute = useNavigate();
  const urlParams = useParams();
  const tabUrl = urlParams['*'];

  const Component = tabComponentMap[
    activeSettingsTab || SETTINGS_TYPE.GENERAL
  ] as React.ElementType;

  const tracker = useILTAdminSnowplowTracker();

  const updateSetting = useCallback(
    ({ type, moduleId, seriesId, data, updatedSetting }: any) => {
      manipulateData({
        operation: UPDATE,
        type,
        moduleUpdater,
        moduleId,
        seriesId,
        data,
      });
      if (updatedSetting === 'moduleAttendanceThresholdChanged') {
        data = {
          percentageThreshold: data?.attendanceSettings?.percentageThreshold,
        };
      } else if (updatedSetting === 'moduleAttendanceSettingsToggled') {
        data = {
          durationBasedAttendanceEnabled: data?.attendanceSettings?.durationBasedAttendanceEnabled,
        };
      }
      sendSettingUpdateTrackEvent(tracker, { data });
    },
    [manipulateData, moduleUpdater, tracker]
  );

  useEffect(() => {
    if (tabUrl && URL_PATH_MAP[tabUrl]) {
      activeSettingsTabHandler(URL_PATH_MAP[tabUrl], tabUrl, { fromMount: true });
    } else {
      activeSettingsTabHandler(SETTINGS_TYPE.GENERAL, settingsTab[0].urlPath, { fromMount: true });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const activeSettingsTabHandler = (
    tabId: SETTINGS_TAB_TYPES,
    urlPath: string,
    { fromMount = false } = {}
  ) => {
    if (tabId === activeSettingsTab) {
      return;
    }

    setActiveSettingsTab(tabId);
    changeRoute(`./${urlPath}`);
    !fromMount && sendSettingTabClickTrackEvent(tracker, { tabId });
  };

  return (
    <StyleWrapper>
      <StyledTabSettingContentWrapper>
        <Row style={settingsContainerRowStyle}>
          <Col span={6} className="setting-menu">
            <LeftBarSettingAffix offsetTop={ADMIN_NAVBAR_HEIGHT}>
              <Scrollbar {...SCROLL_PROPS}>
                <SettingLeftMenuTab
                  setActiveTab={activeSettingsTabHandler}
                  activeTab={activeSettingsTab}
                />
              </Scrollbar>
            </LeftBarSettingAffix>
          </Col>
          <Col span={18} style={settingDetailsColStyle} className="setting-content">
            <Component
              updateSetting={updateSetting}
              moduleData={moduleData}
              companySettings={companySettings}
              trackerPageName={trackerPageName}
            />
          </Col>
        </Row>
      </StyledTabSettingContentWrapper>
    </StyleWrapper>
  );
};

const mapDispatchToProps = (dispatch: any) => ({
  manipulateData: (data: any) => {
    dispatch(getActions(MANIPULATE_SETTINGS)(data));
  },
});

const mapStateToProps = () => ({});

const withReducer = injectReducer({
  name: 'moduleSettings',
  reducer: reducer,
});

const withConnect = connect(mapStateToProps, mapDispatchToProps);
const withSaga = injectSaga({ name: 'moduleSettings', saga: saga });
export default compose<React.ComponentType<TSettingsOwnProps>>(
  createWithPageTimeSpent({ pageName: trackerPageName }),
  withReducer,
  withSaga,
  withConnect
)(Settings);
