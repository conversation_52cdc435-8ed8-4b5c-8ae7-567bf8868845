import { put, takeEvery, call, all } from 'redux-saga/effects';

import { getActions } from '@mindtickle/medux/Action';

import { MANIPULATE_SETTINGS } from './actionTypes';
import { OPERATIONS, STATIC } from './constants';

const { UPDATE } = OPERATIONS;

function* manageSettings({
  payload: { operation, type, moduleUpdater, moduleId, seriesId, data },
}) {
  const { SUCCESS, FAIL } = getActions({
    name: MANIPULATE_SETTINGS,
    options: { async: true },
  });
  try {
    const params = {
      moduleId,
      seriesId,
      data,
    };
    switch (type) {
      // case SETTINGS:
      //   if (operation === UPDATE) {
      //     yield call(updateSetting, params);
      //   }
      //   break;
      case STATIC:
        if (operation === UPDATE) {
          yield call(moduleUpdater, params);
        }
        break;
      default:
    }
    yield put(SUCCESS({ data: { operation, type } }));
  } catch (error) {
    if (error.statusCode === 403) {
      yield put(FAIL(error, { globalError: true }));
    }
    yield put(FAIL({ error, operation, type }));
  }
}

function* handleOperation() {
  yield takeEvery(MANIPULATE_SETTINGS, manageSettings);
}

export default function* () {
  yield all([handleOperation()]);
}
