import Button from '@mindtickle/button';

import { StyledReminderSaveBlock, StyledLinkButton } from './style';

import type { SaveBlockProps } from './typeDefs';

const ReminderSaveBlock = ({
  onDeleteDraftReminder,
  onSaveClick,
  automation,
  disabled,
}: SaveBlockProps) => (
  <StyledReminderSaveBlock>
    <div className="btn-block">
      {automation.isEdited && (
        <StyledLinkButton type={'link'} disabled={disabled} onClick={() => onSaveClick(automation)}>
          Save
        </StyledLinkButton>
      )}
      {automation.isEdited && !automation.isDraft && (
        <Button
          type={'secondary'}
          style={{ marginLeft: 12 }}
          onClick={() => onDeleteDraftReminder(automation)}
        >
          Cancel
        </Button>
      )}
    </div>
  </StyledReminderSaveBlock>
);

export default ReminderSaveBlock;
