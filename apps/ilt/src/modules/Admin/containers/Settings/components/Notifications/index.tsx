import { useEffect, useCallback } from 'react';

import { injectIntl } from 'react-intl';
import { connect } from 'react-redux';
import { useParams } from 'react-router-dom';
import { compose } from 'redux';

import Loader from '@mindtickle/loader';
import { getActions } from '@mindtickle/medux/Action';
// import { injectReducer } from '@mindtickle/medux/Reducer';
// import { injectSaga } from '@mindtickle/medux/Saga';
import { Title } from '@mindtickle/typography';

import usePrevious from '~/hooks/usePrevious';

import {
  ErrorToast,
  LoadingToast,
  SuccessToast,
} from '../../../Track/components/InviteComponent/utils/toast';
import {
  HeaderItem,
  StyledEmailItem,
  StyledEmailSection,
  StyledHeaderRow,
  StyledSendItem,
  StyledStatusItem,
  TableContainer,
} from '../../styles';

import { GET_REMINDER_DATA, MANIPULATE_DATA } from './actionTypes';
import NotificationsTable from './components/NotificationsTable';
import { OPERATIONS } from './constants';
// import reducer from './reducer';
// import saga from './saga';
import messages from './messages';
import { StyledGrid } from './styles';

import type { Props, TOwnProps } from './typeDefs';

const ReminderSettingsHeader = () => (
  <StyledHeaderRow>
    <StyledStatusItem>
      <HeaderItem>Status</HeaderItem>
    </StyledStatusItem>
    <StyledEmailSection>
      <StyledSendItem>
        <HeaderItem>When to send</HeaderItem>
      </StyledSendItem>
      <StyledEmailItem>
        <HeaderItem>Email template</HeaderItem>
      </StyledEmailItem>
    </StyledEmailSection>
  </StyledHeaderRow>
);

const Notifications = (props: Props) => {
  const { moduleId, seriesId } = useParams();
  const { companyId } = props.moduleData;
  const {
    getData,
    loaded,
    hasError,
    isLoading,
    mailTemplates: emailTemplateData,
    mailAutomationData: emailAutomationData,
    manipulateData: manipulateDataAction,
    operationStatus,
    intl,
  } = props;

  const {
    isLoading: isOperationLoading,
    loaded: operationLoaded,
    hasError: operationHasError,
  } = operationStatus || {};

  const prevOperationLoading = usePrevious(isOperationLoading);
  const prevOperationLoaded = usePrevious(operationLoaded);

  useEffect(() => {
    if (isOperationLoading !== prevOperationLoading && isOperationLoading) {
      LoadingToast({ message: intl.formatMessage(messages.reminderInProgress) });
    }
  }, [isOperationLoading, prevOperationLoading, intl]);

  useEffect(() => {
    if (operationLoaded !== prevOperationLoaded && operationLoaded && !operationHasError) {
      SuccessToast({ message: intl.formatMessage(messages.reminderSuccessful) });
    }
    if (operationLoaded !== prevOperationLoaded && operationLoaded && operationHasError) {
      ErrorToast({ message: intl.formatMessage(messages.reminderFailed) });
    }
  }, [operationHasError, operationLoaded, prevOperationLoaded, intl]);

  const manipulateData = useCallback(
    (
      operation: (typeof OPERATIONS)[keyof typeof OPERATIONS],
      data: any,
      successCallback?: () => void
    ) => {
      manipulateDataAction({ moduleId, seriesId, companyId, operation, data, successCallback });
    },
    [companyId, manipulateDataAction, moduleId, seriesId]
  );

  useEffect(() => {
    !loaded && getData({ moduleId, seriesId, companyId });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <StyledGrid>
      <header className="grid-header">
        <div className="title-container">
          <Title level={4}>Emails for the learner</Title>
        </div>
      </header>
      <TableContainer>
        <ReminderSettingsHeader />
        {loaded && !hasError && (
          <NotificationsTable
            emailTemplateData={emailTemplateData}
            emailAutomationData={emailAutomationData}
            manipulateData={manipulateData}
            operationStatus={operationStatus}
          />
        )}
        {isLoading && <Loader size="sizeSmall" />}
      </TableContainer>
    </StyledGrid>
  );
};

const mapStateToProps = (state: any, ownProps: any) => {
  const {
    loaded,
    isLoading,
    hasError,
    mailTemplates,
    operationStatus, // Please note this property is also used in header saving status
    mailAutomationData,
  } = state.moduleReminders;

  const { moduleId, seriesId, companyId } = ownProps;

  return {
    loaded,
    isLoading,
    hasError,
    moduleId,
    seriesId,
    companyId,
    mailTemplates,
    operationStatus,
    mailAutomationData,
  };
};

const mapDispatchToProps = (dispatch: any) => ({
  getData: (data: any) => {
    dispatch(getActions(GET_REMINDER_DATA)(data));
  },
  manipulateData: (data: any) => {
    dispatch(getActions(MANIPULATE_DATA)(data));
  },
});

const withConnect = connect(mapStateToProps, mapDispatchToProps);
// const withReducer = injectReducer({ name: 'moduleReminders', reducer });
// const withSaga = injectSaga({ name: 'moduleReminders', saga });

export default compose<React.ComponentType<TOwnProps>>(
  // withReducer,
  // withSaga,
  withConnect,
  injectIntl
)(Notifications);
