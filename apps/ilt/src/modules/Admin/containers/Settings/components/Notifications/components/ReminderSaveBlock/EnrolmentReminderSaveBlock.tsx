import Button from '@mindtickle/button';

import { StyledLinkButton, StyledEnrolmentReminderSaveBlock } from './style';

import type { SaveBlockProps } from './typeDefs';

const EnrolmentReminderSaveBlock = ({
  onDeleteDraftReminder,
  onSaveClick,
  automation,
  disabled,
}: SaveBlockProps) => (
  <StyledEnrolmentReminderSaveBlock>
    <div className="btn-block">
      {automation.isEdited && (
        <StyledLinkButton type={'link'} disabled={disabled} onClick={() => onSaveClick(automation)}>
          Save
        </StyledLinkButton>
      )}
      {automation.isEdited && !automation.isDraft && (
        <Button
          type={'secondary'}
          style={{ marginLeft: 12 }}
          onClick={() => onDeleteDraftReminder(automation)}
        >
          Cancel
        </Button>
      )}
    </div>
  </StyledEnrolmentReminderSaveBlock>
);

export default EnrolmentReminderSaveBlock;
