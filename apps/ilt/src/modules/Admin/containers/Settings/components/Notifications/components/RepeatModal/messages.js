import { defineMessages } from 'react-intl';

export default defineMessages({
  validationMsg: 'Please enter all details',
  done: 'Done',
  recurrenceSettings: 'Recurrence settings',
  whenLearnerEnrollsInASession: 'When learners enroll in a session.',
  whenSessionEnrolmentMoreThanThreshold: 'When session enrolment more than threshold.',
  inputValidationMessage: 'Use whole numbers between 1 and 100.',
  reminderEndCondition:
    'The repeat reminder will end automatically when the learner’s status for this module changes.',
  enrolmentReminderEndCondition:
    'Repeat reminders will stop automatically once session enrollment exceeds the threshold or the session is completed.',
});
