import styled from 'styled-components';

import Description from '@mindtickle/description';
import Input from '@mindtickle/input';
import { tokens } from '@mindtickle/styles/lib';

export const StyledInformation = styled.div`
  width: 100%;

  .name-section,
  .description-section {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid ${tokens.borderTokens.COLOR_BORDER_SECONDARY} !important;
  }
`;

export const StyledInput = styled(Input)`
  flex-basis: 100%;
`;

export const StyledDescription = styled(Description)`
  flex-basis: 100%;
  .ql-container.ql-snow > .ql-editor {
    overflow-wrap: anywhere;
  }
`;

export const InputContainer = styled.div`
  display: flex;
  flex-direction: column;
  flex-basis: 70%;
`;
