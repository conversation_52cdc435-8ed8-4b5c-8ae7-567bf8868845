import { createReducers } from '@mindtickle/medux/Reducer';

import {
  UPDATE_MAIL_TEMPLATE,
  MANIPULATE_DATA,
  GET_REMINDER_DATA,
  UPDATE_MAIL_AUTOMATION_DATA,
} from './actionTypes';

const reducer = createReducers([
  {
    name: GET_REMINDER_DATA,
    options: {
      async: true,
    },
  },
  {
    name: UPDATE_MAIL_TEMPLATE,
    options: {
      key: 'mailTemplates',
    },
  },
  {
    name: UPDATE_MAIL_AUTOMATION_DATA,
    options: {
      key: 'mailAutomationData',
    },
  },
  {
    name: MANIPULATE_DATA,
    options: {
      key: 'operationStatus',
      async: true,
    },
  },
]);

export default reducer;
