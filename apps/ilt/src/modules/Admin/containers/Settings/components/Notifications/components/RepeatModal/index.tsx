import { useCallback, useEffect, useState } from 'react';

import { FormattedMessage } from 'react-intl';

import { ICON_MAP } from '@mindtickle/icon';
import InputNumber from '@mindtickle/input-number';
import Modal from '@mindtickle/modal';
import Radio from '@mindtickle/radio';
import Select from '@mindtickle/select';
import Tooltip from '@mindtickle/tooltip';

import { getRecurrenceSummary } from '../../helpers';
import { WeeksDaysStatusType } from '../../typeDefs';

import {
  OPTIONS_SINGULAR,
  OPTIONS_PLURAL,
  RECURRENCE_TYPE_CONST,
  RECURRENCE_ENDS_CONST,
  WEEK_DAYS_OBJECT_STATUS,
  DEFAULT_RECURRENCE_ADMIN_INPUT,
} from './constants';
import messages from './messages';
import ModalFooter from './ModalFooter';
import {
  StyledTag,
  StyledTagWrapper,
  StyledTitle,
  Occurren<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ry<PERSON>rapper,
  StyledTex<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  It<PERSON>,
  StyledErrorMessage,
  StyledIcon,
} from './styles';

type RepeatModalType = {
  recurrenceFrequency?: number | null;
  recurrenceType?: string | null;
  recurrenceDays?: Array<string | null> | null;
  recurrenceCount?: number | null;
  recurrenceEndType?: string | null;
  onCancel: Function;
  onSave: Function;
  emailStatus: string;
  isEnrolmentThresholdReminder?: boolean;
};

// eslint-disable-next-line max-statements
const RepeatModal = (payload: RepeatModalType) => {
  const [recurrenceFrequency, setRecurrenceFrequency] = useState<number>(
    payload.recurrenceFrequency || 3
  );
  const [recurrenceType, setRecurrenceType] = useState(
    payload.recurrenceType || RECURRENCE_TYPE_CONST.DAYS
  );
  const [recurrenceDaysStatus, setRecurrenceDaysStatus] =
    useState<WeeksDaysStatusType>(WEEK_DAYS_OBJECT_STATUS);
  const [recurrenceCount, setRecurrenceCount] = useState<number>(
    payload.recurrenceCount || DEFAULT_RECURRENCE_ADMIN_INPUT
  );

  const [recurrenceEndType, setRecurrenceEndType] = useState<string>(
    payload.recurrenceEndType || RECURRENCE_ENDS_CONST.STATE_CHANGE
  );
  const [recurrenceSummary, setRecurrenceSummary] = useState<string>('every 3 days');
  const [isRecurringFrequencyError, setIsRecurringFrequencyCountError] = useState<boolean>(false);
  const [isRecurrenceCountError, setIsRecurrenceCountError] = useState<boolean>(false);
  const [recurrenceTypeOptions, setRecurrenceTypeOptions] = useState<any>(OPTIONS_PLURAL);

  useEffect(() => {
    if (recurrenceEndType === RECURRENCE_ENDS_CONST.STATE_CHANGE) {
      setIsRecurrenceCountError(false);
      setRecurrenceCount(DEFAULT_RECURRENCE_ADMIN_INPUT);
    }
  }, [recurrenceEndType]);

  useEffect(() => {
    if (isValidInput(recurrenceFrequency)) {
      setRecurrenceTypeOptions(
        parseInt(recurrenceFrequency.toString(), 10) === 1 ? OPTIONS_SINGULAR : OPTIONS_PLURAL
      );
    }
  }, [recurrenceFrequency]);

  useEffect(() => {
    const selectedWeekDays =
      Array.isArray(payload.recurrenceDays) && payload.recurrenceDays?.length > 0
        ? payload.recurrenceDays
        : ['Tue'];
    selectedWeekDays.forEach(value => {
      if (value) {
        recurrenceDaysStatus[value] = true;
      }
      setRecurrenceDaysStatus({ ...recurrenceDaysStatus });
    });
  }, [payload.recurrenceDays]);

  const onClickWeekDayHandler = useCallback(
    (selectedWeekDay: string) => {
      const selectedWeekDaysStatus = recurrenceDaysStatus;
      selectedWeekDaysStatus[selectedWeekDay] = !selectedWeekDaysStatus[selectedWeekDay];
      setRecurrenceDaysStatus({ ...selectedWeekDaysStatus });

      let isNoneSelected = true;
      Object.keys(selectedWeekDaysStatus).forEach(key => {
        if (selectedWeekDaysStatus[key]) {
          isNoneSelected = false;
        }
      });

      if (isNoneSelected) {
        selectedWeekDaysStatus['Tue'] = true;
        setRecurrenceDaysStatus({ ...selectedWeekDaysStatus });
      }
    },
    [recurrenceDaysStatus]
  );

  useEffect(() => {
    if (isRecurrenceCountError || isRecurringFrequencyError) {
      return;
    }
    const formattedSummary = getRecurrenceSummary(
      recurrenceFrequency,
      recurrenceType,
      recurrenceDaysStatus
    );
    setRecurrenceSummary(formattedSummary);
  }, [
    isRecurrenceCountError,
    isRecurringFrequencyError,
    recurrenceDaysStatus,
    recurrenceFrequency,
    recurrenceType,
  ]);

  const customFormatter = (value: any) => value;

  const isValidInput = (value: any) => {
    const reg = '^[0-9]+$';
    const numberValue = parseInt(value, 10);
    return (
      value?.toString()?.match(reg) && !isNaN(numberValue) && numberValue >= 1 && numberValue <= 100
    );
  };

  const customParserForRecurringFrequency = (value: any) => {
    if (isValidInput(value)) {
      setIsRecurringFrequencyCountError(false);
      setRecurrenceFrequency(value);
      return value;
    }

    setIsRecurringFrequencyCountError(true);
    setRecurrenceFrequency(value);
    return value;
  };

  const customParserForRecurrenceCount = (value: any) => {
    setRecurrenceCount(value);
    if (isValidInput(value)) {
      setIsRecurrenceCountError(false);
      return value;
    }

    setIsRecurrenceCountError(true);
    return value;
  };

  const onCancelModal = () => {
    payload.onCancel();
  };

  const onSaveRecurrenceSettings = () => {
    const recurrenceCountNumberValue = parseInt(recurrenceCount.toString(), 10);
    const recurrenceFrequencyNumberValue = parseInt(recurrenceFrequency.toString(), 10);

    payload.onSave(
      {
        recurrenceFrequency: recurrenceFrequencyNumberValue,
        recurrenceType: recurrenceType,
        recurrenceCount: recurrenceCountNumberValue,
        recurrenceDays: Object.keys(recurrenceDaysStatus).filter(
          eachDay => recurrenceDaysStatus[eachDay]
        ),
        recurrenceEndType: recurrenceEndType,
      },
      recurrenceSummary
    );
  };

  return (
    <Modal
      visible={true}
      onCancel={onCancelModal}
      onOk={onSaveRecurrenceSettings}
      width={`500px`}
      zIndex={9913}
      title={<FormattedMessage {...messages.recurrenceSettings} />}
      footer={
        <ModalFooter
          onCancel={onCancelModal}
          onOk={onSaveRecurrenceSettings}
          disabled={isRecurrenceCountError || isRecurringFrequencyError}
        />
      }
    >
      <SectionWrapper>
        <StyledTitle>Repeat every</StyledTitle>
        <>
          <StyledTagWrapper isError={isRecurringFrequencyError}>
            <InputNumber
              type="number"
              formatter={customFormatter}
              parser={customParserForRecurringFrequency}
              value={recurrenceFrequency}
              onChange={customParserForRecurringFrequency}
              data-testid="repeatReminderInput"
              style={{ width: '72px' }}
              onBlur={() => recurrenceFrequency}
            />
            <Select
              options={recurrenceTypeOptions}
              onChange={(type: string) => setRecurrenceType(type)}
              value={recurrenceType}
              style={{ minWidth: 90, marginLeft: '16px' }}
              dropdownStyle={{ minWidth: '100px' }}
              dropdownMatchSelectWidth
            />
          </StyledTagWrapper>
          <>
            <StyledErrorMessage
              style={{
                visibility: isRecurringFrequencyError ? 'visible' : 'hidden',
              }}
            >
              Use whole numbers between 1 and 100.
            </StyledErrorMessage>
          </>
        </>
      </SectionWrapper>
      {recurrenceType === RECURRENCE_TYPE_CONST.WEEKS && (
        <SectionWrapper>
          <StyledTitle>Repeat on</StyledTitle>
          <StyledTagWrapper>
            {Object.keys(recurrenceDaysStatus).map((eachDay: string) => (
              <StyledTag
                key={eachDay}
                isActive={recurrenceDaysStatus[eachDay]}
                onClick={() => onClickWeekDayHandler(eachDay)}
              >
                {eachDay}
              </StyledTag>
            ))}
          </StyledTagWrapper>
        </SectionWrapper>
      )}
      <div>
        <StyledTitle>Ends</StyledTitle>
        <div className="ends-radio-buttons-section">
          <Radio.Group
            value={recurrenceEndType}
            defaultValue={RECURRENCE_ENDS_CONST.STATE_CHANGE}
            onChange={(event: any) => {
              setRecurrenceEndType(event.target.value);
            }}
          >
            <Item>
              <Radio value={RECURRENCE_ENDS_CONST.STATE_CHANGE}>
                {payload?.isEnrolmentThresholdReminder ? (
                  <FormattedMessage {...messages.whenSessionEnrolmentMoreThanThreshold} />
                ) : (
                  <FormattedMessage {...messages.whenLearnerEnrollsInASession} />
                )}
              </Radio>
            </Item>
            <Radio value={RECURRENCE_ENDS_CONST.ADMIN_INPUT}>
              <>
                <OccurrencesWrapper isError={isRecurrenceCountError}>
                  <div style={{ marginRight: '16px' }}>After</div>
                  <InputNumber
                    type="number"
                    formatter={customFormatter}
                    parser={customParserForRecurrenceCount}
                    value={recurrenceCount}
                    min={1}
                    max={100}
                    onChange={customParserForRecurrenceCount}
                    data-testid="repeatReminderInputOcc"
                    style={{ width: '72px' }}
                    onBlur={() => recurrenceCount}
                    disabled={recurrenceEndType !== RECURRENCE_ENDS_CONST.ADMIN_INPUT}
                  />
                  <div style={{ marginLeft: '12px' }}>reminders</div>
                  <Tooltip
                    placement={'top'}
                    title={
                      !payload?.isEnrolmentThresholdReminder ? (
                        <FormattedMessage {...messages.reminderEndCondition} />
                      ) : (
                        <FormattedMessage {...messages.enrolmentReminderEndCondition} />
                      )
                    }
                    overlayStyle={{ width: '180px' }}
                  >
                    <StyledIcon type={ICON_MAP.INFO2} className="info-icon" />
                  </Tooltip>
                </OccurrencesWrapper>
                <StyledErrorMessage
                  style={{
                    marginLeft: '56px',
                    visibility: isRecurrenceCountError ? 'visible' : 'hidden',
                  }}
                >
                  <FormattedMessage {...messages.inputValidationMessage} />
                </StyledErrorMessage>
              </>
            </Radio>
          </Radio.Group>
        </div>
      </div>
      <LineWrapper />
      <SummaryWrapper>
        <StyledTitle>Summary:</StyledTitle>
        <StyledText>{recurrenceSummary}</StyledText>
      </SummaryWrapper>
    </Modal>
  );
};

export default RepeatModal;
