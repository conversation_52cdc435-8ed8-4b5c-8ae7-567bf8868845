export const getTwentyFourHrsOptions = () => {
  const options = [];
  let startVal = '';
  for (let counter = 0; counter < 24; counter += 1) {
    startVal = counter < 10 ? '0' : '';
    options.push({ content: `${startVal}${counter}:00`, key: counter * 60 });
    options.push({ content: `${startVal}${counter}:30`, key: counter * 60 + 30 });
  }
  return options;
};

export const getTwentyFourHrsOptionsText = () => {
  const options = [];
  let startVal = '';
  for (let counter = 0; counter < 24; counter += 1) {
    startVal = counter < 10 ? '0' : '';
    options.push({ content: `${startVal}${counter}:00`, key: `${startVal}${counter}:00` });
    options.push({ content: `${startVal}${counter}:30`, key: `${startVal}${counter}:30` });
  }
  return options;
};
