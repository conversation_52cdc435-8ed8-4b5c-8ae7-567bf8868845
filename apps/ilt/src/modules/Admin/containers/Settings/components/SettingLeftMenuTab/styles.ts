import styled from 'styled-components';

import { tokens } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS } from '~/config/constants';

export const StyledSettingLeftMenuWrapper = styled.div`
  height: 100%;
  width: 100%;
  border-right: 1px solid ${tokens.borderTokens.COLOR_BORDER_DEFAULT};

  .${THEME_PREFIX_CLS}-menu {
    height: calc(100vh - 52px);
  }

  .${THEME_PREFIX_CLS}-menu-item {
    margin: 8px 0 0 0;
    height: 36px;
    line-height: 36px;
    padding-left: 16px;
  }

  .${THEME_PREFIX_CLS}-menu-item-selected {
    &::after {
      border-right: 0px solid transparent;
    }
  }

  .${THEME_PREFIX_CLS}-menu-item:not(.${THEME_PREFIX_CLS}-menu-item-disabled) {
    &:hover {
      font-weight: normal;
    }
    &.${THEME_PREFIX_CLS}-menu-item-selected {
      &:hover {
        font-weight: 600;
      }
    }
  }
`;

export const styleMenuItem = { paddingLeft: '16px' };
