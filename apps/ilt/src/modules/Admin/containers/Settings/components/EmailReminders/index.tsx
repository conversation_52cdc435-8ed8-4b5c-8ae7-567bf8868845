import { useState } from 'react';

import { useParams } from 'react-router-dom';
import { useUserAuth } from 'ui_shell/Auth';

import Switch from '@mindtickle/switch';
import { Title } from '@mindtickle/typography';

import { ILT_CALENDAR_AUTO_SYNC_CONFIG_KEY } from '~/modules/Admin/config/sessions.constants';

import { STATIC } from '../../constants';
import {
  StyledToggleHeader,
  StyledRandomizationNoteSmall,
  StyledRandomizationNoteSmallWithWarning,
  StyledSectionWithTooltip,
} from '../../styles';
import Notification from '../Notifications';
import { StyledCard, StyledGrid } from '../Notifications/styles';

import type { TProps } from './typeDefs';

const styleLableContainer = { flexGrow: 1 };

export default function EmailReminders(props: TProps) {
  const { updateSetting, moduleData } = props;
  const { moduleId, seriesId } = useParams();

  const { learnerCnfSsnEmail } = moduleData;

  const [learnerCnfSsnEmailInSettings, setLearnerCnfSsnEmail] = useState(!!learnerCnfSsnEmail);

  const { tempFeatureConfig = {} } = useUserAuth();

  const isCalendarAutoSyncEnabled =
    ILT_CALENDAR_AUTO_SYNC_CONFIG_KEY in tempFeatureConfig
      ? tempFeatureConfig[ILT_CALENDAR_AUTO_SYNC_CONFIG_KEY]
      : true;

  function learnerCnfSsnEmailChangeHandler(value: any) {
    setLearnerCnfSsnEmail(value);
    updateSetting({ type: STATIC, moduleId, seriesId, data: { learnerCnfSsnEmail: value } });
  }

  return (
    <StyledCard>
      <StyledGrid>
        <StyledToggleHeader>
          <div style={styleLableContainer}>
            <StyledSectionWithTooltip toggleDisabled={!learnerCnfSsnEmailInSettings}>
              <Title level={4}>Session confirmation email</Title>
            </StyledSectionWithTooltip>
            <StyledRandomizationNoteSmall toggleDisabled={!learnerCnfSsnEmailInSettings}>
              <span>
                {`Send a confirmation email and notification to the learner when they enroll or join the waiting list for a session.`}
              </span>
            </StyledRandomizationNoteSmall>
            {!learnerCnfSsnEmailInSettings && !!isCalendarAutoSyncEnabled && (
              <StyledRandomizationNoteSmallWithWarning>
                <span>
                  {`If this is switched on, it will automatically sync all events with the learner calendars`}
                </span>
              </StyledRandomizationNoteSmallWithWarning>
            )}
          </div>
          <Switch
            checked={learnerCnfSsnEmailInSettings}
            defaultChecked={learnerCnfSsnEmailInSettings}
            onChange={(e: boolean) => learnerCnfSsnEmailChangeHandler(e)}
          />
        </StyledToggleHeader>
      </StyledGrid>
      <Notification moduleData={moduleData} />
    </StyledCard>
  );
}
