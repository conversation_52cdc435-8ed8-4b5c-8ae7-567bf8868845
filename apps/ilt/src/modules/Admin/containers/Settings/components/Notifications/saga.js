import { put, takeEvery, call, all, select } from 'redux-saga/effects';

import { getActions } from '@mindtickle/medux/Action';

import ReminderService from '~/modules/Admin/api/moduleReminders';

import {
  UPDATE_MAIL_TEMPLATE,
  UPDATE_MAIL_AUTOMATION_DATA,
  MANIPULATE_DATA,
  GET_REMINDER_DATA,
} from './actionTypes';

function* getData({ payload: { moduleId, seriesId, companyId } }) {
  const params = { moduleId, seriesId, companyId };
  const { SUCCESS, FAIL } = getActions({
    name: GET_REMINDER_DATA,
    options: { async: true },
  });
  try {
    const templates = yield call(ReminderService.getMailTemplates, params);
    yield put(getActions(UPDATE_MAIL_TEMPLATE)(templates));
    const { mailAutomationData } = yield call(ReminderService.getReminderAutomations, params);
    yield put(getActions(UPDATE_MAIL_AUTOMATION_DATA)({ ...mailAutomationData }));

    yield put(SUCCESS());
  } catch (error) {
    yield put(FAIL(error));
  }
}

function parseAutomationUpdate({ mailAutomationData, operationResponse, paramData: data }) {
  const { created = [], updated = [], deleted = [] } = operationResponse;
  const parsedMailAutomationData = { ...mailAutomationData };

  if (created.length && created[0]) {
    const { task } = created[0];
    if (!parsedMailAutomationData[task]) {
      parsedMailAutomationData[task] = [];
    }
    parsedMailAutomationData[task] = [...parsedMailAutomationData[task], created[0]];
  }

  if (updated.length && updated[0]) {
    const { task, id } = updated[0];
    parsedMailAutomationData[task] = parsedMailAutomationData[task].map(reminder =>
      reminder.id === id
        ? {
            ...reminder,
            ...updated[0],
          }
        : reminder
    );
  }

  if (deleted.length && deleted[0]) {
    const { task } = data;
    const { id } = deleted[0];

    parsedMailAutomationData[task] = parsedMailAutomationData[task].filter(
      reminder => reminder.id !== id
    );
  }

  return parsedMailAutomationData;
}

function* manipulateData({
  payload: { moduleId, seriesId, companyId, operation, data, successCallback },
}) {
  const { SUCCESS, FAIL } = getActions({
    name: MANIPULATE_DATA,
    options: { async: true },
  });
  try {
    const params = {
      moduleId,
      seriesId,
      companyId,
      operation,
      data,
    };

    const operationResponse = yield call(ReminderService.operateReminders, params);

    const {
      moduleReminders: { mailAutomationData },
    } = yield select();
    yield put(
      getActions(UPDATE_MAIL_AUTOMATION_DATA)(
        parseAutomationUpdate({ mailAutomationData, operationResponse, paramData: data })
      )
    );
    yield put(SUCCESS({ data: { operation } }));
    successCallback?.();
  } catch (error) {
    if (error.statusCode === 403) {
      yield put(FAIL(error, { globalError: true }));
    }
    yield put(FAIL({ ...error, operation }));
  }
}

function* handleLoadData() {
  yield takeEvery(GET_REMINDER_DATA, getData);
}

function* handleOperations() {
  yield takeEvery(MANIPULATE_DATA, manipulateData);
}

export default function* () {
  yield all([handleLoadData(), handleOperations()]);
}
