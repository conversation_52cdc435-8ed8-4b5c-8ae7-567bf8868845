import { FormattedMessage } from 'react-intl';

import Tooltip from '@mindtickle/tooltip';

import messages from '../messages';
import { StyledPrimaryButton, StyledSecondaryButton, Wrapper } from '../styles';

const ModalFooter = ({
  onCancel,
  onOk,
  disabled,
}: {
  onCancel: Function;
  onOk: Function;
  disabled: boolean;
}) => (
  <Wrapper>
    <StyledSecondaryButton type="text" onClick={onCancel}>
      Cancel
    </StyledSecondaryButton>
    <Tooltip
      getPopupContainer={() => document.body}
      title={disabled ? <FormattedMessage {...messages.validationMsg} /> : ''}
    >
      <span>
        <StyledPrimaryButton type="primary" disabled={disabled} onClick={onOk}>
          <span>
            <FormattedMessage {...messages.done} />
          </span>
        </StyledPrimaryButton>
      </span>
    </Tooltip>
  </Wrapper>
);

export default ModalFooter;
