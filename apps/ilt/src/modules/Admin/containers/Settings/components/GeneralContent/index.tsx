import { useCallback, useMemo } from 'react';

import { useParams } from 'react-router-dom';
import { useUserAuth } from 'ui_shell/Auth';

import { STATIC } from '../../constants';
import { StyledSettingsCard } from '../../styles';

import Information from './components/Information';
import ModuleRelevance from './components/ModuleRelevance';
import ShowLearnerTimezone from './components/ShowLearnerTimezone';
import { Thumbnail } from './components/Thumbnail';

import type { thumbnailObjTypeFromShellComponent } from './components/Thumbnail/typeDefs';
import type { TProps } from './typeDefs';

const styleSettingsCard = { margin: '25px' };

const GeneralContent = (props: TProps) => {
  const { moduleData, updateSetting, trackerPageName } = props;
  const { moduleId, seriesId } = useParams();
  const { features } = useUserAuth();
  const moduleRelevanceEnabled = features?.moduleRelevanceEnabled || false;

  const { name: title, description, thumbObj } = moduleData;

  const thumbnailObjForProp = useMemo(
    () => ({
      id: thumbObj.thumbId,
      src: thumbObj.thumbUrl,
    }),
    [thumbObj.thumbId, thumbObj.thumbUrl]
  );

  const titleChangeHandler = useCallback(
    (value: string) => {
      updateSetting({ type: STATIC, moduleId, seriesId, data: { name: value } });
    },
    [moduleId, seriesId, updateSetting]
  );

  const descriptionChangeHandler = useCallback(
    (value: string) => {
      updateSetting({ type: STATIC, moduleId, seriesId, data: { description: value } });
    },
    [moduleId, seriesId, updateSetting]
  );

  const thumbnailChangeHandler = useCallback(
    (value: thumbnailObjTypeFromShellComponent) => {
      updateSetting({
        type: STATIC,
        moduleId,
        seriesId,
        data: {
          thumbObj: {
            thumbId: value.id,
            thumbUrl: value.src,
            thumbType: 81, // TODO: need to check with backend whether thumbnail federation images will always be of type 81, because we dont send this to thumbnail update api it is not pulling thumbail information
          },
        },
      });
    },
    [moduleId, seriesId, updateSetting]
  );

  return (
    <StyledSettingsCard style={styleSettingsCard}>
      <Thumbnail
        thumbnailObj={thumbnailObjForProp}
        thumbnailChangeHandler={thumbnailChangeHandler}
        trackerPageName={trackerPageName}
      />
      <Information
        title={title || ''}
        description={description || ''}
        titleChangeHandler={titleChangeHandler}
        descriptionChangeHandler={descriptionChangeHandler}
      />
      {moduleRelevanceEnabled && (
        <ModuleRelevance
          moduleRelevance={moduleData.moduleRelevance}
          updateSetting={updateSetting}
        />
      )}
      <ShowLearnerTimezone
        showLearnerTimezone={moduleData.showLearnerTimezone}
        updateSetting={updateSetting}
      />
    </StyledSettingsCard>
  );
};

export default GeneralContent;
