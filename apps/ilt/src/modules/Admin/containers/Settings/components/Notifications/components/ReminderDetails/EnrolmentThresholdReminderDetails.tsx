import { useCallback, useMemo, Fragment, useState } from 'react';

import moment, { Moment } from 'moment';
import { FormattedMessage } from 'react-intl';

import CheckBox from '@mindtickle/checkbox';
import { DatePicker } from '@mindtickle/date-filter';
import EllipsisTooltip from '@mindtickle/ellipsis-tooltip';
import Icon, { ICON_MAP } from '@mindtickle/icon';
import InputNumber from '@mindtickle/input-number';
import Popover from '@mindtickle/popover';
import Select from '@mindtickle/select';
import Tooltip from '@mindtickle/tooltip';
import { Text } from '@mindtickle/typography';

import { useILTAdminSnowplowTracker } from '~/modules/Admin/hooks/useILTAdminSnowplowTracker';
import {
  MIXPANEL_UI_EVENTS,
  SNOWPLOW_FIELD_NAMES,
} from '~/modules/Admin/hooks/useILTAdminSnowplowTracker/constants';
import { MODULE_SETTINGS_EVENTS } from '~/modules/Admin/hooks/useILTAdminSnowplowTracker/constants/settings';
import { getCurrentTimeZone } from '~/modules/Admin/utils/timing';
import { parseNotAllowedSymbolsNumberInput } from '~/utils/core';

import { EMAIL_CATEGORY, SETTINGS_TYPE } from '../../../../config';
import { getTwentyFourHrsOptionsText } from '../../config';
import { RECURRENCE_TYPE_CONST } from '../../constants';
import {
  getReminderTextForDays,
  getSchedule,
  getRecurrenceSummary,
  getRecurrenceEndConditionInWords,
} from '../../helpers';
import { ReminderActionType, WeeksDaysStatusType } from '../../typeDefs';
import { handleWrongReminder } from '../../utils';
import DeletePopover from '../DeletePopover';
import EnrolmentReminderSaveBlock from '../ReminderSaveBlock/EnrolmentReminderSaveBlock';
import RepeatModal from '../RepeatModal';

import messages from './messages';
import {
  StyledAddReminder,
  StyledIcon,
  StyledDaysValueContainer,
  StyledLinkWrapper,
  StyledEditIcon,
  StyledDaysInput,
  StyledErrorMessage,
  StyledRepeatContainer,
  StyledSelect,
  EnrolmentReminderSaveContainer,
} from './styles';

import type { ReminderDetailsProps } from './typeDefs';
import type { EmailAutomationTransformed, RecurrenceSettingsType } from '../../typeDefs';

const disabledDate = (current: any) =>
  current && current < moment().endOf('day').subtract(1, 'day');

// eslint-disable-next-line max-statements,complexity
const EnrolmentThresholdReminderDetails = ({
  automation,
  reminderOptions,
  selectedEmailReminderType,
  selectedDate,
  selectedTime,
  onDeleteClick,
  hasSpecificDate,
  daysValue,
  updateDraftReminders,
  onDeleteDraftReminder,
  onSaveClick,
  disableSessionDrawerSettings,
  isEnrolmentThresholdReminder,
  isSessionDrawerReminder,
  sessionId,
}: ReminderDetailsProps) => {
  const [openRepeatReminderModal, setOpenRepeatReminderModal] = useState(false);
  const [areDaysValid, setAreDaysValid] = useState(true);
  const [isReminderEdited, setIsReminderEdited] = useState(false);
  const [invitationDays, setInvitationDaysCount] = useState(daysValue || 0);
  const [popoverVisible, setPopoverVisible] = useState(false);
  const tracker = useILTAdminSnowplowTracker();

  const hidePopover = () => {
    setPopoverVisible(false);
  };

  const handlePopoverVisibleChange = (newOpen: boolean) => {
    setPopoverVisible(newOpen);
  };

  const onReminderTypeChange = useCallback(
    (selectedEmailReminderType: string) => {
      updateDraftReminders({
        type: ReminderActionType.UPDATE_REMINDER_TYPE,
        payload: { automation, selectedEmailReminderType },
      });
    },
    [automation, updateDraftReminders]
  );

  const onDaysValueChange = useCallback(
    (daysValue: number) => {
      if (daysValue >= 1 && daysValue <= 730) {
        updateDraftReminders({
          type: ReminderActionType.UPDATE_REMINDER_DAY_VALUE,
          payload: { automation, daysValue },
        });
      }
    },
    [automation, updateDraftReminders]
  );

  const handleDateChange = useCallback(
    (date: Moment) => {
      updateDraftReminders({
        type: ReminderActionType.UPDATE_REMINDER_DATE,
        payload: { automation, date },
      });
    },
    [automation, updateDraftReminders]
  );

  const handleTimeChange = useCallback(
    (time: number) => {
      updateDraftReminders({
        type: ReminderActionType.UPDATE_REMINDER_TIME,
        payload: { automation, time },
      });
    },
    [automation, updateDraftReminders]
  );

  const reminderDayValueText = useMemo(
    () =>
      getReminderTextForDays(
        selectedEmailReminderType || '',
        isEnrolmentThresholdReminder || false
      ),
    [selectedEmailReminderType]
  );

  const timeZone = useMemo(() => getCurrentTimeZone(), []);

  const onSaveReminderClick = (automation: EmailAutomationTransformed) => {
    const reminderTime: number = parseInt(getSchedule(automation) || '', 10);
    if (reminderTime && reminderTime * 1000 > Date.now()) {
      onSaveClick(automation);
    } else {
      handleWrongReminder();
    }
  };

  const recurrenceSummaryText = useMemo(() => {
    if (!automation?.recurrenceSettings?.recurrenceDays) {
      return 'every 3 days';
    }
    let recurrenceDaysStatus: WeeksDaysStatusType = {};
    automation?.recurrenceSettings?.recurrenceDays.forEach(value => {
      if (value) {
        recurrenceDaysStatus[value] = true;
      }
    });

    const summary = getRecurrenceSummary(
      automation?.recurrenceSettings?.recurrenceFrequency,
      automation?.recurrenceSettings?.recurrenceType,
      recurrenceDaysStatus
    );

    if (summary === 'Error') {
      return 'every 3 days';
    }
    return summary;
  }, [
    automation?.recurrenceSettings?.recurrenceDays,
    automation?.recurrenceSettings?.recurrenceFrequency,
    automation?.recurrenceSettings?.recurrenceType,
  ]);

  const OnSelectRecurring = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      updateDraftReminders({
        type: ReminderActionType.UPDATE_RECURRENCE_SETTINGS,
        payload: {
          automation,
          recurrenceSettings: {
            ...(automation.recurrenceSettings || {}),
            recurring: e.target.checked,
          },
        },
      });
    },
    [automation, updateDraftReminders]
  );

  const onCancelRepeatReminderModal = () => {
    setOpenRepeatReminderModal(false);
  };

  const OnSaveModal = (recurrenceSettings: RecurrenceSettingsType, recurrenceSummary: string) => {
    updateDraftReminders({
      type: ReminderActionType.UPDATE_RECURRENCE_SETTINGS,
      payload: {
        automation,
        recurrenceSettings: {
          ...(automation.recurrenceSettings || {}),
          ...recurrenceSettings,
        },
      },
    });

    tracker.trackStructuredEvent({
      eventName: MIXPANEL_UI_EVENTS.RECURRENCE_SETTINGS_UPDATED,
      [SNOWPLOW_FIELD_NAMES.REPEAT_EVERY]: recurrenceSettings?.recurrenceType,
      [SNOWPLOW_FIELD_NAMES.REPEAT_EVERY_COUNT]: recurrenceSettings?.recurrenceFrequency,
      [SNOWPLOW_FIELD_NAMES.ILT_SESSION_ID]: sessionId,
      [SNOWPLOW_FIELD_NAMES.REPEAT_ON]:
        recurrenceSettings?.recurrenceType === RECURRENCE_TYPE_CONST.WEEKS
          ? recurrenceSettings.recurrenceDays
          : undefined,
      [SNOWPLOW_FIELD_NAMES.ENDS_ON]: getRecurrenceEndConditionInWords(
        recurrenceSettings?.recurrenceEndType || '',
        recurrenceSettings?.recurrenceCount || 0
      ),
    });

    setOpenRepeatReminderModal(false);
  };

  const onClickEditRecurrence = () => {
    setOpenRepeatReminderModal(true);

    tracker.trackStructuredEvent({
      eventName: MIXPANEL_UI_EVENTS.EDIT_RECURRENCE_SETTINGS_CLICKED,
      ilt_session_id: sessionId,
    });
  };

  const onSaveClickWrapper = async () => {
    if (!areDaysValid) {
      return;
    }

    // @ts-ignore
    const onSuccessTracker = automation => {
      tracker.trackStructuredEvent({
        eventName: MODULE_SETTINGS_EVENTS.MODULE_SETTINGS_UPDATED,
        [SNOWPLOW_FIELD_NAMES.REMINDER_OPERATION]: automation?.type,
        [SNOWPLOW_FIELD_NAMES.SETTING_TYPE]: SETTINGS_TYPE.EMAILS_FOR_LEARNER,
        // @ts-ignore
        [SNOWPLOW_FIELD_NAMES.SETTING_CATEGORY]: EMAIL_CATEGORY.NOT_ENROLLED,
        [SNOWPLOW_FIELD_NAMES.SUB_SETTING_TYPE]: automation?.selectedEmailReminderType || 'NA',
        [SNOWPLOW_FIELD_NAMES.SUB_SETTING_VALUE]: automation?.hasSpecificDate
          ? moment
              .unix(parseInt(getSchedule(automation)))
              .utc()
              .format('YYYY-MM-DD HH:mm:ss')
          : automation?.daysValue,
        [SNOWPLOW_FIELD_NAMES.RECURRENCE]: automation?.recurrenceSettings?.recurring ? 'Yes' : 'No',
        [SNOWPLOW_FIELD_NAMES.RECURRENCE_SUMMARY]: recurrenceSummaryText,
        [SNOWPLOW_FIELD_NAMES.EMAIL_TEMPLATE_SELECTED_ID]: automation?.selectedTemplate || 'NA',
        [SNOWPLOW_FIELD_NAMES.SUB_SETTING_TYPE]: 'enrollment_threshold_reminder',
        [SNOWPLOW_FIELD_NAMES.EMAIL_TEMPLATE_SELECTED]: automation?.selectedTemplate
          ? automation?.selectedTemplate
          : 'NA',
      });
    };

    setIsReminderEdited(false);
    const successCallback = () =>
      onSuccessTracker({
        ...automation,
        daysValue: invitationDays,
        isEdited: true,
      });

    onSaveClick({ ...automation, daysValue: invitationDays, isEdited: true }, successCallback);
  };

  const onDeleteDraftReminderWrapper = async () => {
    setIsReminderEdited(false);
    setAreDaysValid(true);
    setInvitationDaysCount(daysValue || 0);
    onDeleteDraftReminder(automation);
  };

  return (
    <StyledAddReminder>
      <div className="due-date-select-section">
        <div className="select-container-vertical">
          <StyledSelect
            options={reminderOptions}
            value={selectedEmailReminderType}
            onChange={onReminderTypeChange}
            style={{ width: '280px' }}
            dropdownStyle={{ width: '300px' }}
            disabled={disableSessionDrawerSettings}
          />
          {!disableSessionDrawerSettings && (
            <Popover
              content={
                <DeletePopover
                  onDelete={() => {
                    onDeleteClick(automation);
                    hidePopover();
                  }}
                  onCancel={hidePopover}
                />
              }
              trigger="click"
              placement="topLeft"
              align={{ targetOffset: [14, 2] }}
              visible={popoverVisible}
              onVisibleChange={handlePopoverVisibleChange}
            >
              <StyledIcon type={ICON_MAP.DELETE} className="icon" />
            </Popover>
          )}
        </div>
        {!hasSpecificDate ? (
          <StyledDaysValueContainer>
            <div>
              <StyledDaysInput>
                <InputNumber
                  type="number"
                  precision={0}
                  onChange={onDaysValueChange}
                  min={1}
                  max={730}
                  onKeyDown={parseNotAllowedSymbolsNumberInput}
                  status={daysValue && daysValue >= 1 && daysValue <= 730 ? '' : 'error'}
                  value={daysValue}
                  placeholder={'0'}
                  disabled={disableSessionDrawerSettings}
                />
                <Text className="lowercase margin-left">{reminderDayValueText}</Text>
              </StyledDaysInput>
              {!areDaysValid && (
                <StyledErrorMessage>
                  <FormattedMessage {...messages.reminderValidationError} />
                </StyledErrorMessage>
              )}
              <StyledRepeatContainer>
                <CheckBox
                  checked={automation?.recurrenceSettings?.recurring}
                  onChange={OnSelectRecurring}
                  style={{ marginRight: '12px' }}
                  disabled={disableSessionDrawerSettings}
                />
                <div className="recurrenceSummaryTextClass">
                  <EllipsisTooltip
                    title={`Repeat ${recurrenceSummaryText}`}
                    linesToClamp={2}
                    width="210px"
                  >
                    <StyledLinkWrapper>
                      <span className="repeatText">Repeat </span> {recurrenceSummaryText}
                    </StyledLinkWrapper>
                  </EllipsisTooltip>
                </div>
                {!disableSessionDrawerSettings && (
                  <Tooltip placement={'top'} title="Edit recurrence settings">
                    <StyledEditIcon
                      type={ICON_MAP.EDIT2}
                      onClick={onClickEditRecurrence}
                      overlayStyle={{ width: '100px' }}
                    />
                  </Tooltip>
                )}
              </StyledRepeatContainer>
              <div>
                {!isSessionDrawerReminder && (automation.isEdited || isReminderEdited) && (
                  <EnrolmentReminderSaveContainer>
                    <EnrolmentReminderSaveBlock
                      automation={{
                        ...automation,
                        isEdited: automation.isEdited || isReminderEdited,
                      }}
                      onDeleteDraftReminder={onDeleteDraftReminderWrapper}
                      onSaveClick={onSaveClickWrapper}
                      disabled={!areDaysValid}
                    />
                  </EnrolmentReminderSaveContainer>
                )}
              </div>
              {openRepeatReminderModal && (
                <RepeatModal
                  onCancel={onCancelRepeatReminderModal}
                  onSave={OnSaveModal}
                  task={automation.task}
                  recurrenceFrequency={automation.recurrenceSettings?.recurrenceFrequency}
                  recurrenceType={automation.recurrenceSettings?.recurrenceType}
                  recurrenceCount={automation.recurrenceSettings?.recurrenceCount}
                  // @ts-ignore
                  recurrenceDays={automation.recurrenceSettings?.recurrenceDays}
                  recurrenceEndType={automation.recurrenceSettings?.recurrenceEndType}
                  isEnrolmentThresholdReminder={isEnrolmentThresholdReminder}
                />
              )}
            </div>
          </StyledDaysValueContainer>
        ) : (
          <Fragment>
            <div className="select-date-block">
              <div className="date-block">
                <DatePicker
                  wrapperClassName={'date-picker-wrapper-class'}
                  inputReadOnly
                  suffixIcon={
                    <Icon type={ICON_MAP.EDITSCHEDULE} className="custom-calendar-icon" />
                  }
                  format={'MMM DD, YYYY'}
                  onChange={(value: Moment) => handleDateChange(value)}
                  showToday={false}
                  value={selectedDate}
                  disabledDate={disabledDate}
                  defaultValue={selectedDate}
                  style={{ width: '140px', display: 'inline-block' }}
                  disabled={disableSessionDrawerSettings}
                  allowClear={false}
                />
                <span className="to-text"> at </span>
                <Select
                  dropdownStyle={{ width: '124px' }}
                  style={{ minWidth: '86px', display: 'inline-block', verticalAlign: 'bottom' }}
                  value={selectedTime}
                  onChange={handleTimeChange}
                  className="select-time"
                  options={getTwentyFourHrsOptionsText()}
                  disabled={disableSessionDrawerSettings}
                />
                <span className="to-text">{timeZone}</span>
                <div className="responsive-save-block-1440">
                  {!isSessionDrawerReminder && automation.isEdited && (
                    <EnrolmentReminderSaveBlock
                      automation={automation}
                      onDeleteDraftReminder={onDeleteDraftReminder}
                      onSaveClick={onSaveReminderClick}
                      disabled={disableSessionDrawerSettings}
                    />
                  )}
                </div>
              </div>
            </div>
          </Fragment>
        )}
      </div>
    </StyledAddReminder>
  );
};

export default EnrolmentThresholdReminderDetails;
