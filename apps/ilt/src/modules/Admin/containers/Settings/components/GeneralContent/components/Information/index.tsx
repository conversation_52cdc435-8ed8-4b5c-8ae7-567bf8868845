import React, { useState } from 'react';

import { Grid } from '@mindtickle/card';
import { Title } from '@mindtickle/typography';

import SubmitPanel from '~/components/SubmitPanel';
import useTextSubmitHandler from '~/hooks/useTextSubmitHandler';
import GET_ERROR_MESSAGES from '~/modules/Admin/config/error.messages';
import { ERROR_CODES } from '~/modules/Admin/constants/errors';
import { isUnSafeString } from '~/utils';

import { StyledInformation, StyledInput, StyledDescription, InputContainer } from './styles';

import type { Props } from './typeDefs';

const Information = (props: Props) => {
  const { title, description, titleChangeHandler, descriptionChangeHandler } = props;
  const {
    showSubmitPanel: showSubmitPanelTitle,
    onSubmit: onSubmitTitle,
    text: titleText,
    submitEnabled: titleSubmitEnabled,
    onUpdateText: onTitleUpdateText,
    onCancel: onTitleCancel,
  } = useTextSubmitHandler({
    initialText: title,
    successCallback: titleChangeHandler,
  });

  const {
    showSubmitPanel: showSubmitPanelDescription,
    onSubmit: onSubmitDescription,
    text: descriptionText,
    submitEnabled: descriptionSubmitEnabled,
    onUpdateText: onDescriptionUpdateText,
    onCancel: onDescriptionCancel,
  } = useTextSubmitHandler({
    initialText: description,
    successCallback: descriptionChangeHandler,
    allowEmptyText: true,
  });
  const [nameEditError, setNameEditError] = useState(false);
  const onTitleUpdateTextWithValidation = (value: string) => {
    if (isUnSafeString(value)) {
      setNameEditError(true);
    } else {
      setNameEditError(false);
      onTitleUpdateText(value);
    }
  };

  const onTitleCancelWithValidation = () => {
    setNameEditError(false);
    onTitleCancel();
  };

  return (
    <StyledInformation>
      <Grid className="name-section">
        <Title level={4}>Name</Title>
        <InputContainer>
          <StyledInput
            key="settings-name"
            placeholder=""
            value={titleText}
            maxLength={100}
            showMaxLengthInAddon
            onChange={(e: React.ChangeEvent<HTMLInputElement>, value: string) =>
              onTitleUpdateTextWithValidation(value)
            }
            errorMessage={
              nameEditError
                ? GET_ERROR_MESSAGES(ERROR_CODES.ENTITY_NAME_INVALID)?.defaultMessage
                : ''
            }
          />
          {showSubmitPanelTitle && (
            <div style={{ marginTop: '12px' }}>
              <SubmitPanel
                onSubmit={onSubmitTitle}
                onCancel={onTitleCancelWithValidation}
                isSubmitDisabled={!titleSubmitEnabled || nameEditError}
              />
            </div>
          )}
        </InputContainer>
      </Grid>
      <Grid className="description-section">
        <Title level={4}>Description</Title>
        <InputContainer>
          <StyledDescription
            id="moduleDescription"
            placeholder="Type here..."
            height={88}
            isControlled={true}
            content={descriptionText}
            className={'module-description-content'}
            bounds={`.module-description-content`}
            onChange={onDescriptionUpdateText}
          />
          {showSubmitPanelDescription && (
            <SubmitPanel
              onSubmit={onSubmitDescription}
              onCancel={onDescriptionCancel}
              isSubmitDisabled={!descriptionSubmitEnabled}
            />
          )}
        </InputContainer>
      </Grid>
    </StyledInformation>
  );
};

export default Information;
