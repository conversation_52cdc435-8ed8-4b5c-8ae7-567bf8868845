import styled, { css } from 'styled-components';

import Button from '@mindtickle/button';
import PrimaryButton from '@mindtickle/button/lib/PrimaryButton';
import Icon from '@mindtickle/icon';
import { tokens } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS } from '~/config/constants';

export const StyledTag = styled.div<{ isActive?: boolean }>`
  padding: 0 12px;
  border: ${props =>
    props.isActive
      ? `1px solid ${tokens.borderTokens.COLOR_BORDER_ACCENT}`
      : `1px solid ${tokens.borderTokens.COLOR_BORDER_STRONG}`};
  margin-right: 6px;
  margin-top: 6px;
  border-radius: 16px;
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  background: ${props =>
    props.isActive
      ? `${tokens.bgTokens.COLOR_BG_ACCENT_SELECTED}`
      : `${tokens.bgTokens.COLOR_BG_DEFAULT}`};
  color: ${props =>
    props.isActive
      ? `${tokens.textTokens.COLOR_TEXT_ACCENT}`
      : `${tokens.textTokens.COLOR_TEXT_TERTIARY}`};
  font-weight: 600;
  cursor: pointer;
`;

export const StyledTagWrapper = styled.div<{ isError?: boolean }>`
  display: flex;

  ${props =>
    props.isError &&
    css`
      .${THEME_PREFIX_CLS}-input-number {
        border: 1px solid ${tokens.borderTokens.COLOR_BORDER_DANGER};
        border-radius: 4px;
      }
      .${THEME_PREFIX_CLS}-input-number:hover {
        border: 1px solid ${tokens.borderTokens.COLOR_BORDER_DANGER} !important;
      }
    `}
`;

export const StyledTitle = styled.div`
  color: ${tokens.textTokens.COLOR_TEXT_DEFAULT};
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 16px;
  font-family: Inter;
`;

export const OccurrencesWrapper = styled.div<{ isError?: boolean }>`
  display: flex;
  align-items: center;

  .info-icon {
    margin-top: 2px;
  }

  ${props =>
    props.isError &&
    css`
      .${THEME_PREFIX_CLS}-input-number {
        border: 1px solid ${tokens.borderTokens.COLOR_BORDER_DANGER} !important;
        border-radius: 4px;
      }

      .${THEME_PREFIX_CLS}-input-number:hover {
        border: 1px solid ${tokens.borderTokens.COLOR_BORDER_DANGER} !important;
      }
    `}
`;

export const LineWrapper = styled.div`
  width: 100%;
  height: 1px;
  background: #d9d9d9;
  margin: 16px 0 32px 0;
`;

export const SummaryWrapper = styled.div`
  display: flex;
  align-items: flex-start;
`;

export const StyledText = styled.div`
  color: ${tokens.textTokens.COLOR_TEXT_ACCENT};
  font-size: 14px;
  font-weight: 600;
  margin-left: 4px;
  margin-bottom: 16px;
  width: 352px;
  overflow-wrap: break-word;
`;

export const SectionWrapper = styled.div`
  margin-bottom: 16px;
`;

export const Item = styled.div`
  margin-bottom: 16px;
`;

export const StyledErrorMessage = styled.div`
  font-family: Inter;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
  color: ${tokens.textTokens.COLOR_TEXT_DANGER};
  margin-top: 4px;
`;

export const StyledIcon = styled(Icon)`
  &.info-icon {
    color: ${tokens.iconTokens.COLOR_ICON_SECONDARY};
    font-size: 12px;
    padding: 2px 8px;
    cursor: pointer;
  }
`;

export const StyledSecondaryButton = styled(Button)`
  border-radius: 4px;
  border: 1px solid #d4d4d4 !important;
  width: 84px;
`;

export const StyledPrimaryButton = styled(PrimaryButton)`
  width: 84px;
  margin-left: 12px;
`;

export const Wrapper = styled.div`
  .${THEME_PREFIX_CLS}-btn-text {
    width: 84px;
    background: ${tokens.bgTokens.COLOR_BG_DEFAULT};
    color: ${tokens.textTokens.COLOR_TEXT_SECONDARY};
    border: 1px solid #d4d4d4 !important;
  }

  .${THEME_PREFIX_CLS}-modal-footer {
    border: 0 !important;
  }
`;
