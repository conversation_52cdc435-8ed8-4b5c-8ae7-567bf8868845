import Menu from '@mindtickle/menu';

import useNewILTEmailSettingsEnabled from '~/modules/Admin/hooks/useNewILTEmailSettingsEnabled';

import { SETTINGS_TYPE, settingsTab } from '../../config';

import { StyledSettingLeftMenuWrapper, styleMenuItem } from './styles';

import type { TProps } from './typeDefs';

const SettingLeftMenuTab = (props: TProps) => {
  const { setActiveTab, activeTab } = props;
  const { isNewILTEmailSettingsEnabled } = useNewILTEmailSettingsEnabled();
  return (
    <StyledSettingLeftMenuWrapper>
      <Menu mode="vertical" defaultSelectedKeys={[activeTab]} selectedKeys={[activeTab]}>
        {settingsTab.map(item => {
          if (item.id === SETTINGS_TYPE.NOTIFICATIONS && isNewILTEmailSettingsEnabled) {
            item.name = 'Emails and reminders';
          }
          return (
            <Menu.Item
              key={item.id}
              style={styleMenuItem}
              onClick={() => setActiveTab(item.id, item.urlPath)}
            >
              {item.name}
            </Menu.Item>
          );
        })}
      </Menu>
    </StyledSettingLeftMenuWrapper>
  );
};

export default SettingLeftMenuTab;
