import { EmailAutomationsTypes, EmailReminderListType, WeeksDaysMapType } from './typeDefs';

export const SIZE = 1000;
export const START = 0;

export const OPERATIONS = {
  UPDATE: 'updateReminder',
  ADD: 'createReminder',
  REMOVE: 'removeReminder',
};

export const REMINDER_KEYS = {
  [EmailAutomationsTypes.NOTIFICATION_NOT_ENROLLED_IN_ANY_SESSION]: {
    INVITED_BUT_NOT_ENROLLED: 'invited_but_not_enrolled',
    SPECIFIC_DATE: 'specific_date',
    SPECIFIC_DATE_NOTIFY: 'invited_but_not_enrolled',
  },
};

type DisplayOrderType = { [key: string]: number };
export const EMAILAUTOMATION_DISPLAY_ORDER: DisplayOrderType = {
  [EmailAutomationsTypes.INVITATION]: 0,
  [EmailAutomationsTypes.NOTIFICATION_NOT_ENROLLED_IN_ANY_SESSION]: 1,
  [EmailAutomationsTypes.NOTIFICATION_SESSION_ATTENDANCE_MARKED]: 2,
  [EmailAutomationsTypes.ENROLLED_IN_EVENT]: 3,
  [EmailAutomationsTypes.ENROLLED_IN_SESSION_AT_EVENT_LEVEL]: 4,
  [EmailAutomationsTypes.ENROLLED_IN_SESSION_AT_SESSION_LEVEL]: 5,
  [EmailAutomationsTypes.ILT_SESSION_SELF_ENROLMENT]: 6,
  [EmailAutomationsTypes.LEARNER_ADDED_TO_WAITLIST_ILT_EVENT]: 7,
  [EmailAutomationsTypes.LEARNER_ADDED_TO_WAITLIST_ILT_SESSION]: 8,
  [EmailAutomationsTypes.UNENROLLMENT_IN_ILT_EVENT]: 9,
  [EmailAutomationsTypes.UNENROLLMENT_IN_ILT_SESSION]: 10,
  [EmailAutomationsTypes.ILT_EVENT_CANCELLED]: 11,
  [EmailAutomationsTypes.ILT_EVENT_DELETED]: 12,
  [EmailAutomationsTypes.ILT_SESSION_CANCELLED]: 13,
  [EmailAutomationsTypes.UPCOMING_ILT_SESSION_REMINDER]: 14,
  [EmailAutomationsTypes.ILT_SESSION_UPDATED]: 15,
  [EmailAutomationsTypes.ILT_SESSION_DELETED]: 16,
  [EmailAutomationsTypes.ILT_SESSION_COMPLETED]: 17,
  [EmailAutomationsTypes.ILT_EVENT_COMPLETED]: 18,
};

export const NEW_EMAIL_SETTINGS_EMAILAUTOMATION_DISPLAY_ORDER: DisplayOrderType = {
  [EmailAutomationsTypes.INVITATION]: 0,
  [EmailAutomationsTypes.NOTIFICATION_NOT_ENROLLED_IN_ANY_SESSION]: 1,
  [EmailAutomationsTypes.ENROLLED_IN_EVENT]: 2,
  [EmailAutomationsTypes.ENROLLED_IN_SESSION_AT_EVENT_LEVEL]: 3,
  [EmailAutomationsTypes.ENROLLED_IN_SESSION_AT_SESSION_LEVEL]: 4,
  [EmailAutomationsTypes.ILT_SESSION_SELF_ENROLMENT]: 5,
  [EmailAutomationsTypes.LEARNER_ADDED_TO_WAITLIST_ILT_EVENT]: 6,
  [EmailAutomationsTypes.LEARNER_ADDED_TO_WAITLIST_ILT_SESSION]: 7,
  [EmailAutomationsTypes.UNENROLLMENT_IN_ILT_EVENT]: 8,
  [EmailAutomationsTypes.UNENROLLMENT_IN_ILT_SESSION]: 9,
  [EmailAutomationsTypes.ILT_EVENT_CANCELLED]: 10,
  [EmailAutomationsTypes.ILT_EVENT_DELETED]: 11,
  [EmailAutomationsTypes.ILT_SESSION_CANCELLED]: 12,
  [EmailAutomationsTypes.UPCOMING_ILT_SESSION_REMINDER]: 13,
  [EmailAutomationsTypes.ILT_SESSION_UPDATED]: 14,
  [EmailAutomationsTypes.ILT_SESSION_DELETED]: 15,
  [EmailAutomationsTypes.NOTIFICATION_SESSION_ATTENDANCE_MARKED]: 16,
  [EmailAutomationsTypes.ILT_SESSION_COMPLETED]: 17,
  [EmailAutomationsTypes.ILT_EVENT_COMPLETED]: 18,
};

export const NO_CONDITION_NO_SCHEDULE_TASKS = [
  EmailAutomationsTypes.INVITATION,
  EmailAutomationsTypes.NOTIFICATION_SESSION_ATTENDANCE_MARKED,
  EmailAutomationsTypes.ENROLLED_IN_EVENT,
  EmailAutomationsTypes.ENROLLED_IN_SESSION_AT_EVENT_LEVEL,
  EmailAutomationsTypes.ENROLLED_IN_SESSION_AT_SESSION_LEVEL,
  EmailAutomationsTypes.ILT_SESSION_SELF_ENROLMENT,
  EmailAutomationsTypes.LEARNER_ADDED_TO_WAITLIST_ILT_EVENT,
  EmailAutomationsTypes.LEARNER_ADDED_TO_WAITLIST_ILT_SESSION,
  EmailAutomationsTypes.UNENROLLMENT_IN_ILT_EVENT,
  EmailAutomationsTypes.UNENROLLMENT_IN_ILT_SESSION,
  EmailAutomationsTypes.ILT_EVENT_CANCELLED,
  EmailAutomationsTypes.ILT_EVENT_DELETED,
  EmailAutomationsTypes.ILT_SESSION_CANCELLED,
  EmailAutomationsTypes.UPCOMING_ILT_SESSION_REMINDER,
  EmailAutomationsTypes.ILT_SESSION_UPDATED,
  EmailAutomationsTypes.ILT_SESSION_DELETED,
  EmailAutomationsTypes.ILT_SESSION_COMPLETED,
  EmailAutomationsTypes.ILT_EVENT_COMPLETED,
];

export const DEFAULT_TEMPLATE_SELECTION = {
  [EmailAutomationsTypes.NOTIFICATION_NOT_ENROLLED_IN_ANY_SESSION]:
    '_learner_ilt_reminder_not_enrolled',
};

export const REMINDER_CONFIG: Record<
  EmailAutomationsTypes,
  ReadonlyArray<EmailReminderListType>
> = {
  [EmailAutomationsTypes.INVITATION]: [],
  [EmailAutomationsTypes.NOTIFICATION_SESSION_ATTENDANCE_MARKED]: [],
  [EmailAutomationsTypes.NOTIFICATION_NOT_ENROLLED_IN_ANY_SESSION]: [
    {
      key: REMINDER_KEYS[EmailAutomationsTypes.NOTIFICATION_NOT_ENROLLED_IN_ANY_SESSION]
        .INVITED_BUT_NOT_ENROLLED,
      content: 'No. of days after invitation',
    },
    {
      key: REMINDER_KEYS[EmailAutomationsTypes.NOTIFICATION_NOT_ENROLLED_IN_ANY_SESSION]
        .SPECIFIC_DATE,
      content: 'Specific date',
    },
  ],
  [EmailAutomationsTypes.ENROLLED_IN_EVENT]: [],
  [EmailAutomationsTypes.ENROLLED_IN_SESSION_AT_EVENT_LEVEL]: [],
  [EmailAutomationsTypes.ENROLLED_IN_SESSION_AT_SESSION_LEVEL]: [],
  [EmailAutomationsTypes.ILT_SESSION_SELF_ENROLMENT]: [],
  [EmailAutomationsTypes.LEARNER_ADDED_TO_WAITLIST_ILT_EVENT]: [],
  [EmailAutomationsTypes.LEARNER_ADDED_TO_WAITLIST_ILT_SESSION]: [],
  [EmailAutomationsTypes.UNENROLLMENT_IN_ILT_EVENT]: [],
  [EmailAutomationsTypes.UNENROLLMENT_IN_ILT_SESSION]: [],
  [EmailAutomationsTypes.ILT_EVENT_CANCELLED]: [],
  [EmailAutomationsTypes.ILT_EVENT_DELETED]: [],
  [EmailAutomationsTypes.ILT_SESSION_CANCELLED]: [],
  [EmailAutomationsTypes.UPCOMING_ILT_SESSION_REMINDER]: [],
  [EmailAutomationsTypes.ILT_SESSION_UPDATED]: [],
  [EmailAutomationsTypes.ILT_SESSION_DELETED]: [],
  [EmailAutomationsTypes.NOTIFICATION_SESSION_ATTENDANCE_MARKED]: [],
  [EmailAutomationsTypes.ILT_SESSION_COMPLETED]: [],
  [EmailAutomationsTypes.ILT_EVENT_COMPLETED]: [],
};

export const ALLOW_EMAIL_REMINDER: Record<EmailAutomationsTypes, boolean> = {
  [EmailAutomationsTypes.INVITATION]: false,
  [EmailAutomationsTypes.NOTIFICATION_NOT_ENROLLED_IN_ANY_SESSION]: true,
  [EmailAutomationsTypes.NOTIFICATION_SESSION_ATTENDANCE_MARKED]: false,
  [EmailAutomationsTypes.ENROLLED_IN_EVENT]: false,
  [EmailAutomationsTypes.ENROLLED_IN_SESSION_AT_EVENT_LEVEL]: false,
  [EmailAutomationsTypes.ENROLLED_IN_SESSION_AT_SESSION_LEVEL]: false,
  [EmailAutomationsTypes.ILT_SESSION_SELF_ENROLMENT]: false,
  [EmailAutomationsTypes.LEARNER_ADDED_TO_WAITLIST_ILT_EVENT]: false,
  [EmailAutomationsTypes.LEARNER_ADDED_TO_WAITLIST_ILT_SESSION]: false,
  [EmailAutomationsTypes.UNENROLLMENT_IN_ILT_EVENT]: false,
  [EmailAutomationsTypes.UNENROLLMENT_IN_ILT_SESSION]: false,
  [EmailAutomationsTypes.ILT_EVENT_CANCELLED]: false,
  [EmailAutomationsTypes.ILT_EVENT_DELETED]: false,
  [EmailAutomationsTypes.ILT_SESSION_CANCELLED]: false,
  [EmailAutomationsTypes.UPCOMING_ILT_SESSION_REMINDER]: false,
  [EmailAutomationsTypes.ILT_SESSION_UPDATED]: false,
  [EmailAutomationsTypes.ILT_SESSION_DELETED]: false,
  [EmailAutomationsTypes.NOTIFICATION_SESSION_ATTENDANCE_MARKED]: false,
  [EmailAutomationsTypes.ILT_SESSION_COMPLETED]: false,
  [EmailAutomationsTypes.ILT_EVENT_COMPLETED]: false,
};

export const DEFAULT_REMINDER_TYPE = 132;

export const DEFAULT_TIME = '00:00';

export const DEFAULT_DAYS = 1;

export const DEFAULT_SCHEDULE = '0 0 0 * * ? *';

export const MAX_REMINDER_LIMIT = 5;

export const OPTIONS_SINGULAR = [
  { key: 'DAYS', content: 'day' },
  { key: 'WEEKS', content: 'week' },
];

export const OPTIONS_PLURAL = [
  { key: 'DAYS', content: 'days' },
  { key: 'WEEKS', content: 'weeks' },
];

export const RECURRENCE_TYPE_CONST = {
  DAYS: 'DAYS',
  WEEKS: 'WEEKS',
};

export const FULL_WEEK_DAYS_MAP: WeeksDaysMapType = {
  Sun: 'Sunday',
  Mon: 'Monday',
  Tue: 'Tuesday',
  Wed: 'Wednesday',
  Thu: 'Thursday',
  Fri: 'Friday',
  Sat: 'Saturday',
};
