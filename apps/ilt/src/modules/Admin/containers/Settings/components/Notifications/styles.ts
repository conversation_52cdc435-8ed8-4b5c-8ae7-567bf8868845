import styled from 'styled-components';

import Card, { Grid } from '@mindtickle/card';
import Icon from '@mindtickle/icon';
import { tokens, mixins } from '@mindtickle/styles/lib';
import { Link } from '@mindtickle/typography';

import { THEME_PREFIX_CLS } from '~/config/constants';
import { flexContainer } from '~/styles/mixins';

export const StyledGrid = styled(Grid)`
  .title-container {
    display: inline-flex;
    justify-content: flex-start;
    margin-bottom: 24px;

    .info-icon {
      margin-left: 8px;
      margin-top: 5px;
      color: ${tokens.iconTokens.COLOR_ICON_SECONDARY};
      font-size: 12px;
    }
  }

  .grid-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .due-date-radio-section {
    .${THEME_PREFIX_CLS}-radio {
      padding-right: 8px;
    }
    .${THEME_PREFIX_CLS}-radio-checked::after {
      border: 1px solid #0000;
    }
  }
`;

export const StyledCard = styled(Card)`
  && {
    padding: 0;
    margin: 24px;
  }

  .${THEME_PREFIX_CLS}-card-grid {
    width: 100%;
    box-shadow: none;
    border-bottom: 1px solid ${tokens.borderTokens.COLOR_BORDER_SECONDARY};

    &:last-child {
      border-bottom: none;
    }
  }

  .${THEME_PREFIX_CLS}-card-grid-hoverable:hover {
    box-shadow: none;
  }

  .small-text {
    ${mixins.smallBlackText()}
    display: inline-block;
    margin: 8px 8px 8px 0;
  }

  .lowercase {
    text-transform: lowercase;
  }

  .small-text-margin {
    margin: 24px 0 8px 0;
  }
  .margin-left {
    margin-left: 8px;
  }
  .margin-right {
    margin-right: 8px;
  }

  .block {
    display: block;
  }
`;

export const StyledLink = styled(Link)`
  margin-left: 8px;
  word-break: normal;
  font-size: 12px;
  line-height: 16px;
`;

export const StyledAddLink = styled(Link)<{ emptyList: boolean }>`
  margin-left: 16px;
  margin-top: ${({ emptyList }) => (!emptyList ? '16px' : 0)};
  max-width: 116px;
`;

export const StyledAddEnrolmentReminder = styled(Link)<{ emptyList: boolean }>`
  margin-bottom: 16px;
  margin-top: ${({ emptyList }) => (!emptyList ? '16px' : 0)};
  max-width: 116px;
`;

export const StyledAddIcon = styled(Icon)`
  margin-right: 8px;
  font-size: 8px;
  vertical-align: middle;
`;

export const StyledItemRow = styled.div`
  ${flexContainer({ alignItems: 'center' })};
  padding: 20px 0;
  border-bottom: 1px solid ${tokens.borderTokens.COLOR_BORDER_SECONDARY};
  &:last-child {
    border-bottom: none;
  }
`;

export const StyledReminderDataColumn = styled.div`
  ${flexContainer({ flexDirection: 'column' })};
  width: 80%;
`;

export const StyledReminderDataRow = styled.div<{ lastRow: boolean }>`
  ${flexContainer({ alignItems: 'flex-start' })};
  width: 100%;
  border-bottom: ${({ lastRow }) =>
    !lastRow ? `1px solid ${tokens.borderTokens.COLOR_BORDER_SECONDARY}` : 'none'};
  padding-bottom: ${({ lastRow }) => (!lastRow ? '24px' : 0)};
  margin-bottom: ${({ lastRow }) => (!lastRow ? '24px' : 0)};
`;
