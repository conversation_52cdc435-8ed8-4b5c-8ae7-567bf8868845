import { Fragment, useState } from 'react';

import { FormattedMessage } from 'react-intl';
import { useParams } from 'react-router-dom';

import Switch from '@mindtickle/switch';
import { Title } from '@mindtickle/typography';

import useEnrollmentPermissionsEnabled from '~/modules/Admin/hooks/useEnrollmentPermissionsEnabled';
import useMultidayEnabled from '~/modules/Admin/hooks/useMultidayEnabled';

import { STATIC } from '../../constants';
import messages from '../../messages';
import {
  StyledCard,
  StyledDivider,
  StyledToggleHeader,
  StyledRandomizationNoteSmall,
  StyledSectionWithTooltip,
} from '../../styles';

import type { TProps } from './typeDefs';

export const Enrolment = (props: TProps) => {
  const { updateSetting } = props;
  const { moduleId, seriesId } = useParams();
  const { isMultidayEnabled } = useMultidayEnabled();
  const { isEnrolmentPermissionEnabled } = useEnrollmentPermissionsEnabled();

  const enrolmentSettingsEnabledMsg = (
    <FormattedMessage {...messages.ENROLLMENT_SETTINGS_BUTTON_ENABLED} />
  );
  const enrolmentSettingsDisabledMsg = (
    <FormattedMessage {...messages.ENROLLMENT_SETTINGS_BUTTON_DISABLED} />
  );

  const { restrictLearnerEnroll, allowMultipleSession } = props.moduleData;

  const [restrictLearnerEnrollInSettings, setRestrictLearnerEnroll] =
    useState(restrictLearnerEnroll);
  const [allowMultipleSessionInSettings, setAllowMultipleSession] = useState(allowMultipleSession);

  function restrictLearnerEnrollChangeHandler(value: boolean) {
    setRestrictLearnerEnroll(value);
    updateSetting({ type: STATIC, moduleId, seriesId, data: { restrictLearnerEnroll: value } });
  }

  function allowMultipleSessionChangeHandler(value: boolean) {
    setAllowMultipleSession(value);
    updateSetting({ type: STATIC, moduleId, seriesId, data: { allowMultipleSession: value } });
  }

  return (
    <StyledCard>
      <Fragment>
        <StyledToggleHeader>
          <div>
            <StyledSectionWithTooltip toggleDisabled={!restrictLearnerEnrollInSettings}>
              <Title level={4}>Restrict learners from enrolling</Title>
            </StyledSectionWithTooltip>
            <StyledRandomizationNoteSmall toggleDisabled={!restrictLearnerEnrollInSettings}>
              <span>
                {`If enabled, learners won't be able to enroll or unenroll in sessions${
                  isMultidayEnabled ? ' or events' : ''
                }.`}
              </span>
            </StyledRandomizationNoteSmall>
          </div>
          <Switch
            checked={restrictLearnerEnrollInSettings}
            defaultChecked={restrictLearnerEnrollInSettings}
            onChange={(e: boolean) => restrictLearnerEnrollChangeHandler(e)}
          />
        </StyledToggleHeader>
      </Fragment>
      <Fragment>
        <StyledDivider />
        <StyledToggleHeader>
          <div>
            <StyledSectionWithTooltip toggleDisabled={!allowMultipleSessionInSettings}>
              <Title level={4}>Enable multiple enrollments for learners</Title>
            </StyledSectionWithTooltip>
            <StyledRandomizationNoteSmall toggleDisabled={!allowMultipleSessionInSettings}>
              <span>
                {isEnrolmentPermissionEnabled ? (
                  <div>
                    {enrolmentSettingsEnabledMsg}
                    <br />
                    {enrolmentSettingsDisabledMsg}
                  </div>
                ) : (
                  <div>{enrolmentSettingsEnabledMsg}</div>
                )}
              </span>
            </StyledRandomizationNoteSmall>
          </div>
          <Switch
            onChange={(e: boolean) => allowMultipleSessionChangeHandler(e)}
            checked={allowMultipleSessionInSettings}
            defaultChecked={allowMultipleSessionInSettings}
          />
        </StyledToggleHeader>
      </Fragment>
    </StyledCard>
  );
};

export default Enrolment;
