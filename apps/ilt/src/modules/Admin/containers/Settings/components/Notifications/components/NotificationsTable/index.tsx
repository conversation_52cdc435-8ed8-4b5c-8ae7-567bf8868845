import { Fragment, useMemo, useReducer, useCallback, useState, useEffect } from 'react';

import { useParams } from 'react-router-dom';
import { useUserAuth } from 'ui_shell/Auth';

import { ICON_MAP } from '@mindtickle/icon';

import useMultidayEnabled from '~/modules/Admin/hooks/useMultidayEnabled';
import useNewILTEmailSettingsEnabled from '~/modules/Admin/hooks/useNewILTEmailSettingsEnabled';

import { StyledReminderDateItem, StyledStatusItem, StyledTemplateItem } from '../../../../styles';
import { OPERATIONS } from '../../constants';
import {
  draftReminderReducer,
  getEmailReminderSettingsData,
  isValidAutomationRequest,
  getRequestBodyForCreateEmailAutomation,
  getRequestBodyForUpdateEmailAutomation,
  getRequestBodyForDeleteEmailAutomation,
} from '../../helpers';
import {
  StyledAddIcon,
  StyledItemRow,
  StyledAddLink,
  StyledReminderDataColumn,
  StyledReminderDataRow,
} from '../../styles';
import { EmailAutomationTransformed, ReminderActionType, ReminderTableProps } from '../../typeDefs';
import EmailTemplate from '../EmailTemplate';
import ReminderDetails from '../ReminderDetails';
import ReminderSaveBlock from '../ReminderSaveBlock';

const NotificationsTable = ({
  emailTemplateData,
  emailAutomationData,
  manipulateData,
  operationStatus,
}: ReminderTableProps) => {
  const [draftReminders, updateDraftReminders] = useReducer(draftReminderReducer, []);
  const [, setCurrentOperationAutomation] = useState<EmailAutomationTransformed>();

  const { isMultidayEnabled } = useMultidayEnabled();
  const { tempFeatureConfig } = useUserAuth();
  const isNewEmailExperienceEnabled = tempFeatureConfig?.isNewEmailExperienceEnabled || false;
  const { isNewILTEmailSettingsEnabled } = useNewILTEmailSettingsEnabled();
  const { moduleId } = useParams();
  const {
    company: { id: cname },
  } = useUserAuth();

  const emailReminderSettingsData = useMemo(
    () =>
      emailAutomationData && emailTemplateData
        ? getEmailReminderSettingsData(
            emailAutomationData,
            emailTemplateData,
            draftReminders,
            isMultidayEnabled,
            isNewILTEmailSettingsEnabled
          )
        : [],
    [
      draftReminders,
      emailAutomationData,
      emailTemplateData,
      isMultidayEnabled,
      isNewILTEmailSettingsEnabled,
    ]
  );

  const deleteDraftReminder = useCallback((automation: EmailAutomationTransformed) => {
    updateDraftReminders({
      type: ReminderActionType.DELETE_REMINDER,
      payload: { automation },
    });
  }, []);

  const onSaveClick = useCallback(
    (automation: EmailAutomationTransformed, successCallback?: () => void) => {
      if (!isValidAutomationRequest(automation)) {
        return;
      }
      setCurrentOperationAutomation(automation);
      if (automation.isDraft) {
        const requestBody = getRequestBodyForCreateEmailAutomation(automation, moduleId, cname);
        manipulateData(OPERATIONS.ADD, requestBody, successCallback);
      } else if (automation.isEdited) {
        const requestBody = getRequestBodyForUpdateEmailAutomation(automation, moduleId, cname);
        manipulateData(OPERATIONS.UPDATE, requestBody, successCallback);
      }
    },
    [cname, moduleId, manipulateData]
  );

  const onDeleteClick = useCallback(
    (automation: EmailAutomationTransformed) => {
      if (automation.isDraft) {
        deleteDraftReminder(automation);
        return;
      }
      setCurrentOperationAutomation(automation);
      const requestBody = getRequestBodyForDeleteEmailAutomation(automation);
      manipulateData(OPERATIONS.REMOVE, requestBody);
    },
    [manipulateData, deleteDraftReminder]
  );

  useEffect(() => {
    const { loaded = false, hasError = false } = operationStatus || {};
    if (loaded && !hasError) {
      setCurrentOperationAutomation(automation => {
        automation && deleteDraftReminder(automation);
        return undefined;
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [operationStatus?.loaded]);

  return (
    <Fragment>
      {emailReminderSettingsData.map(settings => (
        <StyledItemRow key={settings.key}>
          <StyledStatusItem>{settings.status}</StyledStatusItem>
          <StyledReminderDataColumn>
            {settings.reminderData.map((reminder, index) => (
              <StyledReminderDataRow
                key={reminder.id}
                lastRow={settings.reminderData.length - 1 === index}
              >
                <StyledReminderDateItem>
                  {settings.reminderDateText || (
                    <ReminderDetails
                      key={reminder.id}
                      hasSpecificDate={reminder.hasSpecificDate}
                      reminderOptions={reminder.reminderOptions}
                      selectedDate={reminder.selectedDate}
                      selectedEmailReminderType={reminder.selectedEmailReminderType}
                      daysValue={reminder.daysValue}
                      selectedTime={reminder.selectedTime}
                      updateDraftReminders={updateDraftReminders}
                      onDeleteClick={onDeleteClick}
                      automation={reminder}
                      onSaveClick={onSaveClick}
                      onDeleteDraftReminder={deleteDraftReminder}
                    />
                  )}
                  {settings.reminderDateText && reminder.isEdited && (
                    <ReminderSaveBlock
                      key={reminder.id}
                      automation={reminder}
                      onDeleteDraftReminder={deleteDraftReminder}
                      onSaveClick={onSaveClick}
                    />
                  )}
                </StyledReminderDateItem>

                <StyledTemplateItem>
                  <EmailTemplate
                    key={`email_template_${reminder.id}`}
                    automation={reminder}
                    value={reminder.selectedTemplate}
                    templateData={settings.emailTemplates}
                    updateDraftReminders={updateDraftReminders}
                    isNewEmailExperienceEnabled={isNewEmailExperienceEnabled}
                    automationTask={settings?.key}
                    status={settings?.status}
                    reminderDateText={settings?.reminderDateText}
                  />
                </StyledTemplateItem>
              </StyledReminderDataRow>
            ))}
            {settings.allowAddReminder && (
              <StyledAddLink
                emptyList={!settings.reminderData.length}
                onClick={() =>
                  updateDraftReminders({
                    type: ReminderActionType.CREATE_REMINDER,
                    payload: {
                      automationType: settings.key,
                      emailTemplatesData: settings.emailTemplates,
                    },
                  })
                }
              >
                <StyledAddIcon type={ICON_MAP.ADD} />
                Add reminder
              </StyledAddLink>
            )}
          </StyledReminderDataColumn>
        </StyledItemRow>
      ))}
    </Fragment>
  );
};

export default NotificationsTable;
