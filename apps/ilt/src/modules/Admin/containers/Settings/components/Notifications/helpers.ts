import moment, { Moment } from 'moment/moment';

import { getTimestamp, isValidTimestamp, getTimeString } from '~/utils/dateTime';

import { RECURRENCE_ENDS_CONST } from './components/RepeatModal/constants';
import {
  ALLOW_EMAIL_REMINDER,
  EMAILAUTOMATION_DISPLAY_ORDER,
  NO_CONDITION_NO_SCHEDULE_TASKS,
  DEFAULT_DAYS,
  DEFAULT_REMINDER_TYPE,
  DEFAULT_SCHEDULE,
  DEFAULT_TIME,
  DEFAULT_TEMPLATE_SELECTION,
  REMINDER_CONFIG,
  REMINDER_KEYS,
  OPTIONS_SINGULAR,
  OPTIONS_PLURAL,
  FULL_WEEK_DAYS_MAP,
  RECURRENCE_TYPE_CONST,
  NEW_EMAIL_SETTINGS_EMAILAUTOMATION_DISPLAY_ORDER,
} from './constants';
import { EmailAutomationsTypes, ReminderActionType } from './typeDefs';

import type {
  EmailAutomationData,
  EmailAutomationTransformed,
  EmailAutomation,
  EmailReminderSettingsData,
  EmailTemplateListType,
  EmailTemplatesType,
  ReminderAction,
  TDeleteEmailAutomationRequest,
  UpdateEmailAutomationRequest,
  CreateEmailAutomationRequest,
  WeeksDaysStatusType,
} from './typeDefs';

export const getEmailReminderSettingsData = (
  emailAutomationData: EmailAutomationData,
  emailTemplatesData: ReadonlyArray<EmailTemplatesType>,
  draftReminders: ReadonlyArray<EmailAutomationTransformed>,
  isMultidayEnabled: boolean,
  isNewILTEmailSettingsEnabled: boolean
): EmailReminderSettingsData => {
  let displayOrder = EMAILAUTOMATION_DISPLAY_ORDER;
  if (isNewILTEmailSettingsEnabled) {
    displayOrder = NEW_EMAIL_SETTINGS_EMAILAUTOMATION_DISPLAY_ORDER;
  }
  const emailReminderSettingsData =
    emailAutomationData &&
    Object.keys(emailAutomationData)
      .sort(
        (a, b) =>
          (displayOrder[a as keyof typeof displayOrder] || 0) -
          (displayOrder[b as keyof typeof displayOrder] || 0)
      )
      .reduce<EmailReminderSettingsData>((acc, automationKey) => {
        const automationType = getAutomationTypeFromKey(automationKey);
        const automationData = emailAutomationData[automationType];
        if (
          isNewILTEmailSettingsEnabled ||
          automationType === EmailAutomationsTypes.INVITATION ||
          automationType === EmailAutomationsTypes.NOTIFICATION_SESSION_ATTENDANCE_MARKED ||
          automationType === EmailAutomationsTypes.NOTIFICATION_NOT_ENROLLED_IN_ANY_SESSION
        ) {
          acc.push({
            key: automationType,
            status: getReminderStatus(automationType, isMultidayEnabled),
            allowAddReminder: ALLOW_EMAIL_REMINDER[automationType],
            reminderDateText: getReminderText(automationType, isMultidayEnabled),
            emailTemplates: getEmailTemplatesList(emailTemplatesData, automationType),
            reminderData: getTransformedAutomationData(automationType, automationData),
          });
        }
        return acc;
      }, []);

  return mergeDraftReminders(draftReminders, emailReminderSettingsData);
};

export const mergeDraftReminders = (
  draftReminders: ReadonlyArray<EmailAutomationTransformed>,
  emailReminderSettingsData: EmailReminderSettingsData
): EmailReminderSettingsData => {
  const transformedReminderSettings = [...emailReminderSettingsData];
  draftReminders.forEach(reminder => {
    const reminderSettingIndex = transformedReminderSettings.findIndex(
      setting => setting.key === reminder.task
    );
    if (reminderSettingIndex !== -1) {
      const reminderSettings = transformedReminderSettings[reminderSettingIndex];
      const reminderDataIndex = reminderSettings.reminderData.findIndex(
        data => data.id === reminder.id
      );
      if (reminderDataIndex !== -1) {
        transformedReminderSettings[reminderSettingIndex].reminderData[reminderDataIndex] =
          reminder;
      } else {
        transformedReminderSettings[reminderSettingIndex].reminderData.push(reminder);
      }
    }
  });
  return transformedReminderSettings;
};

/* eslint-disable max-statements */
export const getReminderText = (
  automationType: EmailAutomationsTypes,
  isMultidayEnabled: boolean
): string | undefined => {
  if (automationType === EmailAutomationsTypes.INVITATION) {
    return 'On invitation';
  }
  if (automationType === EmailAutomationsTypes.NOTIFICATION_SESSION_ATTENDANCE_MARKED) {
    return `When admin updates session attendance`;
  }
  if (automationType === EmailAutomationsTypes.ENROLLED_IN_EVENT) {
    return 'When learner is enrolled to an event';
  }
  if (automationType === EmailAutomationsTypes.ENROLLED_IN_SESSION_AT_EVENT_LEVEL) {
    return 'When a learner is enrolled to an event and auto-enrolled to a session';
  }
  if (automationType === EmailAutomationsTypes.ENROLLED_IN_SESSION_AT_SESSION_LEVEL) {
    return 'When a learner is enrolled to a session';
  }
  if (automationType === EmailAutomationsTypes.ILT_SESSION_SELF_ENROLMENT) {
    return 'When a learner self enrolls to a session';
  }
  if (automationType === EmailAutomationsTypes.LEARNER_ADDED_TO_WAITLIST_ILT_EVENT) {
    return 'When a learner is added to waitlist on an event';
  }
  if (automationType === EmailAutomationsTypes.LEARNER_ADDED_TO_WAITLIST_ILT_SESSION) {
    return 'When a learner is added to waitlist on a session';
  }
  if (automationType === EmailAutomationsTypes.UNENROLLMENT_IN_ILT_EVENT) {
    return 'When learner is unenrolled from an event';
  }
  if (automationType === EmailAutomationsTypes.UNENROLLMENT_IN_ILT_SESSION) {
    return 'When a learner is unenrolled from a session';
  }
  if (automationType === EmailAutomationsTypes.ILT_EVENT_CANCELLED) {
    return 'When an event is cancelled';
  }
  if (automationType === EmailAutomationsTypes.ILT_EVENT_DELETED) {
    return 'When an event is deleted';
  }
  if (automationType === EmailAutomationsTypes.ILT_SESSION_CANCELLED) {
    return 'When a session is cancelled';
  }
  if (automationType === EmailAutomationsTypes.UPCOMING_ILT_SESSION_REMINDER) {
    return 'Will be sent according to the reminder frequency set for each session';
  }
  if (automationType === EmailAutomationsTypes.ILT_SESSION_UPDATED) {
    return 'When a session is updated with time and location';
  }
  if (automationType === EmailAutomationsTypes.ILT_SESSION_DELETED) {
    return 'When a session is deleted';
  }
  if (automationType === EmailAutomationsTypes.ILT_SESSION_COMPLETED) {
    return 'When a learner watches a session recording, and the completion status is marked as completed.';
  }
  if (automationType === EmailAutomationsTypes.ILT_EVENT_COMPLETED) {
    return 'When the event is marked as complete for a learner';
  }
  return;
};
/* eslint-disable max-statements */

export const getTransformedAutomationData = (
  automationType: EmailAutomationsTypes,
  automationData: ReadonlyArray<EmailAutomation>
): Array<EmailAutomationTransformed> =>
  automationData.map(automation => {
    const hasSpecificDate = getHasSpecificDate(automation);
    const selectedEmailReminderType = getSelectedEmailReminderType(automationType, automation);
    return {
      ...automation,
      isDraft: false,
      isEdited: false,
      selectedTemplate: getSelectedEmailTemplate(automationType, automation.templateId),
      selectedEmailReminderType,
      hasSpecificDate,
      daysValue: getDaysValue(automation),
      selectedDate: getSelectedDate(automation),
      selectedTime: getSelectedTime(automation),
      reminderOptions: REMINDER_CONFIG[automationType],
    };
  });

export const getHasSpecificDateFromReminderType = (type: string): boolean =>
  type ===
  REMINDER_KEYS[
    EmailAutomationsTypes.NOTIFICATION_NOT_ENROLLED_IN_ANY_SESSION as keyof typeof REMINDER_KEYS
  ].SPECIFIC_DATE;

export const getHasSpecificDate = (automation: EmailAutomation): boolean =>
  !isNaN(Number(automation.schedule));

export const getDaysValue = (automation: EmailAutomation): number | undefined => {
  if (!getHasSpecificDate(automation)) {
    return parseInt(automation.condition.split('==')[1]);
  }
};

export const getSelectedDate = (automation: EmailAutomation): Moment | undefined => {
  if (getHasSpecificDate(automation) && isValidTimestamp(automation.schedule)) {
    const scheduleTimestamp = getTimestamp(automation.schedule);
    return moment(scheduleTimestamp).set({ hour: 0, minute: 0, second: 0, millisecond: 0 });
  }
};

export const getSelectedTime = (automation: EmailAutomation): string | undefined => {
  if (getHasSpecificDate(automation) && isValidTimestamp(automation.schedule)) {
    const scheduleTimestamp = getTimestamp(automation.schedule);
    const date = moment(scheduleTimestamp);
    return `${getTimeString(date.hour())}:${getTimeString(date.minute())}`;
  }
};

export const getSelectedEmailReminderType = (
  automationType: EmailAutomationsTypes,
  automation: EmailAutomation
): string | undefined => {
  if (automationType === EmailAutomationsTypes.NOTIFICATION_NOT_ENROLLED_IN_ANY_SESSION) {
    if (getHasSpecificDate(automation)) {
      return REMINDER_KEYS[EmailAutomationsTypes.NOTIFICATION_NOT_ENROLLED_IN_ANY_SESSION]
        .SPECIFIC_DATE;
    } else {
      return REMINDER_KEYS[EmailAutomationsTypes.NOTIFICATION_NOT_ENROLLED_IN_ANY_SESSION]
        .INVITED_BUT_NOT_ENROLLED;
    }
  }
};

export const getSelectedEmailTemplate = (
  automationType: EmailAutomationsTypes,
  templateId: string
): string | undefined => {
  if (templateId != null) return templateId;
  if (
    automationType === EmailAutomationsTypes.INVITATION ||
    automationType === EmailAutomationsTypes.NOTIFICATION_SESSION_ATTENDANCE_MARKED ||
    automationType === EmailAutomationsTypes.ENROLLED_IN_EVENT ||
    automationType === EmailAutomationsTypes.ENROLLED_IN_SESSION_AT_EVENT_LEVEL ||
    automationType === EmailAutomationsTypes.ENROLLED_IN_SESSION_AT_SESSION_LEVEL ||
    automationType === EmailAutomationsTypes.ILT_SESSION_SELF_ENROLMENT ||
    automationType === EmailAutomationsTypes.LEARNER_ADDED_TO_WAITLIST_ILT_EVENT ||
    automationType === EmailAutomationsTypes.LEARNER_ADDED_TO_WAITLIST_ILT_SESSION ||
    automationType === EmailAutomationsTypes.UNENROLLMENT_IN_ILT_EVENT ||
    automationType === EmailAutomationsTypes.UNENROLLMENT_IN_ILT_SESSION ||
    automationType === EmailAutomationsTypes.ILT_EVENT_CANCELLED ||
    automationType === EmailAutomationsTypes.ILT_EVENT_DELETED ||
    automationType === EmailAutomationsTypes.ILT_SESSION_CANCELLED ||
    automationType === EmailAutomationsTypes.UPCOMING_ILT_SESSION_REMINDER ||
    automationType === EmailAutomationsTypes.ILT_SESSION_UPDATED ||
    automationType === EmailAutomationsTypes.ILT_SESSION_DELETED ||
    automationType === EmailAutomationsTypes.ILT_SESSION_COMPLETED ||
    automationType === EmailAutomationsTypes.ILT_EVENT_COMPLETED
  ) {
    return '0';
  }
  return;
};

export const getEmailTemplatesList = (
  emailTemplatesData: ReadonlyArray<EmailTemplatesType>,
  automationType: EmailAutomationsTypes
): ReadonlyArray<EmailTemplateListType> => {
  const finalList: Array<EmailTemplateListType> = [];
  if (
    automationType === EmailAutomationsTypes.INVITATION ||
    automationType === EmailAutomationsTypes.NOTIFICATION_SESSION_ATTENDANCE_MARKED ||
    automationType === EmailAutomationsTypes.ENROLLED_IN_EVENT ||
    automationType === EmailAutomationsTypes.ENROLLED_IN_SESSION_AT_EVENT_LEVEL ||
    automationType === EmailAutomationsTypes.ENROLLED_IN_SESSION_AT_SESSION_LEVEL ||
    automationType === EmailAutomationsTypes.ILT_SESSION_SELF_ENROLMENT ||
    automationType === EmailAutomationsTypes.LEARNER_ADDED_TO_WAITLIST_ILT_EVENT ||
    automationType === EmailAutomationsTypes.LEARNER_ADDED_TO_WAITLIST_ILT_SESSION ||
    automationType === EmailAutomationsTypes.UNENROLLMENT_IN_ILT_EVENT ||
    automationType === EmailAutomationsTypes.UNENROLLMENT_IN_ILT_SESSION ||
    automationType === EmailAutomationsTypes.ILT_EVENT_CANCELLED ||
    automationType === EmailAutomationsTypes.ILT_EVENT_DELETED ||
    automationType === EmailAutomationsTypes.ILT_SESSION_CANCELLED ||
    automationType === EmailAutomationsTypes.UPCOMING_ILT_SESSION_REMINDER ||
    automationType === EmailAutomationsTypes.ILT_SESSION_UPDATED ||
    automationType === EmailAutomationsTypes.ILT_SESSION_DELETED ||
    automationType === EmailAutomationsTypes.ILT_SESSION_COMPLETED ||
    automationType === EmailAutomationsTypes.ILT_EVENT_COMPLETED
  ) {
    finalList.push({ key: '0', content: 'None' });
  }

  emailTemplatesData.forEach(template => {
    if (!finalList.find(item => item.key === template.id)) {
      finalList.push({
        key: template.id,
        content: template.name,
      });
    }
  });
  return finalList;
};

/* eslint-disable complexity */
export const getReminderStatus = (
  automationType: EmailAutomationsTypes,
  isMultidayEnabled: boolean
): string => {
  switch (automationType) {
    case EmailAutomationsTypes.INVITATION:
      return 'Invited';
    case EmailAutomationsTypes.NOTIFICATION_NOT_ENROLLED_IN_ANY_SESSION:
      return `Not enrolled in any session${isMultidayEnabled ? ' or event' : ''}`;
    case EmailAutomationsTypes.NOTIFICATION_SESSION_ATTENDANCE_MARKED:
      return 'Enrolled';
    case EmailAutomationsTypes.ENROLLED_IN_EVENT:
      return 'Enrolled';
    case EmailAutomationsTypes.ENROLLED_IN_SESSION_AT_EVENT_LEVEL:
      return 'Enrolled';
    case EmailAutomationsTypes.ENROLLED_IN_SESSION_AT_SESSION_LEVEL:
      return 'Enrolled';
    case EmailAutomationsTypes.ILT_SESSION_SELF_ENROLMENT:
      return 'Enrolled';
    case EmailAutomationsTypes.LEARNER_ADDED_TO_WAITLIST_ILT_EVENT:
      return 'Waitlisted';
    case EmailAutomationsTypes.LEARNER_ADDED_TO_WAITLIST_ILT_SESSION:
      return 'Waitlisted';
    case EmailAutomationsTypes.UNENROLLMENT_IN_ILT_EVENT:
      return 'Unenrolled';
    case EmailAutomationsTypes.UNENROLLMENT_IN_ILT_SESSION:
      return 'Unenrolled';
    case EmailAutomationsTypes.ILT_EVENT_CANCELLED:
      return 'Enrolled';
    case EmailAutomationsTypes.ILT_EVENT_DELETED:
      return 'Enrolled';
    case EmailAutomationsTypes.ILT_SESSION_CANCELLED:
      return 'Enrolled';
    case EmailAutomationsTypes.UPCOMING_ILT_SESSION_REMINDER:
      return 'Enrolled';
    case EmailAutomationsTypes.ILT_SESSION_UPDATED:
      return 'Enrolled';
    case EmailAutomationsTypes.ILT_SESSION_DELETED:
      return 'Enrolled';
    case EmailAutomationsTypes.ILT_SESSION_COMPLETED:
      return 'Enrolled';
    case EmailAutomationsTypes.ILT_EVENT_COMPLETED:
      return 'Enrolled';
  }
};
/* eslint-disable complexity */

export const getDefaultTemplateForAutomationType = (
  automationType: EmailAutomationsTypes,
  emailTemplatesData: ReadonlyArray<EmailTemplateListType>
): string => {
  if (automationType === EmailAutomationsTypes.NOTIFICATION_NOT_ENROLLED_IN_ANY_SESSION) {
    return (
      DEFAULT_TEMPLATE_SELECTION[EmailAutomationsTypes.NOTIFICATION_NOT_ENROLLED_IN_ANY_SESSION] ||
      emailTemplatesData?.[0]?.key ||
      'None'
    );
  }
  return 'None';
};

export const getDefaultSelectedEmailReminderType = (
  automationType: EmailAutomationsTypes
): string => REMINDER_CONFIG[automationType][0]?.key || '';

export const getReminderTextForDays = (
  reminderType: string,
  isEnrolmentThresholdReminder?: boolean
): string => {
  switch (reminderType) {
    case REMINDER_KEYS[EmailAutomationsTypes.NOTIFICATION_NOT_ENROLLED_IN_ANY_SESSION]
      .INVITED_BUT_NOT_ENROLLED:
      return isEnrolmentThresholdReminder ? ' day(s) before session' : ' day(s) after invitation';
    default:
      return '';
  }
};

export const getDefaultDate = (): Moment =>
  moment().set({ hour: 0, minute: 0, second: 0, millisecond: 0 }).add(3, 'day');

export const getAutomationTypeFromKey = (type: string): EmailAutomationsTypes => {
  switch (type) {
    case EmailAutomationsTypes.INVITATION:
      return EmailAutomationsTypes.INVITATION;
    case EmailAutomationsTypes.NOTIFICATION_NOT_ENROLLED_IN_ANY_SESSION:
      return EmailAutomationsTypes.NOTIFICATION_NOT_ENROLLED_IN_ANY_SESSION;
    case EmailAutomationsTypes.NOTIFICATION_SESSION_ATTENDANCE_MARKED:
      return EmailAutomationsTypes.NOTIFICATION_SESSION_ATTENDANCE_MARKED;
    case EmailAutomationsTypes.ENROLLED_IN_SESSION_AT_SESSION_LEVEL:
      return EmailAutomationsTypes.ENROLLED_IN_SESSION_AT_SESSION_LEVEL;
    case EmailAutomationsTypes.ENROLLED_IN_SESSION_AT_EVENT_LEVEL:
      return EmailAutomationsTypes.ENROLLED_IN_SESSION_AT_EVENT_LEVEL;
    case EmailAutomationsTypes.ILT_SESSION_SELF_ENROLMENT:
      return EmailAutomationsTypes.ILT_SESSION_SELF_ENROLMENT;
    case EmailAutomationsTypes.LEARNER_ADDED_TO_WAITLIST_ILT_SESSION:
      return EmailAutomationsTypes.LEARNER_ADDED_TO_WAITLIST_ILT_SESSION;
    case EmailAutomationsTypes.UNENROLLMENT_IN_ILT_SESSION:
      return EmailAutomationsTypes.UNENROLLMENT_IN_ILT_SESSION;
    case EmailAutomationsTypes.ILT_SESSION_CANCELLED:
      return EmailAutomationsTypes.ILT_SESSION_CANCELLED;
    case EmailAutomationsTypes.ILT_SESSION_DELETED:
      return EmailAutomationsTypes.ILT_SESSION_DELETED;
    case EmailAutomationsTypes.UPCOMING_ILT_SESSION_REMINDER:
      return EmailAutomationsTypes.UPCOMING_ILT_SESSION_REMINDER;
    case EmailAutomationsTypes.ILT_SESSION_UPDATED:
      return EmailAutomationsTypes.ILT_SESSION_UPDATED;
    case EmailAutomationsTypes.ILT_SESSION_COMPLETED:
      return EmailAutomationsTypes.ILT_SESSION_COMPLETED;
    case EmailAutomationsTypes.ENROLLED_IN_EVENT:
      return EmailAutomationsTypes.ENROLLED_IN_EVENT;
    case EmailAutomationsTypes.LEARNER_ADDED_TO_WAITLIST_ILT_EVENT:
      return EmailAutomationsTypes.LEARNER_ADDED_TO_WAITLIST_ILT_EVENT;
    case EmailAutomationsTypes.UNENROLLMENT_IN_ILT_EVENT:
      return EmailAutomationsTypes.UNENROLLMENT_IN_ILT_EVENT;
    case EmailAutomationsTypes.ILT_EVENT_CANCELLED:
      return EmailAutomationsTypes.ILT_EVENT_CANCELLED;
    case EmailAutomationsTypes.ILT_EVENT_DELETED:
      return EmailAutomationsTypes.ILT_EVENT_DELETED;
    case EmailAutomationsTypes.ILT_EVENT_COMPLETED:
      return EmailAutomationsTypes.ILT_EVENT_COMPLETED;
    default:
      return EmailAutomationsTypes.INVITATION;
  }
};

// eslint-disable-next-line max-statements
export const draftReminderReducer = (
  state: ReadonlyArray<EmailAutomationTransformed>,
  action: ReminderAction
): ReadonlyArray<EmailAutomationTransformed> => {
  const { type, payload } = action;
  switch (type) {
    case ReminderActionType.CREATE_REMINDER: {
      const { automationType, emailTemplatesData } = payload;
      return [...state, createDefaultReminder(automationType, emailTemplatesData)];
    }
    case ReminderActionType.UPDATE_REMINDER_TEMPLATE: {
      const { automation, selectedTemplate } = payload;
      const reminder = state.find(rem => rem.id === automation.id);
      if (reminder) {
        const transformedReminder = {
          ...reminder,
          isEdited: true,
          selectedTemplate,
        };
        return [...state.filter(rem => rem.id !== automation.id), transformedReminder];
      } else {
        const transformedReminder = {
          ...automation,
          isEdited: true,
          selectedTemplate,
        };
        return [...state, transformedReminder];
      }
    }
    case ReminderActionType.UPDATE_REMINDER_TYPE: {
      const { automation, selectedEmailReminderType } = payload;
      const reminder = state.find(rem => rem.id === automation.id);
      if (reminder) {
        const transformedReminder = {
          ...reminder,
          hasSpecificDate: getHasSpecificDateFromReminderType(selectedEmailReminderType),
          isEdited: true,
          selectedEmailReminderType,
          daysValue: DEFAULT_DAYS,
          selectedDate: getDefaultDate(),
          selectedTime: DEFAULT_TIME,
        };
        return [...state.filter(rem => rem.id !== automation.id), transformedReminder];
      } else {
        const transformedReminder = {
          ...automation,
          hasSpecificDate: getHasSpecificDateFromReminderType(selectedEmailReminderType),
          isEdited: true,
          selectedEmailReminderType,
          daysValue: DEFAULT_DAYS,
          selectedDate: getDefaultDate(),
          selectedTime: DEFAULT_TIME,
        };
        return [...state, transformedReminder];
      }
    }
    case ReminderActionType.UPDATE_REMINDER_DAY_VALUE: {
      const { automation, daysValue } = payload;
      const reminder = state.find(rem => rem.id === automation.id);
      if (reminder) {
        const transformedReminder = {
          ...reminder,
          isEdited: true,
          daysValue,
        };
        return [...state.filter(rem => rem.id !== automation.id), transformedReminder];
      } else {
        const transformedReminder = {
          ...automation,
          isEdited: true,
          daysValue,
        };
        return [...state, transformedReminder];
      }
    }
    case ReminderActionType.UPDATE_REMINDER_DATE: {
      const { automation, date } = payload;
      const reminder = state.find(rem => rem.id === automation.id);
      const selectedDate = date && date.set({ hour: 0, minute: 0, second: 0, millisecond: 0 });
      if (reminder) {
        const transformedReminder = {
          ...reminder,
          isEdited: true,
          selectedDate,
        };
        return [...state.filter(rem => rem.id !== automation.id), transformedReminder];
      } else {
        const transformedReminder = {
          ...automation,
          isEdited: true,
          selectedDate,
        };
        return [...state, transformedReminder];
      }
    }
    case ReminderActionType.UPDATE_REMINDER_TIME: {
      const { automation, time } = payload;
      const reminder = state.find(rem => rem.id === automation.id);
      if (reminder) {
        const transformedReminder = {
          ...reminder,
          isEdited: true,
          selectedTime: time,
        };
        return [...state.filter(rem => rem.id !== automation.id), transformedReminder];
      } else {
        const transformedReminder = {
          ...automation,
          isEdited: true,
          selectedTime: time,
        };
        return [...state, transformedReminder];
      }
    }
    case ReminderActionType.UPDATE_RECURRENCE_SETTINGS: {
      const { automation, recurrenceSettings } = payload;
      const reminder = state.find(rem => rem.id === automation.id);
      if (reminder) {
        const transformedReminder = {
          ...reminder,
          isEdited: true,
          recurrenceSettings: recurrenceSettings,
        };
        return [...state.filter(rem => rem.id !== automation.id), transformedReminder];
      } else {
        const transformedReminder = {
          ...automation,
          isEdited: true,
          recurrenceSettings: recurrenceSettings,
        };
        return [...state, transformedReminder];
      }
    }
    case ReminderActionType.DELETE_REMINDER: {
      const { automation } = payload;
      return state.filter(rem => rem.id !== automation.id);
    }
    default:
      return state;
  }
};

export const createDefaultReminder = (
  automationType: EmailAutomationsTypes,
  emailTemplatesData: ReadonlyArray<EmailTemplateListType>
): EmailAutomationTransformed => ({
  isEdited: true,
  isDraft: true,
  task: automationType,
  company: '',
  condition: '',
  hasSpecificDate: false,
  id: Date.now().toString(),
  mailJobId: '',
  reminderOptions: REMINDER_CONFIG[automationType],
  schedule: '',
  templateId: '',
  type: DEFAULT_REMINDER_TYPE,
  selectedTemplate: getDefaultTemplateForAutomationType(automationType, emailTemplatesData),
  selectedEmailReminderType: getDefaultSelectedEmailReminderType(automationType),
  daysValue: DEFAULT_DAYS,
  selectedDate: getDefaultDate(),
  selectedTime: DEFAULT_TIME,
  entity: '',
});

export const isValidAutomationRequest = (automation: EmailAutomationTransformed): boolean => {
  if (!automation.selectedTemplate) {
    //TODO: Select valid email template
    return false;
  }
  if (automation.task === EmailAutomationsTypes.NOTIFICATION_NOT_ENROLLED_IN_ANY_SESSION) {
    if (automation.selectedTemplate === 'None') {
      //TODO: Select valid email template
      return false;
    }

    if (!automation.selectedEmailReminderType) {
      //TODO: Select valid email reminder Type
      return false;
    }
    //TODO: Add validations if automation.hasSpecificDate and dates invalid or !automation.hasSpecificDate and dates less than equal to 0
    if (automation.hasSpecificDate && !automation.selectedDate) {
      return false;
    }
  }

  return true;
};

export const getEmailCondition = (
  automation: EmailAutomationTransformed,
  moduleId: string,
  cname: string,
  task: string
): string => {
  let emailReminderType = automation.selectedEmailReminderType;
  if (
    automation.selectedEmailReminderType === 'specific_date' &&
    // @ts-ignore
    REMINDER_KEYS[task] &&
    // @ts-ignore
    REMINDER_KEYS[task]['SPECIFIC_DATE_NOTIFY']
  ) {
    // @ts-ignore
    emailReminderType = REMINDER_KEYS[task]['SPECIFIC_DATE_NOTIFY'];
  }
  let condition = `${emailReminderType}("${cname}","${moduleId}")`;
  if (!automation.hasSpecificDate) {
    condition = `${condition}==${automation.daysValue}d`;
  }
  return condition;
};

export const getSchedule = (automation: EmailAutomationTransformed): string => {
  if (automation.hasSpecificDate && automation.selectedDate && automation.selectedTime != null) {
    let totalSeconds = automation.selectedDate
      .set({ hour: 0, minute: 0, second: 0, millisecond: 0 })
      // .utcOffset('+05:30') // IST offset
      .valueOf();
    totalSeconds = Math.round(totalSeconds / 1000);
    const selectedTime = getMinutesFromSelectedTime(automation.selectedTime);
    totalSeconds += Number(selectedTime) * 60;
    return `${totalSeconds}`;
  }
  return DEFAULT_SCHEDULE;
};

export const getMinutesFromSelectedTime = (selectedTime: string): string => {
  if (!selectedTime) {
    return `0`;
  }
  const selectedTimeArray = selectedTime.split(':');
  const hour = Number(selectedTimeArray[0]);
  const minute = Number(selectedTimeArray[1]);
  return `${hour * 60 + minute}`;
};

export const getRequestBodyForCreateEmailAutomation = (
  automation: EmailAutomationTransformed,
  moduleId: string,
  cname: string
): CreateEmailAutomationRequest => ({
  condition: getEmailCondition(automation, moduleId, cname, automation.task),
  task: automation.task,
  templateId: automation.selectedTemplate || '',
  schedule: getSchedule(automation),
  recurrenceSettings: automation.recurrenceSettings,
});

const checkNoConditionNoScheduleTask = (task: string) =>
  NO_CONDITION_NO_SCHEDULE_TASKS.includes(task as (typeof NO_CONDITION_NO_SCHEDULE_TASKS)[number]);

const getEmailConditionForUpdate = (
  automation: EmailAutomationTransformed,
  moduleId: string,
  cname: string,
  task: string
) =>
  checkNoConditionNoScheduleTask(task)
    ? null
    : getEmailCondition(automation, moduleId, cname, automation.task);

const getScheduleForUpdate = (automation: EmailAutomationTransformed, task: string) =>
  checkNoConditionNoScheduleTask(task) ? null : getSchedule(automation);

export const getRequestBodyForUpdateEmailAutomation = (
  automation: EmailAutomationTransformed,
  moduleId: string,
  cname: string
): UpdateEmailAutomationRequest => ({
  condition: getEmailConditionForUpdate(automation, moduleId, cname, automation.task),
  task: automation.task,
  templateId: automation.selectedTemplate || '',
  id: automation.id,
  schedule: getScheduleForUpdate(automation, automation.task),
  recurrenceSettings: automation.recurrenceSettings,
});

export const getRequestBodyForDeleteEmailAutomation = (
  automation: EmailAutomationTransformed
): TDeleteEmailAutomationRequest => ({
  task: automation.task,
  id: automation.id,
  mailJobId: automation.mailJobId,
});

export const getEmailTemplatesUrl = (template: string): string =>
  `/account_settings?tab=managetemplates&template=${template}`;

export const getRecurrenceSummary = (
  recurrenceFrequency?: number | null,
  recurrenceType?: string | null,
  recurrenceDaysStatus?: WeeksDaysStatusType
): any => {
  if (!recurrenceFrequency || !recurrenceType || !recurrenceDaysStatus) {
    return 'Error';
  }

  try {
    const selectedRecurrenceFrequency = parseInt(recurrenceFrequency.toString(), 10);
    let formattedSummary = 'every ';
    if (selectedRecurrenceFrequency === 1) {
      formattedSummary =
        formattedSummary + OPTIONS_SINGULAR.find(option => option.key === recurrenceType)?.content;
    } else {
      formattedSummary =
        formattedSummary +
        `${selectedRecurrenceFrequency} ` +
        OPTIONS_PLURAL.find(option => option.key === recurrenceType)?.content;
    }

    if (recurrenceType === RECURRENCE_TYPE_CONST.WEEKS) {
      formattedSummary += ' on ';
      Object.keys(recurrenceDaysStatus).forEach(eachDay => {
        if (recurrenceDaysStatus[eachDay]) {
          formattedSummary += FULL_WEEK_DAYS_MAP[eachDay] + ', ';
        }
      });
      return formattedSummary.slice(0, -2);
    } else {
      return formattedSummary;
    }
  } catch {
    // eslint-disable-next-line no-console
    console.log('Error in formatting Recurrence Frequency');
    return 'Error';
  }
};

export const getRecurrenceEndConditionInWords = (
  recurrenceEndType: string,
  recurrenceCount: number
) => {
  if (recurrenceEndType === RECURRENCE_ENDS_CONST.STATE_CHANGE) {
    return `When learners enroll in the session/event`;
  }
  return `After ${recurrenceCount} reminders`;
};
