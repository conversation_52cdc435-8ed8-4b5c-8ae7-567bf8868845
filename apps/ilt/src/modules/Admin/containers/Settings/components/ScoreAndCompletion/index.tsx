import { useCallback, useMemo, useState } from 'react';

import { FormattedMessage } from 'react-intl';
import { useParams } from 'react-router-dom';
import { useUserAuth } from 'ui_shell/Auth';

import { Grid } from '@mindtickle/card';
import InputNumber from '@mindtickle/input-number';
import Radio from '@mindtickle/radio';
import Switch from '@mindtickle/switch';
import Toggle from '@mindtickle/switch';
import { Title, Paragraph } from '@mindtickle/typography';
import { debounce } from '@mindtickle/utils';

import {
  COMPLETION_CRITERIA_ENUM,
  COMPLETION_CRITERIA_TEXTS,
} from '~/modules/Admin/config/sessions.constants';
import { DEFAULT_TIME } from '~/modules/Admin/containers/Settings/components/Notifications/constants';
import {
  getDaysValue,
  getHasSpecificDate,
  getSelectedDate,
  getSelectedTime,
} from '~/modules/Admin/containers/Settings/components/Notifications/helpers';
import EnrolmentEmailSettings from '~/modules/Admin/containers/Settings/components/ScoreAndCompletion/EnrolmentEmailSettings';
import { parseNotAllowedSymbolsNumberInput } from '~/utils/core';

import { STATIC } from '../../constants';
import {
  StyledGridwithTootip,
  StyledInput,
  StyledInputContainer,
  StyledInputParaContainer,
  StyledNote,
  StyledRadioButtonSettingTab,
  StyledSectionWithTooltip,
  StyledSettingHeaderWithToolTip,
  StyledSettingsCard,
  StyledText,
  StyledTimeLimitNote,
  StyledToggleDiv,
  StyledErrorNote,
} from '../../styles';

import messages from './messages';
import { styleScoreCompletionSettingCard, styleScoreNote } from './styles';
import { defaultScoreFormatter } from './utils';

import type { TProps } from './typeDefs';
import type { RadioChangeEvent } from 'antd';

// eslint-disable-next-line max-statements
const ScoreAndCompletion = (props: TProps) => {
  const {
    tempFeatureConfig: {
      isCaptureAutoAttendanceFeatureEnabled = false,
      isEnrollmentThresholdEmailEnabled = false,
    },
  } = useUserAuth();
  const { updateSetting, companySettings, moduleData } = props;

  const { moduleId, seriesId } = useParams();
  const {
    defaultSessionCompletionCriteria,
    scoring: defaultScoringOnTask,
    score: defaultMaxScoreOnCompletion,
    companyId,
  } = props.moduleData;

  const enrolmentThresholdEmailTemplate = '_ilt_session_minimum_enrollment_settings_breach';
  const enrolmentThresholdEmailTask = 'not_enrolled_to_session_by_date';
  const defaultRecurrenceSettings = {
    recurring: false,
    recurrenceFrequency: 3,
    recurrenceType: 'DAYS',
    recurrenceEndType: 'STATE_CHANGE',
    recurrenceCount: 8,
    recurrenceDays: ['Tue'],
  };
  const reminderOptions = [
    {
      key: 'invited_but_not_enrolled',
      content: 'No. of days before session',
    },
    {
      key: 'specific_date',
      content: 'Specific date',
    },
  ];
  function addCnameModuleId(input: string, strToAdd: string): string {
    const delimiter = '==';
    const index = input.indexOf(delimiter);

    if (index !== -1) {
      return input.slice(0, index) + strToAdd + input.slice(index);
    } else {
      return input + strToAdd;
    }
  }

  const modifyReminders = (
    reminders: any,
    isDraft: boolean,
    isEdited: boolean,
    companyId: string,
    moduleId: string,
    operation?: string,
    mailJobIndex?: string,
    isNewReminder?: boolean
    // eslint-disable-next-line max-params
  ): any =>
    reminders?.map((reminder: any, index: any) => ({
      templateId: reminder?.templateId,
      task: reminder?.templateId,
      schedule: reminder?.schedule,
      condition: addCnameModuleId(reminder?.condition, `("${companyId}","${moduleId}")`),
      recurrenceSettings: reminder?.recurrenceSettings,
      company: companyId,
      entity: moduleId,
      operation: operation === 'CREATE' ? 'CREATE' : operation,
      mailJobId: reminder?.mailJobId
        ? reminder?.mailJobId
        : `Reminder-${reminders?.length === 1 ? mailJobIndex : index}`,
      isDraft: isDraft,
      isEdited: isEdited,
      isNewReminder: isNewReminder || false,
      selectedTemplate: enrolmentThresholdEmailTemplate,
      selectedEmailReminderType: reminder?.condition?.includes('==')
        ? reminderOptions[0].key
        : reminderOptions[1].key,
      hasSpecificDate: getHasSpecificDate(reminder),
      daysValue: getDaysValue(reminder),
      selectedDate: getSelectedDate(reminder),
      selectedTime: getSelectedTime(reminder) || DEFAULT_TIME,
      reminderOptions: reminderOptions,
    }));

  const moduleAttendanceSettings = moduleData?.attendanceSettings;
  const companyAttendanceSettings = companySettings?.attendanceSettings;

  const [moduleAttendanceEnabled, setModuleAttendanceEnabled] = useState<boolean>(
    moduleAttendanceSettings?.durationBasedAttendanceEnabled || false
  );
  const [moduleAttendanceThreshold, setModuleAttendanceThreshold] = useState<number>(
    !moduleAttendanceSettings?.percentageThreshold ||
      moduleAttendanceSettings?.percentageThreshold === 0
      ? companyAttendanceSettings?.percentageThreshold
      : moduleAttendanceSettings?.percentageThreshold
  );
  const [moduleAttendanceError, setModuleAttendanceError] = useState<boolean>(false);

  const [defaultScoringOnTaskInSettings, setDefaultScoringOnTask] = useState(defaultScoringOnTask);
  const [defaultMaxScoreOnCompletionInSettings, setDefaultMaxScoreOnCompletion] = useState(
    defaultMaxScoreOnCompletion
  );
  const [defaultSessionCompletionCriteriaInSettings, setCompletionCriteria] = useState(
    defaultSessionCompletionCriteria
  );

  const companyEnrolmentEmailSettings = companySettings?.enrolmentThresholdEmailSettings;
  const moduleEnrolmentEmailSettings = moduleData?.enrolmentThresholdEmailSettings;

  const [moduleEnrolmentEmailEnabled, setModuleEnrolmentEmailEnabled] = useState<boolean>(
    moduleEnrolmentEmailSettings?.isEnabled || false
  );
  const [moduleEnrolmentEmailThreshold, setModuleEnrolmentEmailThreshold] = useState<number>(
    moduleEnrolmentEmailSettings?.enrolmentThreshold || 0
  );
  const [enrolmentReminders, setEnrolmentReminders] = useState({
    key: '_reminder_not_enrolled_in_session',
    status: 'Not enrolled in any session or event',
    allowAddReminder: true,
    reminderDateText: undefined,
    emailTemplates: [
      {
        key: enrolmentThresholdEmailTemplate,
        content: 'Room shared',
      },
    ],
    reminderData: modifyReminders(
      props?.moduleData?.enrolmentThresholdEmailSettings?.reminders,
      false,
      false,
      companyId,
      moduleId,
      'NO_CHANGE'
    ),
  });

  const [originalEnrolmentReminders, setOriginalEnrolmentReminders] = useState({
    key: '_reminder_not_enrolled_in_session',
    status: 'Not enrolled in any session or event',
    allowAddReminder: true,
    reminderDateText: undefined,
    emailTemplates: [
      {
        key: enrolmentThresholdEmailTemplate,
        content: 'Room shared',
      },
    ],
    reminderData: modifyReminders(
      props?.moduleData?.enrolmentThresholdEmailSettings?.reminders,
      false,
      false,
      companyId,
      moduleId,
      'NO_CHANGE'
    ),
  });

  function defaultScoringOnTaskChangeHandler(value: boolean) {
    setDefaultScoringOnTask(value);
    updateSetting({ type: STATIC, moduleId, seriesId, data: { scoring: value } });
  }

  function moduleAttendanceEnabledHandler(value: boolean) {
    const debouncedUpdateSetting = debounce(
      (value: any) =>
        updateSetting({
          type: STATIC,
          moduleId,
          seriesId,
          data: {
            attendanceSettings: {
              durationBasedAttendanceEnabled: value,
              percentageThreshold:
                moduleAttendanceThreshold === 0
                  ? companyAttendanceSettings?.percentageThreshold
                  : moduleAttendanceThreshold,
            },
          },
          updatedSetting: 'moduleAttendanceSettingsToggled',
        }),
      200
    );
    setModuleAttendanceError(false);
    setModuleAttendanceEnabled(value);
    setModuleAttendanceThreshold(moduleAttendanceThreshold);
    debouncedUpdateSetting(value);
  }

  const moduleAttendanceThresholdHandler = (value: number) => {
    setModuleAttendanceThreshold(value);
    setModuleAttendanceError(false);
    const debouncedUpdateSetting = debounce(
      (value: any) =>
        updateSetting({
          type: STATIC,
          moduleId,
          seriesId,
          data: {
            attendanceSettings: {
              durationBasedAttendanceEnabled: moduleAttendanceEnabled,
              percentageThreshold:
                value === 0 ? companyAttendanceSettings?.percentageThreshold : value,
            },
          },
          updatedSetting: 'moduleAttendanceThresholdChanged',
        }),
      200
    );
    debouncedUpdateSetting(value);
  };

  const transformEnrolmentReminders = (reminders: any) => {
    let transformedEnrolmentReminders = reminders?.map((reminder: any, index: number) => {
      if (reminder?.isDraft === false && reminder?.isEdited === false) {
        return {
          operation: reminder?.operation,
          templateId: enrolmentThresholdEmailTemplate,
          task: enrolmentThresholdEmailTask,
          schedule: reminder?.schedule,
          condition: reminder?.condition?.replace(/\([^)]*\)/g, ''),
          recurrenceSettings: reminder?.recurrenceSettings || defaultRecurrenceSettings,
        };
      } else if (originalEnrolmentReminders?.reminderData?.[index]) {
        return {
          operation: originalEnrolmentReminders.reminderData[index]?.operation,
          templateId: enrolmentThresholdEmailTemplate,
          task: enrolmentThresholdEmailTask,
          schedule: originalEnrolmentReminders.reminderData[index]?.schedule,
          condition: originalEnrolmentReminders.reminderData[index]?.condition?.replace(
            /\([^)]*\)/g,
            ''
          ),
          recurrenceSettings:
            originalEnrolmentReminders.reminderData[index]?.recurrenceSettings ||
            defaultRecurrenceSettings,
        };
      } else {
        return null; // Return null for invalid reminders
      }
    });
    transformedEnrolmentReminders = transformedEnrolmentReminders?.filter(
      (item: any) => item !== null && Object.keys(item).length > 0
    );
    return transformedEnrolmentReminders;
  };

  const moduleEnrolmentEmailToggleHandler = useCallback(
    (value: boolean) => {
      const debouncedUpdateSetting = debounce(
        (value: any) =>
          updateSetting({
            type: STATIC,
            moduleId,
            seriesId,
            data: {
              enrolmentThresholdEmailSettings: {
                isEnabled: value,
                enrolmentThreshold: moduleEnrolmentEmailThreshold,
                reminders: transformEnrolmentReminders(enrolmentReminders?.reminderData || []),
              },
            },
          }),
        200
      );

      setModuleEnrolmentEmailEnabled(value);
      debouncedUpdateSetting(value);
    },
    [moduleEnrolmentEmailThreshold, enrolmentReminders]
  );

  const moduleEnrolmentEmailThresholdHandler = useCallback(
    (value: number) => {
      const debouncedUpdateSetting = debounce(
        (value: any) =>
          updateSetting({
            type: STATIC,
            moduleId,
            seriesId,
            data: {
              enrolmentThresholdEmailSettings: {
                isEnabled: moduleEnrolmentEmailEnabled,
                enrolmentThreshold: value,
                reminders: transformEnrolmentReminders(enrolmentReminders?.reminderData || []),
              },
            },
          }),
        200
      );

      setModuleEnrolmentEmailThreshold(value);
      debouncedUpdateSetting(value);
    },
    [moduleEnrolmentEmailEnabled, enrolmentReminders]
  );

  const defaultMaxScoreOnCompletionChangeHandler = useMemo(() => {
    const debouncedUpdateSetting = debounce(
      (value: any) =>
        updateSetting({
          type: STATIC,
          moduleId,
          seriesId,
          data: { score: value },
        }),
      200
    );
    return (value: any) => {
      value = defaultScoreFormatter(value);
      setDefaultMaxScoreOnCompletion(value);
      debouncedUpdateSetting(value);
    };
  }, [moduleId, seriesId, updateSetting]);

  function defaultSessionCompletionCriteriaChangeHandler(value: any) {
    setCompletionCriteria(value);
    updateSetting({
      type: STATIC,
      moduleId,
      seriesId,
      data: { defaultSessionCompletionCriteria: value },
    });
  }

  return (
    <StyledSettingsCard style={styleScoreCompletionSettingCard}>
      <Grid>
        <StyledGridwithTootip>
          <StyledSectionWithTooltip toggleDisabled={!defaultScoringOnTaskInSettings}>
            <Title level={4}>Default max score on attended sessions</Title>
          </StyledSectionWithTooltip>
          <Switch
            checked={defaultScoringOnTaskInSettings}
            defaultChecked={defaultScoringOnTaskInSettings || false}
            onChange={defaultScoringOnTaskChangeHandler}
          />
        </StyledGridwithTootip>
        <StyledToggleDiv
          toggle={defaultScoringOnTaskInSettings}
          display="block"
          className="default-max-score-block"
        >
          <StyledInputParaContainer>
            <InputNumber
              type="number"
              min={0}
              max={1000}
              step={50}
              formatter={defaultScoreFormatter}
              precision={0}
              onKeyDown={parseNotAllowedSymbolsNumberInput}
              defaultValue={defaultMaxScoreOnCompletionInSettings}
              value={defaultMaxScoreOnCompletionInSettings}
              onChange={defaultMaxScoreOnCompletionChangeHandler}
              // data-testid="timeLimitInput"
            />
            <Paragraph className="default-max-score-text">points</Paragraph>
          </StyledInputParaContainer>
          <StyledTimeLimitNote>Enter whole numbers between 0-1000 only.</StyledTimeLimitNote>
        </StyledToggleDiv>
        <Paragraph style={styleScoreNote}>
          <StyledNote>Note:</StyledNote>{' '}
          {defaultScoringOnTaskInSettings
            ? `All the sessions created henceforth will have this score by default.`
            : `Turn this on to set a default score for all sessions created henceforth.`}
        </Paragraph>
      </Grid>

      {isCaptureAutoAttendanceFeatureEnabled &&
        companyAttendanceSettings?.durationBasedAttendanceEnabled && (
          <Grid>
            <StyledGridwithTootip>
              <StyledSectionWithTooltip toggleDisabled={!moduleAttendanceEnabled}>
                <Title level={4}>
                  <FormattedMessage {...messages.AUTO_ATTENDANCE_TOGGLE_TEXT} />
                </Title>
              </StyledSectionWithTooltip>
              <Toggle
                checked={moduleAttendanceEnabled}
                onChange={moduleAttendanceEnabledHandler}
                disabled={!companyAttendanceSettings?.allowEditSettings}
              />
            </StyledGridwithTootip>
            {moduleAttendanceEnabled && (
              <StyledInputContainer>
                <StyledText>
                  <FormattedMessage {...messages.SET_MINIMUM_TIME_TEXT} />
                </StyledText>
                <StyledInput>
                  <InputNumber
                    disabled={!companyAttendanceSettings?.allowEditSettings}
                    type="number"
                    min={1}
                    max={100}
                    step={1}
                    defaultValue={moduleAttendanceThreshold}
                    value={moduleAttendanceThreshold}
                    onChange={moduleAttendanceThresholdHandler}
                    style={{ width: '60px' }}
                    handleVisible={true}
                  />
                  <Paragraph className="default-max-score-text">%</Paragraph>
                </StyledInput>
                {moduleAttendanceError ? (
                  <StyledErrorNote>
                    <FormattedMessage {...messages.DEFAULT_ATTENDANCE_THRESHOLD_ERROR_TEXT} />
                  </StyledErrorNote>
                ) : null}
                <Paragraph style={styleScoreNote}>
                  <StyledNote>Note: </StyledNote>
                  This is a default setting.
                  {!companyAttendanceSettings?.allowEditSettings
                    ? ' You cannot change this setting at anytime'
                    : ' You can change this setting anytime.'}
                </Paragraph>
              </StyledInputContainer>
            )}
          </Grid>
        )}

      {isEnrollmentThresholdEmailEnabled && companyEnrolmentEmailSettings?.isEnabled && (
        <Grid>
          <StyledGridwithTootip>
            <StyledSectionWithTooltip toggleDisabled={!moduleEnrolmentEmailEnabled}>
              <Title level={4}>
                <FormattedMessage {...messages.ENROLMENT_EMAIL_TOGGLE_TEXT} />
              </Title>
            </StyledSectionWithTooltip>
            <Toggle
              checked={moduleEnrolmentEmailEnabled}
              onChange={moduleEnrolmentEmailToggleHandler}
            />
          </StyledGridwithTootip>
          {moduleEnrolmentEmailEnabled && (
            <div>
              <div style={{ display: 'flex' }}>
                <StyledInputContainer>
                  <StyledText>
                    <FormattedMessage {...messages.MINIMUM_ENROLMENT_TEXT} />
                  </StyledText>
                  <StyledInput>
                    <InputNumber
                      type="number"
                      min={0}
                      max={100}
                      step={1}
                      defaultValue={moduleEnrolmentEmailThreshold}
                      value={moduleEnrolmentEmailThreshold}
                      onChange={moduleEnrolmentEmailThresholdHandler}
                      style={{ width: '160px' }}
                      handleVisible={true}
                    />
                  </StyledInput>
                </StyledInputContainer>
                <div style={{ marginTop: '24px', marginLeft: '32px' }}>
                  <StyledText>
                    <FormattedMessage {...messages.REMINDER_TEXT} />
                  </StyledText>
                  <EnrolmentEmailSettings
                    updateSetting={updateSetting}
                    companySettings={companySettings}
                    moduleData={moduleData}
                    moduleEnrolmentEmailEnabled={moduleEnrolmentEmailEnabled}
                    moduleEnrolmentEmailThreshold={moduleEnrolmentEmailThreshold}
                    enrolmentReminders={enrolmentReminders}
                    setEnrolmentReminders={setEnrolmentReminders}
                    modifyReminders={modifyReminders}
                    originalEnrolmentReminders={originalEnrolmentReminders}
                    setOriginalEnrolmentReminders={setOriginalEnrolmentReminders}
                    transformEnrolmentReminders={transformEnrolmentReminders}
                  ></EnrolmentEmailSettings>
                </div>
              </div>
              <Paragraph style={styleScoreNote}>
                <StyledNote>Note: </StyledNote>
                You can change this default setting at anytime. Any changes here will be applicable
                only for the sessions created from now on.
              </Paragraph>
            </div>
          )}
        </Grid>
      )}

      <StyledRadioButtonSettingTab>
        <Grid>
          <StyledSettingHeaderWithToolTip>
            <Title level={4}>Default completion criteria</Title>
          </StyledSettingHeaderWithToolTip>
          <Radio.Group
            value={defaultSessionCompletionCriteriaInSettings}
            onChange={(e: RadioChangeEvent) =>
              defaultSessionCompletionCriteriaChangeHandler(e.target.value)
            }
            className="vertical-radio-style"
          >
            <Radio value={COMPLETION_CRITERIA_ENUM.ATTENDED}>
              <FormattedMessage {...COMPLETION_CRITERIA_TEXTS[COMPLETION_CRITERIA_ENUM.ATTENDED]} />
            </Radio>
            <Radio value={COMPLETION_CRITERIA_ENUM.ATTENDED_OR_WATCHED}>
              <FormattedMessage
                {...COMPLETION_CRITERIA_TEXTS[COMPLETION_CRITERIA_ENUM.ATTENDED_OR_WATCHED]}
              />
            </Radio>
          </Radio.Group>
          <Paragraph>
            <StyledNote>Note:</StyledNote> This is a default setting. You can change this while
            creating a session or uploading a recording for a session.
          </Paragraph>
        </Grid>
      </StyledRadioButtonSettingTab>
    </StyledSettingsCard>
  );
};

export default ScoreAndCompletion;
