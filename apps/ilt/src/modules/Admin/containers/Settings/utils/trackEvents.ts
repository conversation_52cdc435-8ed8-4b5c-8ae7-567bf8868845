import { getRecurrenceSummary } from '~/modules/Admin/containers/Settings/components/Notifications/helpers';
import { WeeksDaysStatusType } from '~/modules/Admin/containers/Settings/components/Notifications/typeDefs';
import {
  MIXPANEL_UI_EVENTS,
  SNOWPLOW_FIELD_NAMES,
} from '~/modules/Admin/hooks/useILTAdminSnowplowTracker/constants';
import {
  MODULE_SETTINGS_EVENTS,
  SETTINGS_UPDATED_TRACK_PROPERTIES,
  MIXPANEL_SETTING_TYPE,
  MIXPANEL_SUB_SETTING_TYPE,
  MIXPANEL_SUB_SETTING_VALUE_PREPROCESSOR,
  MIXPANEL_SUB_TYPE_AND_TYPE_MAP,
} from '~/modules/Admin/hooks/useILTAdminSnowplowTracker/constants/settings';
import type { TSnowplowTrackerType } from '~/modules/Admin/hooks/useILTAdminSnowplowTracker/typeDefs';

import { SETTINGS_TYPE } from '../config';

const SETTING_PROPERTIES_SNOWPLOW_MAP = {
  name: MIXPANEL_SUB_SETTING_TYPE.NAME,
  description: MIXPANEL_SUB_SETTING_TYPE.DESCRIPTION,
  moduleRelevance: MIXPANEL_SUB_SETTING_TYPE.MODULE_RELEVANCE,
  showLearnerTimezone: MIXPANEL_SUB_SETTING_TYPE.SESSION_TIME_ZONE_TOGGLE,
  allowMultipleSession: MIXPANEL_SUB_SETTING_TYPE.MULTI_SESSION_TOGGLE,
  restrictLearnerEnroll: MIXPANEL_SUB_SETTING_TYPE.LEARNER_ENROLLMENT_TOGGLE,
  learnerCnfSsnEmail: MIXPANEL_SUB_SETTING_TYPE.SESSION_CONFIRMATION_TOGGLE,
  score: MIXPANEL_SUB_SETTING_TYPE.DEFAULT_SESSION_SCORE,
  scoring: MIXPANEL_SUB_SETTING_TYPE.DEFAULT_SESSION_SCORE_TOGGLE,
  defaultSessionCompletionCriteria: MIXPANEL_SUB_SETTING_TYPE.COMPLETION_CRITERIA,
  durationBasedAttendanceEnabled: MIXPANEL_SUB_SETTING_TYPE.MINIMUM_ATTENDANCE_REQUIRED_TOGGLE,
  percentageThreshold: MIXPANEL_SUB_SETTING_TYPE.MINIMUM_ATTENDANCE_REQUIRED_PERCENTAGE,
  enrolmentThresholdEmailSettings: MIXPANEL_SUB_SETTING_TYPE.ENROLMENT_THRESHOLD_EMAIL_SETTINGS,
};

const TAB_NAME_MAP: Record<string, string> = {
  [SETTINGS_TYPE.GENERAL]: MIXPANEL_SETTING_TYPE.GENERAL,
  [SETTINGS_TYPE.SCORE_AND_COMPLETION]: MIXPANEL_SETTING_TYPE.SCCORING_AND_COMPLETION,
  [SETTINGS_TYPE.ENROLMENT]: MIXPANEL_SETTING_TYPE.ENROLLMENT,
  [SETTINGS_TYPE.NOTIFICATIONS]: MIXPANEL_SETTING_TYPE.NOTIFICATIONS,
};

export const mixpanelConstants = {
  SCORING_AND_COMPLETION: 'scoring_and_completion',
  SET_ENROLLMENT_THRESHOLD_TOGGLE: 'set_enrollment_threshold_toggle',
  ENROLLMENT_THRESHOLD_REMINDER: 'enrollment_threshold_reminder',
  OPERATION_NO_CHANGE: 'NO_CHANGE',
  OPERATION_DELETE: 'DELETE',
  OPERATION_UPDATE: 'UPDATE',
  OPERATION_CREATE: 'CREATE',
};

// eslint-disable-next-line max-depth
export const sendSettingUpdateTrackEvent = (
  tracker: TSnowplowTrackerType,
  { data = {} }: { [key: string]: any }
): void => {
  try {
    const dataKeys = Object.keys(data);
    const settingProperty = (dataKeys.length && dataKeys[0]) || '';
    if (settingProperty === MIXPANEL_SUB_SETTING_TYPE.ENROLMENT_THRESHOLD_EMAIL_SETTINGS) {
      sendEnrolmentThresholdSettingUpdateTrackEvent(tracker, data?.enrolmentThresholdEmailSettings);
      return;
    }
    if (
      settingProperty &&
      SETTING_PROPERTIES_SNOWPLOW_MAP[
        settingProperty as keyof typeof SETTING_PROPERTIES_SNOWPLOW_MAP
      ]
    ) {
      const subType =
        SETTING_PROPERTIES_SNOWPLOW_MAP[
          settingProperty as keyof typeof SETTING_PROPERTIES_SNOWPLOW_MAP
        ];
      const settingType = MIXPANEL_SUB_TYPE_AND_TYPE_MAP[subType];
      let subTypeValue = data[settingProperty];
      // eslint-disable-next-line max-depth
      if (MIXPANEL_SUB_SETTING_VALUE_PREPROCESSOR[subType]) {
        subTypeValue = MIXPANEL_SUB_SETTING_VALUE_PREPROCESSOR[subType](subTypeValue);
      }
      tracker.trackStructuredEvent({
        eventName: MODULE_SETTINGS_EVENTS.MODULE_SETTINGS_UPDATED,
        [SETTINGS_UPDATED_TRACK_PROPERTIES.SETTING_TYPE]: settingType,
        [SETTINGS_UPDATED_TRACK_PROPERTIES.SUB_SETTING_TYPE]: subType,
        [SETTINGS_UPDATED_TRACK_PROPERTIES.SUB_SETTING_VALUE]: subTypeValue,
      });
    }
  } catch (err) {
    // do nothing
  }
};

// eslint-disable-next-line max-depth
export const sendEnrolmentThresholdSettingUpdateTrackEvent = (
  tracker: TSnowplowTrackerType,
  data: any,
  eventName?: string
): void => {
  try {
    const isEnabled = data.isEnabled;
    const enrolmentThreshold = data.enrolmentThreshold;
    let updatedReminder: any = {};
    if (data?.isDelete) {
      updatedReminder = data?.deletedReminder;
      updatedReminder.operation = mixpanelConstants.OPERATION_DELETE;
    } else {
      updatedReminder = data.reminders.filter(
        (rem: any) => rem.operation !== mixpanelConstants.OPERATION_NO_CHANGE
      )[0];
    }

    // eslint-disable-next-line max-depth
    if (!updatedReminder) {
      tracker.trackStructuredEvent({
        eventName: eventName ? eventName : MODULE_SETTINGS_EVENTS.MODULE_SETTINGS_UPDATED,
        setting_type: mixpanelConstants.SCORING_AND_COMPLETION,
        sub_setting_type: mixpanelConstants.SET_ENROLLMENT_THRESHOLD_TOGGLE,
        sub_setting_value: isEnabled ? 'on' : 'off',
        enrollment_threshold: enrolmentThreshold,
      });
      return;
    }

    let eventData: any = {
      enrollment_threshold: enrolmentThreshold,
      recurrence: updatedReminder.recurrenceSettings.recurring ? 'Yes' : 'No',
      recurrence_Summary: getSummary(updatedReminder),
      reminder_operation: updatedReminder.operation,
    };

    // eslint-disable-next-line max-depth
    if (updatedReminder.condition.split('==')[1]) {
      eventData = {
        ...eventData,
        enrollment_reminder_days_before_session: updatedReminder.condition
          ?.split('==')[1]
          ?.split('d')[0],
      };
    } else {
      eventData = {
        ...eventData,
        enrollment_reminder_specific_date: updatedReminder.schedule,
      };
    }

    tracker.trackStructuredEvent({
      eventName: eventName ? eventName : MODULE_SETTINGS_EVENTS.MODULE_SETTINGS_UPDATED,
      setting_type: mixpanelConstants.SCORING_AND_COMPLETION,
      sub_setting_type: mixpanelConstants.ENROLLMENT_THRESHOLD_REMINDER,
      ...eventData,
    });
  } catch (err) {
    // do nothing
  }
};

export const getSummary = (automation: any) => {
  if (!automation?.recurrenceSettings?.recurrenceDays) {
    return 'every 3 days';
  }
  let recurrenceDaysStatus: WeeksDaysStatusType = {};
  automation?.recurrenceSettings?.recurrenceDays.forEach((value: any) => {
    if (value) {
      recurrenceDaysStatus[value] = true;
    }
  });

  const summary = getRecurrenceSummary(
    automation?.recurrenceSettings?.recurrenceFrequency,
    automation?.recurrenceSettings?.recurrenceType,
    recurrenceDaysStatus
  );

  if (summary === 'Error') {
    return 'every 3 days';
  }
  return summary;
};

export const sendSettingTabClickTrackEvent = (
  tracker: TSnowplowTrackerType,
  { tabId }: { tabId: string }
) => {
  try {
    tracker.trackStructuredEvent({
      eventName: MODULE_SETTINGS_EVENTS.MODULE_SETTINGS_CLICKED,
      [SETTINGS_UPDATED_TRACK_PROPERTIES.SETTING_TYPE]: TAB_NAME_MAP[tabId],
    });
  } catch (err) {
    // do nothing
  }
};

export const sendThumbnailClickedEvent = (
  tracker: TSnowplowTrackerType,
  { pageName }: { pageName: string }
) => {
  try {
    tracker.trackStructuredEvent({
      eventName: MIXPANEL_UI_EVENTS.THUMBNAIL_EDIT_CLICKED,
      [SNOWPLOW_FIELD_NAMES.PAGE_NAME]: pageName,
      [SETTINGS_UPDATED_TRACK_PROPERTIES.SETTING_TYPE]: TAB_NAME_MAP[SETTINGS_TYPE.GENERAL],
    });
  } catch (err) {
    // do nothing
  }
};
