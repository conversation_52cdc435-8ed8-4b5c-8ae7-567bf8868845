import React, { useState } from 'react';

import camelcase from 'camelcase';
import { isEmpty } from 'lodash';

import Button, { BUTTON_TYPES } from '@mindtickle/button';
import FileUploader from '@mindtickle/file-uploader';
import IconWithGradient, { ICON_MAP } from '@mindtickle/icon-with-gradient';
import Tooltip from '@mindtickle/tooltip';
import { isValidEmail, isUndefined, isObject, prune } from '@mindtickle/utils';

import { MINDTICKLE_EVENT_SAMPLE_COLUMNS } from '~/modules/Admin/config/track.constants';
import { getAttendedValueFromSheet } from '~/modules/Admin/utils';
import { isValidUsername } from '~/utils';
import { getDataFromSheet } from '~/utils/xlsx';

import { ALL_LEARNERS_FETCH_STATUS } from '../../../../constants';
import AttendanceModalTable from '../AttendanceModalTable';
import IncorrectFileModal from '../IncorrectFileModal';

import { SheetJSFT, EMAIL_KEY } from './constants';
import UploadAttendanceModalContainer, {
  StyledModalFooter,
  StyledModalTitle,
  StyledModal,
  StyledLoader,
  uploadIconStyle,
} from './styles';

const SIZE_4MB = 4 * 1024 * 1024;

const ModalTitle = ({ downloadSample, sampleDataStatus }: any) => (
  <StyledModalTitle>
    <div className="title">{'Upload event attendance'}</div>
    <Tooltip
      title={
        sampleDataStatus === ALL_LEARNERS_FETCH_STATUS.FAILED
          ? 'Failed to create sample'
          : !sampleDataStatus || sampleDataStatus === ALL_LEARNERS_FETCH_STATUS.LOADING
          ? 'Creating sample'
          : ''
      }
    >
      <span>
        <Button
          type={
            sampleDataStatus === ALL_LEARNERS_FETCH_STATUS.FAILED
              ? BUTTON_TYPES.DANGER
              : BUTTON_TYPES.SECONDARY
          }
          onClick={downloadSample}
          loading={sampleDataStatus === ALL_LEARNERS_FETCH_STATUS.LOADING}
          disabled={sampleDataStatus !== ALL_LEARNERS_FETCH_STATUS.SUCCESS}
        >
          {'Download sample'}
        </Button>
      </span>
    </Tooltip>
  </StyledModalTitle>
);

const ModalFooter = ({ onClose, onReupload, onUpdate }: any) => (
  <StyledModalFooter>
    <Button type="text" onClick={onClose}>
      {'Cancel'}
    </Button>
    <Button type="secondary" onClick={onReupload}>
      {'Reupload'}
    </Button>
    <Button type="primary" onClick={onUpdate}>
      {'Upload attendance'}
    </Button>
  </StyledModalFooter>
);

const convertToLearnersArray = (data: any) => {
  if (!isUndefined(data) && isObject(data) && !isEmpty(data)) {
    let learners: any = Object.values(data)[0];

    learners = learners.map((learner: any) => {
      const attended = getAttendedValueFromSheet(learner.attended);
      return { ...learner, attended };
    });
    return learners;
  }
  return [];
};

const convertToLearnerMap = (learners: any, isUIDEnabled: any) => {
  const columnKeyMap = MINDTICKLE_EVENT_SAMPLE_COLUMNS.reduce(
    (columnKeyMap: any, { key, column }: any) => {
      columnKeyMap[camelcase(column) as keyof typeof columnKeyMap] = key;
      return columnKeyMap;
    },
    {}
  );
  columnKeyMap[EMAIL_KEY as keyof typeof columnKeyMap] = EMAIL_KEY; // To handle zoom upload where we get the column as Email instead of Username

  return learners.reduce((map: any, learner: any = {}) => {
    const usernameOrEmail = learner.username || learner.email;
    if (!usernameOrEmail) return map;
    if (isUIDEnabled && learner.username) {
      if (!isValidUsername(usernameOrEmail)) return map;
    } else {
      if (!isValidEmail(usernameOrEmail)) return map;
    }
    map[usernameOrEmail] = Object.keys(learner).reduce((learnerDetails: any, key: any) => {
      learnerDetails[columnKeyMap[key] as keyof typeof learnerDetails] = learner[key];
      return learnerDetails;
    }, {});
    return map;
  }, {});
};

const UploadAttendanceModal = ({
  onClose,
  onUpdateAttendance,
  sampleDataStatus,
  isUIDEnabled,
  downloadSample,
}: any) => {
  const [isFileProcessing, setIsFileProcessing] = useState<boolean>(false);
  const [showFileError, setShowFileError] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string | undefined>(undefined);
  const [attendanceData, setAttendanceData] = useState<any>({});
  const [showAttendanceModal, setShowAttendanceModal] = useState<boolean>(false);

  const processFile = (binaryString: any, readAsBinaryString: any) => {
    let options = {};
    let data = getDataFromSheet(binaryString, readAsBinaryString, options);

    const learners = convertToLearnersArray(data);
    const learnersData = convertToLearnerMap(learners, isUIDEnabled);
    setIsFileProcessing(false);
    setShowAttendanceModal(true);
    setAttendanceData(learnersData);
  };

  const handleFileRead = (file: any, fileList: any[]) => {
    if (fileList.length !== 1) {
      updateFileErrors({ isError: true, errorMessage: 'Please select exactly 1 supported file.' });
      return;
    }
    if (file.size > SIZE_4MB) {
      updateFileErrors({
        isError: true,
        errorMessage: 'Size of the file should not be more than 4MB.',
      });
      return;
    }
    setIsFileProcessing(true);

    const reader = new FileReader();
    const readAsBinaryString = !!reader.readAsBinaryString;
    reader.onload = (event: any) => {
      const binaryString = event.target.result;
      processFile(binaryString, readAsBinaryString);
    };
    if (readAsBinaryString) {
      reader.readAsBinaryString(file);
    } else {
      reader.readAsArrayBuffer(file);
    }
    // Required to cancel upload by antd
    return false;
  };

  const updateFileErrors = ({
    isError,
    errorMessage,
  }: {
    isError: boolean;
    errorMessage: string | undefined;
  }) => {
    setShowFileError(isError);
    setErrorMessage(errorMessage);
  };

  const onReupload = () => {
    setAttendanceData({});
    setShowAttendanceModal(false);
  };

  const onUpdate = () => {
    const attendanceDataForUpload = prune(Object.values(attendanceData));
    onUpdateAttendance(attendanceDataForUpload);
    onClose();
  };

  const onRemove = ({ learnerEmail }: { learnerEmail: string }) => {
    const updatedAttendanceData = { ...attendanceData };
    delete updatedAttendanceData[learnerEmail];
    setAttendanceData(updatedAttendanceData);
  };

  const updateLearnerAttendance = ({
    learnerEmail,
    attendedStatus,
  }: {
    learnerEmail: string;
    attendedStatus: string;
  }) => {
    const updatedAttendanceData = { ...attendanceData };
    updatedAttendanceData[learnerEmail] = {
      ...updatedAttendanceData[learnerEmail],
      attendedStatus,
    };
    setAttendanceData(updatedAttendanceData);
  };
  return (
    <React.Fragment>
      <StyledModal
        visible={true}
        onCancel={onClose}
        destroyOnClose
        zIndex={9980}
        title={<ModalTitle downloadSample={downloadSample} sampleDataStatus={sampleDataStatus} />}
        maskClosable={false}
        width={showAttendanceModal ? 900 : 700}
        bodyStyle={{
          height: 596,
          ...(showAttendanceModal ? { padding: '0 32px' } : { paddingBottom: '32px' }),
        }}
        footer={
          showAttendanceModal && (
            <ModalFooter onClose={onClose} onReupload={onReupload} onUpdate={onUpdate} />
          )
        }
      >
        {!showAttendanceModal ? (
          <UploadAttendanceModalContainer>
            <FileUploader.Dragger
              className="dragger"
              disabled={false}
              accept={SheetJSFT}
              multiple={false}
              beforeUpload={handleFileRead}
              openFileDialogOnClick={false}
              showUploadList={false}
            >
              <div>
                {isFileProcessing ? (
                  <StyledLoader message={'Loading file..'} />
                ) : (
                  <div>
                    <div className="upload-icon-container">
                      <IconWithGradient type={ICON_MAP.UPLOAD} style={uploadIconStyle} />
                    </div>
                    <div className="drop-file-text">
                      {'Drop your .xlsx file to upload attendance'}
                    </div>
                    <div>or</div>
                    <FileUploader
                      multiple={false}
                      className="uploadButton"
                      disabled={isFileProcessing}
                      accept={SheetJSFT}
                      beforeUpload={handleFileRead}
                      showUploadList={false}
                    >
                      <Button type="primary">{'Upload file'}</Button>
                    </FileUploader>
                    <div className="type-text">{'Supported file type: .xlsx'}</div>
                    <div className="advice-text">
                      <span className="note">{'Note: '}</span>
                      <ul className="note-list">
                        <li>
                          {`Attendance will be marked only for the sessions where the learner is enrolled in.`}
                        </li>
                        <li>
                          {
                            'The attendance of only existing sessions would be updated and sessions added in the future would not be updated.'
                          }
                        </li>
                        <li>
                          {
                            'For best results edit the file in either MS Excel or Google Sheets. Please read the instructions provided in the instructions tab of the sample file before editing it.'
                          }
                        </li>
                      </ul>
                    </div>
                  </div>
                )}
              </div>
            </FileUploader.Dragger>
          </UploadAttendanceModalContainer>
        ) : (
          <AttendanceModalTable
            learnersData={attendanceData}
            updateLearnerAttendance={updateLearnerAttendance}
            onRemove={onRemove}
          />
        )}
      </StyledModal>
      <IncorrectFileModal
        visible={showFileError}
        message={errorMessage}
        onClose={() => updateFileErrors({ isError: false, errorMessage: undefined })}
      />
    </React.Fragment>
  );
};

export default UploadAttendanceModal;
