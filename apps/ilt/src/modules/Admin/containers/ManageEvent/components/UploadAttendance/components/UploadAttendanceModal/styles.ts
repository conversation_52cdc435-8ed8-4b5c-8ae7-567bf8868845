import styled from 'styled-components';

import Loader from '@mindtickle/loader';
import Modal from '@mindtickle/modal';
import { tokens, mixins, theme } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS } from '~/config/constants';

export const StyledModalFooter = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: flex-end;

  background-color: ${tokens.bgTokens.COLOR_BG_TERTIARY};
  border-radius: 8px;
`;

export const StyledModalTitle = styled.div`
  display: flex;
  padding: 2px 2px;

  .title {
    ${mixins.fontStack({
      fontWeight: theme.fontWeight.SEMIBOLD,
      fontSize: theme.fontSizes.MEDIUM_NUM,
      lineHeight: theme.lineHeight.MEDIUM_NUM,
    })}
    margin-right: 10px;
  }
`;

export const StyledModal = styled(Modal)``;

export const StyledLoader = styled(Loader)`
  padding: 160px 0;
`;

export const uploadIconStyle = {
  height: 180,
  width: 180,
};

const UploadAttendanceModalContainer = styled.div`
  div {
    .upload-icon-container {
      display: flex;
      justify-content: space-around;
      align-items: center;

      margin-top: 20px;
    }
  }

  .drop-file-text {
    ${mixins.fontStack({
      color: tokens.textTokens.COLOR_TEXT_DEFAULT,
      fontWeight: theme.fontWeight.REGULAR,
      fontSize: theme.fontSizes.TEXT,
      lineHeight: theme.lineHeight.TEXT,
    })}
    margin-bottom: 8px;
  }
  .uploadButton {
    .${THEME_PREFIX_CLS}-upload.${THEME_PREFIX_CLS}-upload-select {
      &,
      .${THEME_PREFIX_CLS}-upload {
        padding-top: 8px;
      }
      padding-bottom: 24px;
    }
  }
  .type-text {
    ${mixins.fontStack({
      color: tokens.textTokens.COLOR_TEXT_SECONDARY,
      fontWeight: theme.fontWeight.REGULAR,
      fontSize: theme.fontSizes.SMALLTEXT,
      lineHeight: theme.lineHeight.SMALLTEXT,
    })}
  }
  .advice-text {
    ${mixins.fontStack({
      color: tokens.textTokens.COLOR_TEXT_DEFAULT,
      fontWeight: theme.fontWeight.REGULAR,
      fontSize: theme.fontSizes.SMALLTEXT,
      lineHeight: theme.lineHeight.SMALLTEXT,
    })}
    margin: 24px 105px;

    .note {
      ${mixins.fontStack({
        fontWeight: theme.fontWeight.SEMIBOLD,
        fontSize: theme.fontSizes.SMALLTEXT,
        lineHeight: theme.lineHeight.SMALLTEXT,
      })}
    }
    .note-list {
      text-align: left;
    }
  }
`;

export default UploadAttendanceModalContainer;
