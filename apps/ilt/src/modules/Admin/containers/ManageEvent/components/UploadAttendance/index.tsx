import { ATTENDANCE } from '~/modules/Admin/config/track.constants';
import { getExportFileName, getEventExportRosterData } from '~/modules/Admin/utils';
import { exportFile } from '~/utils/xlsx';

import UploadAttendanceModal from './components/UploadAttendanceModal';

import type { TUploadAttendance } from './typeDefs';

export default function UploadAttendance({
  onClose,
  allLearnersForSampleFile,
  allLearnersFetchStatus,
  sessionDetails,
  updateAttendance,
  isUIDEnabled,
}: TUploadAttendance) {
  const downloadSample = () => {
    const filename = getExportFileName(sessionDetails.name);
    const learners = allLearnersForSampleFile.map((learner: any) => ({
      ...learner,
      eventName: sessionDetails.name,
    }));
    let data = getEventExportRosterData(learners);
    return exportFile(filename, data);
  };

  const onUpdateAttendance = (learners: Array<any> = []) => {
    learners = learners.map(learner => {
      const attendedStatus = learner.attendedStatus && ATTENDANCE[learner.attendedStatus].value;
      return { ...learner, attendedStatus, sessionId: sessionDetails.id };
    });

    updateAttendance({ learners, isViaUpload: true }); // isViaUpload used for tracking and toast purpose
  };

  return (
    <UploadAttendanceModal
      downloadSample={downloadSample}
      sampleDataStatus={allLearnersFetchStatus}
      onClose={onClose}
      onUpdateAttendance={onUpdateAttendance}
      isUIDEnabled={isUIDEnabled}
    />
  );
}
