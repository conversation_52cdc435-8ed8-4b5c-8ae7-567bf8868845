export const learnerDetailsCol = {
  title: 'Learner',
  dataIndex: 'learnerDetails',
  width: '20%',
  sorter: true,
  sortDirections: ['ascend', 'descend', 'ascend'],
  defaultSortOrder: 'ascend',
};

export const enrollWaitlistCol = {
  title: 'Enrollment/waitlist status',
  dataIndex: 'enrollmentWaitliststatus',
  width: '14%',
};

export const enrolledCol = {
  title: 'Enrolled',
  dataIndex: 'enrolled',
  width: '7%',
};

export const waitlistedCol = {
  title: 'Waitlisted',
  dataIndex: 'waitListed',
  width: '7%',
};

export const attendanceCol = {
  title: 'Attendance status',
  dataIndex: 'attendanceStatus',
  width: '12%',
};

export const completionCol = {
  title: 'Completion',
  dataIndex: 'completion',
  width: '8%',
};

export const scoreCol = {
  title: 'Score',
  dataIndex: 'score',
  width: '8%',
};

export const enrollWaitlistedOnCol = {
  title: 'Enrolled/waitlisted on',
  dataIndex: 'enrolledWaitlistedOn',
  width: '14%',
  sorter: true,
  sortDirections: ['ascend', 'descend', 'ascend'],
};

export const learnerOperationCol = {
  title: '',
  dataIndex: 'learnerOperations',
  width: '5%',
};

export const TABLE_SORT_KEYS = {
  learnerDetails: 'name',
  invitedOn: 'invitedOn',
  enrolledWaitlistedOn: 'joinedOn',
  score: 'iltScore',
};
