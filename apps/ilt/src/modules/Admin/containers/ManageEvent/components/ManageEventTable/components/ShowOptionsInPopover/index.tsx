import PopoverOptions from '../PopoverOptions';

import { StyledPopover } from './styles';

const ShowOptionsInPopover = ({
  children,
  title,
  options,
}: {
  children: any;
  title: JSX.Element;
  options: { id: string; name: string; startTime: string }[];
}): JSX.Element => (
  <StyledPopover content={<PopoverOptions options={options} />} title={title}>
    {children}
  </StyledPopover>
);
export default ShowOptionsInPopover;
