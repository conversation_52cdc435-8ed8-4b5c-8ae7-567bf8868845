import Button from '@mindtickle/button';
import Search from '@mindtickle/search';
import SelectWithSearch from '@mindtickle/select-with-search';

import RemoteGroupFilter from '~/components/RemoteGroupFilter';

import { FiltersContainer } from './styles';

import type { TManageEventFilters } from './typeDefs';

export default function ManageEventFilters({ filtersConfig, onReset }: TManageEventFilters) {
  return (
    <FiltersContainer>
      <SelectWithSearch
        buttonLabel={filtersConfig.ENROLLMENT_FILTER.placeholder}
        value={filtersConfig.ENROLLMENT_FILTER.value}
        isButton
        noneLabel={'All'}
        showSearch={false}
        onChange={filtersConfig.ENROLLMENT_FILTER.onChange}
        options={filtersConfig.ENROLLMENT_FILTER.options}
        className=""
      />
      <RemoteGroupFilter
        values={filtersConfig.GROUP_FILTER.values}
        onChange={filtersConfig.GROUP_FILTER.onChange}
      />
      <Search
        placeholder={filtersConfig.SEARCH.placeholder}
        value={filtersConfig.SEARCH.value}
        onSearch={filtersConfig.SEARCH.onSearch}
      />
      <Button className="reset-button" type="text" onClick={onReset}>
        Reset filters
      </Button>
    </FiltersContainer>
  );
}
