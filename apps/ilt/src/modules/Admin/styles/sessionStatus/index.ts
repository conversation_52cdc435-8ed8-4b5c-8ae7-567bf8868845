import styled from 'styled-components';

import { BadgeWithStatus } from '@mindtickle/badge';
import { tokens, mixins } from '@mindtickle/styles/lib';

export const StyledLiveSession = styled.div`
  ${mixins.activeBlackLink}
  background-color: ${tokens.bgTokens.COLOR_BG_SUCCESS_STRONG};
  display: flex;
  align-items: center;
  border-radius: 4px;
  padding: 2px 8px 3px;
  .dot {
    background-color: ${tokens.bgTokens.COLOR_BG_DEFAULT};
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }
  .live-text {
    ${mixins.whiteText}
    font-weight: 600;
    padding-left: 4px;
    letter-spacing: 1px;
  }
`;

export const StyledUpcomingSessionToken = styled(BadgeWithStatus)`
  ${mixins.activeBlackLink};
`;

export const StyledPastSessionToken = styled(BadgeWithStatus)`
  color: ${tokens.textTokens.COLOR_TEXT_SECONDARY};
`;

export const StyledCancelledSessionToken = styled(BadgeWithStatus)`
  ${mixins.activeBlackLink};
`;

export const StyledStyledUnpublishedSessionToken = styled(BadgeWithStatus)`
  ${mixins.activeBlackLink};
`;
