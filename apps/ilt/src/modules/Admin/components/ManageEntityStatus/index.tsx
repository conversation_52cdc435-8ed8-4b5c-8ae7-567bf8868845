import React from 'react';

import { BG_SHADE, STATUS_TYPE } from '@mindtickle/badge';
import Icon, { ICON_MAP } from '@mindtickle/icon';
import Tooltip from '@mindtickle/tooltip';

import { SESSION_STATUS_TYPE_LABELS } from '~/modules/Admin/config/sessions.constants';
import {
  StyledLiveSession,
  StyledCancelledSessionToken,
  StyledPastSessionToken,
  StyledUpcomingSessionToken,
} from '~/modules/Admin/styles/sessionStatus';

import { cancelledIconStyle } from './styles';

import type { TManageEntityStatusProps } from './typeDefs';

const ManageEntityStatus = ({ entityDetails }: TManageEntityStatusProps) => {
  const { isOver, isOngoing, isUpcoming, isCancelled, cancellationReason } = entityDetails;

  if (isCancelled) {
    return (
      <React.Fragment>
        <StyledCancelledSessionToken bgShade={BG_SHADE.LIGHT} status={STATUS_TYPE.STOP}>
          {SESSION_STATUS_TYPE_LABELS.CANCELLED}
        </StyledCancelledSessionToken>
        <Tooltip
          key={`cancelled`}
          title={`Reason for cancelation: ${cancellationReason}`}
          placement={'top'}
          trigger={'hover'}
        >
          <Icon style={cancelledIconStyle} type={ICON_MAP.INFO2}></Icon>
        </Tooltip>
      </React.Fragment>
    );
  } else if (isUpcoming) {
    return (
      <StyledUpcomingSessionToken bgShade={BG_SHADE.LIGHT} status={STATUS_TYPE.SUCCESS}>
        {SESSION_STATUS_TYPE_LABELS.UPCOMING}
      </StyledUpcomingSessionToken>
    );
  } else if (isOngoing) {
    return (
      <StyledLiveSession>
        <div className={'dot'} />
        <div className={'live-text'}>{SESSION_STATUS_TYPE_LABELS.LIVE}</div>
        <div className="clear" />
      </StyledLiveSession>
    );
  } else if (isOver) {
    return (
      <StyledPastSessionToken bgShade={BG_SHADE.LIGHT} status={STATUS_TYPE.PROCESSING}>
        {SESSION_STATUS_TYPE_LABELS.PAST}
      </StyledPastSessionToken>
    );
  }

  return <React.Fragment></React.Fragment>;
};

export default ManageEntityStatus;
