import { Suspense, lazy } from 'react';

import Loader from '@mindtickle/loader';
import { noop } from '@mindtickle/utils';

import { PAGE_LOAD_DATA } from '~/modules/Admin/config/constants';
import createWithPageTimeSpent from '~/modules/Admin/hocs/withPageTimeSpent';

import type { TPublishWrapperProps } from './typeDefs';

const PublishDraft = lazy(
  () => import(/* webpackChunkName: "ilt-publishdraft" */ '~/modules/Admin/containers/PublishDraft')
);

const PublishHistory = lazy(
  () =>
    import(/* webpackChunkName: "ilt-publishhistory" */ '~/modules/Admin/containers/PublishHistory')
);

function PublishWrapper(props: TPublishWrapperProps) {
  const {
    isPublished,
    onPublish,
    moduleId,
    seriesId,
    companyId,
    moduleType,
    defaultInviteOption,
    showInviteOptions,
  } = props;

  return isPublished ? (
    <Suspense fallback={<Loader size="sizeSmall" />}>
      <PublishHistory
        moduleId={moduleId}
        seriesId={seriesId}
        companyId={companyId}
        moduleType={moduleType}
        showInviteOptions={showInviteOptions}
        defaultInviteOption={defaultInviteOption}
        onPublish={onPublish}
      />
    </Suspense>
  ) : (
    <Suspense fallback={<Loader size="sizeSmall" />}>
      <PublishDraft
        moduleId={moduleId}
        seriesId={seriesId}
        companyId={companyId}
        onPublish={onPublish}
      />
    </Suspense>
  );
}

PublishWrapper.defaultProps = {
  onPublish: noop,
};

export default createWithPageTimeSpent({ pageName: PAGE_LOAD_DATA.ILT_PUBLISH_PAGE.PAGE_NAME })(
  PublishWrapper
);
