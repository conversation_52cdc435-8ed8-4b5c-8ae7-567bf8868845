import { Link, useParams } from 'react-router-dom';

import { InlineSkeleton } from '@mindtickle/skeleton';
import Tooltip from '@mindtickle/tooltip';
import { noop } from '@mindtickle/utils';

import { DROPDOWN_TRIGGER_OPTIONS, DROPDOWN_PLACEMENT } from './constants';
import { StyledDropdown, SeriesNameContainer, skeletonStyle } from './styles';

import type { TSeriesDropdown } from './typeDefs';

const SeriesDropdown = ({
  sessionId,
  eventId,
  seriesIds = [],
  seriesInfoMap: {
    isLoading: isSeriesMapLoading,
    data: seriesMap = {},
    hasError: seriesMapHasError,
  } = {},
}: TSeriesDropdown) => {
  const { moduleId } = useParams();
  const options: Array<{ key: string; content: JSX.Element }> = [];

  if (isSeriesMapLoading) return <InlineSkeleton active={false} style={skeletonStyle} />;

  if (seriesMapHasError) {
    options.push({
      content: <div>{'Something went wrong'}</div>,
      key: 'somethingWentWrong',
    });
  } else {
    const urlSuffix = `sessions/manage/${
      eventId ? `event/${eventId}${sessionId ? `/session/${sessionId}` : ''}` : sessionId
    }`;
    seriesIds.forEach(seriesId => {
      const { name: seriesName, hasAccess: hasAccessToSeries = false } = seriesMap[seriesId] || {};

      const option = {
        key: seriesId,
        content: hasAccessToSeries ? (
          <Link to={`/new/ui/admin/ilt/${seriesId}/${moduleId}/${urlSuffix}`} target="_blank">
            <SeriesNameContainer>{seriesName}</SeriesNameContainer>
          </Link>
        ) : (
          <Tooltip title={'You do not have access to this series'}>
            <SeriesNameContainer>{seriesName}</SeriesNameContainer>
          </Tooltip>
        ),
      };
      options.push(option);
    });
  }
  return (
    <StyledDropdown
      trigger={DROPDOWN_TRIGGER_OPTIONS}
      options={options}
      onSelect={noop}
      placement={DROPDOWN_PLACEMENT}
      getPopupContainer={(triggerNode: HTMLElement) => triggerNode.parentElement?.parentElement}
    >
      <span className="link">{`${seriesIds.length} other series`}</span>
    </StyledDropdown>
  );
};

export default SeriesDropdown;
