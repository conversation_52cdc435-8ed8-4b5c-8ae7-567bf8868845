import { FormattedMessage } from 'react-intl';

import { StyledLoader } from '../SessionsWrapper/styles';

import { getOperationMessage, checkIsFullLoaderOperations } from './utils';

import type { TEntitiesOperationModeLoaderProps } from './typeDefs';
export function EntitiesOperationModeLoader({
  operationStatus,
}: TEntitiesOperationModeLoaderProps) {
  const { loadingData: { operation = '' } = {}, isLoading = false } = operationStatus || {};
  if (!isLoading || !operation || !checkIsFullLoaderOperations(operation)) {
    return null;
  }
  const message = getOperationMessage(operation);
  return (
    <StyledLoader
      size="sizeBig"
      type="Full"
      message={message ? <FormattedMessage {...message} /> : ''}
    />
  );
}
