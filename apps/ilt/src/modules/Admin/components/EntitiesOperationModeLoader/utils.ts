import { OPERATIONS } from '~/modules/Admin/config/sessions.constants';

import messages from './messages';

export function checkIsFullLoaderOperations(operation: string) {
  switch (operation) {
    case OPERATIONS.UPDATE:
    case OPERATIONS.ADD:
    case OPERATIONS.ADD_BULK:
    case OPERATIONS.UPDATE_EVENT:
    case OPERATIONS.ADD_EVENT:
    case OPERATIONS.COPY_EVENT:
      return true;
  }
  return false;
}

export function getOperationMessage(operation: string) {
  if (operation === OPERATIONS.UPDATE) {
    return messages.UPDATE_SESSION;
  }
  if (operation === OPERATIONS.ADD) {
    return messages.CREATE_SESSION;
  }
  if (operation === OPERATIONS.ADD_BULK) {
    return messages.CREATE_SESSIONS;
  }
  if (operation === OPERATIONS.UPDATE_EVENT) {
    return messages.UPDATE_EVENT;
  }
  if (operation === OPERATIONS.ADD_EVENT) {
    return messages.CREATE_EVENT;
  }
  if (operation === OPERATIONS.COPY_EVENT) {
    return messages.COPY_EVENT;
  }
  return null;
}
