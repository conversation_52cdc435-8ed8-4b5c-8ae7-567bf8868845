import styled from 'styled-components';

import Loader from '@mindtickle/loader';
import { tokens, mixins, theme } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS } from '~/config/constants';

export const UploadSessionModalContainer = styled.div`
  div {
    .upload-icon-container {
      display: flex;
      justify-content: space-around;
      align-items: center;

      margin-top: 20px;
    }
  }

  .drop-file-text {
    ${mixins.fontStack({
      color: tokens.textTokens.COLOR_TEXT_DEFAULT,
      fontWeight: theme.fontWeight.REGULAR,
      fontSize: theme.fontSizes.TEXT,
      lineHeight: theme.lineHeight.TEXT,
    })}
    margin-bottom: 8px;
  }
  .uploadButton {
    .${THEME_PREFIX_CLS}-upload.${THEME_PREFIX_CLS}-upload-select {
      &,
      .${THEME_PREFIX_CLS}-upload {
        padding-top: 8px;
      }
      padding-bottom: 24px;
    }
  }
  .type-text {
    ${mixins.fontStack({
      color: tokens.textTokens.COLOR_TEXT_SECONDARY,
      fontWeight: theme.fontWeight.REGULAR,
      fontSize: theme.fontSizes.SMALLTEXT,
      lineHeight: theme.lineHeight.SMALLTEXT,
    })}
  }
  .advice-text {
    ${mixins.fontStack({
      color: tokens.textTokens.COLOR_TEXT_DEFAULT,
      fontWeight: theme.fontWeight.REGULAR,
      fontSize: theme.fontSizes.SMALLTEXT,
      lineHeight: theme.lineHeight.SMALLTEXT,
    })}
    margin: 24px 130px;
    text-align: left;

    .note {
      ${mixins.fontStack({
        fontWeight: theme.fontWeight.SEMIBOLD,
        fontSize: theme.fontSizes.SMALLTEXT,
        lineHeight: theme.lineHeight.SMALLTEXT,
      })}
    }
  }
`;

export const StyledLoader = styled(Loader)`
  padding: 160px 0;
`;

export const uploadIconStyle = {
  height: 180,
  width: 180,
};
