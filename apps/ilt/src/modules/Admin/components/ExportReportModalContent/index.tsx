import React, { useCallback } from 'react';

import Button, { BUTTON_TYPES } from '@mindtickle/button';
import Form from '@mindtickle/form';
import Input from '@mindtickle/input';

import { RuleMapping } from './constants';
import { ContentContainer } from './styles';

import type { TExportModalContent } from './typeDefs';

const ExportReportModalContent = ({
  email,
  setEmail,
  onCancel,
  onOk,
  cancelButtonText,
  okButtonText,
}: TExportModalContent) => {
  const [form] = Form.useForm();
  const onChange = useCallback(
    (e: React.FormEvent<HTMLInputElement>) => {
      const text = e.currentTarget.value;
      form
        .validateFields()
        .then(() => {
          setEmail(text);
        })
        .catch(() => {
          setEmail('');
        });
    },
    [form, setEmail]
  );
  return (
    <ContentContainer>
      <div className="content-wrapper-style">
        <div className="content-text">Detailed report will be sent to the following email ID</div>
        <Form form={form}>
          <Form.Item name={'email'} rules={RuleMapping['email']}>
            <Input value={email} onChange={onChange} placeholder="Enter email" />
          </Form.Item>
        </Form>
      </div>
      <div className="footer">
        <Button className="cancel-button" type={BUTTON_TYPES.TEXT} onClick={onCancel}>
          {cancelButtonText}
        </Button>
        <Button type={BUTTON_TYPES.PRIMARY} disabled={!Boolean(email)} onClick={onOk}>
          {okButtonText}
        </Button>
      </div>
    </ContentContainer>
  );
};

export default ExportReportModalContent;
