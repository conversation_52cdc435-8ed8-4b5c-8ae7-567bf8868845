import Icon, { ICON_MAP } from '@mindtickle/icon';

import { StyledGoBackToDashboard } from './styles';

import type { TGoBackProps } from './typeDefs';

const GoBack = ({ label, goToDestination }: TGoBackProps) => (
  <StyledGoBackToDashboard onClick={goToDestination}>
    <Icon type={ICON_MAP.LEFT_ARROW} className="go-back-icon" />
    <div className="go-back">{label}</div>
  </StyledGoBackToDashboard>
);

export default GoBack;
