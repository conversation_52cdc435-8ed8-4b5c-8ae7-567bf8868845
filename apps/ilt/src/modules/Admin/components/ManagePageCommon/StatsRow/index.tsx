import React from 'react';

import Icon, { ICON_MAP } from '@mindtickle/icon';
import { InlineSkeleton } from '@mindtickle/skeleton';

import SeriesDropdown from '~/modules/Admin/components/SeriesDropdown';
import { DOUBLE_DASH } from '~/modules/Admin/constants';

import { grayIconStyle, circleIconStyle } from '../styles';
import { checkHasMultiPresentInfo } from '../utils';

import { skeletonStyle } from './styles';

import type { TSeriesInfoMap, TEntityStatsFull } from '../typeDefs';

const EnrollmentCount = ({
  count,
  areStatsLoading,
}: {
  count: string | number | undefined;
  areStatsLoading: boolean;
}) =>
  areStatsLoading ? (
    <InlineSkeleton active={false} style={skeletonStyle} />
  ) : (
    <span>{count || count === 0 ? count : DOUBLE_DASH}</span>
  );

const StatsRow = ({
  sessionId,
  eventId,
  entityStats,
  seriesInfoMap,
  areStatsLoading,
  freezeStats,
  showWaitingHyphened,
}: {
  sessionId: string;
  eventId: string;
  entityStats?: TEntityStatsFull;
  seriesInfoMap: TSeriesInfoMap;
  areStatsLoading: boolean;
  freezeStats?: React.ReactNode;
  showWaitingHyphened?: boolean;
}) => {
  const {
    enrolled,
    waiting,
    attended,
    waitingViaThisSeries,
    enrolledViaThisSeries,
    multipresentSeriesIds = [],
  } = entityStats || {};

  const { showEnrolledViaThisSeries, showWaitingViaThisSeries, showMultiseriesInfo } =
    checkHasMultiPresentInfo(entityStats);

  return (
    <React.Fragment>
      <div className="info-box">
        <Icon type="group" style={grayIconStyle}></Icon>
        <div className="count-text">
          <span>{'Enrolled: '}</span>
          <EnrollmentCount count={enrolled} areStatsLoading={areStatsLoading} />
          {showEnrolledViaThisSeries && <span>{`(${enrolledViaThisSeries} in this series)`}</span>}
          <span>{' · Attended: '}</span>
          <EnrollmentCount count={attended} areStatsLoading={areStatsLoading} />
          <span>{' · Waiting: '}</span>
          {showWaitingHyphened ? (
            <span>{DOUBLE_DASH}</span>
          ) : (
            <>
              <EnrollmentCount count={waiting} areStatsLoading={areStatsLoading} />
              {showWaitingViaThisSeries && (
                <span>{` (${waitingViaThisSeries} in this series)`}</span>
              )}
            </>
          )}
        </div>
      </div>
      {showMultiseriesInfo && (
        <React.Fragment>
          <Icon type={ICON_MAP.CIRCLE} style={circleIconStyle} />
          <div className="info-box">
            <Icon type={ICON_MAP.MULTIPRESENT} style={grayIconStyle} />
            <div className="count-text">
              <span>{`Learners in`}</span>
              <SeriesDropdown
                sessionId={sessionId}
                eventId={eventId}
                seriesIds={multipresentSeriesIds}
                seriesInfoMap={seriesInfoMap}
              />
            </div>
          </div>
        </React.Fragment>
      )}
      {freezeStats}
    </React.Fragment>
  );
};

export default StatsRow;
