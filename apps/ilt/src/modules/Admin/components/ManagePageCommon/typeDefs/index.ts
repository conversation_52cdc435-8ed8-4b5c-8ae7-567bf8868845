export type TEntityStatsFull = {
  loaded?: boolean;
  attended: number;
  enrolled: number;
  waiting: number;
  waitingViaThisSeries: number;
  enrolledViaThisSeries: number;
  multipresentSeriesIds: string[];
  hasMultiseriesStats: boolean;
  learnerOtherSeriesMap?: {
    [userId: string]: string[];
  };
};

export type TSeriesInfoMap = Partial<{
  isLoading: boolean;
  loaded: boolean;
  hasError: boolean;
  partial: boolean;
  data: {
    [key: string]: {
      id: string;
      name: string;
      hasAccess: boolean;
    };
  };
}>;

export type TEventDetails = any;
export type TSessionDetails = any;
