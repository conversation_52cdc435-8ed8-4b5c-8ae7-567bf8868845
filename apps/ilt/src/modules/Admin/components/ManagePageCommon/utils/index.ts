import type { TEntityStatsFull } from '../typeDefs';
export function checkHasMultiPresentInfo(entityStats?: TEntityStatsFull) {
  const {
    enrolled,
    waiting,
    waitingViaThisSeries,
    enrolledViaThisSeries,
    hasMultiseriesStats = false,
  } = entityStats || {};
  const showEnrolledViaThisSeries = hasMultiseriesStats && enrolled !== enrolledViaThisSeries;
  const showWaitingViaThisSeries = hasMultiseriesStats && waiting !== waitingViaThisSeries;
  const showMultiseriesInfo = showEnrolledViaThisSeries || showWaitingViaThisSeries;
  return {
    showEnrolledViaThisSeries,
    showWaitingViaThisSeries,
    showMultiseriesInfo,
  };
}
