import React from 'react';

import { FormattedMessage } from 'react-intl';

import Icon, { ICON_MAP } from '@mindtickle/icon';
import Tooltip from '@mindtickle/tooltip';

import { ENROLLMENT_LOCK_WARNING } from '~/modules/Admin/config/sessions.constants';
import { EVENT_ENROLLMENT_ENUM } from '~/modules/Admin/constants/events';
import useEnrolmentFreezeDetails from '~/modules/Admin/hooks/useEnrolmentFreezeDetails';
import { MESSAGES_SESSION_INFO } from '~/modules/Admin/messages/sessions';

import { grayIconStyle, circleIconStyle } from '../styles';

import { infoIconStyle } from './styles';

import type { TEventDetails, TSessionDetails } from '../typeDefs';

const EnrollmentFreezeStats = ({
  sessionDetails,
  eventDetails,
  showCircleIcon,
}: {
  sessionDetails?: TSessionDetails;
  eventDetails?: TEventDetails;
  showCircleIcon: boolean;
}) => {
  const isOnlyEvent =
    (!sessionDetails || !sessionDetails.id) && eventDetails && eventDetails.id ? true : false;
  const {
    enrollmentFreezeStatus,
    enrollmentFreezeEpoch,
    timezone = {},
    entityType,
  } = sessionDetails || {};
  const {
    enrollmentType: eventEnrollmentType,
    enrollmentFreezeStatus: eventEnrollmentFreezeStatus,
    enrollmentFreezeDaysBeforeEvent: eventEnrollmentFreezeDaysBeforeEvent,
    enrollmentFreezeTimezone: eventEnrollmentFreezeTimezone,
    enrollmentFreezeEpoch: eventEnrollmentFreezeEpoch,
    startTime: eventStartTime,
    entityType: eventEntityType,
  } = eventDetails || {};
  const isEventEnrollmentFreezeApplicable = eventEnrollmentType === EVENT_ENROLLMENT_ENUM.EVENT;

  const {
    enrollmentFreezeEnabled,
    isEnrollmentFrozen,
    enrollmentFreezeDateTime,
    isRowEligibleForEnrolmentFreeze,
  } = useEnrolmentFreezeDetails({
    entityType: isOnlyEvent ? eventEntityType : entityType,
    timezone,
    enrollmentFreezeStatus: isEventEnrollmentFreezeApplicable
      ? eventEnrollmentFreezeStatus
      : enrollmentFreezeStatus,
    enrollmentFreezeEpoch: isEventEnrollmentFreezeApplicable
      ? eventEnrollmentFreezeEpoch
      : enrollmentFreezeEpoch,
    enrollmentFreezeTimezone: eventEnrollmentFreezeTimezone,
    eventEnrollmentType,
    enrollmentFreezeDaysBeforeEvent: eventEnrollmentFreezeDaysBeforeEvent,
    eventStartTime,
  });

  if (!enrollmentFreezeEnabled || !isRowEligibleForEnrolmentFreeze)
    return <React.Fragment></React.Fragment>;
  return (
    <React.Fragment>
      {showCircleIcon && <Icon type={ICON_MAP.CIRCLE} style={circleIconStyle} />}
      <div className="info-box">
        <Icon
          type={isEnrollmentFrozen ? ICON_MAP.LOCKED : ICON_MAP.UNLOCKED}
          style={grayIconStyle}
        />
        <div className="count-text">
          {isEnrollmentFrozen ? (
            <span>
              {isOnlyEvent
                ? ENROLLMENT_LOCK_WARNING.NO_DATE_EVENT
                : ENROLLMENT_LOCK_WARNING.NO_DATE}
            </span>
          ) : (
            <React.Fragment>
              <FormattedMessage {...MESSAGES_SESSION_INFO.ENROLL_FREEZE_WARNING} />
              <span>{enrollmentFreezeDateTime}</span>
            </React.Fragment>
          )}
        </div>
        <Tooltip
          title={
            <FormattedMessage
              {...(isOnlyEvent
                ? MESSAGES_SESSION_INFO.ENROLLMENT_FREEZE_SETTING_EVENT
                : MESSAGES_SESSION_INFO.ENROLLMENT_FREEZE_SETTING)}
            />
          }
        >
          <Icon type={ICON_MAP.INFO2} style={infoIconStyle}></Icon>
        </Tooltip>
      </div>
    </React.Fragment>
  );
};

export default EnrollmentFreezeStats;
