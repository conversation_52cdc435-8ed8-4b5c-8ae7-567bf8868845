import styled from 'styled-components';

import { tokens } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS } from '~/config/constants';

export const ShareLiveChallengeActions = styled.div`
  display: flex;
  flex-direction: row-reverse;
  width: 100%;

  button {
    margin-left: 8px !important;
    padding: 0 8px !important;
  }
`;

export const StyledShareLiveChallengeModalContainer = styled.div``;

export const SelectEmails = styled.div`
  width: 100%;
  margin-top: 20px;

  .${THEME_PREFIX_CLS}-select-selection {
    border: 1px solid ${tokens.borderTokens.COLOR_BORDER_STRONG} !important;
    border-radius: 4px;
  }

  div.${THEME_PREFIX_CLS}-select {
    width: 100%;
  }
`;

export const EmailError = styled.div`
  color: red;
`;

export const ShareLiveChallengeDescription = styled.div`
  color: ${tokens.textTokens.COLOR_TEXT_SECONDARY};
`;
