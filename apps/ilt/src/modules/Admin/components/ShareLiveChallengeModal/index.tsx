import { useCallback, useEffect, useRef, useState } from 'react';

import emailValidator from 'email-validator';
import { FormattedMessage } from 'react-intl';
import { useDispatch, useSelector } from 'react-redux';

import Button from '@mindtickle/button';
import { getActions } from '@mindtickle/medux/Action';
import Modal, { MODAL_SIZE } from '@mindtickle/modal';
import Select from '@mindtickle/select';
import { errorToast, successToast } from '@mindtickle/toast';

import { MASKED_MODAL_INDEX } from '~/config/constants';
import { MANAGE_LIVE_CHALLENGE } from '~/modules/Admin/actionTypes';

import { LC_MODAL_DIALOG, TOAST_MESSAGES, getLCOperation } from './constants';
import {
  EmailError,
  ShareLiveChallengeActions,
  ShareLiveChallengeDescription,
  SelectEmails,
  StyledShareLiveChallengeModalContainer,
} from './styles';
import { TShareLiveChallengeModal } from './typeDefs';

const ShareLiveChallengeModal = ({
  onCancel,
  sessionId,
  lcUniqueCode,
  action,
}: TShareLiveChallengeModal) => {
  const modalContainerRef = useRef<HTMLDivElement>(null);

  const [emailIds, setEmailIds] = useState<string[]>([]);
  const [error, setError] = useState(false);
  const LCOperation = getLCOperation(action);

  const dispatch = useDispatch();

  const emailInput = useRef('');

  const operationStatus = useSelector((state: any) => state.ilt.liveChallenge.operationStatus);
  const inProgress = operationStatus && operationStatus.isLoading;

  const resetOperation = useCallback(() => {
    dispatch(getActions({ name: MANAGE_LIVE_CHALLENGE, options: { async: true } }).RESET());
  }, [dispatch]);

  const closeModal = useCallback(() => {
    !inProgress && onCancel();
    resetOperation();
  }, [inProgress, onCancel, resetOperation]);

  useEffect(() => {
    if (
      operationStatus &&
      ((operationStatus.data &&
        operationStatus.data.operation === LCOperation &&
        operationStatus.loaded) ||
        (operationStatus.error &&
          operationStatus.error.data.operation === LCOperation &&
          operationStatus.hasError))
    ) {
      closeModal();
      if (operationStatus.hasError) {
        errorToast({ message: <FormattedMessage {...TOAST_MESSAGES[action].errorMessage} /> });
      } else if (operationStatus.loaded) {
        successToast({ message: <FormattedMessage {...TOAST_MESSAGES[action].successMessage} /> });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps -- rest dependencies are constants
  }, [closeModal, operationStatus]);

  const handleEmailChange = (emailIds: string[]) => {
    emailInput.current = '';
    if (!emailIds || !Array.isArray(emailIds)) {
      setError(true);
      return;
    }

    const invalidEmails = emailIds.filter(email => !emailValidator.validate(email));
    emailIds = emailIds.filter(email => emailValidator.validate(email));
    if (invalidEmails.length > 0 || emailIds.length === 0) {
      setError(true);
    } else {
      setError(false);
    }
    setEmailIds(emailIds);
  };

  const handleEmailType = (emailId: string) => {
    emailInput.current = emailId;
  };

  const sendLinkEmail = () => {
    if (emailIds.length > 0 && !inProgress && emailInput.current === '') {
      dispatch(
        getActions(MANAGE_LIVE_CHALLENGE)({
          sessionId,
          emailIds: emailIds,
          operation: LCOperation,
          ...(lcUniqueCode && { uniqueCode: lcUniqueCode }),
        })
      );
    }
  };

  const ModalFooter = () => (
    <ShareLiveChallengeActions>
      <Button
        onClick={() => sendLinkEmail()}
        disabled={emailIds.length === 0 || inProgress}
        type="primary"
      >
        <FormattedMessage {...LC_MODAL_DIALOG[action].OK_TEXT} />
      </Button>
      <Button type="text" onClick={() => closeModal()} disabled={inProgress}>
        <FormattedMessage {...LC_MODAL_DIALOG[action].CANCEL_TEXT} />
      </Button>
    </ShareLiveChallengeActions>
  );

  const EmailModalTitle = () => <FormattedMessage {...LC_MODAL_DIALOG[action].TITLE} />;

  const EmailModalContent = () => (
    <div>
      <ShareLiveChallengeDescription>
        <FormattedMessage {...LC_MODAL_DIALOG[action].DESCRIPTION} />
      </ShareLiveChallengeDescription>

      <SelectEmails>
        <Select
          mode="tags"
          autoFocus
          value={emailIds}
          tokenSeparators={[',']}
          allowClear
          autoClearSearchValue={false}
          dropdownStyle={{ display: 'none' }}
          onChange={handleEmailChange}
          onSearch={handleEmailType}
          placeholder="Enter email ids separated with comma"
          disabled={false}
        />
      </SelectEmails>
      {error && <EmailError>Please enter a valid email ID</EmailError>}
    </div>
  );

  return (
    <StyledShareLiveChallengeModalContainer ref={modalContainerRef}>
      <Modal
        title={<EmailModalTitle />}
        visible={true}
        onOk={() => sendLinkEmail()}
        onCancel={onCancel}
        centered={true}
        getPopupContainer={() => modalContainerRef.current}
        maskClosable={false}
        keyboard={false}
        size={MODAL_SIZE.SMALL}
        footer={<ModalFooter />}
        zIndex={MASKED_MODAL_INDEX}
      >
        <EmailModalContent />
      </Modal>
    </StyledShareLiveChallengeModalContainer>
  );
};

export default ShareLiveChallengeModal;
