import {
  LC_EMAIL,
  LC_EXPORT,
  LC_MODAL_ACTIONS,
  LC_OPERATIONS,
  LIVE_CHALLENGE,
} from '~/modules/Admin/config/live-challenges.constants';

export function getLCOperation(action: string) {
  return action === LC_MODAL_ACTIONS.EXPORT_REPORT
    ? LC_OPERATIONS.LC_EXPORT_REPORT
    : LC_OPERATIONS.LC_EMAIL_LINKS;
}

export const TOAST_MESSAGES = {
  [LC_MODAL_ACTIONS.EXPORT_REPORT]: {
    errorMessage: LIVE_CHALLENGE.EXPORT_FAILED,
    successMessage: LIVE_CHALLENGE.EXPORTED_SUCCESSFULLY,
  },
  [LC_MODAL_ACTIONS.EMAIL_LINKS]: {
    errorMessage: LIVE_CHALLENGE.LINK_SHARE_FAIL,
    successMessage: LIVE_CHALLENGE.LINK_SHARE_SUCCESS,
  },
};

export const LC_MODAL_DIALOG = {
  [LC_MODAL_ACTIONS.EXPORT_REPORT]: {
    OK_TEXT: LC_EXPORT.EMAIL_BTN_TXT,
    CANCEL_TEXT: LC_EXPORT.CANCEL,
    TITLE: LC_EXPORT.TITLE,
    DESCRIPTION: LC_EXPORT.DESCRIPTION,
  },
  [LC_MODAL_ACTIONS.EMAIL_LINKS]: {
    OK_TEXT: LC_EMAIL.EMAIL_BTN_TXT,
    CANCEL_TEXT: LC_EMAIL.CANCEL,
    TITLE: LC_EMAIL.TITLE,
    DESCRIPTION: LC_EMAIL.DESCRIPTION,
  },
};
