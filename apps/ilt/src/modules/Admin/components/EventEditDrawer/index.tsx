import { useEffect, useRef, useMemo } from 'react';

import debounce from 'lodash/debounce';
import { FormattedMessage } from 'react-intl';
// @ts-ignore
import { withUserAuth } from 'ui_shell/Auth';

import Button from '@mindtickle/button';
import Form from '@mindtickle/form';
import Modal, { MODAL_SIZE } from '@mindtickle/modal';
import Scrollbar from '@mindtickle/scrollbar';
import Tabs from '@mindtickle/tabs';

import { MASKED_MODAL_INDEX, THEME_PREFIX_CLS } from '~/config/constants';
import EVENT_INFO_MESSAGES from '~/modules/Admin/messages/events/infoMessages';

import { MODES } from '../../config/sessions.constants';
import { useAttachments } from '../../containers/Attachments/hooks';
import { StyledFooter, StyledToastWrapper } from '../SessionEditDrawer/styles';

import EventDrawerTitle from './components/EventDrawerTitle';
import { DRAWER_TABS, TOAST_WRAPPER_ID, errorFieldsTabsMapping } from './constants';
import { useTabsConfig, useEventDrawerStateAndConstants } from './hooks';
import { StyledEventDrawer, StyledEventDrawerTabContainer } from './styles';

import type { TEventEditDrawer } from './typeDefs';

function EventEditDrawer(props: TEventEditDrawer) {
  const {
    mode,
    inProgress,
    onClose,
    data,
    triggerEditAction,
    update,
    sessionsList = [],
    setShowEventModLoader,
  } = props;

  const {
    isReadOnlyModeEnabled,
    event,
    setEvent,
    activeTab,
    setActiveTab,
    errorTabs,
    setErrorTabs,
    scrollbarMinHeight,
  } = useEventDrawerStateAndConstants(props);

  const [eventForm] = Form.useForm();
  const currentPropsRef = useRef(props);
  const prevPropsRef = useRef(props);
  const tabsConfig = useTabsConfig({
    errorTabs,
    event,
    setEvent,
    isReadOnlyModeEnabled,
    eventForm,
    mode,
    data,
    sessionsCountWithinEvent: sessionsList.length,
  });
  const { dispatchGetMedias } = useAttachments();

  const closeModal = () => {
    setEvent({});
    onClose();
  };

  const onDiscard = () => {
    closeModal();
  };

  const handleDiscardWithConfirmation = () => {
    isReadOnlyModeEnabled
      ? onDiscard()
      : Modal.confirm({
          prefixCls: THEME_PREFIX_CLS,
          content: <FormattedMessage {...EVENT_INFO_MESSAGES.DISCARD_WITH_CONFIRMATION_CONTENT} />,
          centered: true,
          onOk: onDiscard,
          okButtonText: (
            <FormattedMessage {...EVENT_INFO_MESSAGES.DISCARD_WITH_CONFIRMATION_OK_BUTTON} />
          ),
          title: <FormattedMessage {...EVENT_INFO_MESSAGES.DISCARD_WITH_CONFIRMATION_TITLE} />,
          isDestructiveAction: 'true',
          okButtonProps: { type: 'danger' },
          zIndex: MASKED_MODAL_INDEX,
          size: MODAL_SIZE.SMALL,
        });
  };

  const renderActionButtons = () => (
    <StyledFooter>
      <Button type={'text'} onClick={handleDiscardWithConfirmation}>
        Discard
      </Button>
      <Button type="primary" name="eventSubmit" onClick={eventForm.submit}>
        {mode === MODES.EDIT ? 'Save event' : 'Create event'}
      </Button>
    </StyledFooter>
  );

  const navigateToErrorTab = ({ errorFields }: { errorFields: any }) => {
    let errorTab = '';
    const tabKeys = new Set<string>([]);
    if (errorFields && errorFields[0].name[0]) {
      for (let key in errorFieldsTabsMapping) {
        if (
          errorFieldsTabsMapping[key as keyof typeof errorFieldsTabsMapping].includes(
            errorFields[0].name[0].replace(/\d+/g, '') // * Assuming that the error field name will always be in the format of string(FieldName)+index'
          )
        ) {
          errorTab = key;
          break;
        }
      }
    }
    errorTab && setActiveTab(errorTab);
    for (let i = 0; i < errorFields.length; i++) {
      for (let key in errorFieldsTabsMapping) {
        if (
          errorFieldsTabsMapping[key as keyof typeof errorFieldsTabsMapping].includes(
            errorFields[i].name[0].replace(/\d+/g, '') //* Assuming that the error field name will always be in the format of string(FieldName)+index
          )
        ) {
          tabKeys.add(key);
        }
      }
    }
    setErrorTabs(tabKeys);
  };

  const showErrorStatesOnChange = useMemo(() => {
    const debouncedErrorState = debounce((changedFields: any, allFields: any) => {
      const tabKeys = new Set<string>([]);
      const errorFields = allFields.filter((field: any) => field.errors.length > 0);
      for (let i = 0; i < errorFields.length; i++) {
        for (let key in errorFieldsTabsMapping) {
          if (
            errorFieldsTabsMapping[key as keyof typeof errorFieldsTabsMapping].includes(
              errorFields[i].name[0].replace(/\d+/g, '') //* Assuming that the error field name will always be in the format of string(FieldName)+index
            )
          ) {
            tabKeys.add(key);
          }
        }
      }
      setErrorTabs(tabKeys);
    }, 900);
    return debouncedErrorState;
  }, [setErrorTabs]);

  useEffect(() => {
    if (mode === MODES.EDIT || mode === MODES.VIEW) {
      const mediaIds = event.attachments.map((attachment: any) => attachment.id);
      dispatchGetMedias({ mediaIds: mediaIds });
    }
    return () => {
      closeModal();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    // Set the current props to the prev props ref
    prevPropsRef.current = currentPropsRef.current;
    // Update the current props ref
    currentPropsRef.current = props;
  });

  return (
    <Form
      name="event"
      onFinish={(values: any) => {
        setShowEventModLoader(true);
        setErrorTabs(new Set([]));
        update(event);
      }}
      initialValues={{
        eventName: event.name,
        completionCriteria: event.completionCriteria,
      }}
      onFinishFailed={navigateToErrorTab}
      form={eventForm}
      scrollToFirstError
      disabled={isReadOnlyModeEnabled}
      onFieldsChange={showErrorStatesOnChange}
    >
      <StyledEventDrawer
        title={
          <EventDrawerTitle
            mode={mode}
            eventName={event.name}
            eventId={event.id}
            triggerEditAction={triggerEditAction}
            isReadOnlyModeEnabled={isReadOnlyModeEnabled}
            showEditButton={!data.isCancelled}
          />
        }
        placement="right"
        width={1009}
        drawerStyle={{
          overflow: 'hidden',
        }}
        bodyStyle={{
          padding: '0px 0px',
        }}
        visible={true}
        footer={(!isReadOnlyModeEnabled || inProgress) && renderActionButtons()}
        onClose={handleDiscardWithConfirmation}
        maskClosable={isReadOnlyModeEnabled ? true : false}
      >
        <StyledToastWrapper id={TOAST_WRAPPER_ID} />
        <StyledEventDrawerTabContainer>
          <Tabs
            defaultActiveKey={DRAWER_TABS.EVENT_DETAILS}
            activeKey={activeTab}
            onChange={setActiveTab}
            tabPosition={'left'}
            style={{ height: '100%' }}
          >
            {tabsConfig.map((tab: any) => (
              <Tabs.TabPane key={tab.key} tab={tab.title} forceRender={tab.forceRender}>
                <Scrollbar autoHeight autoHeightMin={scrollbarMinHeight}>
                  {tab.content}
                </Scrollbar>
              </Tabs.TabPane>
            ))}
          </Tabs>
        </StyledEventDrawerTabContainer>
      </StyledEventDrawer>
    </Form>
  );
}

EventEditDrawer.defaultProps = {
  data: {},
};

export default withUserAuth(EventEditDrawer);
