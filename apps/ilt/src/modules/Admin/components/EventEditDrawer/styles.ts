import styled, { css } from 'styled-components';

import { THEME_PREFIX_CLS } from '~/config/constants';

import { StyledSessionDrawer, StyledTabContainer } from '../SessionEditDrawer/styles';

export const tabContainerCommonMixin = () => css`
  margin-bottom: 16px;
`;

export const StyledEventDrawer = styled(StyledSessionDrawer)``;

export const StyledEventDrawerTabContainer = styled(StyledTabContainer)`
  > .${THEME_PREFIX_CLS}-tabs-left {
    > .${THEME_PREFIX_CLS}-tabs-nav {
      & .${THEME_PREFIX_CLS}-tabs-tab {
        width: 250px;
      }
    }
  }
`;
