import type { Dispatch, SetStateAction } from 'react';

import { TTimezone } from '../../../SessionEditDrawer/typeDefs';
import { TUpdateEventState } from '../../typeDefs';

export interface TEnrollment {
  event: any;
  updateEventState: TUpdateEventState;
  mode: string;
  data?: any;
  isReadOnlyModeEnabled: boolean;
}

export interface TSelectAbsolute {
  eventStartTime: number;
  freezeDate: number;
  setFreezeDate: Dispatch<SetStateAction<number>>;
  validateError: boolean;
  disableFreezeEdit: boolean;
  freezeTimezone?: TTimezone;
  isFreezePast: boolean;
  isReadModeEnabled: boolean;
  updateEventState: TUpdateEventState;
}

export interface TEnrollmentFreeze {
  eventStartTime: number;
  enrollmentFreezeStatus: string;
  updateEventState: TUpdateEventState;
  enrollmentFreezeTime: number;
  disableFreezeEdit: boolean;
  enrollmentFreezeTimezone?: TTimezone;
  isReadOnlyModeEnabled: boolean;
  mode: string;
  enrollmentFreezeDaysBeforeEvent: number;
}

export interface TEnrollmentFreezeSection {
  event: any;
  updateEventState: TUpdateEventState;
  mode: string;
  data?: any;
  isReadOnlyModeEnabled: boolean;
}

export interface TEnrollmentLimitSection {
  event: any;
  updateEventState: TUpdateEventState;
  isReadOnlyModeEnabled: boolean;
  isPublished?: boolean;
}

export interface TEnrollmentFreezeDatepicker {
  validateError?: boolean;
  isPast?: boolean;
  disabled?: boolean;
}

export interface TSelectRelative {
  numDays: number;
  setNumDays: Dispatch<SetStateAction<number>>;
  disableFreezeEdit: boolean;
  isFreezePast: boolean;
  isReadModeEnabled: boolean;
  mode: string;
}

export interface TEnrollmentFreezeInput {
  isPast?: boolean;
}

export interface TEventEnrollment {
  isReadOnlyModeEnabled: boolean;
  updateEventState: TUpdateEventState;
  enrollmentType: string;
  isPublished?: boolean;
}
