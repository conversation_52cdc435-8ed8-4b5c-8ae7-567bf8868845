import { useState, useEffect } from 'react';

import Form from '@mindtickle/form';
import InputNumber from '@mindtickle/input-number';

import { StyledReminderContainer } from '~/modules/Admin/components/SessionEditDrawer/components/Reminders/components/SetReminder/styles';
import { convertToSeconds, splitSeconds } from '~/utils/index';

import type { TReminderTime, TSetReminder } from '../../typeDefs';

const SetReminder = ({ seconds, onChange, index, form }: TSetReminder) => {
  const { days: initDays, hours: initHours, minutes: initMins } = splitSeconds(seconds);
  const [days, setDays] = useState(initDays);
  const [hours, setHours] = useState(initHours);
  const [minutes, setMinutes] = useState(initMins);

  useEffect(() => {
    setDays(initDays);
    setHours(initHours);
    setMinutes(initMins);
  }, [initDays, initHours, initMins]);

  useEffect(() => {
    let reminderTime = { days, hours, minutes };
    reminderTime = updateMinimumTime(reminderTime);
    form.setFieldsValue({
      [`reminderDays${index}`]: days,
      [`reminderHours${index}`]: hours,
      [`reminderMinutes${index}`]: minutes,
    });
    onChange(convertToSeconds(reminderTime));
    // eslint-disable-next-line react-hooks/exhaustive-deps -- onChange just updates session state with the set reminders
  }, [days, hours, minutes, form, index]);

  const updateMinimumTime = ({ days, hours, minutes }: TReminderTime) => {
    minutes = days || hours || minutes ? minutes : 1;
    setMinutes(minutes);
    return {
      days,
      hours,
      minutes,
    };
  };

  const hasError = (fieldName: string) => {
    const fieldErrors = form.getFieldError(fieldName);
    const isFieldTouched = form.isFieldTouched(fieldName);
    return isFieldTouched && fieldErrors && fieldErrors.length > 0;
  };

  const formatValue = (value: string) => {
    if (value !== '') {
      return Number(value);
    } else {
      return 0;
    }
  };

  return (
    <StyledReminderContainer>
      <div className={'common-styling-reminder'}>
        <Form.Item
          name={`reminderDays${index}`}
          validateStatus={hasError(`reminder${index}`) ? 'error' : null}
        >
          <InputNumber
            name={`reminderDays${index}`}
            className={'reminder-input-styling'}
            value={days}
            type="number"
            max={90}
            min={0}
            formatter={formatValue}
            defaultValue={days}
            onChange={setDays}
            precision={0}
          />
        </Form.Item>
        <div>days</div>
      </div>
      <div className={'common-styling-reminder'}>
        <Form.Item
          name={`reminderHours${index}`}
          validateStatus={hasError(`reminder${index}`) ? 'error' : null}
        >
          <InputNumber
            name={`reminderHours${index}`}
            className={'reminder-input-styling'}
            value={hours}
            type="number"
            max={24}
            min={0}
            formatter={formatValue}
            defaultValue={hours}
            onChange={setHours}
            precision={0}
          />
        </Form.Item>
        <div>hours</div>
      </div>
      <div className={'common-styling-reminder'}>
        <Form.Item
          name={`reminderMinutes${index}`}
          validateStatus={hasError(`reminder${index}`) ? 'error' : null}
        >
          <InputNumber
            name={`reminderMinutes${index}`}
            className={'reminder-input-styling'}
            value={minutes}
            type="number"
            max={59}
            min={days || hours ? 0 : 1}
            formatter={formatValue}
            defaultValue={minutes}
            onChange={setMinutes}
            precision={0}
          />
        </Form.Item>
        <div>minutes</div>
      </div>
    </StyledReminderContainer>
  );
};

export default SetReminder;
