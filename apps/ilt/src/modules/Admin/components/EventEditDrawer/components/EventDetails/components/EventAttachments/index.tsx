import _findIndex from 'lodash/findIndex';

import Form from '@mindtickle/form';

import Attachments from '~/modules/Admin/components/Attachment';
import { StyledAttachmentsView } from '~/modules/Admin/components/SessionEditDrawer/components/SessionDetails/components/SessionAttachments/styles';

import { EVENT_FORM_FIELDS } from '../../../../constants';

import type { TEventAttachment } from '../../typeDefs';

const EventAttachments = ({
  attachments,
  updateEventState,
  isReadOnlyModeEnabled,
}: TEventAttachment) => {
  let key = 'attachments';

  const addAttachment = (value: any) => {
    let newAttachments = [...attachments, value];
    updateEventState({ key, value: newAttachments });
  };

  const removeAttachment = (value: any) => {
    const attachmentIndex = _findIndex(
      attachments,
      (attachment: any) => attachment.id === value.id
    );

    let newAttachments = attachments.filter((attachment: any, i: number) => i !== attachmentIndex);
    updateEventState({ key, value: newAttachments });
  };

  const attachmentRenderer = (
    <Attachments
      attachments={attachments}
      add={addAttachment}
      remove={removeAttachment}
      isReadOnly={isReadOnlyModeEnabled}
    />
  );

  return isReadOnlyModeEnabled ? (
    <StyledAttachmentsView>{attachmentRenderer}</StyledAttachmentsView>
  ) : (
    <Form.Item name={EVENT_FORM_FIELDS.ATTACHMENTS}>{attachmentRenderer}</Form.Item>
  );
};

export default EventAttachments;
