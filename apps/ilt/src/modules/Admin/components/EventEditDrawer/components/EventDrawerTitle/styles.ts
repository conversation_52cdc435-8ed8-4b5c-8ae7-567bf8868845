import styled from 'styled-components';

import { mixins } from '@mindtickle/styles/lib';

export const StyledEventDrawerTitleContainer = styled.div`
  margin-left: 8px;
  .view-event-title,
  .edit-event-title,
  .create-event-title {
    ${mixins.h2()};
  }
`;

export const StyledEventViewTitle = styled.div`
  display: flex;
  .edit-event-title {
    margin-right: 30px;
  }

  .edit-event-button {
    margin-left: 24px;
    margin-right: 30px;
  }
`;
