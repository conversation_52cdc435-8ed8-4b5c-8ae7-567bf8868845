import styled from 'styled-components';

import { tokens } from '@mindtickle/styles/lib';
export const StyledEnrollmentFreezeContainer = styled.div`
  padding-right: 32px;
`;

export const StyledEnrollmentLockText = styled.span<{ checked: boolean }>`
  margin-right: 6px;
  font-weight: 600;
  font-size: 14px;
  color: ${props =>
    props.checked ? tokens.textTokens.COLOR_TEXT_DEFAULT : tokens.textTokens.COLOR_TEXT_TERTIARY};
`;
