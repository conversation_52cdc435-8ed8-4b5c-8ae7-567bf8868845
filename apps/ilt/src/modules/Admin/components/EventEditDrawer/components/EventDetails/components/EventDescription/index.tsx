import Description from '@mindtickle/description';
import Form from '@mindtickle/form';
import InnerHTML from '@mindtickle/string-to-html';

import { EVENT_FORM_FIELDS } from '../../../../constants';
import { LabeledEventInfo } from '../../../LabeledEventInfo';

import type { TEventDescription } from '../../typeDefs';

const EventDescription = ({
  description,
  updateEventState,
  isReadOnlyModeEnabled,
}: TEventDescription) => {
  let childProps = {
    maxLength: 1000,
    content: description,
    onChange: (value: string) => {
      updateEventState({ key: 'description', value });
    },
    id: EVENT_FORM_FIELDS.DESCRIPTION,
    className: 'event-description-content',
    bounds: `.event-description-content`,
  };
  const strippedDescription = isReadOnlyModeEnabled ? description.replace(/<[^>]+>/g, '') : '';

  return (
    <>
      {isReadOnlyModeEnabled ? (
        <>
          {strippedDescription.length > 0 && (
            <LabeledEventInfo
              overlayClassName={'description-readonly-overlay'}
              labelText="Description"
              htmlFor="event-description-view"
            >
              <InnerHTML content={description} id="event-description-view" />
            </LabeledEventInfo>
          )}
        </>
      ) : (
        <Form.Item
          label="Description"
          labelCol={{ span: 24 }}
          wrapperCol={{ span: 24 }}
          name={EVENT_FORM_FIELDS.DESCRIPTION}
          className={'event-description'}
        >
          <Description placeholder="Enter event description" {...childProps} />
        </Form.Item>
      )}
    </>
  );
};

export default EventDescription;
