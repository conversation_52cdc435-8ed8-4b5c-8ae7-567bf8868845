import { useState, useRef } from 'react';

import { FormattedMessage } from 'react-intl';

import Divider from '@mindtickle/divider';
import Form from '@mindtickle/form';
import Icon, { ICON_MAP } from '@mindtickle/icon';
import InputNumber from '@mindtickle/input-number';
import Toggle from '@mindtickle/switch';
import Tooltip from '@mindtickle/tooltip';
import { Title } from '@mindtickle/typography';

import {
  StyledEnrollmentLimitContainer,
  StyledEnrollmentLimitText,
  StyledInfoCaption,
  StyledMaxSeatsContainer,
  StyledWaitingListContainer,
  StyledWaitingListText,
} from '~/modules/Admin/components/SessionEditDrawer/components/Enrollment/components/EnrollmentLimitSection/styles';
import { StyledActionRow } from '~/modules/Admin/components/SessionEditDrawer/components/Enrollment/styles';
import { MESSAGES } from '~/modules/Admin/config/sessions.constants';
import ENROLLMENT_LIMIT_MESSAGES from '~/modules/Admin/messages/events/enrollmentLimit';

import { EVENT_FORM_FIELDS } from '../../../../constants';

import { StyledWarningContainer } from './styles';

import type { TEnrollmentLimitSection } from '../../typeDefs';

const EnrollmentLimitSection = (props: TEnrollmentLimitSection) => {
  const {
    event: { maxSeatEnabled, maxSeats, waitingListEnabled, autoEnroll, notifyWaitingList },
    updateEventState,
    isReadOnlyModeEnabled,
    isPublished,
  } = props;
  const [isInputFocused, setIsInputFocused] = useState(false);
  const scrollToSpanRef = useRef<HTMLSpanElement | null>(null);

  const formatValue = (value: string) => {
    if (value !== '') {
      return Number(value);
    } else {
      return 1;
    }
  };

  const onToggleChange = (data: { key: string; value: boolean }) => {
    updateEventState(data);
    if (data && data.value) {
      setTimeout(() =>
        scrollToSpanRef.current?.scrollIntoView({
          behavior: 'smooth',
        })
      );
    }
  };

  const renderMaxSeats = () => {
    const handleInputFocus = () => {
      setIsInputFocused(true);
    };
    const handleInputBlur = () => {
      setIsInputFocused(false);
    };

    return (
      <StyledMaxSeatsContainer>
        <>
          {isReadOnlyModeEnabled ? (
            <div className="enrollment-limit-view">{maxSeats}</div>
          ) : (
            <>
              <Form.Item
                name={EVENT_FORM_FIELDS.MAX_SEATS}
                labelCol={{ span: 24 }}
                wrapperCol={{ span: 24 }}
              >
                <InputNumber
                  name={EVENT_FORM_FIELDS.MAX_SEATS}
                  type="number"
                  min={1}
                  max={10000}
                  value={maxSeats}
                  formatter={formatValue}
                  className={'max-enroll-input'}
                  defaultValue={maxSeats}
                  onChange={(value: number) =>
                    updateEventState({
                      key: 'maxSeats',
                      value: value,
                    })
                  }
                  precision={0}
                  onFocus={handleInputFocus}
                  onBlur={handleInputBlur}
                />
              </Form.Item>
              {isPublished && isInputFocused && (
                <StyledWarningContainer>
                  <Icon type={ICON_MAP.EXCLAMATION} className="info-icon" />
                  <span className="info-text">
                    <FormattedMessage
                      {...ENROLLMENT_LIMIT_MESSAGES.ENROLLMENT_LIMIT_REDUCE_WARNING}
                    />
                  </span>
                </StyledWarningContainer>
              )}
            </>
          )}
        </>
      </StyledMaxSeatsContainer>
    );
  };

  const renderWaitingList = () => (
    <StyledWaitingListContainer>
      <Divider />
      <StyledActionRow>
        <StyledWaitingListText checked={!!waitingListEnabled}>
          <FormattedMessage {...ENROLLMENT_LIMIT_MESSAGES.ENABLE_WAITING_LIST} />
          <Tooltip
            overlayClassName={'tooltipInfo'}
            title={<FormattedMessage {...MESSAGES.INFO.WAITING_LIST_EVENT} />}
            getPopupContainer={() => document.body}
          >
            <Icon type="info2" className={'infoHover'} />
          </Tooltip>
        </StyledWaitingListText>

        <Toggle
          name="waitingListEnabledToggle"
          checked={!!waitingListEnabled}
          onChange={(value: boolean) => onToggleChange({ key: 'waitingListEnabled', value })}
          disabled={isReadOnlyModeEnabled}
        />
      </StyledActionRow>

      {waitingListEnabled && (
        <>
          <Divider />
          <StyledActionRow className="child-action-row">
            <StyledWaitingListText checked={!!autoEnroll}>
              <FormattedMessage {...ENROLLMENT_LIMIT_MESSAGES.AUTO_ENROLL_ON_UNENROLL} />
              <Tooltip
                overlayClassName={'tooltipInfo'}
                title={<FormattedMessage {...MESSAGES.INFO.AUTO_ENROLL} />}
              >
                <Icon type="info2" className={'infoHover'} />
              </Tooltip>
            </StyledWaitingListText>

            <Toggle
              name="autoEnrollToggle"
              onChange={(value: boolean) => onToggleChange({ key: 'autoEnroll', value })}
              checked={!!autoEnroll}
              disabled={isReadOnlyModeEnabled}
            />
            {autoEnroll && (
              <StyledInfoCaption>
                <FormattedMessage {...ENROLLMENT_LIMIT_MESSAGES.AUTO_ENROLL_INFO} />
              </StyledInfoCaption>
            )}
          </StyledActionRow>
        </>
      )}

      {autoEnroll && waitingListEnabled && (
        <>
          <Divider />
          <StyledActionRow className="child-action-row">
            <StyledEnrollmentLimitText checked={!!notifyWaitingList}>
              <FormattedMessage {...ENROLLMENT_LIMIT_MESSAGES.NOTIFY_LEARNERS_ON_ENROLLMENT} />
            </StyledEnrollmentLimitText>
            <Toggle
              name="notifyWaitingListToggle"
              onChange={(value: boolean) => updateEventState({ key: 'notifyWaitingList', value })}
              checked={!!notifyWaitingList}
              disabled={isReadOnlyModeEnabled}
            />
          </StyledActionRow>
        </>
      )}
    </StyledWaitingListContainer>
  );

  return (
    <StyledEnrollmentLimitContainer>
      <Title level={3} className="enrollment-limit-header">
        <FormattedMessage {...ENROLLMENT_LIMIT_MESSAGES.ENROLLMENT_LIMIT} />
      </Title>
      <StyledActionRow>
        <StyledEnrollmentLimitText checked={!!maxSeatEnabled}>
          <FormattedMessage {...ENROLLMENT_LIMIT_MESSAGES.SET_ENROLLMENT_LIMIT} />
          <Tooltip
            overlayClassName={'tooltipInfo'}
            title={<FormattedMessage {...ENROLLMENT_LIMIT_MESSAGES.MAX_SEATS} />}
          >
            <Icon type="info2" className={'infoHover'} />
          </Tooltip>
        </StyledEnrollmentLimitText>

        <Toggle
          name="enrollmentLimitToggle"
          checked={!!maxSeatEnabled}
          onChange={(value: boolean) => onToggleChange({ key: 'maxSeatEnabled', value })}
          disabled={isReadOnlyModeEnabled}
        />
      </StyledActionRow>
      {maxSeatEnabled && renderMaxSeats()}

      {maxSeatEnabled && renderWaitingList()}
      <span ref={scrollToSpanRef}></span>
    </StyledEnrollmentLimitContainer>
  );
};
export default EnrollmentLimitSection;
