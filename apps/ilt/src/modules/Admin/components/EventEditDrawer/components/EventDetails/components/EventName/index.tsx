import Form from '@mindtickle/form';
import Input from '@mindtickle/input';

import { MAX_SESSION_NAME_LENGTH as MAX_EVENT_NAME_LENGTH } from '~/modules/Admin/config/sessions.constants';

import { EVENT_FORM_FIELDS } from '../../../../constants';
import { LabeledEventInfo } from '../../../LabeledEventInfo';

import type { TEventName } from '../../typeDefs';

const EventName = ({ name, updateEventState, isReadOnlyModeEnabled }: TEventName) => {
  const childProps = {
    name: 'title',
    value: name,
    maxLength: MAX_EVENT_NAME_LENGTH,
    onChange: (event: React.FormEvent<HTMLInputElement>) =>
      updateEventState({
        key: 'name',
        value: (event.target as HTMLInputElement).value.substring(0, MAX_EVENT_NAME_LENGTH),
      }),
    disabled: false,
    showMaxLengthInAddon: true,
  };
  return (
    <>
      {isReadOnlyModeEnabled ? (
        <LabeledEventInfo labelText="Event name" htmlFor="event-name-view">
          <div id="event-name-view">{name}</div>
        </LabeledEventInfo>
      ) : (
        <Form.Item
          label="Event name*"
          labelCol={{ span: 24 }}
          wrapperCol={{ span: 24 }}
          name={EVENT_FORM_FIELDS.NAME}
          rules={[{ required: true, message: 'Please enter an event name' }]}
        >
          <Input placeholder="Enter event name" {...childProps} />
        </Form.Item>
      )}
    </>
  );
};

export default EventName;
