import { useCallback, useState } from 'react';

import { FormattedMessage } from 'react-intl';

import Form from '@mindtickle/form';
import Grid from '@mindtickle/grid';
import Icon from '@mindtickle/icon';
import IconWithGradient, { ICON_MAP } from '@mindtickle/icon-with-gradient';
import Pill, { PILL_TYPES } from '@mindtickle/pill';
import { Title } from '@mindtickle/typography';

import { DEFAULT_REMINDER_SECONDS } from '~/modules/Admin/config/sessions.constants';
import REMINDER_MESSAGES from '~/modules/Admin/messages/events/reminders';
import { splitSeconds } from '~/utils';

import {
  StyledEmptyReminderBody,
  StyledEmptyReminderContainer,
  StyledEmptyReminderHeader,
  StyledInfoContainer,
  StyledReminderRow,
  StyledReminderTimes,
  StyledRemindersSection,
  StyledRemindersView,
} from '../../../SessionEditDrawer/components/Reminders/styles';
import { EVENT_FORM_FIELDS } from '../../constants';

import SetReminder from './components/SetReminder';
import { StyledReminderTabContainer } from './styles';

import type { TReminder, THandleReminderChange } from './typeDefs';

const { Row } = Grid;

const Reminders = (props: TReminder) => {
  const {
    event: { reminders = [] },
    updateEventState,
    isReadOnlyModeEnabled,
    form,
  } = props;
  const [initialReminderSeconds, setInitialReminderSeconds] = useState(
    reminders.length === 0
      ? DEFAULT_REMINDER_SECONDS
      : Math.max(...reminders) + DEFAULT_REMINDER_SECONDS
  );

  const addReminder = () => {
    setInitialReminderSeconds(initialReminderSeconds + DEFAULT_REMINDER_SECONDS);
    reminders.unshift(initialReminderSeconds);
    updateEventState({
      key: 'reminders',
      value: reminders,
    });
  };

  const handleReminderChange = useCallback(
    ({ value = 0, index }: THandleReminderChange) => {
      reminders[index] = value;
      updateEventState({ key: 'reminders', value: reminders });
    },
    [reminders, updateEventState]
  );

  return (
    <StyledReminderTabContainer>
      <Title level={3}>
        <FormattedMessage {...REMINDER_MESSAGES.TITLE} />
      </Title>
      <StyledRemindersSection>
        <Row align="middle" className="action-section">
          {!isReadOnlyModeEnabled && (
            <Pill onClick={addReminder} type={PILL_TYPES.ACTION}>
              +&nbsp;&nbsp;
              <FormattedMessage {...REMINDER_MESSAGES.ADD_BUTTON_TEXT} />
            </Pill>
          )}

          <StyledInfoContainer isReadOnlyModeEnabled={isReadOnlyModeEnabled}>
            <Icon type="info2" className="notify-info-icon" />
            <span className={'notify-text'}>
              <FormattedMessage {...REMINDER_MESSAGES.REMINDER_INFO} />
            </span>
          </StyledInfoContainer>
        </Row>
        {reminders.length === 0 && (
          <StyledEmptyReminderContainer>
            <IconWithGradient style={{ width: 300, height: 280 }} type={ICON_MAP.ALARM_CLOCK} />
            <StyledEmptyReminderHeader>
              <FormattedMessage {...REMINDER_MESSAGES.EMPTY_REMINDER_HEADER} />
            </StyledEmptyReminderHeader>
            <StyledEmptyReminderBody>
              <span
                className={isReadOnlyModeEnabled ? '' : 'session-link-color'}
                onClick={isReadOnlyModeEnabled ? undefined : addReminder}
              >
                <FormattedMessage {...REMINDER_MESSAGES.EMPTY_REMINDER_ACTION_TEXT} />{' '}
              </span>
              <span>
                <FormattedMessage {...REMINDER_MESSAGES.EMPTY_REMINDER_SUPPORT_TEXT} />
              </span>
            </StyledEmptyReminderBody>
          </StyledEmptyReminderContainer>
        )}
        <StyledReminderTimes>
          {reminders.map((reminder: number, index: number) => (
            <StyledReminderRow key={index}>
              {isReadOnlyModeEnabled ? (
                <StyledRemindersView>
                  <div className="time-block days">
                    <span className="time">{splitSeconds(reminder).days}</span>
                    <span className="label">{` day${
                      splitSeconds(reminder).days !== 1 ? 's' : '\u00a0\u00a0'
                    }`}</span>
                  </div>
                  <div className="time-block hours">
                    <span className="time">{splitSeconds(reminder).hours}</span>
                    <span className="label"> hours</span>
                  </div>
                  <div className="time-block minutes">
                    <span className="time">{splitSeconds(reminder).minutes}</span>
                    <span className="label"> minutes</span>
                  </div>
                  <div className="fixed-text">
                    <FormattedMessage {...REMINDER_MESSAGES.REMINDER_SUPPORT_TEXT} />
                  </div>
                </StyledRemindersView>
              ) : (
                <>
                  <Form.Item
                    name={EVENT_FORM_FIELDS.REMINDERS + index}
                    rules={[
                      {
                        validator: async (_: any, reminderSeconds: number) => {
                          const restReminders = [
                            ...reminders.slice(0, index),
                            ...reminders.slice(index + 1),
                          ];
                          if (restReminders.includes(reminderSeconds)) {
                            return Promise.reject(
                              'Another reminder is already set for the same time'
                            );
                          } else {
                            return Promise.resolve();
                          }
                        },
                      },
                    ]}
                  >
                    <SetReminder
                      seconds={reminder}
                      onChange={value => handleReminderChange({ value, index })}
                      index={index}
                      reminders={reminders}
                      form={form}
                    />
                  </Form.Item>
                  <FormattedMessage {...REMINDER_MESSAGES.REMINDER_SUPPORT_TEXT} />
                  <Icon
                    type="delete"
                    className="reminder-delete-icon"
                    onClick={() => {
                      reminders.length === 1 && setInitialReminderSeconds(DEFAULT_REMINDER_SECONDS);
                      updateEventState({
                        key: 'reminders',
                        value: reminders.filter((_val: number, i: number) => i !== index),
                      });
                    }}
                  />
                </>
              )}
            </StyledReminderRow>
          ))}
        </StyledReminderTimes>
      </StyledRemindersSection>
    </StyledReminderTabContainer>
  );
};

export default Reminders;
