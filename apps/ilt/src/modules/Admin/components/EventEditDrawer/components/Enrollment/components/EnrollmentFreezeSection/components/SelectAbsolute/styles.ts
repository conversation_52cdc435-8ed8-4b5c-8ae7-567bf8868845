import styled from 'styled-components';

import { mixins } from '@mindtickle/styles/lib';

import { EnrollmentFreezeDatepicker } from '~/modules/Admin/components/SessionEditDrawer/components/Enrollment/components/EnrollmentFreezeSection/components/SelectAbsolute/styles';

export const StyledEnrollmentFreezeDatepicker = styled(EnrollmentFreezeDatepicker)`
  display: block;
  margin-bottom: 16px;
  .timezone-dropdown-label {
    .timezone-label {
      margin-top: 0;
      margin-bottom: 8px;
    }
  }
`;

export const StyledEnrollmentFreezeDateView = styled.div`
  display: flex;
  align-items: end;
  margin-top: 16px;
  & .enrollment-freeze-date-view {
    margin-right: 15px;
  }
  .freeze-date {
    ${mixins.darkText()}
  }
`;
