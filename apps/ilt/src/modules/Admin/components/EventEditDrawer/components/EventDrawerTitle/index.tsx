import EllipsisTooltip from '@mindtickle/ellipsis-tooltip';
import Pill, { PILL_TYPES } from '@mindtickle/pill';

import { MODES } from '~/modules/Admin/config/sessions.constants';
import { ILT_ENTITIES } from '~/modules/Admin/constants/module';

import { StyledEventDrawerTitleContainer, StyledEventViewTitle } from './styles';

import type { TEventDrawerTitle } from '../../typeDefs';

const EventDrawerTitle = (props: TEventDrawerTitle) => {
  const { mode, eventName, eventId, triggerEditAction, isReadOnlyModeEnabled, showEditButton } =
    props;
  const renderViewTitle = () => (
    <StyledEventViewTitle>
      <EllipsisTooltip
        wrapperClassName="view-event-title"
        placement="bottom"
        title={eventName}
        showTooltipWhenEllipsis={true}
      >
        {eventName}
      </EllipsisTooltip>

      {showEditButton && (
        <Pill
          className={'edit-event-button'}
          onClick={() => triggerEditAction({ id: eventId!, entityType: ILT_ENTITIES.EVENT })}
          disabled={false}
          type={PILL_TYPES.ACTION}
        >
          Edit event
        </Pill>
      )}
    </StyledEventViewTitle>
  );

  const renderEditTitle = () => (
    <StyledEventViewTitle>
      <EllipsisTooltip
        wrapperClassName="edit-event-title"
        placement="bottom"
        title={eventName}
        showTooltipWhenEllipsis={true}
      >
        {eventName}
      </EllipsisTooltip>
    </StyledEventViewTitle>
  );

  const renderCreateTitle = () => (
    <StyledEventViewTitle>
      <div className="create-event-title">Create event</div>
    </StyledEventViewTitle>
  );

  return (
    <StyledEventDrawerTitleContainer>
      {mode === MODES.EDIT
        ? renderEditTitle()
        : isReadOnlyModeEnabled
        ? renderViewTitle()
        : renderCreateTitle()}
    </StyledEventDrawerTitleContainer>
  );
};

export default EventDrawerTitle;
