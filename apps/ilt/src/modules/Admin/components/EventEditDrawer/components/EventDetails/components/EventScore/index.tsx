import { FormattedMessage } from 'react-intl';

import { MODES } from '~/modules/Admin/constants/sessions';
import EVENT_DETAILS_MESSAGES from '~/modules/Admin/messages/events/eventDetails';

import { LabeledEventInfo } from '../../../LabeledEventInfo';

import { StyledEventScore } from './styles';

import type { TEventScore } from '../../typeDefs';

const renderMaxScore = (mode: string, maxScore: number, sessionsCountWithinEvent: number) => {
  if (mode === MODES.ADD) {
    return <FormattedMessage {...EVENT_DETAILS_MESSAGES.MAX_SCORE_CREATE} />;
  }
  if (sessionsCountWithinEvent === 0) {
    return <FormattedMessage {...EVENT_DETAILS_MESSAGES.MAX_SCORE_NO_SESSIONS} />;
  }
  if (maxScore === 0) {
    return <FormattedMessage {...EVENT_DETAILS_MESSAGES.MAX_SCORE_ZERO} />;
  }

  return (
    <div className="max-score">
      <span>{maxScore}</span>
    </div>
  );
};

const EventScore = ({ mode, maxScore, sessionsCountWithinEvent }: TEventScore) => (
  <StyledEventScore>
    <LabeledEventInfo
      labelText={<FormattedMessage {...EVENT_DETAILS_MESSAGES.SCORE_TITLE} />}
      htmlFor="event-score-view"
    >
      <div id="event-score-view" className="event-score-view">
        {renderMaxScore(mode, maxScore, sessionsCountWithinEvent)}
      </div>
    </LabeledEventInfo>
  </StyledEventScore>
);

export default EventScore;
