import { useState, useEffect } from 'react';

import moment from 'moment';
import { FormattedMessage } from 'react-intl';

import { EVENT_ENROLLMENT_FREEZE_TYPE_OPTIONS } from '~/modules/Admin/config/events.constants';
import {
  ENROLLMENT_FREEZE_STATUSES,
  ENROLLMENT_LOCK_WARNING,
  MODES,
} from '~/modules/Admin/config/sessions.constants';
import ENROLLMENT_FREEZE_MESSAGES from '~/modules/Admin/messages/events/enrollmentFreeze';
import {
  getEnrollmentFreezeTitle,
  sessionTimeFormatter,
  eventEnrolmentLockWarningTimeFormatter,
} from '~/modules/Admin/utils';
import { getCurrentTimeZoneAbbreviated } from '~/modules/Admin/utils/timing';
import { nextIntervalTimestamp } from '~/utils';
import { getCurrTimeWithOffset } from '~/utils/timezone';

import SelectAbsolute from '../SelectAbsolute';
import SelectRelative from '../SelectRelative';

import {
  StyledEnrollmentFreezeContainer,
  StyledEnrollmentFreezeDropdown,
  StyledErrorMessage,
  StyledEnrollmentTypeContainer,
  DROPDOWN_STYLE,
} from './styles';

import type { TEnrollmentFreeze } from '../../../../typeDefs';

// eslint-disable-next-line complexity
function EnrollmentFreeze({
  eventStartTime,
  enrollmentFreezeStatus,
  updateEventState,
  enrollmentFreezeTime,
  enrollmentFreezeTimezone,
  disableFreezeEdit,
  isReadOnlyModeEnabled,
  mode,
  enrollmentFreezeDaysBeforeEvent,
}: TEnrollmentFreeze) {
  const [numDays, setNumDays] = useState(
    enrollmentFreezeDaysBeforeEvent !== 0 ? enrollmentFreezeDaysBeforeEvent : 5
  );
  const [freezeDate, setFreezeDate] = useState(
    enrollmentFreezeTime !== 0
      ? enrollmentFreezeTime
      : eventStartTime && eventStartTime !== 0
      ? eventStartTime
      : nextIntervalTimestamp(Date.now(), 5)
  );
  const [validateError, setValidateError] = useState(false);
  let currentTimeWithOffset = getCurrTimeWithOffset(enrollmentFreezeTimezone?.offset);
  const isRelativeFreezePast =
    eventStartTime !== 0 &&
    new Date().getTime() - moment(eventStartTime).subtract(numDays, 'days').valueOf() > 0;
  const isAbsoluteFreezePast = currentTimeWithOffset - moment(freezeDate).valueOf() > 0;

  useEffect(() => {
    switch (enrollmentFreezeStatus) {
      case ENROLLMENT_FREEZE_STATUSES.RELATIVE:
        setValidateError(false);
        if (numDays === 0) setNumDays(1);
        updateEventState({
          key: 'enrollmentFreezeEpoch',
          value: eventStartTime ? moment(eventStartTime).subtract(numDays, 'days').valueOf() : 0,
        });
        updateEventState({
          key: 'enrollmentFreezeDaysBeforeEvent',
          value: numDays,
        });
        break;
      case ENROLLMENT_FREEZE_STATUSES.ABSOLUTE:
        if (eventStartTime && freezeDate > eventStartTime) setValidateError(true);
        else setValidateError(false);
        updateEventState({ key: 'enrollmentFreezeEpoch', value: moment(freezeDate).valueOf() });
        break;
      default:
        updateEventState({ key: 'enrollmentFreezeEpoch', value: 0 });
        break;
    }
  }, [eventStartTime, freezeDate, numDays, enrollmentFreezeStatus, updateEventState]);

  return (
    <StyledEnrollmentFreezeContainer isReadOnlyModeEnabled={isReadOnlyModeEnabled}>
      {isReadOnlyModeEnabled ? (
        <div className="enrollment-freeze-type-view">
          {getEnrollmentFreezeTitle(enrollmentFreezeStatus).displayValue}
        </div>
      ) : (
        <StyledEnrollmentFreezeDropdown
          id="enrollmentFreezeType"
          options={EVENT_ENROLLMENT_FREEZE_TYPE_OPTIONS}
          onSelect={(value: string) => updateEventState({ key: 'enrollmentFreezeStatus', value })}
          value={getEnrollmentFreezeTitle(enrollmentFreezeStatus).value}
          validateError={validateError}
          disabled={disableFreezeEdit}
          dropdownMatchSelectWidth={false}
          dropdownStyle={DROPDOWN_STYLE}
        />
      )}

      <StyledEnrollmentTypeContainer>
        {enrollmentFreezeStatus === ENROLLMENT_FREEZE_STATUSES.RELATIVE ? (
          <SelectRelative
            numDays={numDays}
            setNumDays={setNumDays}
            disableFreezeEdit={disableFreezeEdit}
            isFreezePast={isRelativeFreezePast}
            isReadModeEnabled={isReadOnlyModeEnabled}
            mode={mode}
          />
        ) : (
          <SelectAbsolute
            eventStartTime={eventStartTime}
            freezeDate={freezeDate}
            setFreezeDate={setFreezeDate}
            validateError={validateError}
            disableFreezeEdit={disableFreezeEdit}
            freezeTimezone={enrollmentFreezeTimezone}
            isFreezePast={isAbsoluteFreezePast}
            isReadModeEnabled={isReadOnlyModeEnabled}
            updateEventState={updateEventState}
          />
        )}
      </StyledEnrollmentTypeContainer>

      {enrollmentFreezeStatus === ENROLLMENT_FREEZE_STATUSES.RELATIVE &&
        mode !== MODES.ADD &&
        eventStartTime !== 0 &&
        (!isReadOnlyModeEnabled && !disableFreezeEdit && isRelativeFreezePast ? (
          <StyledErrorMessage>
            <FormattedMessage
              {...ENROLLMENT_FREEZE_MESSAGES.FREEZE_ENROLLMENT_PAST}
              values={{
                date: `(${eventEnrolmentLockWarningTimeFormatter(eventStartTime)})`,
              }}
            />
          </StyledErrorMessage>
        ) : (
          <div className={'date-display'}>
            {disableFreezeEdit ? ENROLLMENT_LOCK_WARNING.PAST : ENROLLMENT_LOCK_WARNING.FUTURE}
            {sessionTimeFormatter(enrollmentFreezeTime) +
              getCurrentTimeZoneAbbreviated(enrollmentFreezeTime)}
          </div>
        ))}

      {enrollmentFreezeStatus === ENROLLMENT_FREEZE_STATUSES.ABSOLUTE &&
        (validateError ? (
          <StyledErrorMessage
            className="absolute-select-error"
            isReadOnlyModeEnabled={isReadOnlyModeEnabled}
          >
            <FormattedMessage
              {...ENROLLMENT_FREEZE_MESSAGES.FREEZE_ENROLLMENT_WARNING}
              values={{
                date:
                  eventStartTime && eventStartTime !== 0
                    ? `(${eventEnrolmentLockWarningTimeFormatter(eventStartTime)})`
                    : null,
              }}
            />
          </StyledErrorMessage>
        ) : !isReadOnlyModeEnabled && !disableFreezeEdit && isAbsoluteFreezePast ? (
          <StyledErrorMessage
            className="absolute-select-error"
            isReadOnlyModeEnabled={isReadOnlyModeEnabled}
          >
            <FormattedMessage
              {...ENROLLMENT_FREEZE_MESSAGES.FREEZE_ENROLLMENT_PAST}
              values={{
                date:
                  eventStartTime && eventStartTime !== 0
                    ? `(${eventEnrolmentLockWarningTimeFormatter(eventStartTime)})`
                    : null,
              }}
            />
          </StyledErrorMessage>
        ) : null)}
    </StyledEnrollmentFreezeContainer>
  );
}

export default EnrollmentFreeze;
