import { FormattedMessage } from 'react-intl';

import Divider from '@mindtickle/divider';
import Toggle from '@mindtickle/switch';
import { Title } from '@mindtickle/typography';

import { StyledActionRow } from '~/modules/Admin/components/SessionEditDrawer/components/Enrollment/styles';
import { getTimezoneObject } from '~/modules/Admin/components/SessionsWrapper/utils';
import { ENROLLMENT_FREEZE_STATUSES, MODES } from '~/modules/Admin/config/sessions.constants';
import ENROLLMENT_FREEZE_MESSAGES from '~/modules/Admin/messages/events/enrollmentFreeze';

import EnrollmentFreeze from './components/EnrollmentFreeze';
import { StyledEnrollmentFreezeContainer, StyledEnrollmentLockText } from './styles';

import type { TEnrollmentFreezeSection } from '../../typeDefs';

const EnrollmentFreezeSection = (props: TEnrollmentFreezeSection) => {
  const {
    updateEventState,
    event: {
      enrollmentFreezeStatus,
      startTime,
      enrollmentFreezeEpoch,
      enrollmentFreezeTimezone,
      enrollmentFreezeDaysBeforeEvent,
    },
    mode,
    data: { isPublished, startTime: originalStartTime },
    isReadOnlyModeEnabled,
  } = props;

  const enrollmentFreezeEnabled = enrollmentFreezeStatus !== ENROLLMENT_FREEZE_STATUSES.DISABLED;

  //Handling case when copy session is being created, also unpublished case
  const disableFreezeEdit =
    mode === MODES.EDIT && isPublished && Date.now() - originalStartTime > 0;

  let hideSectionWhenPast = originalStartTime !== 0 && Date.now() - originalStartTime >= 0;

  const hideEnrollmentFreeze = hideSectionWhenPast === true && enrollmentFreezeEnabled === false;
  const timezoneObj = getTimezoneObject(enrollmentFreezeTimezone);

  const setFreezeStatus = (value: boolean) => {
    if (value) {
      updateEventState({
        key: 'enrollmentFreezeStatus',
        value: ENROLLMENT_FREEZE_STATUSES.RELATIVE,
      });
    } else {
      updateEventState({
        key: 'enrollmentFreezeStatus',
        value: ENROLLMENT_FREEZE_STATUSES.DISABLED,
      });
      updateEventState({ key: 'enrollmentFreezeEpoch', value: 0 });
    }
  };

  return (
    <>
      {!hideEnrollmentFreeze && (
        <StyledEnrollmentFreezeContainer>
          <Title level={3}>
            <FormattedMessage {...ENROLLMENT_FREEZE_MESSAGES.TITLE} />
          </Title>
          <StyledActionRow>
            <StyledEnrollmentLockText checked={!!enrollmentFreezeEnabled}>
              <FormattedMessage {...ENROLLMENT_FREEZE_MESSAGES.SUBTITLE} />
            </StyledEnrollmentLockText>

            <Toggle
              name="enrollmentFreezeToggle"
              checked={!!enrollmentFreezeEnabled}
              onChange={setFreezeStatus}
              disabled={isReadOnlyModeEnabled}
            />
          </StyledActionRow>

          {enrollmentFreezeEnabled && (
            <EnrollmentFreeze
              eventStartTime={startTime}
              enrollmentFreezeStatus={enrollmentFreezeStatus}
              updateEventState={updateEventState}
              enrollmentFreezeTime={enrollmentFreezeEpoch}
              disableFreezeEdit={disableFreezeEdit}
              enrollmentFreezeTimezone={timezoneObj}
              isReadOnlyModeEnabled={isReadOnlyModeEnabled}
              mode={mode}
              enrollmentFreezeDaysBeforeEvent={enrollmentFreezeDaysBeforeEvent}
            />
          )}

          <Divider />
        </StyledEnrollmentFreezeContainer>
      )}
    </>
  );
};

export default EnrollmentFreezeSection;
