import { FormattedMessage } from 'react-intl';

import Grid from '@mindtickle/grid';
import Icon, { ICON_MAP } from '@mindtickle/icon';

import { EVENT_TYPES } from '~/modules/Admin/config/events.constants';
import { LOCATION_DROPDOWN_LABEL, MODES } from '~/modules/Admin/config/sessions.constants';
import EVENT_DETAILS_MESSAGES from '~/modules/Admin/messages/events/eventDetails';
import { getSessionDateFormat, getSessionTimeFormat } from '~/modules/Admin/utils';
import { getCurrentTimeZoneAbbreviated } from '~/modules/Admin/utils/timing';

import { LabeledEventInfo } from '../../../LabeledEventInfo';

import { StyledEventDateView } from './styles';

import type { TEventTimeLocation } from '../../typeDefs';

const { Row, Col } = Grid;

export default function EventTimeLocation(props: TEventTimeLocation) {
  const { startTime, endTime, locationType, mode, sessionsCountWithinEvent } = props;
  const localStartTime = new Date(startTime);
  const localEndTime = new Date(endTime);
  return (
    <>
      {mode === MODES.ADD ? (
        <FormattedMessage {...EVENT_DETAILS_MESSAGES.DATE_LOCATION_CREATE} />
      ) : sessionsCountWithinEvent === 0 ? (
        <FormattedMessage {...EVENT_DETAILS_MESSAGES.DATE_LOCATION_NO_SESSIONS} />
      ) : (
        <>
          <Row align={'bottom'}>
            <Col span={7}>
              <StyledEventDateView>
                <LabeledEventInfo className={'time-label'} labelText="Start date">
                  <div>{getSessionDateFormat(localStartTime)}</div>
                </LabeledEventInfo>
                <LabeledEventInfo className={'time-label'} labelText="Start time">
                  <div>{getSessionTimeFormat(localStartTime)}</div>
                </LabeledEventInfo>
              </StyledEventDateView>
            </Col>
            <Col span={1}>
              <Icon type={ICON_MAP.STEP_ARROW} className="more-action-icon" />
            </Col>
            <Col span={7}>
              <StyledEventDateView>
                <LabeledEventInfo className={'time-label'} labelText="End date">
                  <div>{getSessionDateFormat(localEndTime)}</div>
                </LabeledEventInfo>
                <LabeledEventInfo className={'time-label'} labelText="End time">
                  <div>{getSessionTimeFormat(localEndTime)}</div>
                </LabeledEventInfo>
              </StyledEventDateView>
            </Col>
            {/* TODO: Can use "moment.tz(JSON.parse(timezone.originalTimezoneString).id).zoneAbbr()" to get the abbreviation, but not reliable */}
            <Col span={4}>
              <LabeledEventInfo className={'time-label'} labelText="Time zone">
                <div>{getCurrentTimeZoneAbbreviated(startTime)}</div>
              </LabeledEventInfo>
            </Col>
          </Row>

          <LabeledEventInfo
            className={'location-label'}
            labelText={<FormattedMessage {...LOCATION_DROPDOWN_LABEL} />}
          >
            <FormattedMessage
              {...(locationType === EVENT_TYPES.CLASSROOM.value
                ? EVENT_TYPES.CLASSROOM.displayValue
                : locationType === EVENT_TYPES.WEBINAR.value
                ? EVENT_TYPES.WEBINAR.displayValue
                : EVENT_TYPES.HYBRID.displayValue)}
            />
          </LabeledEventInfo>
        </>
      )}
    </>
  );
}
