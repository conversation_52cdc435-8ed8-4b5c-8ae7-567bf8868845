import type { TUpdateEventState } from '../../typeDefs';
import type { FormInstance } from 'antd/lib/form';
export interface TEventName {
  name: string;
  updateEventState: TUpdateEventState;
  isReadOnlyModeEnabled: boolean;
}

export interface TEventDescription {
  description: string;
  updateEventState: TUpdateEventState;
  isReadOnlyModeEnabled: boolean;
}

export interface TEventAttachment {
  attachments: any[];
  updateEventState: TUpdateEventState;
  isReadOnlyModeEnabled: boolean;
}

export interface TEventScore {
  maxScore: number;
  mode: string;
  sessionsCountWithinEvent: number;
}

export interface TEventTimeLocation {
  startTime: number;
  endTime: number;
  locationType: string;
  mode: string;
  sessionsCountWithinEvent: number;
}

export interface TEventDetails {
  event: any;
  updateEventState: TUpdateEventState;
  isReadOnlyModeEnabled: boolean;
  form: FormInstance;
  mode: string;
  sessionsCountWithinEvent: number;
}
