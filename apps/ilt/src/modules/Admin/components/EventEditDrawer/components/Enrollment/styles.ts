import styled from 'styled-components';

import { tokens, mixins } from '@mindtickle/styles/lib';

import { tabContainerCommonMixin } from '../../styles';

export const StyledEnrollmentTabContainer = styled.div`
  ${tabContainerCommonMixin()}
`;

export const StyledRadioWrapper = styled.div`
  margin-top: 12px;
  margin-bottom: 15px;

  .completion-text {
    ${mixins.blackLink}
    margin-bottom: 16px;
  }

  .completion-criteria-radio-group {
    .radio-option-style {
      margin-right: 8px;
    }
  }

  .event-enrollment-title {
    display: flex;
    align-items: baseline;
  }

  .infoHover {
    cursor: pointer;
    margin-left: 8px;
    color: ${tokens.iconTokens.COLOR_ICON_DEFAULT};
    font-size: 14px;
  }
`;

export const StyledEventEnrollmentContainer = styled.div``;

export const StyledInfoContainer = styled.div`
  display: flex;
  align-items: center;
  .info-icon {
    margin-right: 10px;
  }
  .info-text {
    color: ${tokens.textTokens.COLOR_TEXT_SECONDARY};
  }
`;
