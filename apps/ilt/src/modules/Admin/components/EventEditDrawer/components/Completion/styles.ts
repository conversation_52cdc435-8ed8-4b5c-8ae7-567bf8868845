import styled from 'styled-components';

import { mixins } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS } from '~/config/constants';

import { tabContainerCommonMixin } from '../../styles';

export const StyledCompletionWrapper = styled.div`
  ${tabContainerCommonMixin()}
  .completion-text {
    ${mixins.blackLink}
    margin-bottom: 16px;
  }
  .${THEME_PREFIX_CLS}-form-item {
    margin-bottom: 15px;
  }
`;

export const StyledInputWrapper = styled.div`
  display: flex;
  align-items: baseline;

  .completion-criteria-text {
    margin-left: 10px;
  }
`;
