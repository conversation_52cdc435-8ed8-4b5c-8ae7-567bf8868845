import { useState, useEffect } from 'react';

import { FormattedMessage } from 'react-intl';

import Divider from '@mindtickle/divider';
import { Title } from '@mindtickle/typography';

import EVENT_DETAILS_MESSAGES from '~/modules/Admin/messages/events/eventDetails';

import EventAttachments from './components/EventAttachments';
import EventDescription from './components/EventDescription';
import EventName from './components/EventName';
import EventScore from './components/EventScore';
import EventTimeLocation from './components/EventTimeLocation';
import {
  StyledEventDetails,
  StyledEventDateLocationWrapper,
  StyledEventDetailWrapper,
} from './styles';

import type { TEventDetails } from './typeDefs';

function EventDetails(props: TEventDetails) {
  const { event, updateEventState, isReadOnlyModeEnabled, form, mode, sessionsCountWithinEvent } =
    props;
  const [showDescriptionAfterMount, setShowDescriptionAfterMount] = useState(false);

  useEffect(() => {
    setShowDescriptionAfterMount(true);
  }, []);

  useEffect(() => {
    form.setFieldsValue({
      attachments: event?.attachments,
    });
  }, [event, form]);

  return (
    <StyledEventDetails>
      <Title level={3}>
        <FormattedMessage {...EVENT_DETAILS_MESSAGES.TITLE} />
      </Title>
      <StyledEventDetailWrapper>
        <EventName
          name={event.name}
          updateEventState={updateEventState}
          isReadOnlyModeEnabled={isReadOnlyModeEnabled}
        />
        {showDescriptionAfterMount && (
          <EventDescription
            description={event.description}
            updateEventState={updateEventState}
            isReadOnlyModeEnabled={isReadOnlyModeEnabled}
          />
        )}
        <EventAttachments
          attachments={event.attachments}
          updateEventState={updateEventState}
          isReadOnlyModeEnabled={isReadOnlyModeEnabled}
        />
        <EventScore
          maxScore={event.maxScore}
          mode={mode}
          sessionsCountWithinEvent={sessionsCountWithinEvent}
        />
      </StyledEventDetailWrapper>
      <Divider className="session-details-divider" />
      <Title level={3} className="date-location-title">
        <FormattedMessage {...EVENT_DETAILS_MESSAGES.DATE_LOCATION_TITLE} />
      </Title>
      <StyledEventDateLocationWrapper>
        <EventTimeLocation
          startTime={event.startTime}
          endTime={event.endTime}
          locationType={event.type}
          mode={mode}
          sessionsCountWithinEvent={sessionsCountWithinEvent}
        />
      </StyledEventDateLocationWrapper>
    </StyledEventDetails>
  );
}

export default EventDetails;
