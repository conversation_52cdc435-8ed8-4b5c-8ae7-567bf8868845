import styled from 'styled-components';

import { tokens } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS } from '~/config/constants';

import { tabContainerCommonMixin } from '../../styles';

export const StyledEventDetails = styled.div`
  ${tabContainerCommonMixin()}
  padding-right: 32px;
  .event-description {
    .ql-disabled {
      color: ${tokens.textTokens.COLOR_TEXT_SECONDARY};
      background-color: ${tokens.bgTokens.COLOR_BG_DISABLED} !important;
      border-color: ${tokens.borderTokens.COLOR_BORDER_DEFAULT} !important;
      box-shadow: none;
      cursor: not-allowed;
      opacity: 0.5;
    }
  }
  .${THEME_PREFIX_CLS}-form-item {
    margin-bottom: 16px !important;
  }
  .${THEME_PREFIX_CLS}-form-item-label > label::before {
    display: none !important;
  }
  .score-input {
    width: 115px;
  }

  .event-details-divider {
    margin-bottom: 19px;
  }
`;

export const StyledEventDateLocationWrapper = styled.div`
  .time-label {
    margin-top: 0;
  }
  .location-label {
    margin-top: 16px;
  }
`;

export const StyledEventDetailWrapper = styled.div`
  & .infoHover {
    padding: 0 12px;
    color: ${tokens.iconTokens.COLOR_ICON_DEFAULT};
    font-size: 14px;
  }
  .description-readonly-overlay {
    margin-top: 16px;
  }
`;
