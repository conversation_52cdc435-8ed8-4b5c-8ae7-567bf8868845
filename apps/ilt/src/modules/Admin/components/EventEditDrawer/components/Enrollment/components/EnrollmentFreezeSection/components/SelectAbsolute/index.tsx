import { useCallback } from 'react';

import AsyncTimezoneDropdown from '~/components/AsyncTimezoneDropdown';
import DateTimeSelector from '~/modules/Admin/components/DateTimeSelector';
import { getSessionDateFormat, getSessionsTableTimeFormat } from '~/modules/Admin/utils';
import { getCurrTimeWithOffset } from '~/utils/timezone';

import { LabeledEventInfo } from '../../../../../LabeledEventInfo';

import { StyledEnrollmentFreezeDatepicker, StyledEnrollmentFreezeDateView } from './styles';

import type { TSelectAbsolute } from '../../../../typeDefs';

function SelectAbsolute(props: TSelectAbsolute) {
  const {
    eventStartTime,
    freezeDate,
    freezeTimezone,
    setFreezeDate,
    validateError,
    disableFreezeEdit,
    isFreezePast,
    isReadModeEnabled,
    updateEventState,
  } = props;
  let currentTimeWithOffset = getCurrTimeWithOffset(freezeTimezone?.offset);
  let isPast = !disableFreezeEdit && isFreezePast;

  const onTimezoneChange = useCallback(
    (data: any) => {
      updateEventState({
        key: 'enrollmentFreezeTimezone',
        value: JSON.stringify(data),
      });
    },
    [updateEventState]
  );

  return (
    <>
      {isReadModeEnabled ? (
        <StyledEnrollmentFreezeDateView>
          <LabeledEventInfo labelText="Start date" overlayClassName="enrollment-freeze-date-view">
            <div className={'freeze-date'}>{getSessionDateFormat(freezeDate)}</div>
          </LabeledEventInfo>
          <LabeledEventInfo labelText="Start time" className="enrollment-freeze-date-view">
            <div className={'freeze-date'}>{getSessionsTableTimeFormat(freezeDate)}</div>
          </LabeledEventInfo>
          <span className={'freeze-date'}>{freezeTimezone?.shortDisplayName}</span>
        </StyledEnrollmentFreezeDateView>
      ) : (
        <StyledEnrollmentFreezeDatepicker
          validateError={validateError}
          disabled={disableFreezeEdit}
          isPast={isPast}
        >
          <DateTimeSelector
            withBtns={false}
            disable={true}
            disableDateRange={true}
            disableTimeRange={true}
            disableTimeEdit={disableFreezeEdit}
            min={currentTimeWithOffset}
            max={eventStartTime}
            value={freezeDate}
            ok={value => setFreezeDate(value)}
            id="enrollmentFreeze"
            errorStatus={validateError || isPast}
          />
          <LabeledEventInfo
            labelText="Time zone"
            overlayClassName="timezone-dropdown-label"
            className="timezone-label"
          >
            <AsyncTimezoneDropdown
              startTime={freezeDate}
              onChange={onTimezoneChange}
              timezoneString={freezeTimezone?.originalTimezoneString}
              timezoneId={freezeTimezone?.id}
              disabled={!!disableFreezeEdit}
              hasError={validateError || isPast}
            />
          </LabeledEventInfo>
        </StyledEnrollmentFreezeDatepicker>
      )}
    </>
  );
}

export default SelectAbsolute;
