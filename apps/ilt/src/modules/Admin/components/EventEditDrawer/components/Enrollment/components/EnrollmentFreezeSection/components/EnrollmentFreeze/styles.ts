import styled from 'styled-components';

import {
  DisabledMessage,
  EnrollmentFreezeContainer,
  EnrollmentFreezeDropdown,
  EnrollmentTypeContainer,
  ErrorMessage,
} from '~/modules/Admin/components/SessionEditDrawer/components/Enrollment/components/EnrollmentFreezeSection/components/EnrollmentFreeze/styles';

export const StyledEnrollmentFreezeContainer = styled(EnrollmentFreezeContainer)<{
  isReadOnlyModeEnabled?: boolean;
}>`
  margin-bottom: ${props => (props.isReadOnlyModeEnabled ? '0px' : '-17px')};
`;

export const StyledEnrollmentFreezeDropdown = styled(EnrollmentFreezeDropdown)``;

export const StyledErrorMessage = styled(ErrorMessage)<{
  isReadOnlyModeEnabled?: boolean;
}>`
  &.absolute-select-error {
    margin-top: ${props => (props.isReadOnlyModeEnabled ? '4px' : '-8px')};
  }
`;

export const StyledEnrollmentTypeContainer = styled(EnrollmentTypeContainer)``;

export const StyledDisabledMessage = styled(DisabledMessage)``;

export const DROPDOWN_STYLE = { width: '320px' };
