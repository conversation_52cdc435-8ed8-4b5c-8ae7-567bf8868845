import { FormattedMessage } from 'react-intl';

import Divider from '@mindtickle/divider';
import Icon, { ICON_MAP } from '@mindtickle/icon';
import Radio from '@mindtickle/radio';
import Tooltip from '@mindtickle/tooltip';
import { Title } from '@mindtickle/typography';

import { EVENT_ENROLLMENT_TEXTS } from '~/modules/Admin/config/events.constants';
import { EVENT_ENROLLMENT_ENUM } from '~/modules/Admin/constants/events';
import EVENT_ENROLLMENT_MESSAGES from '~/modules/Admin/messages/events/enrollment';

import EnrollmentFreezeSection from './components/EnrollmentFreezeSection';
import EnrollmentLimitSection from './components/EnrollmentLimitSection';
import {
  StyledEnrollmentTabContainer,
  StyledEventEnrollmentContainer,
  StyledInfoContainer,
  StyledRadioWrapper,
} from './styles';

import type { TEnrollment, TEventEnrollment } from './typeDefs';
import type { RadioChangeEvent } from 'antd';

function renderEventEnrollment({
  isReadOnlyModeEnabled,
  updateEventState,
  enrollmentType,
  isPublished,
}: TEventEnrollment) {
  return (
    <StyledEventEnrollmentContainer>
      <Title level={3} className="enrollment-limit-header">
        <FormattedMessage {...EVENT_ENROLLMENT_MESSAGES.TITLE} />
      </Title>
      <StyledRadioWrapper>
        <div className="event-enrollment-title">
          <div className="completion-text">
            <FormattedMessage {...EVENT_ENROLLMENT_MESSAGES.SUBTITLE} />
          </div>
          <Tooltip
            overlayClassName={'tooltipInfo'}
            placement="bottom"
            title={
              <div>
                <FormattedMessage {...EVENT_ENROLLMENT_MESSAGES.TOOLTIP_TEXT_SECTION_1} />
                <br />
                <br />
                <FormattedMessage {...EVENT_ENROLLMENT_MESSAGES.TOOLTIP_TEXT_SECTION_2} />
              </div>
            }
            getPopupContainer={() => document.body}
          >
            <Icon type="info2" className={'infoHover'} />
          </Tooltip>
        </div>

        <Radio.Group
          className={'completion-criteria-radio-group'}
          value={enrollmentType}
          onChange={(e: RadioChangeEvent) =>
            updateEventState({ key: 'enrollmentType', value: e.target.value })
          }
          disabled={isPublished || isReadOnlyModeEnabled}
        >
          <Radio className={'radio-option-style'} value={EVENT_ENROLLMENT_ENUM.EVENT}>
            <FormattedMessage {...EVENT_ENROLLMENT_TEXTS[EVENT_ENROLLMENT_ENUM.EVENT]} />
          </Radio>
          <Radio className={'radio-option-style'} value={EVENT_ENROLLMENT_ENUM.SESSIONS}>
            <FormattedMessage {...EVENT_ENROLLMENT_TEXTS[EVENT_ENROLLMENT_ENUM.SESSIONS]} />
          </Radio>
        </Radio.Group>
      </StyledRadioWrapper>
      <StyledInfoContainer>
        <Icon type={ICON_MAP.EXCLAMATION} className="info-icon" />
        <FormattedMessage {...EVENT_ENROLLMENT_MESSAGES.ENROLLMENT_INFO} />
      </StyledInfoContainer>
    </StyledEventEnrollmentContainer>
  );
}

function Enrollment(props: TEnrollment) {
  const { event, updateEventState, mode, data, isReadOnlyModeEnabled } = props;
  const {
    data: { isPublished },
  } = props;
  return (
    <StyledEnrollmentTabContainer>
      {renderEventEnrollment({
        isReadOnlyModeEnabled,
        updateEventState,
        enrollmentType: event.enrollmentType,
        isPublished,
      })}
      {event.enrollmentType === EVENT_ENROLLMENT_ENUM.EVENT && (
        <>
          <Divider />
          <EnrollmentFreezeSection
            event={event}
            updateEventState={updateEventState}
            mode={mode}
            data={data}
            isReadOnlyModeEnabled={isReadOnlyModeEnabled}
          />
          <EnrollmentLimitSection
            event={event}
            updateEventState={updateEventState}
            isReadOnlyModeEnabled={isReadOnlyModeEnabled}
            isPublished={isPublished}
          />
        </>
      )}
    </StyledEnrollmentTabContainer>
  );
}

export default Enrollment;
