import { useCallback, useEffect } from 'react';

import { FormattedMessage } from 'react-intl';

import Icon from '@mindtickle/icon';

import { ATTACHMENT_FETCH_STATUS, useAttachments } from '~/modules/Admin/containers/Attachments';

import { parseMedia } from '../../Attachment/utils';
import Completion from '../components/Completion';
import Enrollment from '../components/Enrollment';
import EventDetails from '../components/EventDetails';
import Reminders from '../components/Reminders';
import { DRAWER_TAB_TITLES } from '../config';
import { DRAWER_TABS } from '../constants';

import type { TTabConfig, TTabTitle, updateEventStateProps } from '../typeDefs';

const TabTitle = ({ errorTabs, drawerTab }: TTabTitle) => (
  <>
    <FormattedMessage {...DRAWER_TAB_TITLES[drawerTab]} />
    {errorTabs.has(drawerTab) && <Icon type="error" className={'tab-error'} />}
  </>
);

const useTabsConfig = ({
  errorTabs,
  event,
  setEvent,
  isReadOnlyModeEnabled,
  eventForm,
  mode,
  data,
  sessionsCountWithinEvent,
}: TTabConfig) => {
  const { mediasInfoMap } = useAttachments();
  const updateEventState = useCallback(
    ({ key, value }: updateEventStateProps) => {
      setEvent((prevState: any) => ({
        ...prevState,
        [key]: value,
      }));
      // If any dependency is added here, make sure to check all references
    },
    [setEvent]
  );
  useEffect(() => {
    const attachments = event.attachments.map((attachment: any) => {
      if (
        mediasInfoMap[attachment.id] &&
        mediasInfoMap[attachment.id].status === ATTACHMENT_FETCH_STATUS.SUCCESS
      ) {
        return parseMedia(mediasInfoMap[attachment.id].mediaInfo);
      }
      return attachment;
    });
    updateEventState({ key: 'attachments', value: attachments });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [mediasInfoMap, updateEventState]);

  const tabConfig = [
    {
      key: DRAWER_TABS.EVENT_DETAILS,
      title: <TabTitle errorTabs={errorTabs} drawerTab={DRAWER_TABS.EVENT_DETAILS} />,
      forceRender: true,
      content: (
        <EventDetails
          event={event}
          updateEventState={updateEventState}
          mode={mode}
          isReadOnlyModeEnabled={isReadOnlyModeEnabled}
          form={eventForm}
          sessionsCountWithinEvent={sessionsCountWithinEvent}
        />
      ),
    },
    {
      key: DRAWER_TABS.ENROLLMENT,
      title: <TabTitle errorTabs={errorTabs} drawerTab={DRAWER_TABS.ENROLLMENT} />,
      forceRender: true,
      content: (
        <Enrollment
          event={event}
          updateEventState={updateEventState}
          data={data}
          mode={mode}
          isReadOnlyModeEnabled={isReadOnlyModeEnabled}
        />
      ),
    },
    {
      key: DRAWER_TABS.COMPLETION,
      title: <TabTitle errorTabs={errorTabs} drawerTab={DRAWER_TABS.COMPLETION} />,
      forceRender: false,
      content: (
        <Completion
          event={event}
          updateEventState={updateEventState}
          isReadOnlyModeEnabled={isReadOnlyModeEnabled}
        />
      ),
    },
    {
      key: DRAWER_TABS.REMINDERS,
      title: <TabTitle errorTabs={errorTabs} drawerTab={DRAWER_TABS.REMINDERS} />,
      forceRender: false,
      content: (
        <Reminders
          event={event}
          updateEventState={updateEventState}
          isReadOnlyModeEnabled={isReadOnlyModeEnabled}
          form={eventForm}
        />
      ),
    },
  ];

  return tabConfig;
};

export default useTabsConfig;
