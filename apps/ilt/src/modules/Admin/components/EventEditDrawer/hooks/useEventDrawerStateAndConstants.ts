import { useState } from 'react';

import { MODES } from '~/modules/Admin/config/sessions.constants';

import { getEventCopy } from '../../SessionsWrapper/utils';
import { DRAWER_TABS } from '../constants';

import type { TEventEditDrawer } from '../typeDefs';

export default function useEventDrawerStateAndConstants({
  mode,
  initialDrawerTab,
  data,
}: Pick<TEventEditDrawer, 'mode' | 'initialDrawerTab' | 'data'>) {
  const isReadOnlyModeEnabled = mode === MODES.VIEW;
  const [event, setEvent] = useState<any>(getEventCopy({ eventData: data }));
  const [activeTab, setActiveTab] = useState<string>(initialDrawerTab ?? DRAWER_TABS.EVENT_DETAILS);
  const [errorTabs, setErrorTabs] = useState<Set<string>>(new Set([]));
  const drawerHeaderFooterHeight = isReadOnlyModeEnabled ? 60 : 130;
  const scrollbarMinHeight = `calc(100vh - ${drawerHeaderFooterHeight}px)`;
  return {
    isReadOnlyModeEnabled,
    event,
    setEvent,
    activeTab,
    setActiveTab,
    errorTabs,
    setErrorTabs,
    scrollbarMinHeight,
  };
}
