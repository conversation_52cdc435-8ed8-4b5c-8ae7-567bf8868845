import { LabelHTMLAttributes, ReactNode } from 'react';
import type { Dispatch, SetStateAction } from 'react';

import type { TTableActionsIdProp } from '../SessionsWrapper/typeDefs';
import type { FormInstance } from 'antd/lib/form';

export interface TLabeledEventInfo extends LabelHTMLAttributes<HTMLLabelElement> {
  labelText: string | React.ReactNode;
  overlayClassName?: string;
  children: ReactNode;
}

export interface updateEventStateProps {
  key: string;
  value: any;
}

export type TUpdateEventState = ({ key, value }: updateEventStateProps) => void;

export interface TEventEditDrawer {
  onClose: () => void;
  data?: any;
  mode: string;
  userAuth: any;
  update: (data: any) => void;
  sessionsList?: string[];
  initialDrawerTab?: string;
  triggerEditAction: ({ id }: { id: string }) => void;
  setShowEventModLoader: Dispatch<SetStateAction<boolean>>;
  inProgress: boolean;
}

export interface TTabTitle {
  errorTabs: Set<string>;
  drawerTab: string;
}

export interface TTabConfig {
  errorTabs: Set<string>;
  event: any;
  setEvent: Dispatch<SetStateAction<any>>;
  isReadOnlyModeEnabled: boolean;
  eventForm: FormInstance;
  mode: string;
  data?: any;
  sessionsCountWithinEvent: number;
}

export interface TEventDrawerTitle {
  mode: string;
  eventName: string;
  eventId?: string;
  triggerEditAction: (params: TTableActionsIdProp) => void;
  isReadOnlyModeEnabled: boolean;
  showEditButton: boolean;
}
