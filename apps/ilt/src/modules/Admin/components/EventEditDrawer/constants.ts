export const DRAWER_TABS = {
  EVENT_DETAILS: 'eventDetails',
  ENROLLMENT: 'enrollment',
  COMPLETION: 'completion',
  REMINDERS: 'reminders',
};

export const TOAST_WRAPPER_ID = 'eventEditTaostWrapperId';

export const EVENT_FORM_FIELDS = {
  EVENT: 'event',
  NAME: 'eventName',
  DESCRIPTION: 'eventDescription',
  COMPLETION_CRITERIA: 'completionCriteria',
  ENROLLMENT_FREEZE_NUM_DAYS: 'enrollmentFreezeNumDays',
  ENROLLMENT_FREEZE_DATE: 'enrollmentFreezeDate',
  ENROLLMENT_FREEZE_TIME: 'enrollmentFreezeTime',
  MAX_SEATS: 'maxSeats',
  ATTACHMENTS: 'attachments',
  REMINDERS: 'reminder',
};

export const errorFieldsTabsMapping = {
  [DRAWER_TABS.EVENT_DETAILS]: [EVENT_FORM_FIELDS.NAME],
  [DRAWER_TABS.ENROLLMENT]: [
    EVENT_FORM_FIELDS.ENROLLMENT_FREEZE_NUM_DAYS,
    EVENT_FORM_FIELDS.ENROLLMENT_FREEZE_DATE,
    EVENT_FORM_FIELDS.ENROLLMENT_FREEZE_TIME,
  ],
  [DRAWER_TABS.REMINDERS]: [EVENT_FORM_FIELDS.REMINDERS],
};

export const DEFAULT_EVENT_COMPLETION_CRITERIA_PERCENT = 100;
