import styled from 'styled-components';

import { mixins } from '@mindtickle/styles/lib';

export const StyledAttachments = styled.div`
  button.uploadAttachments {
    ${mixins.actionLink()};
    padding-left: 0px;
    padding-top: 0px;
    .attachment-icon {
      margin-right: 8px;
    }
  }
  .attachment-item:nth-last-child(2) {
    margin-bottom: 12px;
  }
  .attachment-item {
    margin-bottom: 12px;
  }
`;
