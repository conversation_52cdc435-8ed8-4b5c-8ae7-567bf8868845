import Button from '@mindtickle/button';
import Icon, { ICON_MAP } from '@mindtickle/icon';

import { ATTACHMENT_FETCH_STATUS } from '../../containers/Attachments/constants';
import { useAttachments } from '../../containers/Attachments/hooks';

import AttachedItem from './components/AttachedItem';
import SupportingDocUploaderInModal from './components/SupportingDocUploaderInModal';
import { StyledAttachments } from './styles';
import { parseMedia } from './utils';

import type { TAttachment } from './typeDefs';

const AddNewAttachment = () => (
  <Button type="text" className={'uploadAttachments'}>
    <Icon type={ICON_MAP.ATTACHEMENT} className={'attachment-icon'} />
    Attach supporting file
  </Button>
);

const Attachment = (props: TAttachment) => {
  const { attachments } = props;
  const { dispatchUpdateMedia, mediasInfoMap } = useAttachments();
  const addAttachment = ({ mediaInfo }: { mediaInfo: any }) => {
    dispatchUpdateMedia({ mediaId: mediaInfo.id, mediaInfo });
    const newAttachment = parseMedia(mediaInfo);
    props.add(newAttachment);
  };
  const removeAttachment = (id: string) => {
    const removedAttachment = props.attachments.filter(
      // eslint-disable-next-line eqeqeq
      (attachment = {}) => attachment.id == id
    );
    props.remove(removedAttachment[0]);
  };
  return (
    <StyledAttachments className={props.className}>
      {attachments.map((attachment, index) => {
        const mediaInfo = mediasInfoMap[attachment?.id] || {};
        return (
          <AttachedItem
            key={index}
            attachment={attachment}
            onRemove={removeAttachment}
            isReadOnly={props.isReadOnly}
            isAttachmentError={mediaInfo.status === ATTACHMENT_FETCH_STATUS.FAILED}
            isAttachmentLoading={mediaInfo.status === ATTACHMENT_FETCH_STATUS.LOADING}
          />
        );
      })}
      {!props.isReadOnly && (
        <SupportingDocUploaderInModal
          target={<AddNewAttachment />}
          onUploadComplete={addAttachment}
        />
      )}
    </StyledAttachments>
  );
};

Attachment.defaultProps = {
  attachments: [],
};

export default Attachment;
