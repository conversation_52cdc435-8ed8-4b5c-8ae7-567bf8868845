export interface TSupportingDocUploaderInModal {
  target?: any;
  onUploadComplete?: (media: any) => void;
}

export interface TAttachedItem {
  attachment?: any;
  onRemove: (id: string) => void;
  isReadOnly: boolean;
  isAttachmentError: boolean;
  isAttachmentLoading: boolean;
}

export interface TAttachment {
  className?: string;
  attachments: any[];
  add: Function;
  remove: Function;
  isReadOnly: boolean;
}
