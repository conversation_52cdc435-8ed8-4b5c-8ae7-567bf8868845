import { MODAL_SIZE } from '@mindtickle/modal';
import withModal from '@mindtickle/modal/withModal';
import SupportingDocUploader from '@mindtickle/uploader/SupportingDocUploader';

import { MASKED_MODAL_INDEX } from '~/config/constants';

import uploaderStyle, { StyledModalTitle, modalBodyStyles } from './styles';

import type { TSupportingDocUploaderInModal } from '../../typeDefs';

const SupportingDocUploaderInModal = ({
  target,
  onUploadComplete,
}: TSupportingDocUploaderInModal) => {
  const withModalProps = {
    propsToPass: {
      size: MODAL_SIZE.LARGE,
      zIndex: MASKED_MODAL_INDEX,
      title: <StyledModalTitle>Upload attachment</StyledModalTitle>,
      bodyStyle: modalBodyStyles,
    },
    withTrigger: true,
    withUrl: false,
    placeholder: target,
  };
  const UploaderInModalWrapper = () => (
    <SupportingDocUploader uploaderStyle={uploaderStyle} onUploadComplete={onUploadComplete} />
  );
  const MOD = withModal(withModalProps)(UploaderInModalWrapper);
  return <MOD />;
};

export default SupportingDocUploaderInModal;
