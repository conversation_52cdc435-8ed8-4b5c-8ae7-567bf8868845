import type { TAuthPermissionsDefinition } from '~/typeDefs';

import type { TAB_TYPES as TTAB_TYPES } from './constants';
import type { LIFECYCLE_STAGES } from 'ui_shell/GlobalConstants';

export type { TTAB_TYPES };

export interface TProps {
  activeTab: string;
  setActiveTab: (a: TTAB_TYPES) => void;
  hasUnpublishedChanges: boolean;
  isPublished: boolean;
  globalPermissions: TAuthPermissionsDefinition;
  seriesPermissions: any;
  isSiteOwner: boolean;
  analyticsUrl: string;
}

export type TNavBarTabProperties = {
  id: TTAB_TYPES;
  name: string;
  content: null | any;
  path: string;
  lifecycleStage: (typeof LIFECYCLE_STAGES)[keyof typeof LIFECYCLE_STAGES];
};

export type TNavBarTabsPropsTypes = TNavBarTabProperties[];
