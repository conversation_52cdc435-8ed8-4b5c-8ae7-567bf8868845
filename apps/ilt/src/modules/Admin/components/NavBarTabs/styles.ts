import styled from 'styled-components';

import Affix from '@mindtickle/affix';
import { tokens } from '@mindtickle/styles/lib';
import { theme, mixins } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS, ADMIN_NAVBAR_HEIGHT } from '~/config/constants';
const { fontSizes } = theme;

export const StyledNavBarTabsWrapper = styled.div`
  height: 100%;
  .tab-name-link {
    text-decoration: none;
    color: inherit;
  }
  .${THEME_PREFIX_CLS}-tabs {
    .${THEME_PREFIX_CLS}-tabs-nav {
      margin-bottom: 0;
      height: ${ADMIN_NAVBAR_HEIGHT}px;
    }
  }
  .${THEME_PREFIX_CLS}-tabs-nav-list {
    .${THEME_PREFIX_CLS}-tabs-tab {
      &:nth-last-child(2) .tab-icon {
        display: none;
      }
      position: relative;
      padding: 0;
      .tab-name-link {
        padding: 16px 8px 16px 12px;
      }
    }

    .tab-icon {
      position: absolute;
      margin-left: 16px;
      font-size: ${fontSizes.H3};
      color: ${tokens.textTokens.COLOR_TEXT_DISABLED};
      pointer-events: none;
    }

    .publish-icon {
      color: ${tokens.iconTokens.COLOR_ICON_DANGER};
      margin-left: 4px;
      font-size: 14px;
    }
  }
  .${THEME_PREFIX_CLS}-tabs-content {
    padding: 0;
  }
`;

export const StyledNavBarAffix = styled(Affix)`
  .${THEME_PREFIX_CLS}-affix {
    background-color: ${tokens.bgTokens.COLOR_BG_DEFAULT};
  }
  position: relative;
`;

export const StyledViewAnalyticsWrapper = styled.div`
  position: absolute;
  top: 50%;
  right: 32px;
  transform: translateY(-50%);

  a.${THEME_PREFIX_CLS}-typography {
    display: flex;
    ${mixins.smallDarkLink()};
    font-weight: 600;

    &:hover {
      color: ${tokens.textTokens.COLOR_TEXT_ACCENT};
    }
    &.${THEME_PREFIX_CLS}-typography-disabled {
      color: ${tokens.textTokens.COLOR_TEXT_DISABLED};
    }

    .analytics-icon {
      ${mixins.smallDarkLink()};
      margin-right: 4px;
      color: inherit;
    }

    .analytics-right-icon {
      ${mixins.smallDarkLink()};
      margin-left: 4px;
      color: inherit;
    }
  }
`;
