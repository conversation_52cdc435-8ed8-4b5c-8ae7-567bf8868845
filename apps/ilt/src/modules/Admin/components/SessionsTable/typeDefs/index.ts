import type { EVENT_ENROLLMENT_ENUM } from '~/modules/Admin/constants/events';
import type { TILTEntities } from '~/modules/Admin/typeDefs';

import type {
  TSelectedCountInfo,
  TEntitiesOnSelectInfo,
  TEntityOnSelectInfo,
  TTableActions,
} from '../../SessionsWrapper/typeDefs';
import type { SUPPORTED_SORTING, SORTING_ORDER } from '../constants';

export type TILT_ENTITIES = TILTEntities;

export { TEntitiesOnSelectInfo, TEntityOnSelectInfo };
export type TSupportedSortingField = (typeof SUPPORTED_SORTING)[keyof typeof SUPPORTED_SORTING];
export type TSortingOrder = (typeof SORTING_ORDER)[keyof typeof SORTING_ORDER];
export interface TDuration {
  displayValue: string;
  value: number;
}

export interface TTimezone {
  displayName: string;
  hasdst: boolean;
  isdst: boolean;
  name: string;
  offset: number;
  region: string;
  shortDisplayName: string;
  originalTimezoneString: string;
}
export interface TInstructor {
  description: string;
  name: string;
  email: string;
}

export interface TWebAutoAttendanceSettings {
  isLinkValidated: boolean;
  isAutoAttendanceEnabled: boolean;
  lastValidityStatus: string;
  thresholdConfig?: any;
  webinarSource?: string;
  webMeetingSettings?: any;
}

export interface TCheckInSettings {
  isSelfCheckInActivated: boolean;
  isSelfCheckInTimeRestricted: boolean;
  secondsRelativeToSessionStartToAllowCheckIn: number;
  secondsRelativeToSessionStopToAllowCheckIn: number;
  secondsBeforeCheckInStartToTriggerMails: number;
  selfCheckInCode: string;
  selfCheckInQrCodeUrl: string;
  informInstructorsAboutCheckIn: boolean;
  informFacilitatorsAboutCheckin: boolean;
}

export interface TTableEntityObject {
  entityType: TILTEntities;
  parentId?: string;
  sessions?: TTableEntityObject[];
  id: string;
  name: string;
  description?: string;
  type: string; //
  attachments: any[];
  maxScore: number;
  startTime?: number;
  endTime?: number;
  originalStartTime?: number;
  originalEndTime?: number;
  localStartTime?: string;
  localEndTime?: string;
  duration?: TDuration;
  timezone?: TTimezone;
  reminderEnabled: boolean;
  reminders: number[];
  instructors?: TInstructor[];
  isInstructorAvailable?: boolean;
  sendInstructorMail?: boolean;
  facilitators?: string[];
  isFacilitatorAvailable?: boolean;
  autoEnroll: boolean;
  maxSeats: number;
  maxSeatEnabled: boolean;
  waitingListEnabled: boolean;
  notifyWaitingList: boolean;
  enrollmentType?: (typeof EVENT_ENROLLMENT_ENUM)[keyof typeof EVENT_ENROLLMENT_ENUM];
  enrollmentFreezeStatus: string;
  enrollmentFreezeEpoch: number;
  enrollmentFreezeTimezone?: TTimezone;
  enrollmentFreezeDaysBeforeEvent?: number;
  isCancelled: boolean;
  isPublished: boolean;
  isOngoing: boolean;
  isOver: boolean;
  isUpcoming: boolean;
  cancellationReason: string;
  webAutoAttendanceSettings?: TWebAutoAttendanceSettings;
  checkInSettings?: TCheckInSettings;
  liveChallenge?: {
    uniqueCode: string;
    hasEnded?: boolean;
  };
  isPartialStaticData: boolean;
  noStaticData?: boolean;
  enrolled?: number;
  waiting?: number;
  attended?: number;
  hasStatsLoaded: boolean;
  isFilterHidden: boolean;
}

export interface TSessionsTable {
  data: TTableEntityObject[];
  hasMore: boolean;
  loadMoreLoading: boolean;
  loading: boolean;
  invitedLearnersCount: number;
  haveTrackAccess: boolean;
  haveBuildAccess: boolean;
  actions: TTableActions;
  selectedSessions: string[];
  multiSelectAction: TMultiSelectAction;
  selectedCountInfo: TSelectedCountInfo;
  colSortOrder: TSortingOrder;
  colSortField: TSupportedSortingField;
}

export interface TSessionOperations {
  isSessionOver: boolean;
  isSessionLive: boolean;
  isSessionPublished: boolean;
  isSessionCancelled: boolean;
  isParentCancelled?: boolean;
  actions: TTableActions;
  sessionId: string;
  liveChallenge: any;
  isPartialStaticData: boolean;
  entityType: TILT_ENTITIES;
  parentId?: string;
}

export interface TGetEntitiesOperationParams {
  isSessionOver: boolean;
  isSessionLive: boolean;
  isSessionPublished: boolean;
  isSessionCancelled: boolean;
  isParentCancelled: boolean;
  actions: TTableActions;
  sessionId: string;
  liveChallenge: any;
  isPartialStaticData: boolean;
  entityType: TILT_ENTITIES;
  parentId?: string;
  isEventManagePage: boolean;
}

export interface TShowHideSession {
  onClick: () => void;
  hiddenSessionCount: number;
  isSessionCanBeShown?: boolean;
}
export interface TExpandedTableData {
  data: TTableEntityObject[];
  parentData: TTableEntityObject;
  key: string;
  parentId: string;
  tableColumns: any;
  actions: TTableActions;
  haveTrackAccess: boolean;
  haveBuildAccess: boolean;
  invitedLearnersCount: number;
  isParentSelected: boolean;
  selectedSessions: string[];
}

export interface TSessionName {
  sessionName: string;
  sessionType: string;
  sessionStatus: string;
  liveChallenge?: any;
  hasCheckbox?: boolean;
  checked?: boolean;
  onChecked?: (e: any) => void;
  // rowIndex: string;
  // handleOperation: any;
  entityType: TILT_ENTITIES;
  haveBuildAccess?: boolean;
  isEventCancelled?: boolean;
}

export interface TSessionDate {
  sessionStartTime?: number;
  sessionTimezone: any;
  sessionDuration: any;
  localStartTime: any;
  noDate?: boolean;
}

export interface TTableData {
  key: string;
  sessionName: JSX.Element;
  sessionStartDate: JSX.Element;
  sessionInstructors: JSX.Element;
  manageSession: JSX.Element;
  sessionEnrollments: JSX.Element;
  SessionAttendance: JSX.Element;
  sessionWaitlisted: JSX.Element;
  sessionAttendanceMode: JSX.Element;
  sessionRecordings: JSX.Element;
  sessionOperations: JSX.Element;
  childSessions: any;
  entityType: TILT_ENTITIES;
}

export interface TSessionEnrollments {
  isPublished: boolean;
  sessionEnrollments?: number;
  maxSeats: number;
  maxSeatEnabled: boolean;
  enrollmentFreezeStatus: string;
  enrollmentFreezeEpoch: number;
  timezone: any;
  isCancelled: boolean;
  isPartialStaticData: boolean;
  statsLoading: boolean;
  enrollmentFreezeTimezone?: any;
  enrollmentFreezeDaysBeforeEvent?: number;
  eventEnrollmentType?: (typeof EVENT_ENROLLMENT_ENUM)[keyof typeof EVENT_ENROLLMENT_ENUM];
  entityType: TILTEntities;
  eventStartTime?: number;
}

export interface TSessionAttendance {
  isPublished: boolean;
  sessionAttendance?: number;
  hasStatsLoaded: boolean;
  isEvent?: boolean;
}

export interface TSessionWaitlisted {
  isPublished: boolean;
  waitingListEnabled: boolean;
  sessionWaitlisted?: number;
  hasStatsLoaded: boolean;
  isPartialStaticData: boolean;
  isEvent?: boolean;
  isSessionLevelEnrollmentEnabled?: boolean;
}

export interface TSessionAttendanceMode {
  loading: React.ReactNode;
  checkInSettings?: any;
  webAutoAttendanceSettings?: {
    isAutoAttendanceEnabled: boolean;
    isLinkValidated: boolean;
  };
  isEvent?: boolean;
}

export interface TSessionInstructor {
  instructors?: {
    description: string;
    name: string;
    email: string;
  }[];
  isEvent?: boolean;
}

export interface TSessionRecordings {
  isPublished: boolean;
  id: string;
  onShowRecordingsClick: ({ id }: { id: string }) => void;
  entityType: TILT_ENTITIES;
}

export interface TCalculateRecordingText {
  saveStatus: string;
  draftRecordings: any;
  sessionRecordings: any;
}

export interface TManageSessionButton {
  isPublished: boolean;
  haveTrackAccess: boolean;
  invitedLearnersCount: number;
  id: string;
  parentId?: string;
  entityType: TILT_ENTITIES;
}

export interface TMultiSelectAction {
  deleteEntities: () => void;
  unselectAll: () => void;
  selectAll: (checked: boolean) => void;
}

export interface TBuildActionBar {
  multiSelectAction: TMultiSelectAction;
  selectedCountInfo: TSelectedCountInfo;
}

export interface TToggleCollapse {
  expandContainer: HTMLDivElement;
  expandAll: boolean;
  tableData: TTableData[];
  setExpandAll: React.Dispatch<React.SetStateAction<boolean>>;
  setExpandedRowKeys: (keys: string[]) => void;
}

export interface TUseRowDetailsToggle {
  tableRef: React.RefObject<HTMLInputElement>;
}

export interface TUseTableColumnsProps {
  haveBuildAccess: boolean;
  isMultidayEnabled: boolean;
  colSortField: TSupportedSortingField;
  colSortOrder: TSortingOrder;
}

export interface TUseTableData {
  selectedSessions: string[];
  data: TTableEntityObject[];
  actions: TTableActions;
  invitedLearnersCount: number;
  haveTrackAccess: boolean;
}
