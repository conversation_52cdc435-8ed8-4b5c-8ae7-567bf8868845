import cs from 'classnames';

import { InlineSkeleton } from '@mindtickle/skeleton';
import { noop } from '@mindtickle/utils';

import { EVENT_ENROLLMENT_ENUM } from '~/modules/Admin/constants/events';
import { checkIsEvent } from '~/modules/Admin/utils/checkEntityType';
import { getCurrentTimeZoneAbbreviated } from '~/modules/Admin/utils/timing';

import SessionStatusProcessor from '../../SessionStatusProcessor';
import ManageSessionButton from '../components/ManageSessionButton';
import SessionAttendance from '../components/SessionAttendance/index';
import SessionAttendanceMode from '../components/SessionAttendanceMode';
import SessionDate from '../components/SessionDate';
import SessionEnrollments from '../components/SessionEnrollments';
import SessionInstructors from '../components/SessionInstructors';
import SessionName from '../components/SessionName';
import SessionOperations from '../components/SessionOperations';
import SessionRecordings from '../components/SessionRecordings';
import SessionWaitlisted from '../components/SessionWaitlisted';
import { StyledLineContainer } from '../styles';

import type { TILT_ENTITIES } from '../typeDefs';
import type { TCreateRowDataParam, TCreateChildRowDataParam } from '../typeDefs/rowData';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';
const eventTimezoneFiller = (startTime?: number) => ({
  shortDisplayName: `(${getCurrentTimeZoneAbbreviated(startTime)})`,
});

export function createRowData({
  entity,
  actions,
  haveTrackAccess,
  invitedLearnersCount,
  isSelected,
}: TCreateRowDataParam) {
  const isEvent = checkIsEvent(entity.entityType);
  return {
    key: entity.id,
    sessionName: (
      <SessionName
        entityType={entity.entityType as TILT_ENTITIES}
        sessionName={entity.name}
        sessionType={entity.type}
        sessionStatus={SessionStatusProcessor(entity)}
        liveChallenge={entity.liveChallenge}
      />
    ),
    sessionStartDate: (
      <SessionDate
        sessionStartTime={entity.startTime}
        sessionTimezone={isEvent ? eventTimezoneFiller(entity.startTime) : entity.timezone}
        sessionDuration={entity.duration}
        localStartTime={entity.localStartTime}
        noDate={isEvent && !entity.startTime}
      />
    ),
    sessionInstructors: <SessionInstructors isEvent={isEvent} instructors={entity.instructors} />,
    sessionEnrollments: (
      <SessionEnrollments
        sessionEnrollments={entity.enrolled}
        isPublished={entity.isPublished}
        maxSeats={entity.maxSeats}
        maxSeatEnabled={entity.maxSeatEnabled}
        enrollmentFreezeStatus={entity.enrollmentFreezeStatus}
        enrollmentFreezeEpoch={entity.enrollmentFreezeEpoch}
        timezone={entity.timezone}
        isCancelled={entity.isCancelled}
        statsLoading={!entity.hasStatsLoaded}
        isPartialStaticData={entity.isPartialStaticData}
        entityType={entity.entityType}
        enrollmentFreezeTimezone={entity.enrollmentFreezeTimezone}
        enrollmentFreezeDaysBeforeEvent={entity.enrollmentFreezeDaysBeforeEvent}
        eventEnrollmentType={entity.enrollmentType}
        eventStartTime={entity.startTime}
      />
    ),
    SessionAttendance: (
      <SessionAttendance
        sessionAttendance={entity.attended}
        isPublished={entity.isPublished}
        hasStatsLoaded={!!entity.hasStatsLoaded}
        isEvent={isEvent}
      />
    ),
    sessionWaitlisted: (
      <SessionWaitlisted
        isPublished={entity.isPublished}
        sessionWaitlisted={entity.waiting}
        hasStatsLoaded={!!entity.hasStatsLoaded}
        waitingListEnabled={entity.waitingListEnabled}
        isPartialStaticData={entity.isPartialStaticData}
        isEvent={isEvent}
        isSessionLevelEnrollmentEnabled={entity.enrollmentType === EVENT_ENROLLMENT_ENUM.SESSIONS}
      />
    ),
    sessionAttendanceMode: (
      <SessionAttendanceMode
        isEvent={isEvent}
        loading={entity.isPartialStaticData}
        webAutoAttendanceSettings={entity.webAutoAttendanceSettings}
        checkInSettings={entity.checkInSettings}
      />
    ),
    sessionRecordings: (
      <SessionRecordings
        isPublished={entity.isPublished}
        id={entity.id}
        onShowRecordingsClick={actions.openUploadRecordings}
        entityType={entity.entityType as TILT_ENTITIES}
      />
    ),
    manageSession: (
      <ManageSessionButton
        isPublished={entity.isPublished}
        haveTrackAccess={haveTrackAccess}
        invitedLearnersCount={invitedLearnersCount}
        id={entity.id}
        parentId={entity.parentId}
        entityType={entity.entityType as TILT_ENTITIES}
      />
    ),
    sessionOperations: (
      <SessionOperations
        isSessionCancelled={entity.isCancelled}
        isSessionLive={entity.isOngoing}
        isSessionPublished={entity.isPublished}
        isSessionOver={entity.isOver}
        actions={actions}
        sessionId={entity.id}
        liveChallenge={entity.liveChallenge}
        isPartialStaticData={entity.isPartialStaticData}
        entityType={entity.entityType}
      />
    ),
    childSessions: entity.sessions,
    entityType: entity.entityType as TILT_ENTITIES,
    isPartialStaticData: entity.isPartialStaticData,
    noRowClick: entity.isPartialStaticData,
    isSelected,
    data: entity,
    isFilterRowHidden: entity.isFilterHidden,
  };
}

export function createChildRowData({
  parentId,
  parentData,
  session,
  actions,
  isSelected,
  isParentSelected,
  haveTrackAccess,
  haveBuildAccess,
  invitedLearnersCount,
  showSession,
  onChildSelection,
}: TCreateChildRowDataParam) {
  if (session.noStaticData) {
    return {
      key: session.id,
      columnMinusFirst: '',
      columnZero: (
        <StyledLineContainer className={cs('no-data-row')}>
          <div className="inner-line" />
        </StyledLineContainer>
      ),
      sessionName: <InlineSkeleton />,
      noRowClick: true,
      addColSpan: true,
    };
  }

  const {
    enrollmentType: eventEnrollmentType,
    enrollmentFreezeStatus: eventEnrollmentFreezeStatus,
    enrollmentFreezeDaysBeforeEvent: eventEnrollmentFreezeDaysBeforeEvent,
    enrollmentFreezeTimezone: eventEnrollmentFreezeTimezone,
    enrollmentFreezeEpoch: eventEnrollmentFreezeEpoch,
    startTime: eventStartTime,
    isCancelled: isEventCancelled,
  } = parentData;
  const isEventEnrollmentFreezeApplicable = eventEnrollmentType === EVENT_ENROLLMENT_ENUM.EVENT;

  return {
    key: session.id,
    columnMinusFirst: '',
    columnZero: (
      <StyledLineContainer className={cs({ filtered: !showSession })}>
        <div className="inner-line" />
      </StyledLineContainer>
    ),
    sessionName: (
      <SessionName
        entityType={session.entityType as TILT_ENTITIES}
        sessionName={session.name}
        sessionType={session.type}
        sessionStatus={SessionStatusProcessor(session)}
        liveChallenge={session.liveChallenge}
        hasCheckbox={true}
        checked={isParentSelected || isSelected}
        haveBuildAccess={haveBuildAccess}
        isEventCancelled={isEventCancelled}
        onChecked={
          !haveBuildAccess || isEventCancelled
            ? noop
            : (e: CheckboxChangeEvent) => {
                onChildSelection({
                  id: session.id,
                  parentId,
                  entityType: session.entityType,
                  checked: e.target.checked,
                });
              }
        }
      />
    ),
    sessionStartDate: (
      <SessionDate
        sessionStartTime={session.startTime}
        sessionTimezone={session.timezone}
        sessionDuration={session.duration}
        localStartTime={session.localStartTime}
      />
    ),
    sessionInstructors: <SessionInstructors instructors={session.instructors} />,
    sessionEnrollments: (
      <SessionEnrollments
        sessionEnrollments={session.enrolled}
        isPublished={session.isPublished}
        maxSeats={session.maxSeats}
        maxSeatEnabled={session.maxSeatEnabled}
        enrollmentFreezeStatus={
          isEventEnrollmentFreezeApplicable
            ? eventEnrollmentFreezeStatus
            : session.enrollmentFreezeStatus
        }
        enrollmentFreezeEpoch={
          isEventEnrollmentFreezeApplicable
            ? eventEnrollmentFreezeEpoch
            : session.enrollmentFreezeEpoch
        }
        timezone={session.timezone}
        isCancelled={session.isCancelled}
        statsLoading={!session.hasStatsLoaded}
        isPartialStaticData={session.isPartialStaticData}
        entityType={session.entityType}
        enrollmentFreezeTimezone={eventEnrollmentFreezeTimezone}
        enrollmentFreezeDaysBeforeEvent={eventEnrollmentFreezeDaysBeforeEvent}
        eventEnrollmentType={eventEnrollmentType}
        eventStartTime={eventStartTime}
      />
    ),
    SessionAttendance: (
      <SessionAttendance
        sessionAttendance={session.attended}
        isPublished={session.isPublished}
        hasStatsLoaded={!!session.hasStatsLoaded}
      />
    ),
    sessionWaitlisted: (
      <SessionWaitlisted
        isPublished={session.isPublished}
        hasStatsLoaded={!!session.hasStatsLoaded}
        isPartialStaticData={session.isPartialStaticData}
        sessionWaitlisted={session.waiting}
        waitingListEnabled={session.waitingListEnabled}
      />
    ),
    sessionAttendanceMode: (
      <SessionAttendanceMode
        loading={session.isPartialStaticData}
        webAutoAttendanceSettings={session.webAutoAttendanceSettings}
        checkInSettings={session.checkInSettings}
      />
    ),
    sessionRecordings: (
      <SessionRecordings
        isPublished={session.isPublished}
        id={session.id}
        onShowRecordingsClick={actions.openUploadRecordings}
        entityType={session.entityType as TILT_ENTITIES}
      />
    ),
    manageSession: (
      <ManageSessionButton
        isPublished={session.isPublished}
        haveTrackAccess={haveTrackAccess}
        invitedLearnersCount={invitedLearnersCount}
        id={session.id}
        parentId={session.parentId}
        entityType={session.entityType as TILT_ENTITIES}
      />
    ),
    sessionOperations: (
      <SessionOperations
        isParentCancelled={isEventCancelled}
        isSessionCancelled={session.isCancelled}
        isSessionLive={session.isOngoing}
        isSessionPublished={session.isPublished}
        isSessionOver={session.isOver}
        actions={actions}
        sessionId={session.id}
        liveChallenge={session.liveChallenge}
        isPartialStaticData={session.isPartialStaticData}
        entityType={session.entityType as TILT_ENTITIES}
        parentId={parentId}
      />
    ),
    entityType: session.entityType,
    noRowClick: session.isPartialStaticData,
    isFilterRowHidden: session.isFilterHidden,
    isSelected: isParentSelected || isSelected,
  };
}
