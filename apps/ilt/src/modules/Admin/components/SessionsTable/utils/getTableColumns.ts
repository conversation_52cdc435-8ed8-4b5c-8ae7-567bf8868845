import { SORTING_ORDER, COLUMN_SORT_NAME_MAPPING } from '../constants';

import type { TSortingOrder, TSupportedSortingField } from '../typeDefs';

const getSessionTableColumns = ({
  haveBuildAccess,
  isMultidayEnabled,
  colSortOrder = SORTING_ORDER.ASC,
  colSortField = COLUMN_SORT_NAME_MAPPING.sessionStartDate,
}: {
  haveBuildAccess: boolean;
  isMultidayEnabled: boolean;
  colSortOrder?: TSortingOrder;
  colSortField?: TSupportedSortingField;
}) => {
  const defaultSortOrder = colSortOrder === SORTING_ORDER.ASC ? 'ascend' : 'descend';
  return [
    {
      title: `${isMultidayEnabled ? 'Event/' : ''}Session name`,
      dataIndex: 'sessionName',
      sorter: true,
      sortDirections: ['ascend', 'descend', 'ascend'],
      ...(COLUMN_SORT_NAME_MAPPING.sessionName === colSortField && { defaultSortOrder }),
      width: '15%',
      // width: 320,
    },
    {
      title: 'Start date',
      dataIndex: 'sessionStartDate',
      sorter: true,
      sortDirections: ['ascend', 'descend', 'ascend'],
      ...(COLUMN_SORT_NAME_MAPPING.sessionStartDate === colSortField && { defaultSortOrder }),
      width: '15%',
      // width: 320,
    },
    {
      title: 'Instructors',
      dataIndex: 'sessionInstructors',
      width: '18%',
      // width: 184,
    },
    {
      title: 'Enrolled',
      dataIndex: 'sessionEnrollments',
      width: '7%',
      // width: 88,
    },
    {
      title: 'Attended',
      dataIndex: 'SessionAttendance',
      width: '7%',
      // width: 88,
    },
    {
      title: 'Waitlisted',
      dataIndex: 'sessionWaitlisted',
      width: '7%',
      // width: 88,
    },
    {
      title: 'Attendance mode',
      dataIndex: 'sessionAttendanceMode',
      width: '12%',
      // width: 116,
    },
    {
      title: 'Recording',
      dataIndex: 'sessionRecordings',
      width: '7%',
      // width: 84,
    },
    {
      title: '',
      dataIndex: 'manageSession',
      width: '7%',
      // width: 90,
    },
    {
      title: '',
      dataIndex: 'sessionOperations',
      width: '5%',
      // width: 78,
      render: (element: JSX.Element, record: JSX.Element[]) => {
        if (!haveBuildAccess) {
          return null;
        }

        return element;
      },
    },
  ];
};

export default getSessionTableColumns;
