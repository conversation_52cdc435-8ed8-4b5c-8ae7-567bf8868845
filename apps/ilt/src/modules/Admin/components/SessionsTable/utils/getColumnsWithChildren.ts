const handleColSpanOnName = (text: React.ReactNode, row: { addColSpan?: boolean }) => ({
  children: text,
  props: {
    colSpan: row.addColSpan ? 4 : 1,
  },
});
export const extraColumnsWidth = {
  minusColumn: 38,
  zeroColumn: 32,
} as const;
const getSessionTableColumnsWithChildren = ({ haveBuildAccess }: { haveBuildAccess: boolean }) => [
  {
    key: 'columnMinusFirst',
    title: '',
    dataIndex: 'columnMinusFirst',
    width: extraColumnsWidth.minusColumn,
  },
  {
    key: 'columnZero',
    title: '',
    dataIndex: 'columnZero',
    width: extraColumnsWidth.zeroColumn,
  },
  {
    title: '',
    dataIndex: 'sessionName',
    sorter: true,
    sortDirections: ['ascend', 'descend', 'ascend'],
    width: '15%',
    render: handleColSpanOnName,
  },
  {
    title: 'Start date',
    dataIndex: 'sessionStartDate',
    sorter: true,
    sortDirections: ['ascend', 'descend', 'ascend'],
    defaultSortOrder: 'ascend',
    width: '15%',
    // width: 320,
  },
  {
    title: 'Instructors',
    dataIndex: 'sessionInstructors',
    width: '18%',
    // width: 184,
  },
  {
    title: 'Enrolled',
    dataIndex: 'sessionEnrollments',
    width: '7%',
    // width: 88,
  },
  {
    title: 'Attended',
    dataIndex: 'SessionAttendance',
    width: '7%',
    // width: 88,
  },
  {
    title: 'Waitlisted',
    dataIndex: 'sessionWaitlisted',
    width: '7%',
    // width: 88,
  },
  {
    title: 'Attendance mode',
    dataIndex: 'sessionAttendanceMode',
    width: '12%',
    // width: 116,
  },
  {
    title: 'Recording',
    dataIndex: 'sessionRecordings',
    width: '7%',
    // width: 84,
  },
  {
    title: '',
    dataIndex: 'manageSession',
    width: '7%',
    // width: 90,
  },
  {
    title: '',
    dataIndex: 'sessionOperations',

    render: (element: JSX.Element, record: JSX.Element[]) => {
      if (!haveBuildAccess) {
        return null;
      }

      return element;
    },
    width: '5%',
    // width: 78,
  },
];

export default getSessionTableColumnsWithChildren;
