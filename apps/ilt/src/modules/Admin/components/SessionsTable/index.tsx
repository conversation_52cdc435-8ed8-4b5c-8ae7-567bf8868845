import { useEffect, useState, useRef } from 'react';

import cs from 'classnames';
import { FormattedMessage } from 'react-intl';

import Checkbox from '@mindtickle/checkbox';
import Icon, { ICON_MAP } from '@mindtickle/icon';
import IconWithGradient from '@mindtickle/icon-with-gradient';
import { noop } from '@mindtickle/utils';

import useMultidayEnabled from '~/modules/Admin/hooks/useMultidayEnabled';
import { checkIsEvent } from '~/modules/Admin/utils/checkEntityType';

import BuildActionBar from './components/BuildActionBar';
import EventSessionsTable from './components/EventSessionsTable';
import ToggleCollapse from './components/ToggleCollapse';
import { COLUMN_SORT_NAME_MAPPING, SORTING_ORDER } from './constants';
import useRowDetailsToggle from './hooks/useRowDetailsToggle';
import useTableColumns from './hooks/useTableColumns';
import useTableData from './hooks/useTableData';
import messages from './messages';
import {
  StyledNoDataWrapper,
  StyledSessionsTable,
  StyledIconCheckbox,
  StyledTable,
  StyledLineWrapper,
  StyledLine,
} from './styles';
import { extraColumnsWidth } from './utils/getColumnsWithChildren';
import { stopOnClickEventPropagation } from './utils/stopEventPropagation';

import type {
  TSessionsTable,
  TEntitiesOnSelectInfo,
  TTableEntityObject,
  TILT_ENTITIES,
} from './typeDefs';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';

const SessionsTable = (props: TSessionsTable) => {
  const {
    data,
    actions,
    loading,
    loadMoreLoading,
    hasMore,
    haveTrackAccess,
    haveBuildAccess,
    invitedLearnersCount,
    selectedSessions,
    multiSelectAction,
    selectedCountInfo,
    colSortField,
    colSortOrder,
  } = props;
  const { isMultidayEnabled } = useMultidayEnabled();
  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);
  const tableContainerRef = useRef<HTMLInputElement>(null);
  const [expandAll, setExpandAll] = useState(true);

  const isRowExpanded = (key: string) => expandedRowKeys.includes(key);

  const { tableColumns, childTableColumns } = useTableColumns({
    haveBuildAccess,
    isMultidayEnabled,
    colSortField,
    colSortOrder,
  });

  const { tableData, newIdsForExpansion } = useTableData({
    selectedSessions,
    data,
    actions,
    invitedLearnersCount,
    haveTrackAccess,
  });

  const rowSelection = {
    hideSelectAll: !haveBuildAccess,
    onSelectAll: multiSelectAction.selectAll,
    selectedRowKeys: selectedSessions,
    renderCell: (
      checked: boolean,
      record: { key: string; entityType: TILT_ENTITIES; childSessions: TTableEntityObject[] }
    ) => (
      <StyledIconCheckbox
        className="render-cell-icon-checkbox-container"
        haveBuildAccess={haveBuildAccess}
        checked={checked}
      >
        <Checkbox
          className="render-cell-checkbox"
          checked={checked}
          onClick={stopOnClickEventPropagation}
          onChange={
            haveBuildAccess
              ? (e: CheckboxChangeEvent) => {
                  const tempSelectedEntities: TEntitiesOnSelectInfo = [
                    {
                      id: record.key,
                      entityType: record.entityType,
                      checked: e.target.checked,
                    },
                  ];
                  if (checkIsEvent(record.entityType)) {
                    for (const childSession of record.childSessions) {
                      tempSelectedEntities.push({
                        id: childSession.id,
                        entityType: childSession.entityType,
                        parentId: childSession.parentId,
                        checked: false,
                      });
                    }
                  }
                  actions.onSelect(tempSelectedEntities);
                }
              : noop
          }
        />
        <Icon
          className="render-cell-icon"
          type={checkIsEvent(record.entityType) ? ICON_MAP.EVENT : ICON_MAP.SESSION}
        />
        {checkIsEvent(record.entityType) && isRowExpanded(record.key) && (
          <StyledLineWrapper className="header-line-wrapper">
            <StyledLine className="header-line" />
          </StyledLineWrapper>
        )}
      </StyledIconCheckbox>
    ),
    columnWidth: extraColumnsWidth.zeroColumn,
  };

  // eslint-disable-next-line react-hooks/exhaustive-deps -- calling this without dependency array conciously
  useEffect(() => {
    if (newIdsForExpansion.length) {
      setExpandedRowKeys(expandedRows => {
        const newExpandedRows = [...expandedRows];
        for (const id of newIdsForExpansion) {
          if (!expandedRows.includes(id)) {
            newExpandedRows.push(id);
          }
        }
        return newExpandedRows;
      });
      setExpandAll(true);
    }
  });
  const expandContainer = useRowDetailsToggle({
    tableRef: tableContainerRef,
  });

  return (
    <StyledSessionsTable loading={loading} ref={tableContainerRef}>
      {expandContainer && (
        <ToggleCollapse
          expandContainer={expandContainer}
          expandAll={expandAll}
          tableData={tableData}
          setExpandAll={setExpandAll}
          setExpandedRowKeys={setExpandedRowKeys}
        />
      )}
      <StyledTable
        dataSource={tableData}
        columns={tableColumns}
        className="multiday-table"
        hasSeparators={true}
        expandable={{
          defaultExpandAllRows: true,
          expandedRowKeys: expandedRowKeys,
          onExpandedRowsChange: (_expandedRowKeys: string[]) => {
            setExpandedRowKeys(_expandedRowKeys);
          },
          expandedRowRender: (record: {
            childSessions: TTableEntityObject[];
            key: string;
            isSelected: boolean;
            data: TTableEntityObject;
          }) => (
            <EventSessionsTable
              selectedSessions={selectedSessions}
              tableColumns={childTableColumns}
              data={record.childSessions}
              parentData={record.data}
              isParentSelected={record.isSelected}
              parentId={record.key}
              key={record.key}
              actions={actions}
              haveTrackAccess={haveTrackAccess}
              haveBuildAccess={haveBuildAccess}
              invitedLearnersCount={invitedLearnersCount}
            />
          ),
          columnWidth: extraColumnsWidth.minusColumn,
          rowExpandable: (record: { entityType?: TILT_ENTITIES }) =>
            checkIsEvent(record.entityType),
          showExpandColumn: true,
          columnTitle: <Icon type={'collapseAllExpanded'} className="collapse-icon" />,
          expandIcon: (params: { expanded: boolean; onExpand: Function; record: any }) => {
            const { expanded, onExpand, record } = params;
            const isEvent = checkIsEvent(record.entityType);
            if (!isEvent) {
              return null;
            }
            return (
              <Icon
                type={expanded ? 'down_fillcaret' : 'right_fillcaret'}
                className={cs('collapse-icon')}
                onClick={(e: React.MouseEvent<HTMLElement>) => {
                  onExpand(record, e);
                  stopOnClickEventPropagation(e);
                }}
              />
            );
          },
        }}
        emptyTableData={
          <StyledNoDataWrapper>
            {!loading && (
              <div>
                <IconWithGradient gradient={true} type="noSearchResults" className="no-data-icon" />
                <div className={'no-result-text'}>
                  <FormattedMessage {...messages.NO_RESULT_FOUND} />
                </div>
                <div className={'no-result-text-subtext'}>
                  <FormattedMessage
                    {...messages.NO_RESULT_FOUND_SUBTEXT}
                    values={{ eventSessionLabel: isMultidayEnabled ? 'session/event' : 'session' }}
                  />
                </div>
              </div>
            )}
          </StyledNoDataWrapper>
        }
        onRow={(record: { key: string; entityType: TILT_ENTITIES; noRowClick?: boolean }) => ({
          ...(!record.noRowClick && {
            onClick: () => {
              actions.viewEntity({ id: record.key, entityType: record.entityType });
            },
          }),
        })}
        rowClassName={(record: any) =>
          cs({
            'filtered-hidden-sessions': record.isFilterRowHidden,
          })
        }
        rowSelection={rowSelection}
        loaderType={loadMoreLoading ? 'skeleton' : 'spin'}
        infiniteScroll={true}
        isMultiSelect={true}
        fetchData={actions.loadMoreEntities}
        hasMore={hasMore}
        threshold={0.9}
        pagination={false}
        loading={!!loading}
        loadingMore={!!loadMoreLoading}
        windowScroll={true}
        scroll={undefined}
        onChange={(_1: any, _2: any, { field, order }: { field: string; order: string }) => {
          actions.sortColumn(
            'sort',
            order === 'ascend' ? SORTING_ORDER.ASC : SORTING_ORDER.DESC,
            COLUMN_SORT_NAME_MAPPING[field as keyof typeof COLUMN_SORT_NAME_MAPPING]
          );
        }}
        footer={
          <BuildActionBar
            multiSelectAction={multiSelectAction}
            selectedCountInfo={selectedCountInfo}
          />
        }
      />
    </StyledSessionsTable>
  );
};

export default SessionsTable;
