import { FormattedMessage } from 'react-intl';

import Tooltip from '@mindtickle/tooltip';

import { DOUBLE_DASH } from '~/modules/Admin/constants';
import { getBodyAsPopupContainer } from '~/utils';

import messages from '../../messages';
import SmallLoadingSkeleton from '../SmallLoadingSkeleton';

import { StyledSessionAttendanceContainer } from './styles';

import type { TSessionAttendance } from '../../typeDefs';

const SessionAttendance = (props: TSessionAttendance) => {
  const { isPublished, sessionAttendance, hasStatsLoaded, isEvent } = props;
  let attendanceElement: React.ReactNode =
    sessionAttendance || sessionAttendance === 0 ? sessionAttendance : DOUBLE_DASH;

  if (isEvent) {
    attendanceElement = (
      <Tooltip
        getPopupContainer={getBodyAsPopupContainer}
        title={<FormattedMessage {...messages.TOOLTIP_EVENT_ATTENDED_COUNT} />}
      >
        {attendanceElement}
      </Tooltip>
    );
  }
  return (
    <StyledSessionAttendanceContainer className="disabled-filtered">
      {isPublished ? hasStatsLoaded ? attendanceElement : <SmallLoadingSkeleton /> : DOUBLE_DASH}
    </StyledSessionAttendanceContainer>
  );
};

export default SessionAttendance;
