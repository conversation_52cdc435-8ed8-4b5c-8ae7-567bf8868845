import { FormattedMessage } from 'react-intl';

import Icon, { ICON_MAP } from '@mindtickle/icon';
import Tooltip from '@mindtickle/tooltip';

import { ENROLLMENT_LOCK_WARNING } from '~/modules/Admin/config/sessions.constants';
import { DOUBLE_DASH } from '~/modules/Admin/constants';
import useEnrolmentFreezeDetails from '~/modules/Admin/hooks/useEnrolmentFreezeDetails';
import { checkIsEvent } from '~/modules/Admin/utils/checkEntityType';
import { getBodyAsPopupContainer } from '~/utils';

import messages from '../../messages';
import SmallLoadingSkeleton from '../SmallLoadingSkeleton';

import {
  StyledEnrollmentInfoContainer,
  StyledMaxSeatsContainer,
  StyledSessionEnrollments,
  StyledSessionEnrollmentsContainer,
} from './styles';

import type { TSessionEnrollments } from '../../typeDefs';

const SessionEnrollments = (props: TSessionEnrollments) => {
  const {
    isPublished,
    maxSeats,
    maxSeatEnabled,
    isCancelled,
    sessionEnrollments,
    statsLoading,
    isPartialStaticData,
    entityType,
  } = props;
  const isEvent = checkIsEvent(entityType);
  const {
    enrollmentFreezeEnabled,
    isEnrollmentFrozen,
    enrollmentFreezeDateTime,
    isRowEligibleForEnrolmentFreeze,
  } = useEnrolmentFreezeDetails(props);

  let enrollmentsElement: React.ReactNode =
    sessionEnrollments || sessionEnrollments === 0 ? sessionEnrollments : DOUBLE_DASH;
  if (isEvent) {
    enrollmentsElement = (
      <Tooltip
        getPopupContainer={getBodyAsPopupContainer}
        title={<FormattedMessage {...messages.TOOLTIP_EVENT_ENROLMENT_COUNT} />}
      >
        {enrollmentsElement}
      </Tooltip>
    );
  }

  const showEnrollmentInfo = () => (
    <StyledEnrollmentInfoContainer>
      <StyledSessionEnrollments loading={statsLoading} className="disabled-filtered">
        {statsLoading ? <SmallLoadingSkeleton /> : enrollmentsElement}
      </StyledSessionEnrollments>
      {!isPartialStaticData &&
        !isCancelled &&
        isRowEligibleForEnrolmentFreeze &&
        enrollmentFreezeEnabled &&
        (isEnrollmentFrozen ? (
          <Tooltip
            overlayClassName={'tooltipInfo'}
            title={
              isEvent ? ENROLLMENT_LOCK_WARNING.NO_DATE_EVENT : ENROLLMENT_LOCK_WARNING.NO_DATE
            }
          >
            <Icon type={ICON_MAP.LOCKED} className={'icon-locked'} />
          </Tooltip>
        ) : (
          <Tooltip
            overlayClassName={'tooltipInfo'}
            title={ENROLLMENT_LOCK_WARNING.FUTURE + enrollmentFreezeDateTime}
          >
            <Icon type={ICON_MAP.UNLOCKED} className={'icon-unlocked'} />
          </Tooltip>
        ))}
    </StyledEnrollmentInfoContainer>
  );
  return (
    <StyledSessionEnrollmentsContainer className="disabled-filtered">
      <div>
        {isPublished ? showEnrollmentInfo() : <span className="no-enrollments">{DOUBLE_DASH}</span>}
      </div>
      {!isPartialStaticData && !!maxSeats && maxSeatEnabled && (
        <StyledMaxSeatsContainer className="disabled-filtered">
          Max: {maxSeats}
        </StyledMaxSeatsContainer>
      )}
    </StyledSessionEnrollmentsContainer>
  );
};

export default SessionEnrollments;
