import {
  MEDIA_STATUS,
  RECORDINGS_LOADING_STATES,
  RECORDINGS_SAVE_STATUS_ENUM,
  UPLOADING_STATUS,
} from '~/modules/Admin/config/recordings.constants';

import { TEXT_STATUS } from './constants';
import { UPLOAD_TEXTS } from './messages';

import type { TCalculateRecordingText } from '../../typeDefs';

// eslint-disable-next-line max-statements
export function calculateRecordingText({
  saveStatus,
  draftRecordings,
  sessionRecordings,
}: TCalculateRecordingText) {
  const { status: recordingsLoadingStatus, recordingsUnpublished } = sessionRecordings || {};

  if (saveStatus === RECORDINGS_SAVE_STATUS_ENUM.FAILED) {
    return {
      message: UPLOAD_TEXTS.FAILED,
      status: TEXT_STATUS.FAILED,
    };
  }

  if (saveStatus === RECORDINGS_SAVE_STATUS_ENUM.INPROGRESS) {
    return {
      message: UPLOAD_TEXTS.SAVING,
      status: TEXT_STATUS.INPROGRESS,
    };
  }

  if (draftRecordings) {
    const { failed, processing, uploading } = draftRecordings.reduce(
      (acc: any, recording: any) => {
        const { uploading /*mediaId*/ } = recording;
        if (uploading === UPLOADING_STATUS.FAILED) {
          acc.failed += 1;
        }
        if (uploading === UPLOADING_STATUS.IN_PROGRESS) {
          acc.uploading += 1;
        }
        return acc;
      },
      { failed: 0, processing: 0, uploading: 0 }
    );
    if (failed) {
      return {
        message: UPLOAD_TEXTS.FAILED,
        status: TEXT_STATUS.FAILED,
      };
    }
    if (uploading || processing) {
      return {
        message: UPLOAD_TEXTS.UPLOADING,
        status: TEXT_STATUS.INPROGRESS,
      };
    }
  }
  if (recordingsUnpublished) {
    const { failed, processing, recordingCount } = recordingsUnpublished.reduce(
      (acc: any, recording: any) => {
        const { mediaStatus } = recording;
        if (mediaStatus === MEDIA_STATUS.PROCESSING) {
          acc.processing += 1;
        }
        if (mediaStatus === MEDIA_STATUS.SUCCESS) {
          acc.recordingCount += 1;
        }
        if (mediaStatus === MEDIA_STATUS.FAILED) {
          acc.failed += 1;
        }
        return acc;
      },
      { failed: 0, processing: 0, recordingCount: 0 }
    );
    if (failed) {
      return {
        message: UPLOAD_TEXTS.FAILED,
        status: TEXT_STATUS.FAILED,
      };
    }
    if (processing) {
      return {
        message: UPLOAD_TEXTS.PROCESSING,
        status: TEXT_STATUS.INPROGRESS,
      };
    }

    if (recordingCount) {
      return {
        message: UPLOAD_TEXTS.UPLOADED,
        status: TEXT_STATUS.LINK,
      };
    }
  } else if (recordingsLoadingStatus === RECORDINGS_LOADING_STATES.INPROGRESS) {
    return {
      message: UPLOAD_TEXTS.LOADING,
      status: TEXT_STATUS.LOADING,
    };
  }
  return {};
}
