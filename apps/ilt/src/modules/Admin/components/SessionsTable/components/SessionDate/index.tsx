import { DOUBLE_DASH } from '~/modules/Admin/constants';
import { getSessionsTableDateFormat, getSessionsTableTimeFormat } from '~/modules/Admin/utils';

import { StyledSessionDateContainer } from './styles';

import type { TSessionDate } from '../../typeDefs';

const SessionDate = ({
  sessionStartTime,
  sessionTimezone,
  sessionDuration,
  localStartTime,
  noDate = false,
}: TSessionDate) => {
  let dateFormat = getSessionsTableDateFormat(localStartTime || sessionStartTime);
  let timeFormat = getSessionsTableTimeFormat(localStartTime || sessionStartTime);
  return (
    <StyledSessionDateContainer>
      {noDate ? (
        <>{DOUBLE_DASH}</>
      ) : (
        <>
          <div className="date">{dateFormat}</div>
          <div className="timezone">
            <span>{timeFormat}</span>
            <span> &nbsp;</span>
            <span>{sessionTimezone.shortDisplayName}</span>
          </div>
          <div className="duration">
            <span>{sessionDuration.displayValue}</span>
          </div>
        </>
      )}
    </StyledSessionDateContainer>
  );
};

export default SessionDate;
