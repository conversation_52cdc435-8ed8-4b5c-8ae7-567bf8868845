import styled, { css } from 'styled-components';

import { tokens, mixins, theme } from '@mindtickle/styles/lib';

import { TEXT_STATUS } from './constants';

export const StyledDiv = styled.span<{ status: string }>`
  font-family: ${theme.fontFamily.DEFAULT};
  ${props => {
    let cssValue = ``;
    if (props.status && props.status !== TEXT_STATUS.LOADING) {
      cssValue += `
      font-weight: 600;
      cursor: pointer;`;
    }

    if (props.status === TEXT_STATUS.LINK) {
      cssValue += `
        color: ${tokens.textTokens.COLOR_TEXT_ACCENT};
      `;
    }
    if (props.status === TEXT_STATUS.FAILED) {
      cssValue += `
        color: ${tokens.textTokens.COLOR_TEXT_DANGER};
      `;
    }
    return css`
      ${cssValue}
    `;
  }}
`;

export const skeletonWithoutIndentProps = {
  avatar: false,
  active: false,
  title: { width: '40%', margin: '5px auto 5px 0' },
};

export const StyledSessionRecordingsContainer = styled.div`
  .upload-loader {
    .spinner {
      margin: 0;
    }
  }
  .recording-uploaded {
    color: ${tokens.filetypeTokens.COLOR_FILETYPE_VIDEO};
    font-size: 25px;
  }
  .recording-upload-failed {
    color: ${tokens.textTokens.COLOR_TEXT_DANGER};
    font-size: 25px;
  }
  .black-normal-text {
    ${mixins.blackText()}
  }
`;
