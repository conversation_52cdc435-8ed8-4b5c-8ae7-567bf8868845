import styled from 'styled-components';

import Popover from '@mindtickle/popover';
import { mixins } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS } from '~/config/constants';

export const StyledSessionInstructorContainer = styled.div`
  .black-normal-text {
    ${mixins.blackText()}
  }
`;

export const StyledInstructorInfo = styled.div`
  .instructor-name {
    ${mixins.blackText};
    display: flex;
  }
  .instructor-email {
    ${mixins.smallDarkLink};
    display: flex;
    margin: 2px 0;
  }
  .instructor-count {
    ${mixins.smallActiveLink};
    font-weight: 600;
    padding: 0;
    display: flex;
    height: 16px;
  }
`;

export const StyledPopover = styled(Popover)`
  .${THEME_PREFIX_CLS}-popover-inner {
    padding: 16px 0px;
  }
  .${THEME_PREFIX_CLS}-popover-arrow {
    background-color: transparent !important;
    transform: translateY(calc(100% - 1px)) translateX(-50%);
  }
`;

export const StyledPopoverInstructorContainer = styled.div`
  width: 300px;
  max-height: 165px;
  overflow: auto;
`;

export const StyledInstructorRow = styled.div`
  display: flex;
  flex-direction: column;
  margin-bottom: '20px';
  .instructor-email {
    margin-top: 2px;
    margin-left: 16px;
    font-size: 12px;
  }
  .instructor-name {
    margin-left: 16px;
    font-size: 14px;
  }
  .instructor-separator {
    margin: 8px 0;
  }
`;
