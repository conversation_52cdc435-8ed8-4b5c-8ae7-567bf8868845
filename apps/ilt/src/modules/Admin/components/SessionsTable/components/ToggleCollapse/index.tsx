import { createPortal } from 'react-dom';

import { ICON_MAP } from '@mindtickle/icon';

import { checkIsEvent } from '~/modules/Admin/utils/checkEntityType';

import { TToggleCollapse } from '../../typeDefs';

import { StyledToggleCollapseIcon } from './styles';

const ToggleCollapse = ({
  expandContainer,
  expandAll,
  tableData,
  setExpandAll,
  setExpandedRowKeys,
}: TToggleCollapse) =>
  createPortal(
    <StyledToggleCollapseIcon
      type={expandAll ? ICON_MAP.COLLAPSE_ALL_EXPANDED : ICON_MAP.EXPAND_ALL_COLLAPSE}
      onClick={() => {
        setExpandAll(expandAll => {
          let keys: string[] = [];
          if (!expandAll) {
            keys = tableData.reduce((acc, { key, entityType }) => {
              if (checkIsEvent(entityType)) {
                acc.push(key);
              }
              return acc;
            }, keys);
          }
          setExpandedRowKeys(keys);
          return !expandAll;
        });
      }}
    />,
    expandContainer
  );

export default ToggleCollapse;
