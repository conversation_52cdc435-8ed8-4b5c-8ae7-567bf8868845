import styled from 'styled-components';

import { tokens, mixins } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS } from '~/config/constants';

export const StyledSessionEnrollmentsContainer = styled.div`
  .icon-locked {
    color: ${tokens.iconTokens.COLOR_ICON_WARNING};
  }
  .icon-unlocked {
    color: ${tokens.iconTokens.COLOR_ICON_DISABLED};
  }
  .no-enrollments {
    margin-right: 20px;
  }
`;

export const StyledEnrollmentInfoContainer = styled.div`
  display: flex;
  align-items: center;
`;

export const StyledSessionEnrollments = styled.span<{ loading: boolean }>`
  ${mixins.blackText}
  margin-right: 10px;
  width: ${props => (props.loading ? '25%' : 'auto')};
  .${THEME_PREFIX_CLS}-skeleton-title {
    width: auto !important;
  }
`;

export const StyledMaxSeatsContainer = styled.div`
  ${mixins.smallDarkLink}
`;
