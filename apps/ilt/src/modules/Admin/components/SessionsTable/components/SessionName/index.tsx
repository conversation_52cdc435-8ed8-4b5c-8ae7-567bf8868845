import isEmpty from 'lodash/isEmpty';
import { FormattedMessage } from 'react-intl';

import EllipsisTooltip from '@mindtickle/ellipsis-tooltip';
import Icon, { ICON_MAP } from '@mindtickle/icon';
import Tooltip from '@mindtickle/tooltip';

import { LIVE_CHALLENGE } from '~/modules/Admin/config/live-challenges.constants';
import entityTypeLabels from '~/modules/Admin/messages/iltEntityTypes';
import { checkIsEvent } from '~/modules/Admin/utils/checkEntityType';
import {
  getEntityFormatTypeIcon,
  getEntityFormatTypeIconTooltip,
} from '~/modules/Admin/utils/entityFormatType';

import { stopOnClickEventPropagation } from '../../utils/stopEventPropagation';

import {
  StyledColumnSessionNameContainer,
  StyledSessionInfoContainer,
  StyledSessionName,
  StyledCheckbox,
  StyledIconCheckbox,
} from './styles';

import type { TSessionName } from '../../typeDefs';

const SessionName = ({
  sessionName,
  sessionType,
  entityType,
  sessionStatus,
  liveChallenge,
  hasCheckbox,
  checked,
  onChecked,
  haveBuildAccess,
  isEventCancelled,
}: TSessionName) => {
  const entityTypeLabel = checkIsEvent(entityType)
    ? entityTypeLabels.EVENT
    : entityTypeLabels.SESSION;

  return (
    <StyledColumnSessionNameContainer>
      {hasCheckbox && (
        <StyledIconCheckbox
          showCheckboxOnHover={!!(haveBuildAccess && !isEventCancelled)}
          className="icon-checkbox-container"
          checked={checked || false}
        >
          <StyledCheckbox
            checked={checked}
            onChange={onChecked}
            onClick={stopOnClickEventPropagation}
            className="child-checkbox"
          />
          <Icon className="render-cell-icon" type={ICON_MAP.SESSION} />
        </StyledIconCheckbox>
      )}
      <div>
        <StyledSessionName>
          <EllipsisTooltip
            wrapperClassName="session-name-tooltip"
            placement="top"
            title={sessionName}
            showTooltipWhenEllipsis={true}
            linesToClamp={2}
          >
            {sessionName}
          </EllipsisTooltip>
        </StyledSessionName>
        <StyledSessionInfoContainer className="info-container">
          <Tooltip
            overlayClassName={'tooltipInfo'}
            title={<FormattedMessage {...getEntityFormatTypeIconTooltip(sessionType)} />}
          >
            <Icon type={getEntityFormatTypeIcon(sessionType)} className={'icon-style'} />
          </Tooltip>
          <FormattedMessage {...entityTypeLabel} />
          {sessionStatus}
          {(liveChallenge || !isEmpty(liveChallenge)) && (
            <Tooltip
              overlayClassName={'tooltipInfo'}
              title={<FormattedMessage {...LIVE_CHALLENGE.LIVE_CHALLENGE} />}
            >
              <Icon type={ICON_MAP.TROPHY} className={'icon-style'} />
            </Tooltip>
          )}
        </StyledSessionInfoContainer>
      </div>
    </StyledColumnSessionNameContainer>
  );
};

export default SessionName;
