import styled, { css } from 'styled-components';

import ActionsDropdown from '@mindtickle/actions-dropdown';
import Button from '@mindtickle/button';
import { tokens, theme } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS } from '~/config/constants';

export const dropdownCommonCss = css`
  .${THEME_PREFIX_CLS}-dropdown-menu {
    padding: 8px;
    display: block;
    border: 1px solid ${tokens.borderTokens.COLOR_BORDER_SECONDARY};

    .arrow-icon {
      position: absolute;
      right: 8px;
      top: 9px;
    }

    .${THEME_PREFIX_CLS}-dropdown-menu-item {
      border-radius: 4px;
      margin-bottom: 4px;
      font-weight: ${theme.fontWeight.SEMIBOLD};

      &:last-child {
        margin-bottom: 0;
      }
      a {
        font-weight: ${theme.fontWeight.SEMIBOLD};
      }
      &:not(.${THEME_PREFIX_CLS}-dropdown-menu-item-danger) {
        color: ${tokens.textTokens.COLOR_TEXT_TERTIARY};

        a {
          color: ${tokens.textTokens.COLOR_TEXT_TERTIARY};
        }

        &:hover {
          color: ${tokens.textTokens.COLOR_TEXT_INVERSE};
          background-color: ${tokens.bgTokens.COLOR_BG_ACCENT};
          a {
            color: ${tokens.textTokens.COLOR_TEXT_INVERSE};
          }
        }
      }
      &.${THEME_PREFIX_CLS}-dropdown-menu-item-disabled {
        color: ${tokens.textTokens.COLOR_TEXT_SECONDARY};
        background: none;
        border-color: ${tokens.bgTokens.COLOR_BG_TRANSPARENT};
        cursor: not-allowed;

        &:hover {
          color: ${tokens.textTokens.COLOR_TEXT_DISABLED};
          background-color: ${tokens.bgTokens.COLOR_BG_TRANSPARENT};
          a {
            color: ${tokens.textTokens.COLOR_TEXT_DISABLED};
          }
        }

        &.${THEME_PREFIX_CLS}-dropdown-menu-item-danger {
          color: ${tokens.textTokens.COLOR_TEXT_INVERSE};
          background-color: ${tokens.bgTokens.COLOR_BG_DANGER_STRONG};
          opacity: 0.5;
        }
      }
    }
    .${THEME_PREFIX_CLS}-dropdown-menu-item a {
      margin: 0;
      padding: 0;
    }
  }
`;

export const StyledActionsDropdown = styled(ActionsDropdown)`
  ${dropdownCommonCss}
  .${THEME_PREFIX_CLS}-dropdown-menu {
    white-space: nowrap;
    display: flex;
    flex-direction: column;
    .${THEME_PREFIX_CLS}-dropdown-menu-item span {
      width: 100%;
    }
  }
`;

export const MoreActionBtn = styled(Button)`
  &&.more-action-btn {
    border-radius: 16px;
    width: 40px;
    height: 24px;
    padding: 1px;
    border: 1px solid ${tokens.borderTokens.COLOR_BORDER_DEFAULT};
    background-color: ${tokens.bgTokens.COLOR_BG_DEFAULT};
    box-shadow: none;

    &:hover {
      border: 1px solid ${tokens.borderTokens.COLOR_BORDER_DEFAULT};
      background-color: ${tokens.bgTokens.COLOR_BG_DEFAULT};
    }

    &:focus {
      border: 1px solid ${tokens.borderTokens.COLOR_BORDER_ACCENT};
      background-color: ${tokens.bgTokens.COLOR_BG_DEFAULT};
      color: ${tokens.textTokens.COLOR_TEXT_ACCENT};

      .more-action-icon {
        color: ${tokens.iconTokens.COLOR_ICON_ACCENT};
      }
    }

    .more-action-icon {
      color: ${tokens.iconTokens.COLOR_ICON_DEFAULT};
    }
  }
`;
