import { FormattedMessage } from 'react-intl';

import Tooltip from '@mindtickle/tooltip';

import { DOUBLE_DASH } from '~/modules/Admin/constants';
import { getBodyAsPopupContainer } from '~/utils';

import messages from '../../messages';
import SmallLoadingSkeleton from '../SmallLoadingSkeleton';

import { StyledSessionWaitlistedContainer } from './styles';

import type { TSessionWaitlisted } from '../../typeDefs';

const SessionWaitlisted = (props: TSessionWaitlisted) => {
  const {
    isPublished,
    hasStatsLoaded,
    isPartialStaticData,
    waitingListEnabled,
    sessionWaitlisted,
    isSessionLevelEnrollmentEnabled,
    isEvent,
  } = props;
  let waitListedElement: React.ReactNode =
    sessionWaitlisted || sessionWaitlisted === 0 ? sessionWaitlisted : DOUBLE_DASH;
  if (isEvent && !isSessionLevelEnrollmentEnabled) {
    waitListedElement = (
      <Tooltip
        getPopupContainer={getBodyAsPopupContainer}
        title={<FormattedMessage {...messages.TOOLTIP_EVENT_WAITLISTED_COUNT} />}
      >
        {waitListedElement}
      </Tooltip>
    );
  }
  return (
    <StyledSessionWaitlistedContainer className="disabled-filtered">
      {isPartialStaticData ? (
        <SmallLoadingSkeleton />
      ) : isEvent && isSessionLevelEnrollmentEnabled ? (
        DOUBLE_DASH
      ) : waitingListEnabled ? (
        !isPublished ? (
          DOUBLE_DASH
        ) : hasStatsLoaded ? (
          waitListedElement
        ) : (
          <SmallLoadingSkeleton />
        )
      ) : (
        'Disabled'
      )}
    </StyledSessionWaitlistedContainer>
  );
};

export default SessionWaitlisted;
