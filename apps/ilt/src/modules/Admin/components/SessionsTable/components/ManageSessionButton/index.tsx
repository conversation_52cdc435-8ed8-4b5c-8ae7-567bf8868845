import { FormattedMessage } from 'react-intl';
import { Link } from 'react-router-dom';
import { LIFECYCLE_STAGES } from 'ui_shell/GlobalConstants';

import Button from '@mindtickle/button';
import Tooltip from '@mindtickle/tooltip';

import ModuleRoutes from '~/modules/Admin/config/routes';
import { MESSAGES } from '~/modules/Admin/config/sessions.constants';
import { checkIsEvent } from '~/modules/Admin/utils/checkEntityType';

import { StyledManageSessionContainer } from './styles';

import type { TManageSessionButton } from '../../typeDefs';

const ManageSessionButton = (props: TManageSessionButton) => {
  const { isPublished, haveTrackAccess, invitedLearnersCount, id, parentId, entityType } = props;
  let content = null;
  const toPrefix = `../${ModuleRoutes.lifecycle[LIFECYCLE_STAGES.BUILD]}/manage/`;
  const manageSessionRoute =
    toPrefix +
    (parentId && id
      ? `event/${parentId}/session/${id}`
      : checkIsEvent(entityType)
      ? `event/${id}`
      : `${id}`);
  const isManageSessionDisabled = !haveTrackAccess || !invitedLearnersCount || !isPublished;

  // TODO(SUGATA): TO check on design whether this will be applicable now since button width is smaller
  //const buttonText = data[rowIndex].isCancelled ? "View enrollment" : "Manage session";

  const manageButton = (
    <Link
      to={manageSessionRoute}
      onClick={event => {
        if (isManageSessionDisabled) {
          event.preventDefault(); // Prevent navigation if button is disabled
        }
      }}
    >
      <Button
        className={'manage-session-button'}
        size="small"
        type="secondary"
        disabled={isManageSessionDisabled}
      >
        Manage
      </Button>
    </Link>
  );

  if (!isPublished) {
    content = (
      <Tooltip
        getPopupContainer={() => document.body}
        title={<FormattedMessage {...MESSAGES.INFO.MANAGING_UNPUBLISHED} />}
      >
        <div>{manageButton}</div>
      </Tooltip>
    );
  } else if (!haveTrackAccess) {
    content = (
      <Tooltip
        getPopupContainer={() => document.body}
        title={<FormattedMessage {...MESSAGES.INFO.NO_TRACK_PERMISSION} />}
      >
        <div>{manageButton}</div>
      </Tooltip>
    );
  } else if (!invitedLearnersCount) {
    content = (
      <Tooltip
        getPopupContainer={() => document.body}
        title={<FormattedMessage {...MESSAGES.INFO.NO_INVITED_LEARNERS} />}
      >
        <div>{manageButton}</div>
      </Tooltip>
    );
  } else {
    content = manageButton;
  }

  return <StyledManageSessionContainer>{content}</StyledManageSessionContainer>;
};

export default ManageSessionButton;
