import AssetIcon, { MEDIA_TYPE, MEDIA_ICON_SIZES } from '@mindtickle/asset-icon';
import Icon, { ICON_MAP } from '@mindtickle/icon';
import Loader from '@mindtickle/loader';
import Skeleton from '@mindtickle/skeleton';

import { DOUBLE_DASH } from '~/modules/Admin/constants';
import { checkIsEvent } from '~/modules/Admin/utils/checkEntityType';

import { TEXT_STATUS } from './constants';
import { useRecordings } from './hooks';
import { UPLOAD_TEXTS } from './messages';
import { StyledDiv, StyledSessionRecordingsContainer, skeletonWithoutIndentProps } from './styles';

import type { TSessionRecordings } from '../../typeDefs';

const messageIconMapping = {
  [UPLOAD_TEXTS.FAILED]: () => <Icon type={ICON_MAP.WARNING} className="recording-upload-failed" />,
  [UPLOAD_TEXTS.UPLOADING]: () => <Loader size={'sizeSmall'} className="upload-loader" />,
  [UPLOAD_TEXTS.SAVING]: () => <Loader size={'sizeSmall'} className="upload-loader" />,
  [UPLOAD_TEXTS.PROCESSING]: () => <Loader size={'sizeSmall'} className="upload-loader" />,
  [UPLOAD_TEXTS.UPLOADED]: () => (
    <AssetIcon
      assetType={MEDIA_TYPE.VIDEO}
      size={MEDIA_ICON_SIZES.DEFAULT}
      className="recording-uploaded"
    />
  ),
  [UPLOAD_TEXTS.LOADING]: () => <Loader size={'sizeSmall'} className="upload-loader" />,
};

const SessionRecordings = ({
  id,
  isPublished,
  entityType,
  onShowRecordingsClick,
}: TSessionRecordings) => {
  const { message, status } = useRecordings({ sessionId: id, isPublished, entityType });

  return (
    <StyledSessionRecordingsContainer>
      {(checkIsEvent(entityType) ? (
        <div className="black-normal-text">{DOUBLE_DASH}</div>
      ) : (
        !!status &&
        (status === TEXT_STATUS.LOADING ? (
          <Skeleton withoutIndentProps={skeletonWithoutIndentProps} />
        ) : (
          <StyledDiv
            onClick={e => {
              e && e.stopPropagation();
              onShowRecordingsClick({ id });
            }}
            status={status}
          >
            {messageIconMapping[message]()}
          </StyledDiv>
        ))
      )) || <div className="black-normal-text">{DOUBLE_DASH}</div>}
    </StyledSessionRecordingsContainer>
  );
};

export default SessionRecordings;
