import Button from '@mindtickle/button';
import Divider from '@mindtickle/divider';
import EllipsisTooltip from '@mindtickle/ellipsis-tooltip';

import { DOUBLE_DASH } from '~/modules/Admin/constants';

import {
  StyledInstructorInfo,
  StyledInstructorRow,
  StyledPopover,
  StyledPopoverInstructorContainer,
  StyledSessionInstructorContainer,
} from './styles';

import type { TSessionInstructor } from '../../typeDefs';

const SessionInstructor = (props: TSessionInstructor) => {
  const { instructors = [], isEvent = false } = props;

  const renderRemainingInstructors = () => {
    const remainingInstructors = instructors.slice(1);
    return (
      <StyledPopoverInstructorContainer>
        {remainingInstructors.map((instructor, index) => (
          <StyledInstructorRow key={index}>
            <div className="instructor-name">{instructor.name}</div>
            <div className="instructor-email">{instructor.email}</div>
            {index < remainingInstructors.length - 1 && (
              <Divider className="instructor-separator" />
            )}
          </StyledInstructorRow>
        ))}
      </StyledPopoverInstructorContainer>
    );
  };

  return (
    <StyledSessionInstructorContainer>
      {isEvent || instructors.length === 0 ? (
        <div className="black-normal-text">{DOUBLE_DASH}</div>
      ) : (
        <StyledInstructorInfo>
          <div className="instructor-name">
            <EllipsisTooltip
              wrapperClassName="view-session-title"
              placement="bottom"
              title={instructors[0].name}
              showTooltipWhenEllipsis={true}
            >
              {instructors[0].name}
            </EllipsisTooltip>
          </div>
          <div className="instructor-email">
            <EllipsisTooltip
              wrapperClassName="view-session-title"
              placement="bottom"
              title={instructors[0].email}
              showTooltipWhenEllipsis={true}
            >
              {instructors[0].email}
            </EllipsisTooltip>
          </div>
          {instructors.length > 1 && (
            <StyledPopover
              content={renderRemainingInstructors()}
              trigger="hover"
              align={{ targetOffset: [0, -4] }}
            >
              <Button className="instructor-count" type="text">
                + {instructors.length - 1} more
              </Button>
            </StyledPopover>
          )}
        </StyledInstructorInfo>
      )}
    </StyledSessionInstructorContainer>
  );
};

export default SessionInstructor;
