import styled, { css } from 'styled-components';

import { BadgeWithStatus } from '@mindtickle/badge';
import Checkbox from '@mindtickle/checkbox';
import { mixins, tokens } from '@mindtickle/styles/lib';

export const StyledIconCheckbox = styled.div<{
  checked: boolean;
  showCheckboxOnHover: boolean;
}>`
  justify-content: center;
  position: relative;
  height: 16px;
  width: 16px;

  &.render-cell-icon-checkbox-container {
    display: flex;
  }
  .child-checkbox .ilt-checkbox {
    top: 0;
  }

  ${({ showCheckboxOnHover }) =>
    showCheckboxOnHover &&
    css`
      &:hover {
        .child-checkbox {
          visibility: visible;
        }

        .render-cell-icon {
          visibility: hidden;
        }
      }
    `}

  .child-checkbox {
    visibility: ${({ checked }) => (checked ? 'visible' : 'hidden')};
    display: inline-flex;
  }

  .render-cell-icon {
    position: absolute;
    font-size: 16px;
    &.icon-session {
      font-size: 15px;
    }
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    color: ${tokens.iconTokens.COLOR_ICON_DEFAULT};
    visibility: ${({ checked }) => (checked ? 'hidden' : 'visible')};
  }
`;

export const StyledCheckbox = styled(Checkbox)``;

export const StyledToken = styled(BadgeWithStatus)``;

export const StyledColumnSessionNameContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const StyledSessionInfoContainer = styled.div`
  ${mixins.smallDarkLink};
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;

  .icon-style {
    font-size: 10px;
    color: ${tokens.iconTokens.COLOR_ICON_DEFAULT};
  }
`;

export const StyledSessionName = styled.div`
  ${mixins.blackText}
  font-weight: 600;
  text-align: left;

  .session-name-tooltip {
    word-break: break-word;
  }
`;
