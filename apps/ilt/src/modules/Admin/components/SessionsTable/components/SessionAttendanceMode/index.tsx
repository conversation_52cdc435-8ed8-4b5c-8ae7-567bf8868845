import { DOUBLE_DASH } from '~/modules/Admin/constants';

import SmallLoadingSkeleton from '../SmallLoadingSkeleton';

import { StyledSessionAttendanceModeContainer } from './styles';

import type { TSessionAttendanceMode } from '../../typeDefs';

const SessionAttendanceMode = (props: TSessionAttendanceMode) => {
  const { webAutoAttendanceSettings, checkInSettings = {}, loading, isEvent } = props;
  const autoAttendanceSettings = webAutoAttendanceSettings ?? {
    isAutoAttendanceEnabled: false,
    isLinkValidated: false,
  };
  return (
    <StyledSessionAttendanceModeContainer className="disabled-filtered">
      {isEvent ? (
        DOUBLE_DASH
      ) : loading ? (
        <SmallLoadingSkeleton />
      ) : autoAttendanceSettings.isAutoAttendanceEnabled ? (
        'Auto attendance'
      ) : checkInSettings.isSelfCheckInActivated ? (
        'Self check-in'
      ) : (
        DOUBLE_DASH
      )}
    </StyledSessionAttendanceModeContainer>
  );
};

export default SessionAttendanceMode;
