import { useState } from 'react';

import cs from 'classnames';
import { FormattedMessage } from 'react-intl';

import Icon, { ICON_MAP } from '@mindtickle/icon';
import { PILL_STATES } from '@mindtickle/pill';

import { THEME_PREFIX_CLS } from '~/config/constants';
import { MAX_SESSIONS_WITHIN_EVENT } from '~/modules/Admin/constants/events';
import { ILT_ENTITIES } from '~/modules/Admin/constants/module';

import {
  StyledExpandedTable,
  StyledLineContainer,
  StyledActionPill,
  StyledShowHideSession,
} from '../../styles';
import { createChildRowData } from '../../utils/rowDataCreation';

import messages from './messages';
import { StyledSessionCreateContainer } from './styles';

import type { TShowHideSession, TExpandedTableData, TEntityOnSelectInfo } from '../../typeDefs';

const ShowHideSession = (props: TShowHideSession) => {
  const { onClick, hiddenSessionCount, isSessionCanBeShown } = props;
  return (
    <StyledShowHideSession onClick={onClick}>
      {isSessionCanBeShown === true &&
        `Hide ${hiddenSessionCount} ${hiddenSessionCount > 1 ? 'sessions' : 'session'}`}
      {!isSessionCanBeShown &&
        `${hiddenSessionCount} ${hiddenSessionCount > 1 ? 'sessions' : 'session'} hidden`}
    </StyledShowHideSession>
  );
};

const ExpandedTableData = (props: TExpandedTableData) => {
  const {
    data,
    key,
    parentId,
    parentData,
    tableColumns,
    actions,
    haveTrackAccess,
    haveBuildAccess,
    invitedLearnersCount,
    isParentSelected,
    selectedSessions,
  } = props;
  const { isCancelled: isParentCancelled } = parentData;
  const isMaxSessionLimitReached = data.length >= MAX_SESSIONS_WITHIN_EVENT;
  const [showSession, setShowSession] = useState(false);
  const numberOfHiddenSession = data.reduce(
    (count, child) => (child.isFilterHidden === true ? count + 1 : count),
    0
  );
  const onChildSelection = (entity: TEntityOnSelectInfo) => {
    const childEntities = [entity];
    if (isParentSelected) {
      data.forEach(session => {
        if ((showSession || session.isFilterHidden !== true) && entity.id !== session.id) {
          childEntities.push({
            id: session.id,
            parentId,
            entityType: session.entityType,
            checked: true,
          });
        }
      });
      childEntities.push({
        id: parentId,
        entityType: ILT_ENTITIES.EVENT,
        checked: false,
      });
    }
    actions.onSelect(childEntities);
  };
  const childrenTableData = data.reduce((acc, session) => {
    if (showSession || session.isFilterHidden !== true) {
      const isSelected = selectedSessions.includes(session.id);
      acc.push(
        createChildRowData({
          parentId,
          parentData,
          session,
          actions,
          haveTrackAccess,
          haveBuildAccess,
          invitedLearnersCount,
          showSession,
          isParentSelected,
          isSelected,
          onChildSelection,
        })
      );
    }
    return acc;
  }, [] as any[]);
  if (numberOfHiddenSession) {
    childrenTableData.push({
      key: `hideSession${key}`,
      columnMinusFirst: '',
      columnZero: (
        <StyledLineContainer>
          <div className="inner-line" />
        </StyledLineContainer>
      ),
      sessionName: (
        <ShowHideSession
          onClick={() => setShowSession(showSession => !showSession)}
          hiddenSessionCount={numberOfHiddenSession}
          isSessionCanBeShown={showSession}
        />
      ),
      noRowClick: true,
      addColSpan: true,
    });
  }
  childrenTableData.push({
    key: `addSession${key}`,
    columnMinusFirst: '',
    columnZero: <StyledLineContainer className="add-session" />,
    sessionName: (
      <StyledSessionCreateContainer>
        <StyledActionPill
          {...(((isMaxSessionLimitReached || isParentCancelled) && {
            tagState: PILL_STATES.DISABLED,
          }) ||
            {})}
          onClick={() => actions.createSessionWithinEvent({ parentId })}
        >
          <Icon type={ICON_MAP.ADD2} /> <FormattedMessage {...messages.ADD_SESSION} />
        </StyledActionPill>
        or&nbsp;&nbsp;
        <StyledActionPill
          {...(((isMaxSessionLimitReached || isParentCancelled) && {
            tagState: PILL_STATES.DISABLED,
          }) ||
            {})}
          onClick={() => actions.uploadSessionsWithinEvent({ parentId })}
        >
          <Icon type={ICON_MAP.UPLOADNEWLIST} /> <FormattedMessage {...messages.UPLOAD_SESSION} />
        </StyledActionPill>
      </StyledSessionCreateContainer>
    ),
    noRowClick: true,
    addColSpan: true,
    isAddSessionRow: true,
  });
  return (
    <StyledExpandedTable
      columns={tableColumns}
      showHeader={false}
      dataSource={childrenTableData}
      isMultiSelect={true}
      hasSeparators={true}
      pagination={false}
      tableLayout="fixed"
      className="expanded-table"
      rowClassName={(record: any) =>
        cs('child-table-row', {
          'filtered-hidden-sessions': record.isFilterRowHidden,
          'add-session-row': record.isAddSessionRow,
          [`${THEME_PREFIX_CLS}-table-row-selected`]: record.isSelected,
        })
      }
      onRow={(record: any) => ({
        ...(!record.noRowClick && {
          onClick: () => {
            actions.viewEntity({ id: record.key, entityType: record.entityType, parentId });
          },
        }),
      })}
    />
  );
};

export default ExpandedTableData;
