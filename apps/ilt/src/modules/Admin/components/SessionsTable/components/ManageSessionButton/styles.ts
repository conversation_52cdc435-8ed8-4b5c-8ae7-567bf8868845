import styled from 'styled-components';

import { tokens, theme } from '@mindtickle/styles/lib';
import Tooltip from '@mindtickle/tooltip';

import { THEME_PREFIX_CLS } from '~/config/constants';

export const StyledManageSessionContainer = styled.div`
  .manage-session-button {
    border-radius: 20px;
    height: 24px;
    display: flex;
    align-items: center;
    color: ${tokens.textTokens.COLOR_TEXT_TERTIARY};
  }
`;

export const StyledInfoTooltip = styled(Tooltip)<{ maxWidth?: string }>`
  .${THEME_PREFIX_CLS}-tooltip {
    max-width: ${({ maxWidth }) => maxWidth || '300px'};

    .${THEME_PREFIX_CLS}-tooltip-inner {
      background: ${tokens.bgTokens.COLOR_BG_DEFAULT};
      border: 1px solid ${tokens.borderTokens.COLOR_BORDER_DEFAULT};
      color: black;
      font-family: ${theme.fontFamily.DEFAULT};
      font-size: 12px;
      max-height: 180px;
      overflow-y: auto;
    }

    .${THEME_PREFIX_CLS}-tooltip-arrow {
      border-bottom-color: #aaa;
      border-top-color: #aaa;
    }
  }
`;
