import styled, { css } from 'styled-components';

import { ActionPill } from '@mindtickle/pill';
import { tokens, mixins } from '@mindtickle/styles/lib';
import Table from '@mindtickle/table';

import { ADMIN_NAVBAR_HEIGHT, THEME_PREFIX_CLS } from '~/config/constants';

const disableFilteredData = css`
  &.filtered-hidden-sessions {
    .session-name-tooltip,
    .info-container,
    .black-normal-text,
    .more-action-btn,
    .icon-more_horizontal2,
    .manage-session-button,
    .disabled-filtered,
    .date,
    .timezone,
    .duration,
    .color-light-stop,
    .color-light-warning,
    .color-light-processing,
    .color-light-success,
    .render-cell-icon {
      color: ${tokens.iconTokens.COLOR_ICON_DISABLED};
    }

    .color-light-stop,
    .color-light-warning,
    .color-light-processing,
    .color-light-success {
      background-color: ${tokens.bgTokens.COLOR_BG_DISABLED};
    }
  }
`;

export const StyledShowHideSession = styled.div`
  ${mixins.blackLink()};
  padding: 2px;
  cursor: pointer;
  display: flex;
`;

export const StyledActionPill = styled(ActionPill)`
  .children-wrapper {
    display: flex;
    gap: 6px;

    .icon {
      margin-left: 0;
      font-size: 10px;
      font-weight: 500;
    }
  }
`;

export const StyledSessionsTable = styled.div<{ loading: boolean }>`
  position: relative;

  && .${THEME_PREFIX_CLS}-table-container::before, .${THEME_PREFIX_CLS}-table-container::after {
    box-shadow: none;
    width: 0;
  }

  .${THEME_PREFIX_CLS}-table-body {
    min-height: calc(100vh - 490px);
    padding-bottom: 56px;
  }

  .${THEME_PREFIX_CLS}-table-sticky-header {
    top: ${props => (props.loading ? 0 : ADMIN_NAVBAR_HEIGHT)}px !important;
    position: sticky !important;
    z-index: 2;
  }
`;

export const StyledNoDataWrapper = styled.div`
  padding: 200px 0;
  .no-data-icon {
    margin: 0 auto;
  }
  .no-result-text {
    ${mixins.blackLink}
    text-align: center;
    padding: 1px 0 0 0;
    letter-spacing: 0;
    margin-top: 20px;
  }
  .no-result-text-subtext {
    ${mixins.darkText()};
    margin-left: auto;
    margin-right: auto;
    margin-top: 8px;
    width: 415px;
  }
`;

export const skeletonTitleStyle = { width: '40%', margin: '5px auto 5px 0' };

export const StyledLineWrapper = styled.div`
  display: flex;
  justify-content: center;
  position: absolute;
  width: 14px;
  height: 30px;
  top: 22px;

  .header-line {
    background-color: ${tokens.bgTokens.COLOR_BG_DISABLED};
    border: 0px;
    border-radius: 0px;
    height: 30px;
    top: 0;
    left: 0;
    width: 1px;
    position: relative;
    transform: translateX(-50%);
  }
`;

export const StyledLine = styled.div`
  border-left: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
  border-radius: 0 4px;
  width: 14px;
  height: 92px;
  position: absolute;
  top: -50px;
  left: 22px;
  z-index: 1;

  &.only-child {
    height: 56px;
  }

  &.no-data-row {
    height: 66px;
  }

  &.hidden-session {
    height: 72px;
  }
`;

export const StyledLineContainer = styled.div`
  border-left: 1px solid ${tokens.borderTokens.COLOR_BORDER_DEFAULT};
  width: 14px;
  height: calc(100% + 16px);
  position: absolute;
  top: -10px;
  left: 23px;
  z-index: 1;

  .inner-line {
    width: 12px;
    border-bottom: 1px solid ${tokens.borderTokens.COLOR_BORDER_DEFAULT};
    border-left: 1px solid ${tokens.borderTokens.COLOR_BORDER_DEFAULT};
    position: absolute;
    top: 50%;
    left: -1px;
    transform: translateY(calc(-50% - 3px));
    border-radius: 0 0 0 4px;
    height: 10px;
  }

  &.add-session {
    height: calc(50% + 10px);
    border-bottom: 1px solid ${tokens.borderTokens.COLOR_BORDER_DEFAULT};
    border-radius: 0 0 0 4px;
  }
`;

export const StyledTable = styled(Table)`
  &.multiday-table {
    td.${THEME_PREFIX_CLS}-table-column-sort {
      background-color: transparent;
    }

    .${THEME_PREFIX_CLS}-table-row.${THEME_PREFIX_CLS}-table-row-level-0 {
      &.${THEME_PREFIX_CLS}-table-row-selected {
        td.${THEME_PREFIX_CLS}-table-column-sort {
          background-color: ${tokens.bgTokens.COLOR_BG_ACCENT_SELECTED};
        }

        &:hover {
          td.${THEME_PREFIX_CLS}-table-column-sort {
            background-color: #dbeafa;
          }
        }
      }
    }

    .collapse-icon {
      font-size: 12px;
      display: inline-block;
      cursor: pointer;
      text-align: center;
      color: ${tokens.iconTokens.COLOR_ICON_DEFAULT};
    }

    .${THEME_PREFIX_CLS}-table-row {
      &:hover {
        background-color: ${tokens.bgTokens.COLOR_BG_TERTIARY};
      }

      ${disableFilteredData};
    }

    .${THEME_PREFIX_CLS}-table-expanded-row .${THEME_PREFIX_CLS}-table-cell {
      padding-left: 16px;
    }

    .${THEME_PREFIX_CLS}-table-row-expand-icon-cell {
      padding-right: 12px;
      padding-left: 24px;
    }

    .${THEME_PREFIX_CLS}-table-cell {
      padding-right: 0;

      &:first-child,
      &:nth-last-child(1),
      &:nth-last-child(2) {
        opacity: 1;
        border-radius: 0;
      }

      .manage,
      .more-options {
        font-size: 12px;
        border-radius: 20px;
        margin: 0;
      }

      &.${THEME_PREFIX_CLS}-table-selection-column {
        padding-left: 16px;
      }
    }

    .${THEME_PREFIX_CLS}-table-expanded-row {
      .${THEME_PREFIX_CLS}-table-cell {
        padding: 0;
      }
    }
  }
`;

export const StyledExpandedTable = styled(Table)`
  &&& {
    &.expanded-table {
      border-collapse: collapse;

      .${THEME_PREFIX_CLS}-table-row.${THEME_PREFIX_CLS}-table-row-level-0 {
        &[data-row-key='addSession'] .${THEME_PREFIX_CLS}-table-cell::after {
          border-bottom: none;
        }

        .${THEME_PREFIX_CLS}-table-cell:nth-child(3) {
          padding: 8px 0 8px 12px;
        }
      }

      &::before {
        display: none;
      }

      .${THEME_PREFIX_CLS}-table-content {
        &::before {
          content: '';
          position: absolute;
          background-color: ${tokens.bgTokens.COLOR_BG_SECONDARY};
          top: 5px;
          height: calc(100% - 10px);
          width: 100%;
        }
        &::after {
          content: '';
          position: absolute;
          border-top: 1px solid ${tokens.borderTokens.COLOR_BORDER_SECONDARY};
          top: 2px;
          width: 100%;
        }
        margin-top: -5px;
        margin-bottom: -5px;
        .${THEME_PREFIX_CLS}-table-tbody {
          tr:last-child td::after {
            border-bottom: 0;
          }
        }
      }

      .${THEME_PREFIX_CLS}-table-row.${THEME_PREFIX_CLS}-table-row-level-1 {
        border-collapse: collapse;
      }

      .child-table-row {
        ${disableFilteredData};
      }

      .collapse-icon {
        width: 14px;
        display: inline-block;
      }

      .${THEME_PREFIX_CLS}-table-expanded-row
        .${THEME_PREFIX_CLS}-table-row
        .${THEME_PREFIX_CLS}-table-cell {
        padding-left: 16px;
      }

      .${THEME_PREFIX_CLS}-table-row-expand-icon-cell {
        padding-right: 12px;
      }

      .${THEME_PREFIX_CLS}-table-cell {
        padding: 12px 0px 12px 16px;

        .manage,
        .more-options {
          font-size: 12px;
          border-radius: 20px;
          margin: 0;
        }

        &.${THEME_PREFIX_CLS}-table-selection-column {
          padding-left: 16px;
        }
      }

      .${THEME_PREFIX_CLS}-table-expanded-row {
        .${THEME_PREFIX_CLS}-table-cell {
          padding: 0;
        }
      }
    }
  }
`;

export const StyledIconCheckbox = styled.div<{ checked: boolean; haveBuildAccess: boolean }>`
  display: none;
  justify-content: center;
  position: relative;
  height: 16px;
  width: 16px;

  .${THEME_PREFIX_CLS}-checkbox {
    top: 0;
  }

  &.render-cell-icon-checkbox-container {
    display: flex;

    .render-cell-checkbox {
      visibility: ${({ checked }) => (checked ? 'visible' : 'hidden')};
    }

    .render-cell-icon {
      visibility: ${({ checked }) => (checked ? 'hidden' : 'visible')};
    }
  }

  ${({ haveBuildAccess }) =>
    haveBuildAccess &&
    css`
      &:hover {
        .render-cell-checkbox {
          visibility: visible;
        }

        .render-cell-icon {
          visibility: hidden;
        }
      }
    `}

  .render-cell-checkbox {
    visibility: hidden;
    display: inline-flex;
  }

  .render-cell-icon {
    position: absolute;
    font-size: 16px;
    &.icon-session {
      font-size: 15px;
    }
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    color: ${tokens.iconTokens.COLOR_ICON_DEFAULT};
  }
`;
