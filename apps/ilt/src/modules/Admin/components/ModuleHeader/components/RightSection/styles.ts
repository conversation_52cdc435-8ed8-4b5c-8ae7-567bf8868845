import styled from 'styled-components';

import Icon from '@mindtickle/icon';
import { tokens } from '@mindtickle/styles/lib';
import { theme, mixins } from '@mindtickle/styles/lib';

const { fontWeight, fontSizes } = theme;

export const SectionWrapper = styled.div`
  display: flex;
  align-items: center;
  font-style: normal;
  font-weight: ${fontWeight.SEMIBOLD};
`;

export const StyledIcon = styled(Icon)`
  font-size: ${fontSizes.TEXT};
  padding: 6px;
  color: ${tokens.textTokens.COLOR_TEXT_SUCCESS};
  margin-right: 4px;
  font-size: 14px;
`;

export const SaveState = styled.div`
  margin-right: 24px;
  display: flex;
  align-items: center;
  ${mixins.smallDarkLink()}
`;
