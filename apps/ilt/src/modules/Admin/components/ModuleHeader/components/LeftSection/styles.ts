import styled from 'styled-components';

import Icon from '@mindtickle/icon';
import ModuleIcon from '@mindtickle/module-icon';
import { tokens, mixins } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS } from '~/config/constants';

export const StyledModuleIcon = styled(ModuleIcon)`
  border-radius: 4px;
`;

export const SectionWrapper = styled.div`
  display: flex;
  flex-direction: column;
`;

export const SectionRow = styled.div`
  display: flex;
  margin-bottom: 5px;
  align-items: center;
`;

export const DetailsWrapper = styled.div`
  margin: 0 15px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .${THEME_PREFIX_CLS}-dropdown {
    width: 170px;
  }
`;

export const SubDetailsWrapper = styled.div`
  display: flex;
`;

export const TagsContainer = styled.div<{ isPublished: boolean }>`
  ${mixins.whiteText()}
  font-weight: 600;
  border-radius: 4px;
  padding: 2px 8px;
  margin-right: 8px;
  background-color: ${({ isPublished }) =>
    isPublished
      ? tokens.bgTokens.COLOR_BG_SUCCESS_STRONG
      : tokens.bgTokens.COLOR_BG_WARNING_STRONG};
`;

export const PublishTagRawWrapper = styled.div``;

export const CreatedByDetails = styled.div`
  margin-right: 5px;
  font-size: 12px;
`;

export const StyledIcon = styled(Icon)`
  font-size: 16px;
`;

export const CreatedByName = styled.span`
  color: ${tokens.textTokens.COLOR_TEXT_DEFAULT};
`;

export const skeletonStyle = { height: '12px', width: '100px', verticalAlign: 'middle' };
