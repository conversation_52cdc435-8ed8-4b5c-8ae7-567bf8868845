import { useSelector } from 'react-redux';

import { ICON_MAP } from '@mindtickle/icon';

import { StyledIcon, SaveState } from '../../styles';

export default function ModuleSaveStatus() {
  const { isLoading } =
    useSelector((state: any) => state.ilt.details.moduleSettingsUpdateStatus) || {};
  const moduleRemindersOperationStatus = useSelector(
    (state: any) => state.moduleReminders?.operationStatus
  );
  return (
    <SaveState>
      {isLoading || moduleRemindersOperationStatus?.isLoading ? (
        <span>Auto-saving...</span>
      ) : (
        <>
          <StyledIcon type={ICON_MAP.right} />
          <span>Auto-saved</span>
        </>
      )}
    </SaveState>
  );
}
