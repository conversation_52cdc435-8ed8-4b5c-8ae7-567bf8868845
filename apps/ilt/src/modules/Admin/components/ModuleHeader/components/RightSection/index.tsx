import Todos from '~/modules/Admin/containers/Todos';

import ModuleSaveStatus from './containers/ModuleSettingsSaveState';
import { SectionWrapper } from './styles';

import type { TProps } from './typeDefs';

const RightSection = (props: TProps) => {
  const { moduleId, seriesId } = props;
  return (
    <SectionWrapper>
      <ModuleSaveStatus />
      <Todos moduleId={moduleId} seriesId={seriesId} />
    </SectionWrapper>
  );
};

export default RightSection;
