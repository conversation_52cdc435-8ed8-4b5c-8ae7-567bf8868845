import { defineMessages } from 'react-intl';

export default defineMessages({
  MODULE_OPTION_RENAME: 'Rename module',
  MODULE_OPTION_GET_URL: 'Get module URL',
  MODULE_OPTION_DISCARD: 'Discard module',
  MODU<PERSON>_OPTION_ARCHIVE: 'Archive module',
  CONFIRM_MESSAGE_DISCARD_PUBLISHED: `This {moduleType} is available in {seriesCount} series. Are you sure you want to delete it from all {seriesCount} series ?`,
  CONFIRM_MESSAGE_DISCARD: `Are you sure you want to discard this {moduleType} ?`,
  CONFIRM_MESSAGE_ARCHIVE_PUBLISHED: `This {moduleType} is available in {seriesCount} series. Are you sure you want to archive it from all {seriesCount} series ?`,
  CONFIRM_MESSAGE_ARCHIVE: `Are you sure you want to archive this {moduleType} ?`,
  MODULE_URL_COPIED: 'Module URL copied',
});
