import { Suspense, lazy } from 'react';

import PropTypes from 'prop-types';
import { Routes, Route, Navigate } from 'react-router-dom';
import { LIFECYCLE_STAGES } from 'ui_shell/GlobalConstants';

import Loader from '@mindtickle/loader';

import PageTitle from '~/components/PageTitle';
import ErrorBoundary from '~/modules/Admin/components/ErrorBoundary';
import ManageEventLayout from '~/modules/Admin/containers/ManageEventLayout';
import ManageSession from '~/modules/Admin/containers/ManageSession';

import ModuleRoutes from '../../config/routes';
import { ActiveNavTabSetter, TAB_TYPES } from '../NavBarTabs';
import PrivateRoute from '../PrivateRoute';

const manageSessionPath = ModuleRoutes.manageSession;
const { [LIFECYCLE_STAGES.INVITE]: invitePath } = ModuleRoutes.lifecycle;

const Build = lazy(() =>
  import(
    /* webpackChunkName: "ilt-build" */ /* webpackMode: "eager"*/ '~/modules/Admin/containers/Build'
  )
);

const Track = lazy(() =>
  import(/* webpackChunkName: "ilt-learners" */ '~/modules/Admin/containers/Track')
);

const SessionsRoute = ({ haveAccess, customProps, setActiveTab }) => {
  const { stages, tabType, integrations } = customProps.COMMON_PROPS;
  const { moduleData, seriesData, companyData } = customProps[LIFECYCLE_STAGES.BUILD];
  const tabName = stages[LIFECYCLE_STAGES.BUILD];
  const { onLearnerRemoval, extraConfig, onInvite } = customProps[LIFECYCLE_STAGES.INVITE];

  return (
    <Routes>
      <Route
        path={`/`}
        element={
          <PrivateRoute
            authenticated={
              haveAccess(LIFECYCLE_STAGES.BUILD) || haveAccess(LIFECYCLE_STAGES.INVITE)
            }
          >
            <ActiveNavTabSetter tabKey={TAB_TYPES.BUILD} setActiveTab={setActiveTab} />
            <PageTitle tabName={tabName} tabType={tabType} />
            <ErrorBoundary>
              <Suspense fallback={<Loader size="sizeSmall" />}>
                <Build
                  moduleData={moduleData}
                  seriesData={seriesData}
                  companyData={companyData}
                  extraConfig={extraConfig}
                  integrations={integrations}
                  haveTrackAccess={haveAccess(LIFECYCLE_STAGES.INVITE)}
                  haveBuildAccess={haveAccess(LIFECYCLE_STAGES.BUILD)}
                />
              </Suspense>
            </ErrorBoundary>
          </PrivateRoute>
        }
      />
      <Route
        path={`${manageSessionPath}`}
        element={
          <PrivateRoute authenticated={haveAccess(LIFECYCLE_STAGES.INVITE)}>
            <ActiveNavTabSetter tabKey={TAB_TYPES.BUILD} setActiveTab={setActiveTab} />
            <PageTitle tabName={tabName} tabType={tabType} />
            <ErrorBoundary>
              <Suspense fallback={<Loader size="sizeSmall" />}>
                <ManageSession
                  onLearnerRemoval={onLearnerRemoval}
                  extraConfig={extraConfig}
                  companyData={companyData}
                  integrations={integrations}
                />
              </Suspense>
            </ErrorBoundary>
          </PrivateRoute>
        }
      />
      <Route
        path={`${ModuleRoutes.manageEvent}/*`}
        element={
          <PrivateRoute authenticated={haveAccess(LIFECYCLE_STAGES.INVITE)}>
            <ActiveNavTabSetter tabKey={TAB_TYPES.BUILD} setActiveTab={setActiveTab} />
            <PageTitle tabName={tabName} tabType={tabType} />
            <ManageEventLayout
              extraConfig={extraConfig}
              companyData={companyData}
              integrations={integrations}
              haveTrackAccess={haveAccess(LIFECYCLE_STAGES.INVITE)}
              haveBuildAccess={haveAccess(LIFECYCLE_STAGES.BUILD)}
              onLearnerRemoval={onLearnerRemoval}
            />
          </PrivateRoute>
        }
      />
      <Route
        path={`${invitePath}`}
        element={
          <PrivateRoute authenticated={haveAccess(LIFECYCLE_STAGES.INVITE)}>
            <ActiveNavTabSetter tabKey={TAB_TYPES.INVITE_AND_TRACK} setActiveTab={setActiveTab} />
            <PageTitle tabName={tabName} tabType={tabType} />
            <ErrorBoundary>
              <Suspense fallback={<Loader size="sizeSmall" />}>
                <Track
                  onLearnerRemoval={onLearnerRemoval}
                  extraConfig={extraConfig}
                  onInvite={onInvite}
                />
              </Suspense>
            </ErrorBoundary>
          </PrivateRoute>
        }
      />
      <Route path="*" element={<Navigate to="." replace />} />
    </Routes>
  );
};

SessionsRoute.propTypes = {
  // match: PropTypes.object.isRequired,
  haveAccess: PropTypes.func.isRequired,
  customProps: PropTypes.object.isRequired,
  setActiveTab: PropTypes.func.isRequired,
};

export default SessionsRoute;
