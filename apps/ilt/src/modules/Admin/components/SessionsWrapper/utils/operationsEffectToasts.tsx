import { FormattedMessage } from 'react-intl';

import { errorToast, successToast, infoToast } from '@mindtickle/toast';

import { OPERATIONS } from '~/modules/Admin/config/sessions.constants';
import { TSnowplowTrackerType } from '~/modules/Admin/hooks/useILTAdminSnowplowTracker';
import { SNOWPLOW_FIELD_NAMES } from '~/modules/Admin/hooks/useILTAdminSnowplowTracker/constants';

import messages from '../messages';
import { StyledCancelInfo, StyledLoader } from '../styles';
import { TEntitiesWithBasicInfo } from '../typeDefs';

import { getEntityCountFromBasicInfo } from './operationsUtils';
import { sendSessionOperation, sendDeleteEntitiesOperation } from './trackEvents';

const { CANCEL, CANCEL_EVENT, REMOVE } = OPERATIONS;

const MESSAGE_TYPE = {
  inprogress: 'inprogress',
  success: 'success',
  failed: 'failed',
} as const;

type TMessageType = (typeof MESSAGE_TYPE)[keyof typeof MESSAGE_TYPE];

const getCancelOperationMessageInfo = ({
  messageType,
  operation,
}: {
  messageType: TMessageType;
  operation: typeof CANCEL | typeof CANCEL_EVENT;
}) => {
  let message = null;
  if (messageType === MESSAGE_TYPE.inprogress) {
    message = (
      <StyledCancelInfo>
        <StyledLoader size="sizeXSmall" className={'toast-loader'} />
        <div className={'cancel-info-text'}>
          <FormattedMessage
            {...(operation === CANCEL_EVENT
              ? messages.CANCELLING_EVENT
              : messages.CANCELLING_SESSION)}
          />
        </div>
      </StyledCancelInfo>
    );
  }
  if (messageType === MESSAGE_TYPE.failed) {
    message = (
      <div className={'cancel-info-text'}>
        <FormattedMessage
          {...(operation === CANCEL_EVENT
            ? messages.CANCEL_EVENT_FAILED
            : messages.CANCEL_SESSION_FAILED)}
        />
      </div>
    );
  }
  if (messageType === MESSAGE_TYPE.success) {
    message = (
      <div className={'cancel-info-text'}>
        <FormattedMessage
          {...(operation === CANCEL_EVENT
            ? messages.CANCEL_EVENT_SUCCESS
            : messages.CANCEL_SESSION_SUCCESS)}
        />
      </div>
    );
  }
  return {
    message,
  };
};

const getRemoveOperationMessageValues = ({
  messageType,
  processIds,
  deletedEntities,
}: {
  messageType: TMessageType;
  processIds: TEntitiesWithBasicInfo;
  deletedEntities?: string[];
}) => {
  if (messageType === MESSAGE_TYPE.failed || messageType === MESSAGE_TYPE.success) {
    if (deletedEntities) {
      processIds = processIds.filter(({ id }) => deletedEntities.includes(id));
    }
    const counts = getEntityCountFromBasicInfo({ entities: processIds });
    const sessionLabel = counts.allSessionCount > 1 ? 'sessions' : 'session';
    const eventLabel = counts.eventCount > 1 ? 'events' : 'event';
    const both = counts.allSessionCount && counts.eventCount;
    const moreThanOne = counts.allSessionCount + counts.eventCount > 1;
    let message;
    if (messageType === MESSAGE_TYPE.failed) {
      message = moreThanOne
        ? both
          ? messages.DELETE_FAILED_MULTIPLE_BOTH
          : counts.eventCount
          ? messages.DELETE_FAILED_EVENTS
          : messages.DELETE_FAILED_SESSIONS
        : messages.DELETE_FAILED_SINGLE;
    } else {
      message = moreThanOne
        ? both
          ? messages.DELETE_SUCCESS_MULTIPLE_BOTH
          : counts.eventCount
          ? messages.DELETE_SUCCESS_EVENTS
          : messages.DELETE_SUCCESS_SESSIONS
        : messages.DELETE_SUCCESS_SINGLE;
    }
    const values = moreThanOne
      ? {
          sessionCount: counts.allSessionCount,
          eventCount: counts.eventCount,
          sessionLabel,
          eventLabel,
        }
      : {
          label:
            messageType === MESSAGE_TYPE.failed
              ? counts.eventCount
                ? 'event'
                : 'session'
              : counts.eventCount
              ? 'Event'
              : 'Session',
        };
    return { message, values: values };
  }
  return {
    message: messages.DELETING_SESSIONS_EVENT,
    values: {},
  };
};

const getRemoveOperationMessageInfo = ({
  messageType,
  processIds,
  deletedEntities,
}: {
  messageType: TMessageType;
  processIds: TEntitiesWithBasicInfo;
  deletedEntities?: string[];
}) => {
  let formattedMessage = null;
  const { message, values } = getRemoveOperationMessageValues({
    messageType,
    processIds,
    deletedEntities,
  });
  if (messageType === MESSAGE_TYPE.inprogress) {
    formattedMessage = (
      <StyledCancelInfo>
        <StyledLoader size="sizeXSmall" className={'toast-loader'} />
        <div>
          <FormattedMessage {...message} />
        </div>
      </StyledCancelInfo>
    );
  }
  if (messageType === MESSAGE_TYPE.failed) {
    formattedMessage = (
      <div>
        <FormattedMessage {...message} values={values} />
      </div>
    );
  }
  if (messageType === MESSAGE_TYPE.success) {
    formattedMessage = (
      <div>
        <FormattedMessage {...message} values={values} />
      </div>
    );
  }
  return {
    message: formattedMessage,
  };
};

// https://mindtickle.atlassian.net/browse/LA2-1564
function getRemoveMessageForSuccessApi({
  processIds,
  deletedEntities,
  failedEntities,
}: {
  processIds: TEntitiesWithBasicInfo;
  deletedEntities?: string[];
  failedEntities?: string[];
}) {
  const isFailed =
    (deletedEntities || []).length <= 0 && failedEntities && failedEntities.length > 0
      ? true
      : false;
  return {
    ...getRemoveOperationMessageInfo({
      messageType: isFailed ? MESSAGE_TYPE.failed : MESSAGE_TYPE.success,
      processIds: processIds,
      deletedEntities: (isFailed ? failedEntities : deletedEntities) || [],
    }),
    isFailed,
  };
}

type TToastConfig = { message: React.ReactNode; freeze?: boolean; timeout?: number };

export const showOperationEffectMessages = ({
  operationStatus,
  prevOperationStatus,
  tracker,
  trackerPageName,
}: {
  operationStatus: any;
  prevOperationStatus: any;
  tracker: TSnowplowTrackerType;
  trackerPageName: string;
}) => {
  const {
    data: { operation = {}, postData: { processIds = [] } = {}, response = {} } = {},
    loadingData,
    operation: currentOperation,
    error,
    errorParamData = {},
  } = operationStatus;
  const { loadingData: oldLoadingData } = prevOperationStatus;

  const isLoadingDataPropChanged = oldLoadingData && !loadingData;
  if (loadingData) {
    const toastConfig: TToastConfig = {
      message: null,
      freeze: false,
      timeout: 3000,
    };
    switch (loadingData.operation) {
      case CANCEL:
      case CANCEL_EVENT:
        const { message: cancelMessage } = getCancelOperationMessageInfo({
          messageType: MESSAGE_TYPE.inprogress,
          operation: loadingData.operation,
        });
        toastConfig.message = cancelMessage;
        break;
      //TODO(SUGATA): To figure out if the we can retain the selected sessions count after remove operation for making the below text dynamic
      case REMOVE:
        const { message: deleteMessage } = getRemoveOperationMessageInfo({
          messageType: MESSAGE_TYPE.inprogress,
          processIds: [],
        });
        toastConfig.message = deleteMessage;
        break;
    }
    if (toastConfig.message) {
      infoToast(toastConfig);
    }
  } else if (isLoadingDataPropChanged) {
    const toastConfig: TToastConfig = {
      message: null,
      freeze: false,
      timeout: 3000,
    };
    if (error) {
      switch (currentOperation) {
        case CANCEL:
        case CANCEL_EVENT:
          const { message: cancelMessage } = getCancelOperationMessageInfo({
            messageType: MESSAGE_TYPE.failed,
            operation: currentOperation,
          });
          toastConfig.message = cancelMessage;
          break;
        case REMOVE:
          const { postData: { processIds: failedToProcessIds = [] } = {} } = errorParamData;
          const { message: deleteMessage } = getRemoveOperationMessageInfo({
            messageType: MESSAGE_TYPE.failed,
            processIds: failedToProcessIds,
          });
          toastConfig.message = deleteMessage;
          break;
      }
      if (toastConfig.message) {
        errorToast(toastConfig);
      }
    } else {
      const trackingParam = {
        operationStatus: operationStatus,
        extraTrackingProperties: {
          [SNOWPLOW_FIELD_NAMES.PAGE_NAME]: trackerPageName,
        },
      };
      switch (operation) {
        case CANCEL:
        case CANCEL_EVENT:
          const { message: cancelMessage } = getCancelOperationMessageInfo({
            messageType: MESSAGE_TYPE.success,
            operation,
          });
          toastConfig.message = cancelMessage;
          sendSessionOperation(tracker, trackingParam);
          break;
        case REMOVE:
          const { message: deleteMessage, isFailed } = getRemoveMessageForSuccessApi({
            processIds: processIds,
            deletedEntities: response.deletedEntities,
            failedEntities: response.failedEntities,
          });
          if (!isFailed) {
            toastConfig.message = deleteMessage;
            sendDeleteEntitiesOperation(tracker, trackingParam);
          } else {
            errorToast({ ...toastConfig, message: deleteMessage });
          }
          break;
      }
      if (toastConfig.message) {
        successToast(toastConfig);
      }
    }
  }
};
