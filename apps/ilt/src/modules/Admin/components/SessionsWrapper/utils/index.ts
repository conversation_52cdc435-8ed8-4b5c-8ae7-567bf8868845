import cloneDeep from 'lodash/cloneDeep';

import { EVENT_ENROLLMENT_ENUM } from '~/modules/Admin/constants/events';
import { getTimezoneFromString } from '~/utils/timezone';

import {
  SESSION_TYPES,
  LOCATION_TYPE,
  ENROLLMENT_FREEZE_STATUSES,
  GET_DEFAULT_TIMING,
  DEFAULT_WEBINAR_SETTINGS,
  COMPLETION_CRITERIA_ENUM,
} from '../../../config/sessions.constants';
import { DEFAULT_EVENT_COMPLETION_CRITERIA_PERCENT } from '../../EventEditDrawer/constants';

import type { TTimezone } from '../../SessionEditDrawer/typeDefs';

export const getSessionCopy = ({
  sessionData,
  defaultCompletionCriteria,
  defaultScore,
  eventData,
}: {
  sessionData?: any;
  defaultCompletionCriteria: (typeof COMPLETION_CRITERIA_ENUM)[keyof typeof COMPLETION_CRITERIA_ENUM];
  defaultScore?: boolean | number;
  eventData?: any;
}) => ({
  type: SESSION_TYPES.CLASSROOM.value,
  locationType: LOCATION_TYPE.FACE_TO_FACE,
  maxScore: defaultScore || 0,
  completionCriteria: defaultCompletionCriteria || COMPLETION_CRITERIA_ENUM.ATTENDED,
  enrollmentFreezeStatus: ENROLLMENT_FREEZE_STATUSES.DISABLED,
  enrollmentFreezeEpoch: 0,
  ...GET_DEFAULT_TIMING(),
  notifyWaitingList: true,
  maxSeats: 1,
  autoEnroll: false,
  attachments: [],
  webAutoAttendanceSettings: { ...DEFAULT_WEBINAR_SETTINGS },
  ...(eventData?.reminders && { reminders: eventData.reminders }),
  ...cloneDeep(sessionData),
});

export const getEventCopy = ({ eventData }: { eventData?: any }) => ({
  enrollmentFreezeStatus: ENROLLMENT_FREEZE_STATUSES.DISABLED,
  enrollmentFreezeEpoch: 0,
  enrollmentFreezeDaysBeforeEvent: 0,
  notifyWaitingList: true,
  maxSeats: 1,
  autoEnroll: false,
  attachments: [],
  enrollmentType: EVENT_ENROLLMENT_ENUM.EVENT,
  completionCriteria: DEFAULT_EVENT_COMPLETION_CRITERIA_PERCENT,
  ...cloneDeep(eventData),
});

export const getTimezoneObject = (timezone?: string | TTimezone) => {
  if (!timezone) {
    return undefined;
  }
  const timezoneObj =
    typeof timezone === 'object' ? timezone : getTimezoneFromString(timezone)?.timezone;

  return timezoneObj;
};
