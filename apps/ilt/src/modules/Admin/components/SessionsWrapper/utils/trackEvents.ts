import { noop } from '@mindtickle/utils';

import { OPERATIONS as SESSION_OPERATIONS } from '~/modules/Admin/config/sessions.constants';
import {
  SNOWPLOW_FIELD_NAMES,
  MIXPANEL_UI_EVENTS,
} from '~/modules/Admin/hooks/useILTAdminSnowplowTracker/constants';
import {
  SESSION_EVENTS,
  EVENT_DATA_MAP,
  EVENT_OPERATION_MAP,
} from '~/modules/Admin/hooks/useILTAdminSnowplowTracker/constants/sessions';
import type {
  TEventDataMap,
  TSnowplowTrackerType,
} from '~/modules/Admin/hooks/useILTAdminSnowplowTracker/typeDefs';
import { sendEventUsingOperationStatus } from '~/modules/Admin/hooks/useILTAdminSnowplowTracker/utils';
import type { TILTEntities } from '~/modules/Admin/typeDefs';
import { checkIsEvent } from '~/modules/Admin/utils/checkEntityType';

function isReuploadProcessor(isReupload: boolean) {
  return isReupload ? 'reupload' : 'upload';
}

function trackerWithCatch(
  tracker: TSnowplowTrackerType,
  trackingProperties: { eventName: string; [key: string]: any }
) {
  try {
    tracker.trackStructuredEvent(trackingProperties);
  } catch (err) {
    // do nothing
  }
}

export const sendSessionOperation = (
  tracker: TSnowplowTrackerType,
  {
    operationStatus,
    extraTrackingProperties,
  }: { operationStatus: any; extraTrackingProperties?: { [key: string]: any } }
) => {
  try {
    sendEventUsingOperationStatus(
      tracker,
      { operationStatus, extraTrackingProperties },
      {
        eventOperationMap: EVENT_OPERATION_MAP,
        eventDataMap: EVENT_DATA_MAP as unknown as TEventDataMap,
      }
    );
  } catch (err) {
    // do nothing
  }
};

export const sendCopyIltEvent = (
  tracker: TSnowplowTrackerType,
  {
    operationStatus,
    extraTrackingProperties,
  }: { operationStatus: any; extraTrackingProperties?: { [key: string]: any } }
) => {
  try {
    const { data: { postData = {}, response = {} } = {} } = operationStatus;
    const eventName = SESSION_EVENTS.create_ilt_event;
    const eventPureData = EVENT_DATA_MAP[eventName] as unknown as {
      [key: string]: { value?: string; processor?: (...params: any) => any };
    };

    if (!eventPureData) return;
    const modifiedPostData = { ...(postData || {}), isCopy: true };
    const eventData = Object.keys(eventPureData).reduce((acc, eventDataKey: string) => {
      const { value = undefined, processor = noop } = eventPureData[eventDataKey] || {};
      const eventValue = processor(value, modifiedPostData, response);
      acc[eventDataKey] = eventValue;
      return acc;
    }, {} as { [key: string]: any });
    tracker.trackStructuredEvent({
      eventName: eventName,
      ...eventData,
      ...extraTrackingProperties,
    });
  } catch (err) {
    // do nothing
  }
};

export function sendBulkCreateIltSessionEvent(
  tracker: TSnowplowTrackerType,
  {
    operationStatus,
    extraTrackingProperties = {},
  }: { operationStatus: any; extraTrackingProperties?: { [key: string]: any } }
) {
  try {
    const {
      data: { postData, response, operation } = {
        postData: undefined,
        response: undefined,
        operation: undefined,
      },
    } = operationStatus;
    if (operation === SESSION_OPERATIONS.ADD_BULK) {
      const { entityIds = [], entityMap = {} } = response || {};
      entityIds.forEach((sessionId: any) => {
        sendSessionOperation(tracker, {
          operationStatus: {
            data: {
              operation: SESSION_OPERATIONS.ADD,
              postData: { ...(postData || {}), sessions: [entityMap[sessionId]], isBulk: true },
              response: { entityIds: [sessionId], entityMap },
            },
          },
          extraTrackingProperties,
        });
      });
    }
  } catch (err) {
    // do nothing
  }
}

export function sendBulkUploadClickEvent(
  tracker: TSnowplowTrackerType,
  extraInfo: { pageName: string; parentId?: string }
) {
  const { pageName, parentId } = extraInfo;
  trackerWithCatch(tracker, {
    eventName: SESSION_EVENTS.ilt_bulk_upload_sessions_clicked,
    [SNOWPLOW_FIELD_NAMES.PAGE_NAME]: pageName,
    event_id: parentId,
  });
}

export function sendUploadSessionSampleDownloadEvent(
  tracker: TSnowplowTrackerType,
  extraInfo: { pageName: string }
) {
  const { pageName } = extraInfo;
  trackerWithCatch(tracker, {
    eventName: SESSION_EVENTS.ilt_bulk_upload_sessions_sample_downloaded,
    [SNOWPLOW_FIELD_NAMES.PAGE_NAME]: pageName,
  });
}

export function sendUploadSessionUploadClickedEvent(
  tracker: TSnowplowTrackerType,
  extraInfo: { pageName: string; isReupload: boolean }
) {
  const { pageName, isReupload } = extraInfo;
  trackerWithCatch(tracker, {
    eventName: SESSION_EVENTS.ilt_bulk_upload_sessions_upload_clicked,
    [SNOWPLOW_FIELD_NAMES.PAGE_NAME]: pageName,
    type: isReuploadProcessor(isReupload),
  });
}

export function sendUploadSessionUploadedEvent(
  tracker: TSnowplowTrackerType,
  extraInfo: {
    pageName: string;
    sessionsCount: number;
    sessionsWithErrorCount: number;
    isReupload: boolean;
  }
) {
  const { pageName, sessionsCount, sessionsWithErrorCount, isReupload } = extraInfo;
  trackerWithCatch(tracker, {
    eventName: SESSION_EVENTS.ilt_bulk_upload_sessions_uploaded,
    [SNOWPLOW_FIELD_NAMES.PAGE_NAME]: pageName,
    count_sessions: sessionsCount,
    sessions_with_errors: sessionsWithErrorCount,
    type: isReuploadProcessor(isReupload),
  });
}

export function sendUploadSessionCreatedEvent(
  tracker: TSnowplowTrackerType,
  extraInfo: {
    pageName: string;
    sessionsCount: number;
    sessionsWithErrorCount: number;
  }
) {
  const { pageName, sessionsCount, sessionsWithErrorCount } = extraInfo;
  trackerWithCatch(tracker, {
    eventName: SESSION_EVENTS.ilt_bulk_upload_sessions_created,
    [SNOWPLOW_FIELD_NAMES.PAGE_NAME]: pageName,
    count_sessions: sessionsCount,
    sessions_with_errors: sessionsWithErrorCount,
  });
}

export function sendSessionGetCheckinCodeClicked(
  tracker: TSnowplowTrackerType,
  extraInfo: { pageName: string; id: string; name: string }
) {
  const { pageName, id: session_id = '', name: session_name = '' } = extraInfo;
  trackerWithCatch(tracker, {
    eventName: SESSION_EVENTS.ilt_session_get_checkin_code_clicked,
    [SNOWPLOW_FIELD_NAMES.PAGE_NAME]: pageName,
    session_id,
    session_name,
  });
}

export function sendSessionEmailLCReportSubmitted(
  tracker: TSnowplowTrackerType,
  extraInfo: { pageName: string; sessionId: string; sessionName: string }
) {
  const { pageName, sessionId: session_id = '', sessionName: session_name = '' } = extraInfo;
  trackerWithCatch(tracker, {
    eventName: SESSION_EVENTS.ilt_session_email_live_challenge_report_submitted,
    [SNOWPLOW_FIELD_NAMES.PAGE_NAME]: pageName,
    session_id,
    session_name,
  });
}

export function sendCreateEntityClicked(
  tracker: TSnowplowTrackerType,
  {
    pageName,
    entityType,
    parentId,
  }: { pageName: string; entityType?: TILTEntities; parentId?: string }
) {
  const eventName = checkIsEvent(entityType)
    ? SESSION_EVENTS.create_ilt_event_clicked
    : SESSION_EVENTS.create_ilt_session_clicked;
  trackerWithCatch(tracker, {
    eventName,
    [SNOWPLOW_FIELD_NAMES.PAGE_NAME]: pageName,
    event_id: parentId,
  });
}

export function sendEventSettingsClicked(
  tracker: TSnowplowTrackerType,
  extraInfo: { pageName: string; id: string }
) {
  const { pageName, id: event_id = '' } = extraInfo;
  trackerWithCatch(tracker, {
    eventName: SESSION_EVENTS.ilt_event_settings_clicked,
    [SNOWPLOW_FIELD_NAMES.PAGE_NAME]: pageName,
    event_id,
  });
}

export function sendBulkDeleteClicked(
  tracker: TSnowplowTrackerType,
  extraInfo: { pageName: string }
) {
  const { pageName } = extraInfo;
  trackerWithCatch(tracker, {
    eventName: SESSION_EVENTS.bulk_delete_ilt_object_clicked,
    [SNOWPLOW_FIELD_NAMES.PAGE_NAME]: pageName,
  });
}

export const sendDeleteEntitiesOperation = (
  tracker: TSnowplowTrackerType,
  {
    operationStatus,
    extraTrackingProperties,
  }: { operationStatus: any; extraTrackingProperties?: { [key: string]: any } }
) => {
  try {
    const { data: { postData = { processIds: [] }, response = { deletedEntities: [] } } = {} } =
      operationStatus;
    const succeedProcessIds = postData.processIds.filter(({ id }: { id: string }) =>
      response.deletedEntities.includes(id)
    );
    for (const entity of succeedProcessIds) {
      const eventName = checkIsEvent(entity.entityType)
        ? SESSION_EVENTS.delete_ilt_event
        : SESSION_EVENTS.delete_ilt_session;
      const eventPureData = EVENT_DATA_MAP[eventName] as unknown as {
        [key: string]: { value?: string; processor?: (...params: any) => any };
      };
      if (!eventPureData) continue;
      const modifiedPostData = { ...(postData || {}), processIds: [entity] };
      const eventData = Object.keys(eventPureData).reduce((acc, eventDataKey: string) => {
        const { value = undefined, processor = noop } = eventPureData[eventDataKey] || {};
        const eventValue = processor(value, modifiedPostData, response);
        acc[eventDataKey] = eventValue;
        return acc;
      }, {} as { [key: string]: any });
      tracker.trackStructuredEvent({
        eventName: eventName,
        ...eventData,
        ...extraTrackingProperties,
      });
    }
  } catch (err) {
    // do nothing
  }
};

export const sendEntityLinkCopyClicked = (
  tracker: TSnowplowTrackerType,
  extraInfo: { pageName: string; id: string; entityType: TILTEntities; parentId?: string }
) => {
  const { pageName, entityType, parentId, id } = extraInfo;
  const isEvent = checkIsEvent(entityType);

  trackerWithCatch(tracker, {
    eventName: isEvent
      ? SESSION_EVENTS.copy_ilt_event_link_clicked
      : SESSION_EVENTS.copy_ilt_session_link_clicked,
    [SNOWPLOW_FIELD_NAMES.PAGE_NAME]: pageName,
    session_id: isEvent ? undefined : id,
    event_id: isEvent ? id : parentId,
  });
};

export const sendResetFilterClicked = (
  tracker: TSnowplowTrackerType,
  extraInfo: { pageName: string }
) => {
  const { pageName } = extraInfo;
  trackerWithCatch(tracker, {
    eventName: MIXPANEL_UI_EVENTS.RESET_FILTER_CLICKED,
    [SNOWPLOW_FIELD_NAMES.PAGE_NAME]: pageName,
  });
};
