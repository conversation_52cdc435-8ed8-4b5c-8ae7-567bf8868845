import {
  checkIsEvent,
  checkIsIndependentSession,
  checkIsSessionWithinEvent,
} from '~/modules/Admin/utils/checkEntityType';

import type {
  TSelectedIDsWithMap,
  TConvertToBasicParams,
  TILT_ENTITIES,
  TSessionsMap,
  TEventSessionListMap,
  TEntitiesWithBasicInfo,
} from '../typeDefs';

export function getEntityCountFromBasicInfo({ entities }: { entities: TEntitiesWithBasicInfo }) {
  let sessionCount = 0,
    sessionsWithinEventCount = 0,
    eventCount = 0;
  for (const entity of entities) {
    const { entityType } = entity || {};
    if (checkIsIndependentSession(entityType)) {
      sessionCount++;
    }
    if (checkIsSessionWithinEvent(entityType)) {
      sessionsWithinEventCount++;
    }
    if (checkIsEvent(entityType)) {
      eventCount++;
    }
  }
  return {
    sessionCount,
    sessionsWithinEventCount,
    allSessionCount: sessionCount + sessionsWithinEventCount,
    eventCount,
  };
}

export function getSelectionCountInfo({
  entityIds,
  sessionsMap,
  eventSessionListMap,
}: {
  entityIds: string[];
  sessionsMap: TSessionsMap;
  eventSessionListMap: TEventSessionListMap;
}) {
  // Bahot hi faltu ka logic ke lie karna pad raha hai copy ke wajah se
  let sessionCount = 0,
    sessionsWithinEventCount = 0,
    eventCount = 0;
  for (const id of entityIds) {
    const { entityType } = sessionsMap[id] || {};
    if (checkIsIndependentSession(entityType)) {
      sessionCount++;
    }
    if (checkIsSessionWithinEvent(entityType)) {
      sessionsWithinEventCount++;
    }
    if (checkIsEvent(entityType)) {
      eventCount++;
    }
  }
  let allSessionsOfEventAreSelected = false;
  if (sessionsWithinEventCount && !sessionCount && !eventCount) {
    const parentId = sessionsMap[entityIds[0]].parentId;
    const childrenOfSameParent = entityIds.every(id => sessionsMap[id].parentId === parentId);
    if (
      parentId &&
      childrenOfSameParent &&
      entityIds.length === eventSessionListMap[parentId]?.length
    ) {
      allSessionsOfEventAreSelected = true;
    }
  }
  return {
    sessionCount,
    sessionsWithinEventCount,
    allSessionCount: sessionCount + sessionsWithinEventCount,
    eventCount,
    allSessionsOfEventAreSelected,
  };
}

export function getSelectedEntitiesInfo({
  ids,
  sessionsMap,
  eventSessionListMap,
}: TSelectedIDsWithMap) {
  const entitiesWithBasicInfo = convertSelectedIdsToBasicInfo({
    ids,
    sessionsMap,
  });

  const {
    sessionCount = 0,
    sessionsWithinEventCount = 0,
    eventCount = 0,
    allSessionCount = 0,
    allSessionsOfEventAreSelected,
  } = getSelectionCountInfo({ entityIds: ids, sessionsMap, eventSessionListMap });
  return {
    entitiesWithBasicInfo,
    allSessionsOfEventAreSelected,
    eventCount,
    sessionCount,
    sessionsWithinEventCount,
    allSessionCount,
  };
}

function convertSelectedIdsToBasicInfo({ ids, sessionsMap }: TConvertToBasicParams) {
  const entitiesWithBasicInfo: TEntitiesWithBasicInfo = [];
  for (const id of ids) {
    entitiesWithBasicInfo.push({
      id,
      parentId: sessionsMap[id].parentId,
      entityType: sessionsMap[id].entityType as TILT_ENTITIES,
    });
  }
  return entitiesWithBasicInfo;
}
