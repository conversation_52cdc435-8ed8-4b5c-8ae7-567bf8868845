import { defineMessages } from 'react-intl';

export default defineMessages({
  CANCELLING_SESSION: 'Cancelling session. This will take a few moments.',
  CANCELLING_EVENT: 'Cancelling event. This will take a few moments.',
  CANCEL_SESSION_FAILED: 'Failed to cancel session.',
  CANCEL_EVENT_FAILED: 'Failed to cancel the event.',
  CANCEL_SESSION_SUCCESS: 'Session has been canceled successfully',
  CANCEL_EVENT_SUCCESS: 'Event has been canceled successfully',
  // TODO some correction on the basis of count we need to make
  DELETING_SESSIONS_EVENT: 'Deletion in progress. This will take a few moments.',
  DELETE_FAILED_MULTIPLE_BOTH:
    'Failed to delete {eventCount} {eventLabel} and {sessionCount} {sessionLabel}.',
  DELETE_FAILED_SESSIONS: 'Failed to delete {sessionCount} {sessionLabel}.',
  DELETE_FAILED_EVENTS: 'Failed to delete {eventCount} {eventLabel}.',
  DELETE_FAILED_SINGLE: `Failed to delete the {label}.`,
  DELETE_SUCCESS_MULTIPLE_BOTH:
    '{eventCount} {eventLabel} and {sessionCount} {sessionLabel} have been deleted successfully',
  DELETE_SUCCESS_SESSIONS: '{sessionCount} {sessionLabel} have been deleted successfully',
  DELETE_SUCCESS_EVENTS: '{eventCount} {eventLabel} have been deleted successfully',
  DELETE_SUCCESS_SINGLE: '{label} has been deleted successfully',
  OPERATION_CONFIRM_DEFAULT_OK_BUTTON_TEXT: 'Ok',
  SESSION_LINK_COPIED: 'Session link copied to clipboard successfully',
  EVENT_LINK_COPIED: 'Event link copied to clipboard successfully',
  EVENT_COPIED_SUCCESS: 'Event has been copied successfully',
  EVENT_CREATED_SUCCESS: 'Event has been created successfully',
  EVENT_UPDATED_SUCCESS: 'Event has been updated successfully',
  EVENT_COPY_FAILED: 'Failed to create a copy of the event. Please try again.',
  EVENT_CREATE_FAILED: 'Failed to create this event. Please try again.',
  EVENT_UPDATE_FAILED: 'Failed to update event. Please try again.',
});
