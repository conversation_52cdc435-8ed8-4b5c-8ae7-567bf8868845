import { useCallback, useEffect, useRef, useState } from 'react';

import { FormattedMessage } from 'react-intl';

import CopyToClipboard from '@mindtickle/copy-to-clipboard';
import Modal from '@mindtickle/modal';
import { errorToast, successToast } from '@mindtickle/toast';
import { isEmpty } from '@mindtickle/utils';

import { THEME_PREFIX_CLS } from '~/config/constants';
import { ILT_ENTITIES } from '~/modules/Admin/config/constants';
import { useILTAdminSnowplowTracker } from '~/modules/Admin/hooks/useILTAdminSnowplowTracker';
import { SNOWPLOW_FIELD_NAMES } from '~/modules/Admin/hooks/useILTAdminSnowplowTracker/constants';
import { TILTEntities } from '~/modules/Admin/typeDefs';
import { castArray } from '~/mt-ui-core/utils';
import { getIltSessionUrl } from '~/utils/generateUrls';
import { multipleToast } from '~/utils/multipleToast';

import GET_ERROR_MESSAGES from '../../config/error.messages';
import { LC_MODAL_ACTIONS, MODE_EXPORT_LC_REPORT } from '../../config/live-challenges.constants';
import { MODES, OPERATIONS } from '../../config/sessions.constants';
import { createSessionTableData, getInfoAfterSingleSessionSaved } from '../../utils';
import { checkIsEvent } from '../../utils/checkEntityType';
import { EntitiesOperationModeLoader } from '../EntitiesOperationModeLoader';
import EventEditDrawer from '../EventEditDrawer';
import ManageEventLeftPanel from '../ManageEventLeftPanel';
import SessionEditDrawer from '../SessionEditDrawer';
import ShareLiveChallengeModal from '../ShareLiveChallengeModal';

import CancelSessionModal from './components/CancelSessionModal';
import { getDeleteConfirmationMessages } from './components/SessionsOperationConfirmation';
import UploadSessions from './components/UploadSessions';
import { useCopySession } from './hooks/useCopySession';
import messages from './messages';
import { showOperationEffectMessages } from './utils/operationsEffectToasts';
import { getSelectedEntitiesInfo } from './utils/operationsUtils';
import {
  sendBulkCreateIltSessionEvent,
  sendBulkUploadClickEvent,
  sendCreateEntityClicked,
  sendEventSettingsClicked,
  sendSessionEmailLCReportSubmitted,
  sendSessionGetCheckinCodeClicked,
  sendSessionOperation,
  sendEntityLinkCopyClicked,
} from './utils/trackEvents';

import type {
  TManageEventPanelProps,
  TEntityWithBasicInfo,
  TTableActions,
  TTableActionsIdProp,
  handleModeOperationProps,
} from './typeDefs';
import type {
  TEntitiesOnSelectInfo,
  TSortingOrder,
  TSupportedSortingField,
  TTableEntityObject,
} from '../SessionsTable/typeDefs';

const {
  ADD,
  ADD_EVENT,
  ADD_BULK,
  COPY,
  COPY_EVENT,
  UPDATE,
  UPDATE_EVENT,
  CANCEL,
  CANCEL_EVENT,
  REMOVE,
} = OPERATIONS;

// eslint-disable-next-line max-statements -- TODO: akhil - to reduce it after changes are merged as most of the things are common with sessionsWrapper so don't want to introduce much of the conflict until it goes to prod
const ManageEventPanel = (props: TManageEventPanelProps) => {
  const {
    trackerPageName,
    context,
    sessionsMap,
    eventSessionListMap,
    operationStatus: { loadingData: { operation = {} } = {}, isLoading } = {},
    operationStatus,
    moduleDetails,
    companyData,
    integrations,
    liveChallenge,
    isILTSessionCalendarAutoSyncEnabled,
    eventId,
    urlSelectedId,
    status,
    actions,
    companySettings,
  } = props;
  const { manipulateData } = actions;
  const [selected, setSelected] = useState<string | undefined>(undefined);
  const [selectedParent, setSelectedParent] = useState<string | undefined>(undefined);
  const [selectedEntityType, setSelectedEntityType] = useState<TILTEntities | undefined>(undefined);
  const [mode, setMode] = useState<string | undefined>(undefined);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [initialDrawerTab, setInitialDrawerTab] = useState<string | undefined>(undefined);
  const [initialEventDrawerTab, setInitialEventDrawerTab] = useState<string | undefined>(undefined);
  const [, setShowModLoader] = useState(false); // TODO: akhil remove this after testing finished
  const currentPropsRef = useRef(props);
  const prevPropsRef = useRef(props);

  const tracker = useILTAdminSnowplowTracker();

  const handleChangedSessionSuccessfully = useCallback(() => {
    const { toastInfo } = getInfoAfterSingleSessionSaved({
      operationStatus,
    });
    toastInfo && multipleToast(toastInfo);
  }, [operationStatus]);

  const changeSelected = ({ id, entityType, parentId }: TEntityWithBasicInfo) => {
    setSelected(id);
    setSelectedParent(parentId);
    setSelectedEntityType(entityType);
  };

  const tableActions: TTableActions = {
    deleteEntity: ({ id }: TEntityWithBasicInfo) => removeEntities([id]),
    editEntity: ({ id, entityType, parentId }: TEntityWithBasicInfo) => {
      setMode(MODES.EDIT);
      changeSelected({ id, entityType, parentId });
      setInitialDrawerTab(undefined);
      setInitialEventDrawerTab(undefined);
    },
    cancelEntity: ({ id, entityType, parentId }: TEntityWithBasicInfo) => {
      setMode(MODES.CANCEL);
      changeSelected({ id, entityType, parentId });
    },
    copyEntity: ({ id, entityType, parentId }: TEntityWithBasicInfo) => {
      setShowModLoader(true);
      changeSelected({ id, entityType, parentId });
      if (checkIsEvent(entityType)) {
        handleModeOperation({
          mode: MODES.COPY,
          data: [id],
          selectedEntity: { id, entityType, parentId },
        });
      } else {
        createSessionCopy(sessionsMap[id], { id, entityType, parentId });
      }
    },
    viewEntity: ({ id, entityType, parentId }: TEntityWithBasicInfo) => {
      setMode(MODES.VIEW);
      changeSelected({ id, entityType, parentId });
    },
    copyEntityUrl: ({ id, entityType, parentId }: TEntityWithBasicInfo) => {
      const isEvent = checkIsEvent(entityType);
      CopyToClipboard.copy({
        textToCopy: getIltSessionUrl(context, { id, entityType }),
        onCopy: (_text: string, result: any) =>
          result &&
          successToast({
            message: (
              <FormattedMessage
                {...(isEvent ? messages.EVENT_LINK_COPIED : messages.SESSION_LINK_COPIED)}
              />
            ),
          }),
      });
      sendEntityLinkCopyClicked(tracker, { pageName: trackerPageName, id, entityType, parentId });
    },
    manageCheckIn: ({ id, entityType, parentId }: TEntityWithBasicInfo) => {
      setMode(MODES.VIEW);
      changeSelected({ id, entityType, parentId });
      setInitialDrawerTab('attendance');
      sendSessionGetCheckinCodeClicked(tracker, {
        pageName: trackerPageName,
        id,
        name: sessionsMap[id].name,
      });
    },
    manageEventEnrollment: ({ id, entityType }: TTableActionsIdProp) => {
      setMode(MODES.VIEW);
      setSelected(id);
      setSelectedEntityType(entityType || ILT_ENTITIES.SESSION);
      setInitialEventDrawerTab('enrollment');
      sendEventSettingsClicked(tracker, { pageName: trackerPageName, id });
    },
    uploadSessionsWithinEvent: ({ parentId }: { parentId?: string } = {}) => {
      handleUploadSessions({ parentId });
    },
    createSessionWithinEvent: ({ parentId }: { parentId: string }) => {
      handleCreateSession({ parentId });
    },
    exportLiveChallengeReport: ({ id }: TTableActionsIdProp) => {
      setMode(MODE_EXPORT_LC_REPORT);
      setSelected(id);
      sendSessionEmailLCReportSubmitted(tracker, {
        pageName: trackerPageName,
        sessionId: id,
        sessionName: sessionsMap[id].name,
      });
    },
    onSelect: function (selectedEntities: TEntitiesOnSelectInfo): void {
      throw new Error('Function not implemented.');
    },
    sortColumn: function (
      operation: any,
      sortOrder: TSortingOrder,
      sortField: TSupportedSortingField
    ): void {
      throw new Error('Function not implemented.');
    },
    loadMoreEntities: function (): void {
      throw new Error('Function not implemented.');
    },
    openUploadRecordings: function ({ id }: TTableActionsIdProp): void {
      throw new Error('Function not implemented.');
    },
  };

  const clearMode = () => {
    setMode(undefined);
    setSelected(undefined);
    setSelectedParent(undefined);
    setSelectedEntityType(undefined);
    setInitialDrawerTab(undefined);
  };

  const clearEventMode = () => {
    clearMode();
    setInitialEventDrawerTab(undefined);
  };

  const handleCreateSession = ({
    parentId,
    entityType,
  }: { parentId?: string; entityType?: TILTEntities } = {}) => {
    entityType =
      entityType || (parentId ? ILT_ENTITIES.SESSION_WITHIN_EVENT : ILT_ENTITIES.SESSION);
    setMode(MODES.ADD);
    setSelected(undefined);
    setSelectedEntityType(entityType);
    setSelectedParent(parentId);
    sendCreateEntityClicked(tracker, { pageName: trackerPageName, entityType, parentId });
  };

  const handleUploadSessions = ({ parentId }: { parentId?: string }) => {
    if (parentId) {
      setSelectedParent(parentId);
    }
    setShowUploadModal(true);
    sendBulkUploadClickEvent(tracker, {
      pageName: trackerPageName,
      parentId,
    });
  };

  const createBulkSessions = (sessions: any, parentId?: string) => {
    handleModeOperation({
      mode: MODES.ADD_BULK,
      data: sessions,
      selectedEntity: { parentId },
    });
  };

  const handleModeOperation = ({
    mode,
    data,
    notifyLearnersOnCancellation = 'true',
    selectedEntity,
  }: handleModeOperationProps) => {
    let operation;
    const { entityType, parentId } = selectedEntity;
    const isEvent = checkIsEvent(entityType);
    switch (mode) {
      case MODES.EDIT:
        operation = isEvent ? UPDATE_EVENT : UPDATE;
        break;
      case MODES.ADD:
        operation = isEvent ? ADD_EVENT : ADD;
        break;
      case MODES.ADD_BULK:
        operation = ADD_BULK;
        break;
      case MODES.COPY:
        operation = isEvent ? COPY_EVENT : COPY;
        break;
      case MODES.CANCEL:
        operation = isEvent ? CANCEL_EVENT : CANCEL;
        break;
      default:
      // do nothings;
    }
    data = castArray(data);
    operation &&
      handleOperation({
        operation,
        sessions: data,
        parentId,
        notifyLearnersOnCancellation,
      });
  };

  const createSessionCopy = useCopySession({ handleModeOperation, moduleDetails });

  const withConfirmation = ({
    operation,
    countInfo,
    callback,
  }: {
    operation: string;
    countInfo: any;
    callback: Function;
  }) => {
    let content = <></>,
      title = <></>,
      okButtonText = <FormattedMessage {...messages.OPERATION_CONFIRM_DEFAULT_OK_BUTTON_TEXT} />,
      confirmExtraProps: any = {};

    if (operation === REMOVE) {
      const {
        content: _content,
        okButtonText: _okButtonText,
        title: _title,
      } = getDeleteConfirmationMessages(countInfo);
      content = _content;
      okButtonText = _okButtonText;
      title = _title;
      confirmExtraProps = {
        isDestructiveAction: 'true',
        okButtonProps: { type: 'danger' },
        cancelButtonProps: { type: 'secondary' },
      };
    }

    Modal.confirm({
      prefixCls: THEME_PREFIX_CLS,
      content,
      centered: true,
      onOk: callback,
      okButtonText,
      title: title,
      ...confirmExtraProps,
    });
  };

  const handleOperation = ({
    operation,
    sessions = [],
    processIds = [],
    parentId,
    notifyLearnersOnCancellation = 'true',
    sessionsMap = {},
    isBulk = false, // for tracking purpose
  }: any) => {
    manipulateData({
      operation,
      sessions,
      processIds,
      parentId,
      notifyLearnersOnCancellation,
      sessionsMap,
      isBulk,
    });
  };
  const handleShareCheckInCode = ({
    sessionId,
    session,
    emailIds,
  }: {
    sessionId: string;
    session: any;
    emailIds: [string];
  }) => {
    manipulateData({
      operation: OPERATIONS.SHARE_CHECKIN_CODE,
      sessionId,
      emailIds,
      sessions: [session],
    });
  };
  const removeEntities = (ids: string[] = [], { isBulk = false } = {}) => {
    if (isEmpty(ids)) return;
    const {
      entitiesWithBasicInfo: entitiesWithInfo,
      allSessionsOfEventAreSelected,
      eventCount,
      sessionCount,
      sessionsWithinEventCount,
      allSessionCount,
    } = getSelectedEntitiesInfo({ ids, sessionsMap, eventSessionListMap });

    withConfirmation({
      operation: REMOVE,
      countInfo: {
        sessionCount,
        allSessionsOfEventAreSelected,
        allSessionCount,
        sessionsWithinEventCount,
        eventCount,
      },
      callback: () => {
        handleOperation({
          operation: REMOVE,
          processIds: entitiesWithInfo,
          sessionsMap: sessionsMap,
          isBulk, // for tracking purpose
        });
      },
    });
  };

  const renderPanel = () => (
    <ManageEventLeftPanel
      actions={tableActions}
      status={status}
      data={
        status.loaded
          ? (createSessionTableData({
              sessions: [eventId],
              sessionsMap,
              eventSessionListMap,
            }) as TTableEntityObject[])
          : []
      }
      selectedId={urlSelectedId}
      totalEventSessionsCount={eventSessionListMap?.[eventId]?.length || 0}
    />
  );

  const renderSessionEditDrawer = () => {
    const { loadingData: { operation = {} } = {}, isLoading } = operationStatus || {};
    return (
      <SessionEditDrawer
        visible={true}
        onClose={clearMode}
        integrations={integrations}
        data={(selected ? sessionsMap[selected as keyof typeof sessionsMap] : {}) || {}}
        mode={mode}
        domain={context.domain}
        liveChallenge={liveChallenge}
        resetNewLiveChallengeData={actions.resetNewLiveChallengeData}
        companyData={companyData}
        operationStatus={operationStatus}
        inProgress={[ADD, COPY, UPDATE].includes(operation) && isLoading}
        defaultScore={moduleDetails.scoring && moduleDetails.score}
        onShareCheckInCode={handleShareCheckInCode}
        update={(data: any) =>
          handleModeOperation({
            mode,
            data,
            selectedEntity: { parentId: selectedParent, entityType: selectedEntityType },
          })
        }
        initialDrawerTab={initialDrawerTab}
        triggerEditAction={tableActions.editEntity}
        defaultCompletionCriteria={moduleDetails.defaultSessionCompletionCriteria}
        setShowSessionModLoader={setShowModLoader}
        eventData={
          (selectedParent ? sessionsMap[selectedParent as keyof typeof sessionsMap] : {}) || {}
        }
        manageEventEnrollment={tableActions.manageEventEnrollment}
        moduleDetails={moduleDetails}
        companySettings={companySettings}
      />
    );
  };

  const renderEventEditDrawer = () => {
    const { loadingData: { operation = {} } = {}, isLoading } = operationStatus || {};
    return (
      <EventEditDrawer
        visible={true}
        onClose={clearEventMode}
        data={(selected ? sessionsMap[selected as keyof typeof sessionsMap] : {}) || {}}
        sessionsList={eventSessionListMap[selected || '']}
        mode={mode}
        inProgress={[ADD_EVENT, UPDATE_EVENT].includes(operation) && isLoading}
        update={(data: any) =>
          handleModeOperation({ mode, data, selectedEntity: { entityType: selectedEntityType } })
        }
        initialDrawerTab={initialEventDrawerTab}
        triggerEditAction={tableActions.editEntity}
        setShowEventModLoader={setShowModLoader}
      />
    );
  };

  const renderCancelModal = () => (
    <CancelSessionModal
      sessionsMap={sessionsMap}
      selectedId={selected}
      inProgress={[CANCEL].includes(operation) && isLoading}
      isILTSessionCalendarAutoSyncEnabled={isILTSessionCalendarAutoSyncEnabled}
      handleModeOperation={handleModeOperation}
      clearMode={clearMode}
    />
  );

  const renderExportLCReportModal = () => {
    const session = sessionsMap[selected!];
    return (
      <ShareLiveChallengeModal
        sessionId={session.id}
        lcUniqueCode={session.liveChallenge!.uniqueCode}
        onCancel={clearMode}
        action={LC_MODAL_ACTIONS.EXPORT_REPORT}
      />
    );
  };

  const sessionActionMap = {
    [MODES.VIEW]: renderSessionEditDrawer,
    [MODES.CANCEL]: renderCancelModal,
    [MODES.EDIT]: renderSessionEditDrawer,
    [MODES.ADD]: renderSessionEditDrawer,
    [MODE_EXPORT_LC_REPORT]: renderExportLCReportModal,
  };

  const eventActionMap = {
    [MODES.VIEW]: renderEventEditDrawer,
    [MODES.CANCEL]: renderCancelModal,
    [MODES.EDIT]: renderEventEditDrawer,
  };

  const renderSessionActionOverlay = () => {
    if (mode !== undefined && checkIsEvent(selectedEntityType)) {
      return eventActionMap[mode] && eventActionMap[mode]();
    }
    if (mode !== undefined) {
      return sessionActionMap[mode] && sessionActionMap[mode]();
    }
    return null;
  };

  const renderUploadModal = () =>
    showUploadModal && (
      <UploadSessions
        onClose={() => {
          setShowUploadModal(false);
          setSelectedParent(undefined);
        }}
        uploadSessions={createBulkSessions}
        integrations={integrations}
        defaultCompletionCriteria={moduleDetails.defaultSessionCompletionCriteria}
        trackerPageName={trackerPageName}
        eventData={
          (selectedParent ? sessionsMap[selectedParent as keyof typeof sessionsMap] : {}) || {}
        }
        sessionsList={eventSessionListMap[selectedParent || '']}
        moduleDetails={moduleDetails}
      />
    );

  useEffect(() => {
    // Set the current props to the prev props ref
    prevPropsRef.current = currentPropsRef.current;
    // Update the current props ref
    currentPropsRef.current = props;
  });

  // eslint-disable-next-line max-statements, complexity
  useEffect(() => {
    const {
      loaded: newLoaded,
      data: { operation = {} } = {},
      operation: currentOperation,
      error,
      hasError,
    } = operationStatus;
    const prevProps = prevPropsRef.current;
    const {
      operationStatus: { hasError: prevHasError },
    } = prevProps;
    const { loaded: oldLoaded } = prevProps.operationStatus;

    if (hasError && hasError !== prevHasError) {
      setShowModLoader(false);
      errorToast({ message: <FormattedMessage {...GET_ERROR_MESSAGES(error)} />, timeout: 3000 });
    }
    if (hasError && hasError !== prevHasError && [ADD, UPDATE].includes(currentOperation)) {
      if (error && error.data && error.data.WebConfErrorCode) {
        const message = <FormattedMessage {...GET_ERROR_MESSAGES(error.data.WebConfErrorCode)} />;
        if (message) {
          errorToast({ message, timeout: 3000 });
        }
      }
    }

    if (hasError && hasError !== prevHasError && currentOperation === UPDATE_EVENT) {
      const message = messages.EVENT_UPDATE_FAILED;
      errorToast({
        message: <FormattedMessage {...message} />,
        freeze: false,
        timeout: 3000,
      });
    }

    showOperationEffectMessages({
      trackerPageName,
      operationStatus: operationStatus,
      prevOperationStatus: prevProps.operationStatus,
      tracker,
    });

    switch (operation) {
      case ADD:
      case UPDATE:
        if (newLoaded && oldLoaded !== newLoaded) {
          if (!hasError) {
            setMode(undefined);
            setShowModLoader(false);
            handleChangedSessionSuccessfully();
          }
        }
        break;
      case UPDATE_EVENT:
        if (newLoaded && oldLoaded !== newLoaded && !hasError) {
          setMode(undefined);
          setShowModLoader(false);
          const message =
            operation === COPY_EVENT
              ? messages.EVENT_COPIED_SUCCESS
              : operation === ADD_EVENT
              ? messages.EVENT_CREATED_SUCCESS
              : messages.EVENT_UPDATED_SUCCESS;
          successToast({
            message: <FormattedMessage {...message} />,
            freeze: false,
            timeout: 3000,
          });
        }
        break;
      default:
        break;
    }
    // call tracking events
    if (
      [ADD, UPDATE, UPDATE_EVENT, OPERATIONS.SHARE_CHECKIN_CODE].includes(operation) &&
      newLoaded &&
      oldLoaded !== newLoaded &&
      !hasError
    ) {
      sendSessionOperation(tracker, {
        operationStatus: operationStatus,
        extraTrackingProperties: {
          [SNOWPLOW_FIELD_NAMES.PAGE_NAME]: trackerPageName,
        },
      });
    }
    if (newLoaded && oldLoaded !== newLoaded && !hasError && ADD_BULK === operation) {
      sendBulkCreateIltSessionEvent(tracker, {
        operationStatus: operationStatus,
        extraTrackingProperties: {
          [SNOWPLOW_FIELD_NAMES.PAGE_NAME]: trackerPageName,
        },
      });
    }
  }, [handleChangedSessionSuccessfully, operationStatus, tracker, trackerPageName]);

  return (
    <>
      <EntitiesOperationModeLoader operationStatus={operationStatus} />
      {renderPanel()}
      {renderSessionActionOverlay()}
      {renderUploadModal()}
    </>
  );
};

ManageEventPanel.defaultProps = {
  operationStatus: {},
};

export default ManageEventPanel;
