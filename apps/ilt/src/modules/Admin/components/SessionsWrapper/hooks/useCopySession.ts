import {
  DEFAULT_WEBINAR_SETTINGS,
  LOCATION_SESSION_TYPE_MAP,
  LOCATION_TYPE,
  MAX_SESSION_NAME_LENGTH,
  MODES,
  WEBINAR_MEETING_TYPES,
} from '~/modules/Admin/config/sessions.constants';
import { getWebinarMeetingSettingsKeyByLocationType } from '~/modules/Admin/utils/sessionEdit';

import { getSessionCopy } from '../utils';

import type { TEntityWithBasicInfo, TUseCopySessionProps } from '../typeDefs';

export function useCopySession({ handleModeOperation, moduleDetails }: TUseCopySessionProps) {
  const createSessionCopy = (sessionData: any, selectedEntity: TEntityWithBasicInfo) => {
    let copiedSessionData = getSessionCopy({
      sessionData,
      defaultCompletionCriteria: moduleDetails.defaultSessionCompletionCriteria,
      defaultScore: moduleDetails.scoring && moduleDetails.score,
    });

    copiedSessionData.name = 'Copy of ' + copiedSessionData.name;
    copiedSessionData.name =
      copiedSessionData.name.length > MAX_SESSION_NAME_LENGTH
        ? copiedSessionData.name.substring(0, MAX_SESSION_NAME_LENGTH)
        : copiedSessionData.name;

    delete copiedSessionData.liveChallenge;

    const meetingSettingsKey = getWebinarMeetingSettingsKeyByLocationType(
      copiedSessionData.locationType
    );

    if (WEBINAR_MEETING_TYPES.includes(copiedSessionData.locationType)) {
      copiedSessionData.locationType = LOCATION_TYPE.FACE_TO_FACE;
      copiedSessionData.type = LOCATION_SESSION_TYPE_MAP[copiedSessionData.locationType];
      copiedSessionData.webAutoAttendanceSettings = { ...DEFAULT_WEBINAR_SETTINGS };
      delete copiedSessionData.location;
      delete copiedSessionData[meetingSettingsKey];
    }

    copiedSessionData.webAutoAttendanceSettings.thresholdConfig = {};
    copiedSessionData.webAutoAttendanceSettings.thresholdConfig.isEnabled =
      sessionData?.webAutoAttendanceSettings?.thresholdConfig?.isEnabled;
    copiedSessionData.webAutoAttendanceSettings.thresholdConfig.percentageThreshold =
      sessionData?.webAutoAttendanceSettings?.thresholdConfig?.percentageThreshold;

    copiedSessionData.enrolmentThresholdEmailSettings = {};
    copiedSessionData.enrolmentThresholdEmailSettings.isEnabled =
      sessionData?.enrolmentThresholdEmailSettings?.isEnabled || false;
    copiedSessionData.enrolmentThresholdEmailSettings.enrolmentThreshold =
      sessionData?.enrolmentThresholdEmailSettings?.enrolmentThreshold || 0;
    copiedSessionData.enrolmentThresholdEmailSettings.reminders =
      sessionData?.enrolmentThresholdEmailSettings?.reminders || [];
    copiedSessionData.enrolmentThresholdEmailSettings.reminders =
      copiedSessionData.enrolmentThresholdEmailSettings.reminders.map((reminder: any) => ({
        ...reminder,
        operation: 'COPY',
        mailJobId: '',
      }));

    handleModeOperation({ mode: MODES.ADD, data: copiedSessionData, selectedEntity });
  };
  return createSessionCopy;
}
