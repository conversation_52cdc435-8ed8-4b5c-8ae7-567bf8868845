import type { TEntitiesOnSelectInfo, TTableActionsIdProp, TEntityWithBasicInfo } from './';
import type { TSupportedSortingField, TSortingOrder } from '../../SessionsTable/typeDefs';

export type TTableActions = {
  onSelect: (selectedEntities: TEntitiesOnSelectInfo) => void;
  deleteEntity: ({ id }: TEntityWithBasicInfo) => void;
  editEntity: ({ id, entityType, parentId }: TEntityWithBasicInfo) => void;
  cancelEntity: ({ id, entityType, parentId }: TEntityWithBasicInfo) => void;
  copyEntity: ({ id, entityType, parentId }: TEntityWithBasicInfo) => void;
  viewEntity: ({ id, entityType, parentId }: TEntityWithBasicInfo) => void;
  copyEntityUrl: ({ id, entityType }: TEntityWithBasicInfo) => void;
  sortColumn: (operation: any, sortOrder: TSortingOrder, sortField: TSupportedSortingField) => void;
  loadMoreEntities: () => void;
  manageCheckIn: ({ id, entityType, parentId }: TEntityWithBasicInfo) => void;
  manageEventEnrollment: ({ id, entityType }: TTableActionsIdProp) => void;
  uploadSessionsWithinEvent: ({ parentId }: { parentId?: string | undefined }) => void;
  createSessionWithinEvent: ({ parentId }: { parentId: string }) => void;
  exportLiveChallengeReport: ({ id }: TTableActionsIdProp) => void;
  openUploadRecordings: ({ id }: TTableActionsIdProp) => void;
};
