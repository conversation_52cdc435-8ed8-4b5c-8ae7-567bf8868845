import type { TILTEntities } from '~/modules/Admin/typeDefs';

import type {
  TManipulateDataFunc,
  TGetDataFunc,
  TSessions,
  TSessionsMap,
  TILTFilters,
  TLiveChallenge,
  TEventSessionListMap,
  TEntityFilterHiddenMap,
  TEntityStatsMap,
} from '../../../containers/Build/typeDefs';
import type {
  TGetDataFunc as TManageEventLayoutGetData,
  TManipulateDataFunc as TManageEventLayoutManipulateDataFunc,
} from '../../../containers/ManageEventPanel/typeDefs';
import type {
  TILT_ENTITIES,
  TSupportedSortingField,
  TSortingOrder,
} from '../../SessionsTable/typeDefs';

export type { TTableActions } from './tableActions';

export { TILT_ENTITIES, TSessionsMap, TEventSessionListMap };

export type TSelectedCountInfo = {
  sessionCount: number;
  allSessionsOfEventAreSelected: boolean;
  sessionsWithinEventCount: number;
  eventCount: number;
  allSessionCount: number;
};

export type TSelectedIDsWithMap = {
  ids: string[];
  sessionsMap: TSessionsMap;
  eventSessionListMap: TEventSessionListMap;
};

export type TConvertToBasicParams = {
  ids: string[];
  sessionsMap: TSessionsMap;
};

export type TEntityWithBasicInfo = { id: string; entityType: TILT_ENTITIES; parentId?: string };
export type TEntityOnSelectInfo = TEntityWithBasicInfo & { checked: boolean };
export type TEntitiesWithBasicInfo = TEntityWithBasicInfo[];
export type TEntitiesOnSelectInfo = TEntityOnSelectInfo[];
export interface TSessionOperationConfirmation {
  operation: string;
  countInfo?: any;
}

export interface TSessionsNoData {
  onCreateSessionClick: () => void;
  onUploadSessionsClick: () => void;
  onCreateEventClick: () => void;
}
export interface TOnFilterSelect {
  filterType: string;
  data: any;
}

export type TUseCopySessionProps = {
  handleModeOperation: (params: {
    mode: string;
    data: any;
    selectedEntity: TEntityWithBasicInfo;
    [key: string]: any;
  }) => void;
  moduleDetails: any;
};

export interface SessionsWrapperProps {
  trackerPageName: string;
  actions: {
    manipulateData: TManipulateDataFunc;
    getData: TGetDataFunc;
    onInvite?: () => void;
    resetData: () => void;
    resetNewLiveChallengeData: () => void;
    openUploadRecordings: (sessionDetails: any) => void;
  };
  context: any;
  operationStatus?: any;
  companyData?: object;
  integrations?: object;
  moduleDetails: any;
  haveTrackAccess: boolean;
  haveBuildAccess: boolean;
  sessions: TSessions;
  sessionsMap: TSessionsMap;
  eventSessionListMap: TEventSessionListMap;
  entityFilterHiddenMap: TEntityFilterHiddenMap;
  entityStatsMap: TEntityStatsMap;
  defaultModuleRelevance?: string;
  ILTFilters: TILTFilters;
  colSortOrder: TSortingOrder;
  setColSortOrder: React.Dispatch<React.SetStateAction<TSortingOrder>>;
  colSortField: TSupportedSortingField;
  setColSortField: React.Dispatch<React.SetStateAction<TSupportedSortingField>>;
  updateILTFilters?: (filters: TILTFilters) => void;
  liveChallenge?: TLiveChallenge;
  isILTSessionCalendarAutoSyncEnabled?: boolean;
  companySettings: any;
}

export interface TManageEventPanelProps {
  trackerPageName: string;
  actions: {
    manipulateData: TManageEventLayoutManipulateDataFunc;
    getData: TManageEventLayoutGetData;
    resetData: () => void;
    resetNewLiveChallengeData: () => void;
    openUploadRecordings: (sessionDetails: any) => void;
  };
  context: any;
  operationStatus?: any;
  companyData?: object;
  integrations?: object;
  moduleDetails: any;
  companySettings?: any;
  haveTrackAccess: boolean;
  haveBuildAccess: boolean;
  eventId: string;
  urlSelectedId: string;
  sessionsMap: TSessionsMap;
  eventSessionListMap: TEventSessionListMap;
  defaultModuleRelevance?: string;
  liveChallenge?: TLiveChallenge;
  isILTSessionCalendarAutoSyncEnabled?: boolean;
  status: {
    isLoading: boolean;
    loaded: boolean;
    hasError: boolean;
  };
}

export interface handleModeOperationProps {
  mode?: string;
  data: any;
  selectedEntity: { id?: string; entityType?: TILTEntities; parentId?: string };
  notifyLearnersOnCancellation?: string;
}

export interface TTableActionsIdProp {
  id: string;
  entityType?: TILTEntities;
}
