import styled from 'styled-components';

import Loader from '@mindtickle/loader';
import { tokens, mixins } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS } from '~/config/constants';

export const StyledSplitButton = styled.div`
  margin-left: auto;
  .${THEME_PREFIX_CLS}-dropdown {
    z-index: 9910; // To solve split second visibility of dropdown options on drawer. its original value is 9940. Drawer z-index is 9912. https://mindtickle.atlassian.net/browse/LA2-1490
  }
  .create-dropdown-overlay-class > div {
    width: 376px;
    .create-new-item {
      .create-new-thumbnail {
        > .info-wrp {
          display: flex;
          flex-direction: column;
          row-gap: 4px;
          margin-left: 12px;
        }
        .thumbnail-icon {
          font-size: 16px;
          &.icon-session {
            font-size: 15px;
          }
        }
      }
    }
    .info-name {
      ${mixins.h3()}
    }
  }
  // these classnames are present in createNewDropdown component
  .create-new-drodown.external-create-new {
    display: inline-block;
  }
  .split-button-left {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right: 1px solid ${tokens.borderTokens.COLOR_BORDER_TERTIARY} !important;
  }
  .split-button-right {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    margin-right: 12px;
  }
`;

export const StyledFilterContainer = styled.div`
  border-bottom: 1px solid ${tokens.borderTokens.COLOR_BORDER_SECONDARY};
  display: flex;
  height: 70px;
  align-items: center;
  padding: 0 24px;
`;

export const StyledSessionsTableContainer = styled.div``;

export const StyledSessionWrapperContainer = styled.div``;

export const StyledLoader = styled(Loader)`
  &&.fullPageloadingScreen {
    position: fixed;
    background-color: #ffffffb3;
    .loaderMessage {
      color: ${tokens.textTokens.COLOR_TEXT_DEFAULT};
    }
  }
  &.toast-loader {
    margin-right: 10px;
  }
`;

export const StyledCancelInfo = styled.div`
  display: flex;
  align-items: center;
`;
