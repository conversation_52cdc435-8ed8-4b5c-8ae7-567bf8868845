import styled from 'styled-components';

import { mixins } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS } from '~/config/constants';

export const StyledSessionNameWithAllErrorsContainer = styled.div``;

export const StyledErrorContainer = styled.div`
  ${mixins.error()};
  font-weight: 400;
  margin-top: 4px;
  .error-message {
    display: flex;
    align-items: center;
    .error-icon {
      margin-left: 6px;
      padding-top: 2px;
    }
  }
  .tooltipInfo {
    .${THEME_PREFIX_CLS}-tooltip-content {
      white-space: nowrap;
      display: inline-block;
    }
  }
`;
