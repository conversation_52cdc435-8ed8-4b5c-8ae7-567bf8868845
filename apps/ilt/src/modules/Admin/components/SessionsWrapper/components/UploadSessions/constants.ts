export const VALIDATION_CHECKS = {
  NO_EMPTY: 'NO_EMPTY',
  DATE: 'DATE',
  TIME: 'TIME',
  TIMEZONE: 'TIMEZONE',
  ONE_OF_SESSION_TYPE: 'ONE_OF_ILT_TYPE',
};

export const MAX_INSTRUCTOR_COUNT = 6;

export const SAMPLE_BULK_SESSION_UPLOAD_EXCEL_PATH =
  'https://s3-ap-southeast-1.amazonaws.com/mtapps-cdn.mindtickle.com/ILTSessionBulkUploadTemplate_V5_new_timezone.xlsx';

export const SAMPLE_BULK_SESSION_USER_AUTH_UPLOAD_EXCEL_PATH =
  'https://s3-ap-southeast-1.amazonaws.com/mtapps-cdn.mindtickle.com/ILTSessionBulkUploadTemplate_V5_user_auth.xlsx';

export const ILT_SESSION_TYPES = ['CLASSROOM', 'VIDEO CONFERENCING'];

export const HOST_EMAIL_EXCEL_HEADER =
  'Host Email (required for Webex and MS teams for Auto-attendance)';
export const HOST_EMAIL_EXCEL_HEADER_NOT_PRESENT_ERROR =
  'HOST_EMAIL_EXCEL_HEADER_NOT_PRESENT_ERROR';

export const EXCEL_HEADER_CONFIGURATIONS = (isUserAuthEnabled: boolean) => [
  {
    key: 'name',
    excel_header: 'Session Name',
    validations: [VALIDATION_CHECKS.NO_EMPTY],
  },
  {
    key: 'type',
    excel_header: 'Session Type',
    validations: [VALIDATION_CHECKS.NO_EMPTY, VALIDATION_CHECKS.ONE_OF_SESSION_TYPE],
  },
  {
    key: 'description',
    excel_header: 'Session Description',
    validations: [],
  },
  {
    key: 'start_date',
    excel_header: 'Start Date',
    validations: [VALIDATION_CHECKS.NO_EMPTY, VALIDATION_CHECKS.DATE],
  },
  {
    key: 'start_time',
    excel_header: 'Start Time (24 hours format)',
    validations: [VALIDATION_CHECKS.NO_EMPTY, VALIDATION_CHECKS.TIME],
  },
  {
    key: 'end_date',
    excel_header: 'End Date',
    validations: [VALIDATION_CHECKS.NO_EMPTY, VALIDATION_CHECKS.DATE],
  },
  {
    key: 'end_time',
    excel_header: 'End Time (24 hours format)',
    validations: [VALIDATION_CHECKS.NO_EMPTY, VALIDATION_CHECKS.TIME],
  },
  {
    key: 'enrollment_lock_date',
    excel_header: 'Enrollment Lock Date',
    validations: [VALIDATION_CHECKS.DATE],
  },
  {
    key: 'enrollment_lock_time',
    excel_header: 'Enrollment Lock Time (24 hours format)',
    validations: [VALIDATION_CHECKS.TIME],
  },
  {
    key: 'time_zone',
    excel_header: 'Time Zone',
    validations: [VALIDATION_CHECKS.NO_EMPTY, VALIDATION_CHECKS.TIMEZONE],
  },
  {
    key: 'location',
    excel_header: 'Location/URL',
    validations: [],
  },
  isUserAuthEnabled
    ? {
        key: 'host_email', // This column used only when integration for webex/msteam is enabled and user entered webex/msteams url in location column
        excel_header: HOST_EMAIL_EXCEL_HEADER,
        validations: [],
      }
    : {
        key: 'password_webex', // This column used only when integration for webex is enabled and user entered webex url in location column
        excel_header: 'Password (required for Webex for Auto-attendance)',
        validations: [],
      },
  {
    key: 'reminder',
    excel_header: 'Reminder before',
    validations: [],
  },
  {
    key: 'max_enrolments',
    excel_header: 'Maximum Enrolments',
    validations: [],
  },
  {
    key: 'enable_waiting_list',
    excel_header: 'Waiting list',
    validations: [],
  },
  {
    key: 'score',
    excel_header: 'Score',
    validations: [],
  },
  {
    key: 'sendInstructorMail',
    excel_header: 'Notify Instructors via Email',
    validations: [],
  },
  {
    key: 'instructor_1_name',
    excel_header: 'Instructor 1 Name',
    validations: [],
  },
  {
    key: 'instructor_1_email',
    excel_header: 'Instructor 1 Email',
    validations: [],
  },
  {
    key: 'instructor_1_description',
    excel_header: 'Instructor 1 Description',
    validations: [],
  },
  {
    key: 'instructor_2_name',
    excel_header: 'Instructor 2 Name',
    validations: [],
  },
  {
    key: 'instructor_2_email',
    excel_header: 'Instructor 2 Email',
    validations: [],
  },
  {
    key: 'instructor_2_description',
    excel_header: 'Instructor 2 Description',
    validations: [],
  },
  {
    key: 'instructor_3_name',
    excel_header: 'Instructor 3 Name',
    validations: [],
  },
  {
    key: 'instructor_3_email',
    excel_header: 'Instructor 3 Email',
    validations: [],
  },
  {
    key: 'instructor_3_description',
    excel_header: 'Instructor 3 Description',
    validations: [],
  },
  {
    key: 'instructor_4_name',
    excel_header: 'Instructor 4 Name',
    validations: [],
  },
  {
    key: 'instructor_4_email',
    excel_header: 'Instructor 4 Email',
    validations: [],
  },
  {
    key: 'instructor_4_description',
    excel_header: 'Instructor 4 Description',
    validations: [],
  },
  {
    key: 'instructor_5_name',
    excel_header: 'Instructor 5 Name',
    validations: [],
  },
  {
    key: 'instructor_5_email',
    excel_header: 'Instructor 5 Email',
    validations: [],
  },
  {
    key: 'instructor_5_description',
    excel_header: 'Instructor 5 Description',
    validations: [],
  },
  {
    key: 'instructor_6_name',
    excel_header: 'Instructor 6 Name',
    validations: [],
  },
  {
    key: 'instructor_6_email',
    excel_header: 'Instructor 6 Email',
    validations: [],
  },
  {
    key: 'instructor_6_description',
    excel_header: 'Instructor 6 Description',
    validations: [],
  },
];
