import { useRef } from 'react';

import { injectIntl, FormattedMessage } from 'react-intl';

import Button, { BUTTON_TYPES } from '@mindtickle/button';
import Icon, { ICON_MAP } from '@mindtickle/icon';
import { CreateNewDropdown } from '@mindtickle/sidebar';
import Tooltip from '@mindtickle/tooltip';

import { ILT_ENTITIES } from '~/modules/Admin/config/constants';

import { StyledSplitButton } from '../../styles';

import useCreateDropdownOptions from './hooks/useCreateDropdownOptions';
import messages from './messages';

import type { TCreateEntitiesCta } from './typeDefs';
import type { InjectedIntlProps } from 'react-intl';

function CreateEntitiesSplitButton({
  onCreateSessionClick,
  onCreateEventClick,
  onUploadSessionsClick,
  splitButtonWrapperClassName = '',
  intl,
}: TCreateEntitiesCta & InjectedIntlProps) {
  const createOptionsSplitRef = useRef(null);
  const [creationDropdownOptions] = useCreateDropdownOptions({ intl });

  const handleGetPopupContainer = () => createOptionsSplitRef.current;

  const handleOnSelect = (selectedItem: { key: string; [x: string]: any }) => {
    const { key } = selectedItem;
    if (key === ILT_ENTITIES.SESSION) {
      onCreateSessionClick();
    }
    if (key === ILT_ENTITIES.EVENT) {
      onCreateEventClick();
    }
  };

  return (
    <StyledSplitButton className={splitButtonWrapperClassName} ref={createOptionsSplitRef}>
      <CreateNewDropdown
        getPopupContainer={handleGetPopupContainer}
        CreateNewTrigger={
          <Button type="primary" className="split-button-left">
            <FormattedMessage {...messages.CREATE_CTA_TEXT} />
          </Button>
        }
        overlayClassName={'create-dropdown-overlay-class'}
        onSelect={handleOnSelect}
        createNewOptions={creationDropdownOptions}
      />
      <Tooltip title={<FormattedMessage {...messages.UPLOAD_SESSION_TOOLTIP} />}>
        <Button
          type={BUTTON_TYPES.PRIMARY}
          className="split-button-right"
          onClick={() => onUploadSessionsClick()}
        >
          <Icon type={ICON_MAP.UPLOADNEWLIST} />
        </Button>
      </Tooltip>
    </StyledSplitButton>
  );
}

export default injectIntl(CreateEntitiesSplitButton);
