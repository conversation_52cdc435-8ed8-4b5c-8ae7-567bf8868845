import { useState, useRef } from 'react';

import { injectIntl } from 'react-intl';
import { FormattedMessage } from 'react-intl';
import { useUserAuth } from 'ui_shell/Auth';

import Button from '@mindtickle/button';
import EllipsisTooltip from '@mindtickle/ellipsis-tooltip';
import { isUndefined } from '@mindtickle/utils';

import {
  sendUploadSessionSampleDownloadEvent,
  sendUploadSessionUploadedEvent,
  sendUploadSessionUploadClickedEvent,
  sendUploadSessionCreatedEvent,
} from '~/modules/Admin/components/SessionsWrapper/utils/trackEvents';
import { MAX_SESSIONS_WITHIN_EVENT } from '~/modules/Admin/constants/events';
import { INTEGRATION_SOURCE } from '~/modules/Admin/constants/sessions';
import { useILTAdminSnowplowTracker } from '~/modules/Admin/hooks/useILTAdminSnowplowTracker';
import { checkIntegrationUserAuthEnabled } from '~/modules/Admin/utils/sessionEdit';

import ContentUploader from '../../../ContentUploader';
import CONTENT_UPLOADER_TEXTS from '../../../ContentUploader/messages';

import SessionsModalTable from './components/SessionsModalTable';
import UploadActions from './components/UploadActions';
import { StyledModal, StyledModalTitle, StyledTitleContainer } from './styles';
import {
  convertToSessionsMap,
  downloadSample,
  preProcessUploadedSessions,
  addTimezoneNotAvailableError,
  getTimezoneInfo,
} from './utils';

import type { TTimezoneInfoMapper, TUploadSessions } from './typeDefs';
const ModalTitle = ({
  downloadSample,
  eventName,
}: {
  downloadSample: () => void;
  eventName?: string;
}) => (
  <StyledModalTitle>
    <StyledTitleContainer>
      <div className="title">{'Upload sessions'}</div>
      <Button type="secondary" onClick={downloadSample}>
        {'Download sample'}
      </Button>
    </StyledTitleContainer>

    {eventName && (
      <EllipsisTooltip
        wrapperClassName="event-title"
        placement="bottom"
        title={eventName}
        showTooltipWhenEllipsis={true}
      >
        {'Event: ' + eventName}
      </EllipsisTooltip>
    )}
  </StyledModalTitle>
);

const ModalInfoContent = () => (
  <>
    <div className="type-text">
      <FormattedMessage {...CONTENT_UPLOADER_TEXTS.DEFAULT_SUPPORTED_TYPES} />
    </div>
    <div className="advice-text">
      <span className="note">{'Note: '}</span>
      <FormattedMessage {...CONTENT_UPLOADER_TEXTS.DEFAULT_ADVICE} />
    </div>
  </>
);

const UploadSessions = (props: TUploadSessions) => {
  const {
    onClose,
    uploadSessions,
    defaultCompletionCriteria,
    integrations: { data: integrations = {} },
    trackerPageName,
    eventData,
    sessionsList = [],
    moduleDetails,
  } = props;
  const isWebexUserAuthEnabled = checkIntegrationUserAuthEnabled(
    integrations,
    INTEGRATION_SOURCE.webex
  );
  const isMSTeamsUserAuthEnabled = checkIntegrationUserAuthEnabled(
    integrations,
    INTEGRATION_SOURCE.ms_teams
  );
  const isUserAuthEnabled = isWebexUserAuthEnabled || isMSTeamsUserAuthEnabled;
  const [sessions, setSessions] = useState<any[]>([]);
  const [timezoneInfoMapper, setTimezoneInfoMapper] = useState<TTimezoneInfoMapper>([]);
  const isReupload = useRef(false); // this is used for tracking event purpose
  const [shouldRenderSessions, setShouldRenderSessions] = useState<boolean>(false);
  const [shouldRenderOlderTemplateError, setShouldRenderOlderTemplateError] = useState(false);
  const tracker = useILTAdminSnowplowTracker();
  const uploadLimit = MAX_SESSIONS_WITHIN_EVENT - sessionsList.length;

  const user = useUserAuth();

  const userEmailAddress = user.primaryEmail;

  const renderSessions = async (data: any) => {
    if (!isUndefined(data)) {
      const { allSessions: convertedSessions, err } = convertToSessionsMap({
        sessions: Object.values(data)[0],
        intl: props.intl,
        isUserAuthEnabled: isUserAuthEnabled,
        eventData,
      });
      if (err && isUserAuthEnabled) {
        setShouldRenderOlderTemplateError(true);
        setShouldRenderSessions(true);
        return;
      }
      const timezoneInfoMapper = await getTimezoneInfo(convertedSessions);
      const convertedSessionsWithTimezoneCheck = addTimezoneNotAvailableError(convertedSessions, {
        timezoneInfoMapper: timezoneInfoMapper,
      });

      setSessions(convertedSessionsWithTimezoneCheck);
      setTimezoneInfoMapper(timezoneInfoMapper);
      setShouldRenderSessions(true);
      const sessionsWithErrorCount = convertedSessionsWithTimezoneCheck.reduce(
        (acc: number, item: any) => (item.validationErrors ? acc + 1 : acc),
        0
      );
      sendUploadSessionUploadedEvent(tracker, {
        pageName: trackerPageName || '',
        sessionsCount: convertedSessionsWithTimezoneCheck.length,
        sessionsWithErrorCount,
        isReupload: isReupload.current,
      });
    }
  };

  const onReUpload = () => {
    setSessions([]);
    setShouldRenderSessions(false);
    setShouldRenderOlderTemplateError(false);
    isReupload.current = true;
  };

  const processUpload = (sessions = []) => {
    let filteredCorrectSessions = sessions.filter((session: any) => !session.validationErrors);
    let processedSessions = preProcessUploadedSessions(
      filteredCorrectSessions,
      {
        integrations,
        defaultCompletionCriteria,
        userEmail: userEmailAddress,
        isUserAuthEnabled: isUserAuthEnabled,
      },
      eventData,
      moduleDetails
    );
    uploadSessions(processedSessions, eventData?.id);
    const sessionsWithErrorCount = sessions.reduce(
      (acc: number, item: any) => (item.validationErrors ? acc + 1 : acc),
      0
    );
    sendUploadSessionCreatedEvent(tracker, {
      pageName: trackerPageName || '',
      sessionsCount: sessions.length,
      sessionsWithErrorCount,
    });
  };

  const onSessionRemove = (rowIndex: number) => {
    const updatedSessions = sessions.filter((session: any, index: number) => index !== rowIndex);
    setSessions(updatedSessions);
  };

  const handleDownloadSample = () => {
    downloadSample(isUserAuthEnabled);
    sendUploadSessionSampleDownloadEvent(tracker, { pageName: trackerPageName || '' });
  };

  return (
    <StyledModal
      visible={true}
      onCancel={onClose}
      destroyOnClose
      zIndex={9980}
      title={<ModalTitle downloadSample={handleDownloadSample} eventName={eventData.name} />}
      maskClosable={false}
      width={shouldRenderSessions ? 900 : 700}
      bodyStyle={{
        height: 516,
        ...(shouldRenderSessions ? { padding: '0px 32px' } : { paddingBottom: '32px' }),
      }}
      footer={
        shouldRenderSessions && (
          <UploadActions
            onClose={onClose}
            onReUpload={onReUpload}
            sessions={sessions}
            setSessions={setSessions}
            processUpload={processUpload}
            timezoneInfoMapper={timezoneInfoMapper}
            shouldRenderOlderTemplateError={shouldRenderOlderTemplateError}
            uploadLimit={uploadLimit}
            eventId={eventData.id}
          />
        )
      }
    >
      {!shouldRenderSessions ? (
        <ContentUploader
          callback={renderSessions}
          parserOptions={{ raw: false }}
          UploadModalInfoContainer={ModalInfoContent()}
          uploaderTexts={{
            dropFileText: <FormattedMessage {...CONTENT_UPLOADER_TEXTS.DROP_FILE_SESSIONS} />,
          }}
          beforeUploadTrackingEventCallback={() => {
            sendUploadSessionUploadClickedEvent(tracker, {
              pageName: trackerPageName || '',
              isReupload: isReupload.current,
            });
          }}
        />
      ) : (
        <SessionsModalTable
          sessionsData={sessions}
          onSessionRemove={onSessionRemove}
          shouldRenderOlderTemplateError={shouldRenderOlderTemplateError}
          uploadLimit={uploadLimit}
          eventId={eventData.id}
        />
      )}
    </StyledModal>
  );
};

export default injectIntl(UploadSessions);
