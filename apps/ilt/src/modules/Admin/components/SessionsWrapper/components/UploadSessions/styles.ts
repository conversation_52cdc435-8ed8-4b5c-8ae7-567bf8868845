import styled from 'styled-components';

import Modal from '@mindtickle/modal';
import { mixins, theme } from '@mindtickle/styles/lib';

export const StyledModalTitle = styled.div`
  display: flex;
  flex-direction: column;
  padding: 2px 2px;

  .title {
    ${mixins.fontStack({
      fontWeight: theme.fontWeight.SEMIBOLD,
      fontSize: theme.fontSizes.MEDIUM_NUM,
      lineHeight: theme.lineHeight.MEDIUM_NUM,
    })}
    margin-right: 10px;
  }
  .event-title {
    margin-right: 10px;
    margin-top: 4px;
    ${mixins.smallBlackLink()}
  }
`;

export const StyledModal = styled(Modal)``;

export const StyledTitleContainer = styled.div`
  display: flex;
  align-items: center;
`;
