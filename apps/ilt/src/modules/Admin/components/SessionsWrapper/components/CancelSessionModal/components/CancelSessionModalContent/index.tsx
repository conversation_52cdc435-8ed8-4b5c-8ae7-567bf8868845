import { useState } from 'react';

import { injectIntl, FormattedMessage } from 'react-intl';

import Checkbox from '@mindtickle/checkbox';
import Form from '@mindtickle/form';
import Icon, { ICON_MAP } from '@mindtickle/icon';
import TextArea from '@mindtickle/textarea';

import type { TILT_ENTITIES } from '~/modules/Admin/components/SessionsWrapper/typeDefs';
import { LEARNER_CALENDAR_AUTO_SYNC_INFO, MODES } from '~/modules/Admin/config/sessions.constants';
import { checkIsEvent } from '~/modules/Admin/utils/checkEntityType';

import messages from '../../messages';
import { TCancelSessionModalContent } from '../../typeDefs';

import { StyledAutoSyncMessageContainer, StyledCancelSessionModalContainer } from './styles';

import type { CheckboxChangeEvent } from 'antd/es/checkbox';
import type { InjectedIntlProps } from 'react-intl';

const CancelSessionModalContent = (props: TCancelSessionModalContent & InjectedIntlProps) => {
  const { handleModeOperation, cancelSessionForm, clearMode, intl } = props;
  const [notifyLearners, setNotifyLearners] = useState('true');
  const [cancelReason, setCancelReason] = useState('');
  const isCalendarAutoSyncEnabled = props.isILTSessionCalendarAutoSyncEnabled ?? true;

  const renderConfirmationText = (name: string, entityType: TILT_ENTITIES) => (
    <div className="message-container">
      {checkIsEvent(entityType) ? (
        <FormattedMessage {...messages.CANCEL_EVENT_CONFIRM_TEXT} />
      ) : (
        <FormattedMessage {...messages.CANCEL_CONFIRM_TEXT} values={{ name: <q>{name}</q> }} />
      )}
    </div>
  );

  const renderCancelBox = () => {
    const maxTextLength = 200;
    const textAreaProps = {
      maxLength: maxTextLength,
      maxlength: maxTextLength, // hack as per antd doc maxLength should work but not working
      value: cancelReason || '',
      onChange: (event: any) => setCancelReason(event.target.value),
      className: 'reasonText',
      maxLengthClassName: 'maxLength',
      showMaxLength: true,
      placeholder: intl.formatMessage(messages.ENTER_REASON_PLACEHOLDER),
      //   required: showValidation ? true : false,
    };

    return (
      <div className="inputContainer">
        <Form.Item
          name={'cancelReason'}
          label={<FormattedMessage {...messages.CANCEL_REASON_TEXT_BOX_LABEL} />}
          labelCol={{ span: 24 }}
          wrapperCol={{ span: 24 }}
          rules={[
            { required: true, message: <FormattedMessage {...messages.ERROR_EMPTY_REASON} /> },
          ]}
        >
          <TextArea {...textAreaProps} />
        </Form.Item>
      </div>
    );
  };

  const renderCancelSection = () => {
    const mailToLearners = notifyLearners === 'true';
    return (
      <div className="check-container">
        <span>
          <FormattedMessage {...messages.DISCLAIMER_NOTIFICATION_INSTRUCTOR} />
        </span>
        <div>
          <Checkbox
            onChange={(event: CheckboxChangeEvent) =>
              setNotifyLearners(event.target.checked.toString())
            }
            checked={mailToLearners}
          >
            <span className={'learnerText' + notifyLearners}>
              <FormattedMessage {...messages.LABEL_CHECKBOX_NOTIFY_LEARNERS} />
            </span>
          </Checkbox>
          {!mailToLearners && isCalendarAutoSyncEnabled && (
            <StyledAutoSyncMessageContainer>
              <span className={'infoContainerText'}>
                <Icon type={ICON_MAP.WARNING} className={'warningIcon'} />
                <FormattedMessage {...LEARNER_CALENDAR_AUTO_SYNC_INFO} />
              </span>
            </StyledAutoSyncMessageContainer>
          )}
        </div>
      </div>
    );
  };
  return (
    <StyledCancelSessionModalContainer>
      <Form
        name="cancelSessionForm"
        className={'cancel-modal-form'}
        onFinish={(values: any) => {
          const updatedData = Object.assign({}, props.data);
          updatedData.isCancelled = true;
          updatedData.cancellationReason = cancelReason;
          handleModeOperation({
            mode: MODES.CANCEL,
            data: updatedData,
            notifyLearnersOnCancellation: notifyLearners,
            selectedEntity: { parentId: updatedData.parentId, entityType: updatedData.entityType },
          });
          clearMode();
        }}
        initialValues={{
          reason: '',
          notifyLearners: true,
        }}
        form={cancelSessionForm}
      >
        {renderConfirmationText(props.data['name'], props.data['entityType'])}
        {renderCancelBox()}
        {renderCancelSection()}
      </Form>
    </StyledCancelSessionModalContainer>
  );
};

export default injectIntl(CancelSessionModalContent);
