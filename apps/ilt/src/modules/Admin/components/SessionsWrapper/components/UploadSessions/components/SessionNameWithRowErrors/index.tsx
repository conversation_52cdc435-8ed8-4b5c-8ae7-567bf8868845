import EllipsisTooltip from '@mindtickle/ellipsis-tooltip';
import Icon, { ICON_MAP } from '@mindtickle/icon';
import Tooltip from '@mindtickle/tooltip';

import { ellipsisTooltipCommonProps } from '../SessionsModalTable/constants';

import { StyledErrorContainer, StyledSessionNameWithAllErrorsContainer } from './styles';

import type { TSessionNameWithAllErrors } from '../../typeDefs';

const SessionNameWithRowErrors = (props: TSessionNameWithAllErrors) => {
  const { name, errors } = props;
  const errorMessages = Object.values(errors);
  const errorKeys = Object.keys(errors);

  return (
    <StyledSessionNameWithAllErrorsContainer className="session-name">
      <EllipsisTooltip
        wrapperClassName="session-name-tooltip"
        title={name}
        {...ellipsisTooltipCommonProps}
      >
        {name}
      </EllipsisTooltip>
      {errorMessages.length !== 0 && (
        <StyledErrorContainer>
          {errorMessages.length === 1 ? (
            <EllipsisTooltip
              wrapperClassName="session-name-error-tooltip"
              title={errorMessages[0]}
              {...ellipsisTooltipCommonProps}
            >
              {errorMessages[0]}
            </EllipsisTooltip>
          ) : (
            <Tooltip
              overlayClassName={'tooltipInfo'}
              title={
                <div>
                  {errorMessages.map((error, index) => (
                    <div key={errorKeys[index]}>{error}</div>
                  ))}
                </div>
              }
            >
              <div className="error-message">
                <span>Multiple errors</span>
                <Icon type={ICON_MAP.INFO2} className={'error-icon'} />
              </div>
            </Tooltip>
          )}
        </StyledErrorContainer>
      )}
    </StyledSessionNameWithAllErrorsContainer>
  );
};

export default SessionNameWithRowErrors;
