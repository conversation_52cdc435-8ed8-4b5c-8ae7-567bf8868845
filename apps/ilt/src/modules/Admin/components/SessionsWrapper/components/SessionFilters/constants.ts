import debounce from 'lodash/debounce';
import moment from 'moment';

import {
  DATE_FILTER_OPTIONS,
  MAP_SESSION_TYPE_TO_DISPLAY_VALUE,
  SESSION_LIST_FILTER_PLACEHOLDERS,
  SESSION_LIST_FILTER_TITLES,
  SESSION_STATE_DD_OPTIONS,
  ENTITY_TYPE_DD_OPTIONS,
  SESSION_STATUS_DD_OPTIONS,
  SESSION_TYPES_FILTER_DD_OPTIONS,
  SESSION_TYPES_DD_OPTIONS_WITHOUT_HYBRID,
  SUPPORTED_FILTERS,
} from '~/modules/Admin/config/sessions.constants';
import MESSAGE_ENTITY_TYPE from '~/modules/Admin/messages/sessions/entityType';
import SESSION_STATES from '~/modules/Admin/messages/sessions/sessionStates';
import { getDatesInEpochForDateFilter } from '~/utils';

import { COMPONENT_TYPES } from '../MultiFilterPanel/constants';

import type { TGetFilterDetails } from './typeDefs';
import type { InjectedIntlProps } from 'react-intl';

export const getEntityTypeDetails = ({
  onFilterSelect,
  ILTFilters,
  intl,
}: TGetFilterDetails & InjectedIntlProps) => ({
  type: COMPONENT_TYPES.SELECT_WITH_SEARCH,
  key: SUPPORTED_FILTERS.ENTITY_TYPE,
  props: {
    onChange: (data: any) => {
      onFilterSelect({
        filterType: SUPPORTED_FILTERS.ENTITY_TYPE,
        data: data.value === 'None' ? 'ALL' : data.value,
      });
    },
    isMulti: false,
    placeholder: SESSION_LIST_FILTER_TITLES.ENTITY_TYPE,
    showSearch: false,
    options: ENTITY_TYPE_DD_OPTIONS,
    value: ILTFilters[SUPPORTED_FILTERS.ENTITY_TYPE as keyof typeof ILTFilters] || 'ALL',
    noneLabel: intl.formatMessage(MESSAGE_ENTITY_TYPE.ALL),
    style: { container: (base: any) => ({ ...base, width: '270px' }) },
  },
});

export const getSessionStateDetails = ({
  onFilterSelect,
  ILTFilters,
  intl,
}: TGetFilterDetails & InjectedIntlProps) => ({
  type: COMPONENT_TYPES.SELECT_WITH_SEARCH,
  key: SUPPORTED_FILTERS.SESSION_STATE,
  props: {
    onChange: (data: any) => {
      onFilterSelect({
        filterType: SUPPORTED_FILTERS.SESSION_STATE,
        data: data.value === 'None' ? 'ALL' : data.value,
      });
    },
    isMulti: false,
    placeholder: SESSION_LIST_FILTER_TITLES.SESSION_STATE,
    showSearch: false,
    options: SESSION_STATE_DD_OPTIONS,
    value: ILTFilters[SUPPORTED_FILTERS.SESSION_STATE as keyof typeof ILTFilters] || 'ALL',
    noneLabel: intl.formatMessage(SESSION_STATES.ALL),
  },
});

export const getSessionStatusDetails = ({ onFilterSelect, ILTFilters }: TGetFilterDetails) => ({
  type: COMPONENT_TYPES.DROPDOWN,
  key: SUPPORTED_FILTERS.SESSION_STATUS,
  props: {
    onChange: (data: any[]) => {
      onFilterSelect({
        filterType: SUPPORTED_FILTERS.SESSION_STATUS,
        data: data.map(status => status.value),
      });
    },
    isMulti: true,
    buttonLabel: SESSION_LIST_FILTER_TITLES.SESSION_STATUS,
    showSearch: false,
    options: SESSION_STATUS_DD_OPTIONS,
    defaultTitle: SESSION_LIST_FILTER_TITLES.SESSION_STATUS,
    value: ILTFilters[SUPPORTED_FILTERS.SESSION_STATUS as keyof typeof ILTFilters],
  },
});

export const getDateRangeDetails = ({ onFilterSelect, ILTFilters }: TGetFilterDetails) => {
  const isDateRangeSelected =
    !!ILTFilters[SUPPORTED_FILTERS.ILT_DATE_RANGE_DD as keyof typeof ILTFilters]?.length;
  const datesInMomentFormat = [
    moment(ILTFilters[SUPPORTED_FILTERS.ILT_DATE_RANGE_DD as keyof typeof ILTFilters]?.[0]),
    moment(ILTFilters[SUPPORTED_FILTERS.ILT_DATE_RANGE_DD as keyof typeof ILTFilters]?.[1]),
  ];

  return {
    type: COMPONENT_TYPES.DATE_RANGE_DD,
    key: SUPPORTED_FILTERS.ILT_DATE_RANGE_DD,
    props: {
      placeholder: SESSION_LIST_FILTER_PLACEHOLDERS.DATE_RANGE,
      onChange: (from: any, to: any) => {
        const fromDate = from ? from._d : null;
        const toDate = to ? to._d : null;
        onFilterSelect({
          filterType: SUPPORTED_FILTERS.ILT_DATE_RANGE_DD,
          data: getDatesInEpochForDateFilter(fromDate, toDate),
        });
      },
      value: isDateRangeSelected ? datesInMomentFormat : [],
      options: Object.keys(DATE_FILTER_OPTIONS).map(v => {
        let option = DATE_FILTER_OPTIONS[v as keyof typeof DATE_FILTER_OPTIONS];
        return option;
      }),
      enableFutureDateSelection: true,
      extraDropdownProps: {
        menuStyle: {
          width: '240px',
        },
      },
    },
  };
};

export const getSessionTypeDetails = ({
  onFilterSelect,
  ILTFilters,
  intl,
  isMultidayEnabled,
}: TGetFilterDetails & InjectedIntlProps) => ({
  type: COMPONENT_TYPES.SELECT_WITH_SEARCH,
  key: SUPPORTED_FILTERS.SESSION_TYPE,
  props: {
    onChange: (data: any) => {
      onFilterSelect({
        filterType: SUPPORTED_FILTERS.SESSION_TYPE,
        data: data.value === 'None' ? 'ALL' : data.value,
      });
    },
    isMulti: false,
    placeholder: SESSION_LIST_FILTER_TITLES.SESSION_TYPE,
    showSearch: false,
    options: isMultidayEnabled
      ? SESSION_TYPES_FILTER_DD_OPTIONS
      : SESSION_TYPES_DD_OPTIONS_WITHOUT_HYBRID,
    value: ILTFilters[SUPPORTED_FILTERS.SESSION_TYPE as keyof typeof ILTFilters] || 'ALL',
    noneLabel: intl.formatMessage(MAP_SESSION_TYPE_TO_DISPLAY_VALUE.ALL),
  },
});

export const getSearchFilterDetails = ({
  onFilterSelect,
  ILTFilters,
  isMultidayEnabled,
}: TGetFilterDetails) => ({
  type: COMPONENT_TYPES.SEARCH_WITH_TOOLTIP,
  key: SUPPORTED_FILTERS.SEARCH,
  props: {
    placeholder: SESSION_LIST_FILTER_PLACEHOLDERS.SEARCH_SESSIONS,
    tooltipText: `Search by ${
      isMultidayEnabled ? 'event or ' : ''
    }session name, instructor email ID or name`,
    onSearch: debounce(
      (query: string) =>
        onFilterSelect({
          filterType: SUPPORTED_FILTERS.SEARCH,
          data: query,
        }),
      200
    ),
    value: ILTFilters[SUPPORTED_FILTERS.SEARCH as keyof typeof ILTFilters] || '',
  },
});
