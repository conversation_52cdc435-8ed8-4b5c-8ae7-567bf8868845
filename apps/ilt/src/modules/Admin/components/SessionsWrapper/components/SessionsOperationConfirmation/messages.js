import { defineMessages } from 'react-intl';

export default defineMessages({
  TITLE_DELETE_ALL_SESSIONS_OF_EVENT: 'Delete all sessions in this event?',
  CONTENT_DELETE_ALL_SESSIONS_OF_EVENT:
    "You have selected all sessions in the event to be deleted. Once you delete these sessions, it can't be undone.",
  TITLE_DELETE_SESSIONS_EVENTS:
    'Delete {eventCount} {eventLabel} and {sessionCount} {sessionLabel}?',
  CONTENT_DELETE_SESSIONS_EVENTS:
    "You have selected {eventCount} {eventLabel} and {sessionCount} {sessionLabel} to be deleted. Once you delete these {eventLabel} and {sessionLabel}, it can't be undone.",
  TITLE_DELETE_SESSIONS: 'Delete {sessionCount} sessions?',
  CONTENT_DELETE_SESSIONS: 'You have selected {sessionCount} sessions to be deleted.',
  TITLE_DELETE_SINGLE_SESSION: 'Delete session?',
  CONTENT_DELETE_SINGLE_SESSION: `Once you delete the selected session, it can't be undone.`,
  CONTENT_DELETE_SINGLE_SESSION_PART2: `Are you sure you want to continue?`,
  TITLE_DELETE_EVENTS: 'Delete {eventCount} events?',
  CONTENT_DELETE_EVENTS: 'You have selected {eventCount} events to be deleted.',
  TITLE_DELETE_SINGLE_EVENT: 'Delete event?',
  CONTENT_DELETE_SINGLE_EVENT: `Once you delete this event, all sessions in it will also be deleted and it can't be undone.`,
  DELETE_CONFIRM_OK_BUTTON_TEXT: 'Yes, delete',
});
