import Button from '@mindtickle/button';

import { checkTimezoneAvailable } from '../../utils';

import { StyledModalFooter } from './styles';

import type { TUploadActions } from '../../typeDefs';

const UploadActions = (props: TUploadActions) => {
  const {
    sessions,
    setSessions,
    onClose,
    onReUpload,
    processUpload,
    timezoneInfoMapper,
    uploadLimit,
    eventId,
  } = props;
  const filteredCorrectSessions = sessions.filter((session: any) => !session.validationErrors);
  const isUploadLimitExceeded = eventId && filteredCorrectSessions.length > uploadLimit;

  const updateTimezones = () => {
    const newSessions = sessions.map((session: any) => {
      session['timezoneObject'] = checkTimezoneAvailable(timezoneInfoMapper, session);
      return session;
    });
    setSessions(newSessions);
  };

  const updateAll = () => {
    updateTimezones();
    processUpload(sessions);
    onClose();
  };

  if (props.shouldRenderOlderTemplateError) {
    return (
      <StyledModalFooter>
        <Button type="text" onClick={onClose}>
          {'Cancel'}
        </Button>
        <Button type="primary" onClick={onReUpload}>
          {'Reupload'}
        </Button>
      </StyledModalFooter>
    );
  }

  return (
    <StyledModalFooter>
      <Button type="text" onClick={onClose}>
        {'Cancel'}
      </Button>
      <Button type="secondary" onClick={onReUpload}>
        {'Upload again'}
      </Button>
      <Button
        type="primary"
        disabled={!filteredCorrectSessions.length || isUploadLimitExceeded}
        onClick={updateAll}
      >
        {`Create ${
          filteredCorrectSessions.length > 1
            ? filteredCorrectSessions.length + ' sessions'
            : 'session'
        }`}
      </Button>
    </StyledModalFooter>
  );
};

export default UploadActions;
