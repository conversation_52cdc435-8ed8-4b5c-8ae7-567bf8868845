import { useMemo } from 'react';

import { injectIntl } from 'react-intl';

import Button from '@mindtickle/button';

import MultiFilterPanel from '~/modules/Admin/components/SessionsWrapper/components/MultiFilterPanel';
import { OPERATIONS } from '~/modules/Admin/config/sessions.constants';
import { useILTAdminSnowplowTracker } from '~/modules/Admin/hooks/useILTAdminSnowplowTracker';
import useMultidayEnabled from '~/modules/Admin/hooks/useMultidayEnabled';
import { prepareFilters } from '~/utils';

import { sendResetFilterClicked } from '../../utils/trackEvents';

import {
  getDateRangeDetails,
  getSearchFilterDetails,
  getEntityTypeDetails,
  getSessionStateDetails,
  getSessionStatusDetails,
  getSessionTypeDetails,
} from './constants';
import { StyledSessionFiltersContainer } from './styles';

import type { TSessionFilters } from './typeDefs';
import type { InjectedIntlProps } from 'react-intl';

const { SEARCH } = OPERATIONS;

const SessionFilters = (props: TSessionFilters & InjectedIntlProps) => {
  const {
    ILTFilters,
    updateILTFilters,
    setResetClicked,
    manipulateData,
    haveBuildAccess,
    onFilterSelect,
    trackerPageName,
    intl,
  } = props;
  const tracker = useILTAdminSnowplowTracker();
  const { isMultidayEnabled } = useMultidayEnabled();
  const filtersConfig = useMemo(() => {
    const filters = [];

    if (haveBuildAccess) {
      if (isMultidayEnabled) {
        filters.push(getEntityTypeDetails({ onFilterSelect, ILTFilters, intl }));
      }
      filters.push(
        getSessionStatusDetails({ onFilterSelect, ILTFilters }),
        getSessionStateDetails({ onFilterSelect, ILTFilters, intl }),
        getDateRangeDetails({ onFilterSelect, ILTFilters }),
        getSessionTypeDetails({ onFilterSelect, ILTFilters, intl, isMultidayEnabled })
      );
    }
    filters.push(getSearchFilterDetails({ onFilterSelect, ILTFilters, isMultidayEnabled }));
    return filters;
    // eslint-disable-next-line react-hooks/exhaustive-deps -- intl shouldn't affect this
  }, [ILTFilters, haveBuildAccess, onFilterSelect]);

  const isAnyFilterSet = (filters: any) => {
    let count = 0;
    Object.keys(filters).forEach(key => {
      if (filters[key]) count++;
    });
    return count;
  };

  const clearALLFilters = () => {
    updateILTFilters?.({});
    setResetClicked(true);
    manipulateData({
      operation: SEARCH,
      filters: prepareFilters({}),
    });
    sendResetFilterClicked(tracker, { pageName: trackerPageName });
  };

  const showResetFilters = () => {
    if (isAnyFilterSet(ILTFilters)) {
      return (
        <Button type="text" className={'reset-filters'} onClick={clearALLFilters}>
          <span>Reset filters</span>
        </Button>
      );
    }
  };
  return (
    <StyledSessionFiltersContainer>
      <MultiFilterPanel filters={filtersConfig} className={'multi-filter-panel'} />
      {showResetFilters()}
    </StyledSessionFiltersContainer>
  );
};

export default injectIntl(SessionFilters);
