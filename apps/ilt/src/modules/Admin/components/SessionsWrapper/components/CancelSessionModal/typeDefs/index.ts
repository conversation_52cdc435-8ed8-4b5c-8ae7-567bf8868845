export interface TCancelSessionModal {
  selectedCount?: number;
  sessionsMap: any;
  inProgress: boolean;
  isILTSessionCalendarAutoSyncEnabled?: boolean;
  selectedId?: string;
  handleModeOperation: Function;
  clearMode: Function;
}

export interface TModalFooter {
  onCancel?: Function;
  onOk?: Function;
}

export interface TCancelSessionModalContent {
  data: any;
  inProgress: boolean;
  id?: string;
  isILTSessionCalendarAutoSyncEnabled?: boolean;
  cancelSessionForm: any;
  handleModeOperation: Function;
  clearMode: Function;
}
