import { Fragment } from 'react';

import { FormattedMessage } from 'react-intl';

import messages from '../messages';

import type { TSelectedCountInfo } from '../../../typeDefs';

export default function getDeleteConfirmationMessages(countInfo: TSelectedCountInfo) {
  const {
    sessionCount,
    allSessionsOfEventAreSelected,
    sessionsWithinEventCount,
    eventCount,
    allSessionCount: sessionMergeCount,
  } = countInfo;
  const isMoreSessions = sessionMergeCount > 1;
  const isMoreEvents = eventCount > 1;
  const sessionLabel = isMoreSessions ? 'sessions' : 'session';
  const eventLabel = isMoreEvents ? 'events' : 'event';
  let contentKey = 'content',
    titleKey = 'title';
  const [content, title] = allSessionsOfEventAreSelected
    ? [
        <FormattedMessage key={contentKey} {...messages.CONTENT_DELETE_ALL_SESSIONS_OF_EVENT} />,
        <FormattedMessage key={titleKey} {...messages.TITLE_DELETE_ALL_SESSIONS_OF_EVENT} />,
      ]
    : sessionMergeCount && eventCount
    ? [
        <FormattedMessage
          key={contentKey}
          {...messages.CONTENT_DELETE_SESSIONS_EVENTS}
          values={{
            sessionCount: sessionCount + sessionsWithinEventCount,
            sessionLabel,
            eventLabel,
            eventCount,
          }}
        />,
        <FormattedMessage
          key={titleKey}
          {...messages.TITLE_DELETE_SESSIONS_EVENTS}
          values={{
            sessionCount: sessionCount + sessionsWithinEventCount,
            sessionLabel,
            eventLabel,
            eventCount,
          }}
        />,
      ]
    : eventCount
    ? isMoreEvents
      ? [
          <FormattedMessage
            key={contentKey}
            {...messages.CONTENT_DELETE_EVENTS}
            values={{
              eventCount,
            }}
          />,
          <FormattedMessage
            key={titleKey}
            {...messages.TITLE_DELETE_EVENTS}
            values={{
              eventCount,
            }}
          />,
        ]
      : [
          <FormattedMessage key={contentKey} {...messages.CONTENT_DELETE_SINGLE_EVENT} />,
          <FormattedMessage key={titleKey} {...messages.TITLE_DELETE_SINGLE_EVENT} />,
        ]
    : isMoreSessions
    ? [
        <FormattedMessage
          key={contentKey}
          {...messages.CONTENT_DELETE_SESSIONS}
          values={{
            sessionCount: sessionMergeCount,
          }}
        />,
        <FormattedMessage
          key={titleKey}
          {...messages.TITLE_DELETE_SESSIONS}
          values={{
            sessionCount: sessionMergeCount,
          }}
        />,
      ]
    : [
        <Fragment key={contentKey}>
          <FormattedMessage {...messages.CONTENT_DELETE_SINGLE_SESSION} />
          <br />
          <br />
          <FormattedMessage {...messages.CONTENT_DELETE_SINGLE_SESSION_PART2} />
        </Fragment>,
        <FormattedMessage key={titleKey} {...messages.TITLE_DELETE_SINGLE_SESSION} />,
      ];
  return {
    content: <div>{content}</div>,
    title,
    okButtonText: <FormattedMessage key={contentKey} {...messages.DELETE_CONFIRM_OK_BUTTON_TEXT} />,
  };
}
