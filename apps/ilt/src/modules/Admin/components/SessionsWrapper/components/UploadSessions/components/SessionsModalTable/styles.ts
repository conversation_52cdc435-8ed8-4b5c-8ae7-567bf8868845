import styled from 'styled-components';

import { mixins, tokens } from '@mindtickle/styles/lib';
import Table from '@mindtickle/table';

import { THEME_PREFIX_CLS } from '~/config/constants';

export const StyledUploadSessionsTable = styled(Table)`
  margin-top: 24px;

  .${THEME_PREFIX_CLS}-table table {
    border-spacing: 0 !important;
  }
  .${THEME_PREFIX_CLS}-table-tbody > tr > td {
    border-bottom: 1px solid ${tokens.borderTokens.COLOR_BORDER_SECONDARY};
    border-radius: 0 !important;
  }
  .${THEME_PREFIX_CLS}-table-tbody > tr > td:first-child {
    padding-left: 0px;
  }
  .${THEME_PREFIX_CLS}-table-tbody > tr > td:last-child {
    padding-left: 3px;
  }
  .${THEME_PREFIX_CLS}-table-thead > tr > th:first-child {
    padding-left: 0px;
  }

  td.${THEME_PREFIX_CLS}-table-cell {
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: ${tokens.textTokens.COLOR_TEXT_DEFAULT};
  }
  th.${THEME_PREFIX_CLS}-table-cell {
    color: ${tokens.textTokens.COLOR_TEXT_SECONDARY};
  }

  && .${THEME_PREFIX_CLS}-table-container::before, .${THEME_PREFIX_CLS}-table-container::after {
    box-shadow: none;
    width: 0;
  }
`;

export const StyledErrorMessageContainer = styled.div`
  background-color: ${tokens.bgTokens.COLOR_BG_DANGER};
  border-radius: 4px;
  width: fit-content;
  padding: 8px 16px;
  border-radius: 8px;
  margin-top: 24px;

  & .info-container-text {
    ${mixins.smallBlackLink()}
    display: flex;
    align-items: center;
  }

  & .warning-icon {
    color: ${tokens.iconTokens.COLOR_ICON_DANGER};
    margin-right: 9px;
  }

  & .note-heading {
    ${mixins.activeBlackLink()}
    margin-right: 3px;
  }
`;
