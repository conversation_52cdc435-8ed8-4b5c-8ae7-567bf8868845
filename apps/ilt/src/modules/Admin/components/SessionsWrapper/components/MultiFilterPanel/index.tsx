import DateFilter from '@mindtickle/date-filter';
import Search from '@mindtickle/search';
import Dropdown from '@mindtickle/select-with-search';

import { defaultDateFormatter as defaultFormatter } from '~/utils';

import { COMPONENT_TYPES } from './constants';
import { StyledMultiFilterPanelContainer, StyledToolTip } from './styles';

import type { TMultiFilterPanel } from './typeDefs';

const MultiFilterPanel = (props: TMultiFilterPanel) => {
  const { filters, className } = props;
  const renderFilter = (
    { type, key, props }: { type: string; key: string; props: any },
    index: number
  ) => {
    switch (type) {
      case COMPONENT_TYPES.SEARCH:
        return <Search key={key || `filterSearch${index}`} {...props} />;
      case COMPONENT_TYPES.SEARCH_WITH_TOOLTIP:
        const { tooltipText, ...rest } = props;
        return (
          <StyledToolTip
            key={key || `filterSearchTooltip${index}`}
            overlayStyle={{ zIndex: 999999 }}
            title={tooltipText}
            placement={'bottomLeft'}
            trigger={'hover'}
          >
            <Search key={`filterSearch${index}`} {...rest} />
          </StyledToolTip>
        );
      case COMPONENT_TYPES.DROPDOWN:
        return (
          <Dropdown
            key={key || `filterDropdown${index}`}
            id={'filterOptionsPanelDrpdown-ds'}
            className={'filter-block-styling'}
            onChange={props.onChange}
            isMulti={true}
            showSearch={true}
            isButton
            sortOptions={false}
            hasNone={false}
            {...props}
          />
        );
      case COMPONENT_TYPES.SELECT_WITH_SEARCH:
        return (
          <Dropdown
            key={key || `filterSelectWithSearch${index}`}
            buttonLabel={props.placeholder}
            onChange={props.onChange}
            isMulti={false}
            showSearch={false}
            noneLabel={props.noneLabel}
            placeholder={props.placeholder}
            isButton={true}
            options={props.options}
            value={props.value}
            className={'filter-block-styling'}
            style={props.style}
          />
        );
      case COMPONENT_TYPES.DATE_RANGE_DD:
        return (
          <DateFilter
            key={key || `date_filter_${index}`}
            placeholder={props.placeholder}
            className={'filter-block-styling'}
            onChange={props.onChange}
            options={props.options}
            dateFormatter={defaultFormatter}
            value={props.value}
            enableFutureDateSelection={props.enableFutureDateSelection}
            extraDropdownProps={props.extraDropdownProps}
          />
        );
      default:
        break;
    }
  };

  return (
    <StyledMultiFilterPanelContainer className={className}>
      {filters.map(renderFilter)}
    </StyledMultiFilterPanelContainer>
  );
};

export default MultiFilterPanel;
