import union from 'lodash/union';
import moment from 'moment';
import momentTimezone from 'moment-timezone';

import { isValidEmail } from '@mindtickle/utils';

import TimezonesService from '~/modules/Admin/api/timezonesService';
import {
  COMPLETION_CRITERIA_ENUM,
  ENROLLMENT_FREEZE_STATUSES,
  LOCATION_TYPE,
  MAX_SESSION_NAME_LENGTH,
  MESSAGES,
  SESSION_TYPES,
  WEBINAR_SOURCE,
} from '~/modules/Admin/config/sessions.constants';
import { EVENT_ENROLLMENT_ENUM } from '~/modules/Admin/constants/events';
import { ILT_ENTITIES } from '~/modules/Admin/constants/module';
import { checkUrlEligibilityForIntegrations } from '~/modules/Admin/utils';
import { parseEventEnrollmentFreezeEpoch } from '~/modules/Admin/utils/eventEdit';
import { checkUrlValidationWebinarSourceHasSettingKey } from '~/modules/Admin/utils/sessionEdit';
import { formatDuration } from '~/modules/Admin/utils/timing';
import {
  isValidDate,
  isValidIANATimezone,
  isValidTime,
  escapeEscapedStringFromObject,
} from '~/utils';
import { getCurrTimeWithOffset } from '~/utils/timezone';

import {
  EXCEL_HEADER_CONFIGURATIONS,
  HOST_EMAIL_EXCEL_HEADER,
  HOST_EMAIL_EXCEL_HEADER_NOT_PRESENT_ERROR,
  ILT_SESSION_TYPES,
  MAX_INSTRUCTOR_COUNT,
  SAMPLE_BULK_SESSION_UPLOAD_EXCEL_PATH,
  SAMPLE_BULK_SESSION_USER_AUTH_UPLOAD_EXCEL_PATH,
  VALIDATION_CHECKS,
} from './constants';

import type { TTimezoneInfoMapper, TTimezoneTriggerInfo } from './typeDefs';

const parseSendInstructorMail = (sendInstructorMail: string) => {
  let parsedValue = true;
  if (sendInstructorMail?.trim()) {
    parsedValue = sendInstructorMail.trim().toLowerCase() === 'no' ? false : true;
  }

  return parsedValue;
};

const calculateDuration = (startTime: number, endTime: number) => ({
  displayValue: formatDuration(endTime - startTime),
  value: endTime - startTime,
});

const calculateWaitListEnabled = (session: any) => {
  const { enable_waiting_list } = session;
  if (enable_waiting_list && enable_waiting_list.toUpperCase() === 'ENABLE') return true;
  return false;
};

export const calculateStartTimeEpoch = (session: any) => {
  let { start_date, start_time } = session;
  let startEpoch = moment(start_date + ' ' + start_time, 'DD-MM-YYYY HH:mm').unix() * 1000;
  return startEpoch;
};

export const calculateEndTimeEpoch = (session: any) => {
  let { end_date, end_time } = session;
  let endEpoch = moment(end_date + ' ' + end_time, 'DD-MM-YYYY HH:mm').unix() * 1000;
  return endEpoch;
};

export const calculateCurrTimeInTimezone = (session: any) => {
  const { time_zone } = session;
  const offset = momentTimezone.tz(moment(), time_zone).utcOffset() / 60;
  const currentTimeWithOffset = getCurrTimeWithOffset(offset);
  return currentTimeWithOffset;
};

const calculateEnrollmentFreezeEpoch = (session: any) => {
  let { enrollment_lock_date, enrollment_lock_time } = session;
  const startTimeEpoch = calculateStartTimeEpoch(session);
  // TODO: verify whether this is actually needed, since comparison is being made without offsets
  // const currentTimeEpochWithOffset = calculateCurrTimeInTimezone(session);

  enrollment_lock_time = enrollment_lock_time ? enrollment_lock_time : '00:00';
  let freezeEpoch =
    startTimeEpoch > Date.now()
      ? enrollment_lock_date
        ? moment(enrollment_lock_date + ' ' + enrollment_lock_time, 'DD-MM-YYYY HH:mm').unix() *
          1000
        : 0
      : 0;

  return freezeEpoch;
};

const validateEnrollmentFreeze = (session: any, intl: any, eventData: any) => {
  let validationError = '';
  const {
    enrollmentFreezeDaysBeforeEvent: eventEnrollmentFreezeDaysBeforeEvent,
    enrollmentFreezeStatus: eventEnrollmentFreezeStatus,
    enrollmentFreezeEpoch: eventEnrollmentFreezeEpoch,
    startTime: eventStartTime,
    enrollmentFreezeTimezone,
  } = eventData;
  let enrollmentFreezeEpoch = calculateEnrollmentFreezeEpoch(session);
  const startTimeEpoch = calculateStartTimeEpoch(session);
  if (eventData?.enrollmentType === EVENT_ENROLLMENT_ENUM.EVENT) {
    enrollmentFreezeEpoch =
      eventEnrollmentFreezeStatus !== ENROLLMENT_FREEZE_STATUSES.DISABLED
        ? parseEventEnrollmentFreezeEpoch({
            startTime: eventStartTime ? eventStartTime : startTimeEpoch,
            enrollmentFreezeStatus: eventEnrollmentFreezeStatus,
            enrollmentFreezeEpoch: eventEnrollmentFreezeEpoch,
            enrollmentFreezeDaysBeforeEvent: eventEnrollmentFreezeDaysBeforeEvent,
            enrollmentFreezeTimezone,
          })
        : 0;
  }
  // TODO: verify whether this is actually needed, since comparison is being made without offsets
  // const currentTimeEpochWithOffset = calculateCurrTimeInTimezone(session);

  if (enrollmentFreezeEpoch !== 0) {
    if (enrollmentFreezeEpoch < Date.now()) {
      validationError =
        eventData?.enrollmentType === EVENT_ENROLLMENT_ENUM.EVENT
          ? intl.formatMessage(MESSAGES.INFO.FREEZE_ENROLLMENT_PAST_EVENT)
          : intl.formatMessage(MESSAGES.INFO.FREEZE_ENROLLMENT_PAST);
    } else if (enrollmentFreezeEpoch > startTimeEpoch) {
      validationError =
        eventData?.enrollmentType === EVENT_ENROLLMENT_ENUM.EVENT
          ? intl.formatMessage(MESSAGES.INFO.ENROLLMENT_FREEZE_BEFORE_EVENT)
          : intl.formatMessage(MESSAGES.INFO.FREEZE_ENROLLMENT_WARNING_UPLOAD);
    }
  }

  return validationError;
};

//*NOTE: This function is not present in ss-ui, fixes existing prod issue(end time can be before start time)
const validateSessionTimings = (session: any, intl: any) => {
  let validationError = '';
  const startTimeEpoch = calculateStartTimeEpoch(session);
  const endTimeEpoch = calculateEndTimeEpoch(session);

  if (endTimeEpoch < startTimeEpoch) {
    validationError = 'Session start date must be set before the session end date';
  }

  return validationError;
};

const validateField = (value: any, headingObject: any, intl: any) => {
  let validationErrors: any = [];
  headingObject.validations.forEach((RULE: any) => {
    switch (RULE) {
      case VALIDATION_CHECKS.NO_EMPTY:
        if (!value) validationErrors.push(`${headingObject.excel_header} is empty `);
        break;
      case VALIDATION_CHECKS.ONE_OF_SESSION_TYPE:
        if (value && ILT_SESSION_TYPES.indexOf(value.toUpperCase()) === -1)
          validationErrors.push(
            `${headingObject.excel_header} should be one of ${intl.formatMessage(
              SESSION_TYPES.CLASSROOM.displayValue
            )}/${intl.formatMessage(SESSION_TYPES.WEBINAR.displayValue)} `
          );
        break;
      case VALIDATION_CHECKS.DATE:
        if (value && !isValidDate(value))
          validationErrors.push(`Invalid ${headingObject.excel_header} `);
        break;
      case VALIDATION_CHECKS.TIME:
        if (value && !isValidTime(value))
          validationErrors.push(`Invalid ${headingObject.excel_header} `);
        break;
      case VALIDATION_CHECKS.TIMEZONE:
        if (value && !isValidIANATimezone(value))
          validationErrors.push(
            `Invalid ${headingObject.excel_header}. Please make sure you use the latest excel template.`
          );
        break;
    }
  });
  return validationErrors.join(', ');
};

function validateInstructors({
  manipulatedSession,
  manipulationErrors,
}: {
  manipulatedSession: any;
  manipulationErrors: any;
}) {
  for (let index = 1; index <= MAX_INSTRUCTOR_COUNT; index++) {
    const instructorName = manipulatedSession[`instructor_${index}_name`];
    const instructorEmail = manipulatedSession[`instructor_${index}_email`];
    const instructorNameIssues = [];
    const instructorEmailIssues = [];
    if (instructorEmail && !instructorName) {
      instructorNameIssues.push(
        `Instructor ${index} name is required when instructor ${index} email ID is provided`
      );
    }
    if (instructorName && !instructorEmail) {
      instructorEmailIssues.push(
        `Instructor ${index} email is required when instructor ${index} name is provided`
      );
    }
    if (instructorEmail && !isValidEmail(instructorEmail))
      instructorEmailIssues.push(`Instructor ${index} email ID is invalid`);
    if (instructorNameIssues.length) {
      manipulationErrors[`instructor_${index}_name`] = instructorNameIssues.join(', ');
    }
    if (instructorEmailIssues.length) {
      manipulationErrors[`instructor_${index}_email`] = instructorEmailIssues.join(', ');
    }
  }
}

export const convertToSessionsMap = ({
  sessions,
  intl,
  isUserAuthEnabled,
  eventData,
}: {
  sessions: any;
  intl: any;
  isUserAuthEnabled: boolean;
  eventData?: any;
}) => {
  let headerObjects = sessions?.[0]
    ? Object.keys(sessions[0]).map(index => ({
        index: index,
        value: sessions[0][index],
        // eslint-disable-next-line eqeqeq
        config: EXCEL_HEADER_CONFIGURATIONS(isUserAuthEnabled).find(
          obj => obj.excel_header === sessions[0][index]
        ),
      }))
    : [];

  sessions.shift();

  let allSessions: any = [];

  const isHostEmailHeaderPresent = headerObjects.find(
    (headerObj: any) => headerObj.value === HOST_EMAIL_EXCEL_HEADER
  );
  if (isUserAuthEnabled && !isHostEmailHeaderPresent) {
    return {
      allSessions: allSessions,
      err: HOST_EMAIL_EXCEL_HEADER_NOT_PRESENT_ERROR,
    };
  }

  try {
    allSessions = sessions?.map((rawSession: any) => {
      let manipulatedSession: any = {};
      let fieldError;
      let enrollmentFreezeError;
      let sessionTimingsError;
      let manipulationErrors: any = {};

      headerObjects.forEach((headingObj: any) => {
        if (headingObj.config.key === 'sendInstructorMail') {
          manipulatedSession[headingObj.config.key] = parseSendInstructorMail(
            rawSession[headingObj['index']]
          );
        } else {
          manipulatedSession[headingObj.config.key] = rawSession[headingObj['index']];
        }

        fieldError = validateField(rawSession[headingObj['index']], headingObj.config, intl);
        if (fieldError) manipulationErrors[headingObj.config.key] = fieldError;
      });

      sessionTimingsError = validateSessionTimings(manipulatedSession, intl); //*NOTE: This is not present in ss-ui, fixes existing prod issue(end time can be before start time)
      enrollmentFreezeError = validateEnrollmentFreeze(manipulatedSession, intl, eventData);
      if (enrollmentFreezeError) manipulationErrors['enrollment_lock_date'] = enrollmentFreezeError;
      if (sessionTimingsError) manipulationErrors['start_date'] = sessionTimingsError;
      validateInstructors({ manipulatedSession, manipulationErrors });
      if (Object.keys(manipulationErrors).length > 0)
        manipulatedSession['validationErrors'] = manipulationErrors;
      return manipulatedSession;
    });
  } catch (err) {} //eslint-disable-line

  return { allSessions };
};

export async function getTimezoneInfo(convertedSessions: { [key: string]: any }[]) {
  const timezoneMap: TTimezoneInfoMapper = [];
  const timezoneCallsTriggered: TTimezoneTriggerInfo[] = [];
  function checkHasAlreadyTriggered({ startTimeEpoch, timezoneId }: TTimezoneTriggerInfo) {
    for (const triggerInfo of timezoneCallsTriggered) {
      if (triggerInfo.startTimeEpoch === startTimeEpoch && triggerInfo.timezoneId === timezoneId) {
        return true;
      }
    }
    return false;
  }
  await Promise.all(
    convertedSessions.map(async session => {
      const startTimeEpoch = calculateStartTimeEpoch(session);
      const timezoneId = session.time_zone;

      if (checkHasAlreadyTriggered({ startTimeEpoch, timezoneId })) {
        return true;
      }
      timezoneCallsTriggered.push({ startTimeEpoch, timezoneId });
      try {
        const { timezone_records } = await TimezonesService.getTimezones({
          epoch: Math.floor(startTimeEpoch / 1000),
          timezoneId: timezoneId,
        });
        const timezoneMapObject = {
          startTimeEpoch,
          timezoneId,
          timezoneObject: escapeEscapedStringFromObject({
            id: timezone_records[0].timezone_id,
            offset: timezone_records[0].current_date_offset,
            rawOffset: timezone_records[0].raw_offset,
            name: timezone_records[0].current_date_displayed_timezone,
          }),
        };
        timezoneMap.push(timezoneMapObject);
      } catch (err) {
        // do nothing
      }

      return true;
    })
  );
  return timezoneMap;
}

export const checkTimezoneAvailable = (
  timezoneInfoMapper: TTimezoneInfoMapper,
  convertedSession: { [key: string]: any }
) => {
  for (const timezoneDetail of timezoneInfoMapper) {
    if (
      timezoneDetail.startTimeEpoch === calculateStartTimeEpoch(convertedSession) &&
      timezoneDetail.timezoneId === convertedSession.time_zone &&
      timezoneDetail.timezoneObject
    ) {
      return timezoneDetail.timezoneObject;
    }
  }
  return undefined;
};

export const addTimezoneNotAvailableError = (
  convertedSessions: { [key: string]: any }[],
  { timezoneInfoMapper = [] }: { timezoneInfoMapper: TTimezoneInfoMapper }
) =>
  convertedSessions.map((session: any) => {
    const timezoneObject = checkTimezoneAvailable(timezoneInfoMapper, session);
    if (!timezoneObject) {
      const newSession = { ...session };
      if (!newSession.validationErrors) {
        newSession.validationErrors = {};
      }
      newSession.validationErrors = {
        ...newSession.validationErrors,
        time_zone: 'Failed to load timezone info for this session',
      };
      return newSession;
    }
    return session;
  });

const makeInstructorList = (session: any) => {
  const instructorList = [];
  for (let index = 1; index <= MAX_INSTRUCTOR_COUNT; index++) {
    const instructorName = session[`instructor_${index}_name`];
    const instructorEmail = session[`instructor_${index}_email`];
    const instructorDescription = session[`instructor_${index}_description`] || '';
    if (instructorName && instructorEmail && isValidEmail(instructorEmail))
      instructorList.push({
        name: instructorName,
        email: instructorEmail,
        description: instructorDescription,
      });
  }
  return instructorList;
};

const calculateReminders = (session: any) => {
  const { reminder: reminderString } = session;
  const reminders = reminderString ? reminderString.split(',') : [];

  return reminders.map((reminder: any) => {
    const reminderIntervals = reminder.split('-');
    const reminderInSeconds =
      parseInt(reminderIntervals[0]) * 24 * 60 * 60 +
      parseInt(reminderIntervals[1]) * 60 * 60 +
      parseInt(reminderIntervals[2]) * 60;
    return reminderInSeconds;
  });
};

const changeSessionDataBasedOnEvent = (eventData: any, session: any) => {
  let { max_enrolments } = session;
  let reminders = calculateReminders(session);
  if (eventData?.reminders && reminders.length === 0) {
    reminders = eventData.reminders;
  }

  let maxSeatEnabled = max_enrolments ? true : false;
  let waitingListEnabled = calculateWaitListEnabled(session);
  if (eventData?.enrollmentType === ILT_ENTITIES.EVENT) {
    maxSeatEnabled = false;
    waitingListEnabled = false;
  }

  return { reminders, maxSeatEnabled, waitingListEnabled };
};

export const preProcessUploadedSessions = (
  sessions: any,
  {
    integrations,
    defaultCompletionCriteria,
    userEmail,
    isUserAuthEnabled,
  }: {
    integrations: any;
    defaultCompletionCriteria: (typeof COMPLETION_CRITERIA_ENUM)[keyof typeof COMPLETION_CRITERIA_ENUM];
    userEmail: string;
    isUserAuthEnabled: boolean;
  },
  eventData?: any,
  moduleDetails?: any
) => {
  // eslint-disable-next-line max-statements,complexity
  const processedSessions = sessions.map((session: any) => {
    let {
      name: sessionName,
      type: sessionType,
      description: sessionDescription,
      location: sessionLocation,
      password_webex,
      host_email,
      max_enrolments,
      score,
      sendInstructorMail = true,
      timezoneObject,
    } = session;

    const startTime = calculateStartTimeEpoch(session);
    const endTime = calculateEndTimeEpoch(session);
    const duration = calculateDuration(startTime, endTime);
    const minStartTime = 0; // ????

    const enrollmentFreezeEpoch = calculateEnrollmentFreezeEpoch(session);
    const enrollmentFreezeStatus =
      enrollmentFreezeEpoch > 0
        ? ENROLLMENT_FREEZE_STATUSES.ABSOLUTE
        : ENROLLMENT_FREEZE_STATUSES.DISABLED;

    const instructors = makeInstructorList(session);
    const isInstructorAvailable = instructors.length ? true : undefined;

    const maxSeats = max_enrolments;

    const maxScore = !score || isNaN(score) ? 0 : score;
    let sessionTypeCaps = sessionType.toUpperCase();
    const type =
      (sessionTypeCaps === 'VIDEO CONFERENCING' ? SESSION_TYPES.WEBINAR.value : sessionTypeCaps) ||
      'CLASSROOM';
    const name =
      sessionName.length > MAX_SESSION_NAME_LENGTH
        ? sessionName.substring(0, MAX_SESSION_NAME_LENGTH)
        : sessionName;
    const description = sessionDescription;
    const location = sessionLocation;
    let locationType = LOCATION_TYPE.FACE_TO_FACE;
    const extra: any = {};
    extra.enrolmentThresholdEmailSettings = {
      isEnabled: moduleDetails?.enrolmentThresholdEmailSettings?.isEnabled || false,
      enrolmentThreshold: moduleDetails?.enrolmentThresholdEmailSettings?.enrolmentThreshold || 0,
      reminders: moduleDetails?.enrolmentThresholdEmailSettings?.reminders || [],
    };
    extra.enrolmentThresholdEmailSettings.reminders =
      extra.enrolmentThresholdEmailSettings.reminders.map((reminder: any) => ({
        ...reminder,
        mailJobId: '',
        operation: 'UPLOAD',
      }));
    if (type === SESSION_TYPES.WEBINAR.value) {
      locationType = LOCATION_TYPE.LINK;
      const { eligible, webinarSource } = checkUrlEligibilityForIntegrations({
        location,
        integrations,
      });
      if (eligible) {
        // not adding rest other properties those gets handled in preprocessing at api call
        extra.webAutoAttendanceSettings = {
          webinarSource,
          isAutoAttendanceEnabled: true,
          thresholdConfig: {
            isEnabled: moduleDetails?.attendanceSettings?.durationBasedAttendanceEnabled,
            percentageThreshold: moduleDetails?.attendanceSettings?.percentageThreshold,
          },
        };
        const meetingSettingsKey = checkUrlValidationWebinarSourceHasSettingKey(
          webinarSource || ''
        );
        if (meetingSettingsKey) {
          if (isUserAuthEnabled) {
            extra[meetingSettingsKey] = {
              hostEmail: host_email && isValidEmail(host_email) ? host_email : userEmail,
            };
          } else if (webinarSource === WEBINAR_SOURCE.WEBEX) {
            extra[meetingSettingsKey] = {
              password: password_webex || '',
            };
          }
        }
      }
    } else {
      extra.webAutoAttendanceSettings = {
        isAutoAttendanceEnabled: false,
        isLinkValidated: false,
        lastValidityStatus: 'NONE',
        thresholdConfig: {
          isEnabled: moduleDetails?.attendanceSettings?.durationBasedAttendanceEnabled,
          percentageThreshold: moduleDetails?.attendanceSettings?.percentageThreshold,
        },
        webinarSource: 'NONE',
      };
    }

    const { reminders, maxSeatEnabled, waitingListEnabled } = changeSessionDataBasedOnEvent(
      eventData,
      session
    );
    const reminderEnabled = reminders.length > 0 ? true : false;

    const autoEnroll = waitingListEnabled ? true : undefined;
    const notifyWaitingList = waitingListEnabled ? true : undefined;

    return {
      startTime,
      endTime,
      timezone: timezoneObject,
      duration,
      minStartTime,

      name,
      type,
      description,
      attachments: {},
      location,
      locationType,

      enrollmentFreezeEpoch,
      enrollmentFreezeStatus,

      reminderEnabled,
      reminders,

      maxSeatEnabled,
      maxSeats,

      waitingListEnabled,
      notifyWaitingList,
      autoEnroll,
      maxScore,
      completionCriteria: defaultCompletionCriteria,
      instructors,
      isInstructorAvailable,
      sendInstructorMail,
      ...extra,
    };
  });
  return processedSessions;
};

export const getUniqueStartTimes = (sessions = []) => {
  const startTimeList = sessions.map((session: any) => calculateStartTimeEpoch(session));
  return union(startTimeList);
};

export const downloadSample = (isUserAuthEnabled: boolean) => {
  window.open(
    isUserAuthEnabled
      ? SAMPLE_BULK_SESSION_USER_AUTH_UPLOAD_EXCEL_PATH
      : SAMPLE_BULK_SESSION_UPLOAD_EXCEL_PATH
  );
};
