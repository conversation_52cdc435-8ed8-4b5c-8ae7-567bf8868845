import styled from 'styled-components';

import { tokens } from '@mindtickle/styles/lib';

export const StyledSessionsNoDataContainer = styled.div`
  display: flex;
  flex-direction: column;
  .sessions-no-data-button {
    margin-top: 27px;
  }
`;

export const StyledEmptySessionsWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 27px auto;
  .extra-info-text {
    font-weight: 600;
    font-size: 14px;
    line-height: 20px;
    margin-top: 24px;
  }
  .session-link-color {
    font-weight: 400;
    color: ${tokens.textTokens.COLOR_TEXT_ACCENT};
    cursor: pointer;
  }
`;
