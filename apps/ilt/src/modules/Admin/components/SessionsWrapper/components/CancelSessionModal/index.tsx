import { useRef } from 'react';

import { FormattedMessage } from 'react-intl';

import Button from '@mindtickle/button';
import Form from '@mindtickle/form';
import Modal from '@mindtickle/modal';

import type { TILTEntities } from '~/modules/Admin/typeDefs';
import { checkIsEvent } from '~/modules/Admin/utils/checkEntityType';

import CancelSessionModalContent from './components/CancelSessionModalContent';
import messages from './messages';
import { DeleteActions, StyledDeleteModalContainer } from './styles';
import { TCancelSessionModal, TModalFooter } from './typeDefs';

const ModalFooter = ({ onCancel, onOk }: TModalFooter) => (
  <DeleteActions>
    <Button type="secondary" onClick={onCancel} className={'cancelButton'}>
      <FormattedMessage {...messages.CANCEL_BUTTON_TEXT} />
    </Button>
    <Button type="danger" onClick={onOk} className={'okButton'}>
      <FormattedMessage {...messages.OK_BUTTON_TEXT} />
    </Button>
  </DeleteActions>
);

const DeleteModalTitle = ({ entityType }: { entityType: TILTEntities }) => (
  <FormattedMessage
    {...(checkIsEvent(entityType) ? messages.MODAL_EVENT_TITLE : messages.MODAL_SESSION_TITLE)}
  />
);

const CancelSessionModal = ({
  sessionsMap,
  inProgress,
  isILTSessionCalendarAutoSyncEnabled,
  selectedId,
  handleModeOperation,
  clearMode,
}: TCancelSessionModal) => {
  const modalContainerRef = useRef<HTMLDivElement>(null);
  const [cancelSessionForm] = Form.useForm();
  const data = selectedId ? sessionsMap[selectedId] : {};
  return (
    <StyledDeleteModalContainer ref={modalContainerRef}>
      <Modal
        title={<DeleteModalTitle entityType={data.entityType} />}
        visible={true}
        onOk={() => cancelSessionForm.submit()}
        onCancel={() => clearMode()}
        centered={true}
        getPopupContainer={() => modalContainerRef.current}
        maskClosable={false}
        keyboard={false}
        className={'cancel-modal'}
        footer={
          <ModalFooter onCancel={() => clearMode()} onOk={() => cancelSessionForm.submit()} />
        }
      >
        <CancelSessionModalContent
          cancelSessionForm={cancelSessionForm}
          data={data}
          id={selectedId}
          inProgress={inProgress}
          isILTSessionCalendarAutoSyncEnabled={isILTSessionCalendarAutoSyncEnabled}
          handleModeOperation={handleModeOperation}
          clearMode={clearMode}
        />
      </Modal>
    </StyledDeleteModalContainer>
  );
};

export default CancelSessionModal;
