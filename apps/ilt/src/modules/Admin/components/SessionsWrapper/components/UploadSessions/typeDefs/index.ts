import type { Dispatch, SetStateAction } from 'react';

import { COMPLETION_CRITERIA_ENUM } from '~/modules/Admin/config/sessions.constants';

import type { InjectedIntlProps } from 'react-intl';

export interface TUploadSessions extends InjectedIntlProps {
  onClose: () => void;
  uploadSessions: (sessions: any, parentId?: string) => void;
  integrations: any;
  defaultCompletionCriteria: (typeof COMPLETION_CRITERIA_ENUM)[keyof typeof COMPLETION_CRITERIA_ENUM];
  trackerPageName?: string;
  eventData: any;
  sessionsList?: string[];
  moduleDetails?: any;
}

export type TTimezoneInfoFromService = {
  startTimeEpoch: number;
  timezoneId: string;
  timezoneObject: string;
};

export type TTimezoneTriggerInfo = {
  startTimeEpoch: number;
  timezoneId: string;
};

export type TTimezoneInfoMapper = TTimezoneInfoFromService[];

export interface TUploadActions {
  onClose: () => void;
  onReUpload: () => void;
  sessions: any;
  setSessions: Dispatch<SetStateAction<any>>;
  processUpload: (sessions: any) => void;
  shouldRenderOlderTemplateError: boolean;
  timezoneInfoMapper: TTimezoneInfoMapper;
  uploadLimit: number;
  eventId: string;
}

export interface TSessionsModalTable {
  sessionsData: any;
  onSessionRemove: (rowIndex: number) => void;
  shouldRenderOlderTemplateError: boolean;
  uploadLimit: number;
  eventId?: string;
}

export interface TSessionNameWithAllErrors {
  name: string;
  errors: {
    [key: string]: string;
  };
}
