import { FormattedMessage } from 'react-intl';

import EllipsisTooltip from '@mindtickle/ellipsis-tooltip';
import Icon, { ICON_MAP } from '@mindtickle/icon';

import { MAX_SESSIONS_WITHIN_EVENT } from '~/modules/Admin/constants/events';
import UPLOAD_SESSION_MESSAGES from '~/modules/Admin/messages/sessions/upload';

import SessionNameWithRowErrors from '../SessionNameWithRowErrors';
import UploadErrorOldFile from '../UploadErrorOldFile';

import { SESSION_MODAL_TABLE_COLUMNS, ellipsisTooltipCommonProps } from './constants';
import { StyledErrorMessageContainer, StyledUploadSessionsTable } from './styles';

import type { TSessionsModalTable } from '../../typeDefs';

const SessionsModalTable = ({
  sessionsData,
  onSessionRemove,
  shouldRenderOlderTemplateError,
  uploadLimit,
  eventId,
}: TSessionsModalTable) => {
  const tableData = [];
  const filteredCorrectSessions = sessionsData.filter((session: any) => !session.validationErrors);
  const isUploadLimitExceeded = eventId && filteredCorrectSessions.length > uploadLimit;
  for (let rowIndex = 0; rowIndex < sessionsData.length; rowIndex++) {
    const sessionDetails = sessionsData[rowIndex];
    const errors = sessionDetails.validationErrors || {};

    tableData.push({
      key: `${rowIndex}`,
      sessionName: <SessionNameWithRowErrors name={sessionDetails.name} errors={errors} />,
      sessionStartDate: (
        <div className="session-start-date">
          {sessionDetails.start_date + ' ' + sessionDetails.start_time}
        </div>
      ),
      sessionEndDate: (
        <div className="session-end-date">
          {sessionDetails.end_date + ' ' + sessionDetails.end_time}
        </div>
      ),
      sessionTimezone: (
        <div className="session-timezone">
          <EllipsisTooltip
            wrapperClassName="session-timezone-tooltip"
            title={sessionDetails.time_zone}
            {...ellipsisTooltipCommonProps}
          >
            {sessionDetails.time_zone}
          </EllipsisTooltip>
        </div>
      ),
      sessionLocation: (
        <div className="session-location">
          <EllipsisTooltip
            wrapperClassName="session-location-tooltip"
            title={sessionDetails.location}
            {...ellipsisTooltipCommonProps}
          >
            {sessionDetails.location}
          </EllipsisTooltip>
        </div>
      ),
      deleteOperation: <Icon type={ICON_MAP.DELETE} onClick={() => onSessionRemove(rowIndex)} />,
    });
  }

  if (shouldRenderOlderTemplateError) {
    return <UploadErrorOldFile />;
  }

  return (
    <>
      {isUploadLimitExceeded && (
        <StyledErrorMessageContainer>
          <span className={'info-container-text'}>
            <Icon type={ICON_MAP.WARNING} className={'warning-icon'} />
            <span className="note-heading">Please note: </span>
            <FormattedMessage
              {...UPLOAD_SESSION_MESSAGES.UPLOAD_LIMIT_EXCEEDED}
              values={{
                maxLimit: MAX_SESSIONS_WITHIN_EVENT,
                sessionsCountToDelete: sessionsData.length - uploadLimit,
              }}
            />
          </span>
        </StyledErrorMessageContainer>
      )}
      <StyledUploadSessionsTable
        dataSource={tableData}
        columns={SESSION_MODAL_TABLE_COLUMNS}
        pagination={false}
        sticky
        emptyTableMsg={{ title: 'No results found.', subtitle: '' }}
      />
    </>
  );
};

export default SessionsModalTable;
