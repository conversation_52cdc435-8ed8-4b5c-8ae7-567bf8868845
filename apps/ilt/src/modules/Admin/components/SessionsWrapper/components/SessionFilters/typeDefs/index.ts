import type { Dispatch, SetStateAction } from 'react';

import type { TILTFilters } from '~/modules/Admin/containers/Build/typeDefs';

import type { TOnFilterSelect } from '../../../typeDefs';

export interface TSessionFilters {
  ILTFilters: TILTFilters;
  updateILTFilters?: (filters: TILTFilters) => void;
  setResetClicked: Dispatch<SetStateAction<boolean>>;
  manipulateData: (data: any) => void;
  onFilterSelect: (filters: TOnFilterSelect) => void;
  haveBuildAccess: boolean;
  trackerPageName: string;
}

export interface TGetFilterDetails {
  onFilterSelect: (filters: TOnFilterSelect) => void;
  ILTFilters: TILTFilters;
  isMultidayEnabled?: boolean;
}
