import { useMemo } from 'react';

import { ICON_MAP } from '@mindtickle/icon';

import { ILT_ENTITIES } from '~/modules/Admin/constants/module';
import useMultidayEnabled from '~/modules/Admin/hooks/useMultidayEnabled';

import messages from '../messages';

import type { TUseCreateDropdownOptionsParams } from '../typeDefs';

export default function useCreateDropdownOptions({ intl }: TUseCreateDropdownOptionsParams) {
  const { isMultidayEnabled } = useMultidayEnabled();
  const creationDropdownOptions = useMemo(() => {
    const options: { icon: string; title: string; content: string; key: string }[] = [
      {
        icon: ICON_MAP.SESSION,
        title: intl.formatMessage(messages.SESSION_OPTION),
        content: intl.formatMessage(messages.SESSION_OPTION_SUBTEXT),
        key: ILT_ENTITIES.SESSION,
      },
    ];
    if (isMultidayEnabled) {
      options.unshift({
        icon: ICON_MAP.EVENT,
        title: intl.formatMessage(messages.EVENT_OPTION),
        content: intl.formatMessage(messages.EVENT_OPTION_SUBTEXT),
        key: ILT_ENTITIES.EVENT,
      });
    }
    return options;
  }, [intl, isMultidayEnabled]);
  return [creationDropdownOptions];
}
