import IconWithGradient, { ICON_MAP } from '@mindtickle/icon-with-gradient';

import { MESSAGES } from './constants';
import { Wrapper } from './styles';

function UploadErrorOldFile() {
  return (
    <Wrapper>
      <IconWithGradient type={ICON_MAP.EMPTY_ASSETS_WITH_EXCLAMATION} />
      <div className="message-main">{MESSAGES.UPLOAD_ERROR_MAIN}</div>
      <div className="message-sub">{MESSAGES.UPLOAD_ERROR_SUB}</div>
    </Wrapper>
  );
}

export default UploadErrorOldFile;
