import { defineMessages } from 'react-intl';

export default defineMessages({
  CANCEL_EVENT_CONFIRM_TEXT:
    "Once you cancel this event, all sessions in it will also be canceled and it can't be undone",
  CANCEL_CONFIRM_TEXT: 'Are you sure you want to cancel the session {name}?',
  ENTER_REASON_PLACEHOLDER: 'Reason for cancelation',
  CANCEL_REASON_TEXT_BOX_LABEL: 'Reason for cancelation',
  DISCLAIMER_NOTIFICATION_INSTRUCTOR: 'An email notification will be sent to the instructors.',
  LABEL_CHECKBOX_NOTIFY_LEARNERS: 'Notify learners about the cancelation',
  ERROR_EMPTY_REASON: 'Please enter cancelation reason',
  M<PERSON><PERSON>_EVENT_TITLE: 'Cancel event?',
  MODAL_SESSION_TITLE: 'Cancel session?',
  OK_BUTTON_TEXT: 'Yes, cancel',
  CANCEL_BUTTON_TEXT: 'Discard',
});
