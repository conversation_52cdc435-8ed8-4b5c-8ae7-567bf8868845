import IconWithGradient, { ICON_MAP as GRADIENT_ICON_MAP } from '@mindtickle/icon-with-gradient';

import { TSessionsNoData } from '../../typeDefs';
import CreateEntitiesSplitButton from '../CreateEntitiesSplitButton';

import { StyledEmptySessionsWrapper, StyledSessionsNoDataContainer } from './styles';

const SessionsNoData = (props: TSessionsNoData) => {
  const { onCreateSessionClick, onUploadSessionsClick, onCreateEventClick } = props;
  return (
    <StyledSessionsNoDataContainer>
      <CreateEntitiesSplitButton
        onCreateSessionClick={onCreateSessionClick}
        onUploadSessionsClick={onUploadSessionsClick}
        onCreateEventClick={onCreateEventClick}
        splitButtonWrapperClassName={'sessions-no-data-button'}
      />
      <StyledEmptySessionsWrapper>
        <IconWithGradient
          style={{ width: 300, height: 300 }}
          type={GRADIENT_ICON_MAP.EMPTY_STATE_PARAMETERS}
        />
        <p className={'extraInfoText'}>Sessions in this ILT will appear here</p>
        <div className={'info-text'}>
          Start{' '}
          <span className={'session-link-color'} onClick={onCreateSessionClick}>
            creating single sessions{' '}
          </span>
          or{' '}
          <span className={'session-link-color'} onClick={onUploadSessionsClick}>
            upload sessions in bulk{' '}
          </span>
        </div>
      </StyledEmptySessionsWrapper>
    </StyledSessionsNoDataContainer>
  );
};

export default SessionsNoData;
