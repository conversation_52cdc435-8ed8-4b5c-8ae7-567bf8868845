import styled from 'styled-components';

import { tokens, theme } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS } from '~/config/constants';

export const StyledCancelSessionModalContainer = styled.div`
  .${THEME_PREFIX_CLS}-form-item-label > label {
    font-weight: 600;
    color: ${tokens.textTokens.COLOR_TEXT_DEFAULT};
  }
  .${THEME_PREFIX_CLS}-form-item-label > label::before {
    display: none !important;
  }
  .message-container {
    margin-bottom: 24px;
  }
  .check-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
`;

export const StyledAutoSyncMessageContainer = styled.div`
  background-color: ${tokens.bgTokens.COLOR_BG_WARNING};
  border-radius: 4px;
  width: fit-content;
  padding: 8px 16px;
  margin-top: 10px;
  border-radius: 8px;

  & .infoContainerText {
    font-family: ${theme.fontFamily.DEFAULT};
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
    color: ${tokens.textTokens.COLOR_TEXT_DEFAULT};
    display: flex;
    align-items: center;
  }

  & .warningIcon {
    color: ${tokens.iconTokens.COLOR_ICON_DANGER};
    margin-right: 9px;
  }
`;
