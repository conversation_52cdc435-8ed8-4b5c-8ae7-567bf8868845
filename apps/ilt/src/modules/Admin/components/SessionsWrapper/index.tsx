import { useCallback, useEffect, useRef, useState } from 'react';

import queryString from 'query-string';
import { FormattedMessage } from 'react-intl';

import CopyToClipboard from '@mindtickle/copy-to-clipboard';
import Modal from '@mindtickle/modal';
import { errorToast, successToast } from '@mindtickle/toast';
import { isUndefined, isEmpty, prune } from '@mindtickle/utils';

import { THEME_PREFIX_CLS } from '~/config/constants';
import SessionFilters from '~/modules/Admin/components/SessionsWrapper/components/SessionFilters';
import { ILT_ENTITIES } from '~/modules/Admin/config/constants';
import { useILTAdminSnowplowTracker } from '~/modules/Admin/hooks/useILTAdminSnowplowTracker';
import { SNOWPLOW_FIELD_NAMES } from '~/modules/Admin/hooks/useILTAdminSnowplowTracker/constants';
import { TILTEntities } from '~/modules/Admin/typeDefs';
import { castArray } from '~/mt-ui-core/utils';
import { prepareFilters } from '~/utils';
import {
  CREATE_EVENT_QUERY_PARAM,
  CREATE_SESSION_QUERY_PARAM,
  EVENT_ID_QUERY_PARAM,
  OPEN_DRAWER_QUERY_PARAM,
} from '~/utils/constants';
import { getIltSessionUrl } from '~/utils/generateUrls';
import { multipleToast } from '~/utils/multipleToast';

import GET_ERROR_MESSAGES from '../../config/error.messages';
import { LC_MODAL_ACTIONS, MODE_EXPORT_LC_REPORT } from '../../config/live-challenges.constants';
import { MODES, OPERATIONS } from '../../config/sessions.constants';
import { createSessionTableData, getInfoAfterSingleSessionSaved } from '../../utils';
import { checkIsEvent } from '../../utils/checkEntityType';
import { EntitiesOperationModeLoader } from '../EntitiesOperationModeLoader';
import EventEditDrawer from '../EventEditDrawer';
import SessionEditDrawer from '../SessionEditDrawer';
import SessionsTable from '../SessionsTable';
import ShareLiveChallengeModal from '../ShareLiveChallengeModal';

import CancelSessionModal from './components/CancelSessionModal';
import CreateEntitiesSplitButton from './components/CreateEntitiesSplitButton';
import SessionsNoData from './components/SessionsNoData';
import { getDeleteConfirmationMessages } from './components/SessionsOperationConfirmation';
import UploadSessions from './components/UploadSessions';
import { useCopySession } from './hooks/useCopySession';
import messages from './messages';
import {
  StyledFilterContainer,
  StyledSessionWrapperContainer,
  StyledSessionsTableContainer,
} from './styles';
import { showOperationEffectMessages } from './utils/operationsEffectToasts';
import { getSelectedEntitiesInfo, getSelectionCountInfo } from './utils/operationsUtils';
import {
  sendBulkUploadClickEvent,
  sendSessionEmailLCReportSubmitted,
  sendSessionGetCheckinCodeClicked,
  sendSessionOperation,
  sendCreateEntityClicked,
  sendCopyIltEvent,
  sendEventSettingsClicked,
  sendBulkDeleteClicked,
  sendEntityLinkCopyClicked,
} from './utils/trackEvents';

import type {
  SessionsWrapperProps,
  TEntitiesOnSelectInfo,
  TEntityWithBasicInfo,
  TOnFilterSelect,
  TTableActions,
  TTableActionsIdProp,
  handleModeOperationProps,
} from './typeDefs';
import type {
  TTableEntityObject,
  TSupportedSortingField,
  TSortingOrder,
} from '../SessionsTable/typeDefs';

const {
  ADD,
  ADD_EVENT,
  ADD_BULK,
  COPY,
  COPY_EVENT,
  UPDATE,
  UPDATE_EVENT,
  CANCEL,
  CANCEL_EVENT,
  SEARCH,
  REMOVE,
  LOAD_MORE,
} = OPERATIONS;

// eslint-disable-next-line max-statements
const SessionsWrapper = (props: SessionsWrapperProps) => {
  const {
    trackerPageName,
    context,
    sessionsMap,
    entityFilterHiddenMap,
    eventSessionListMap,
    entityStatsMap,
    operationStatus: { loadingData: { operation = {} } = {}, isLoading } = {},
    isILTSessionCalendarAutoSyncEnabled,
    sessions: { data: sessions },
    updateILTFilters,
    colSortOrder,
    setColSortOrder,
    colSortField,
    setColSortField,
    ILTFilters,
    companySettings,
  } = props;
  const [selected, setSelected] = useState<string | undefined>(undefined);
  const [selectedParent, setSelectedParent] = useState<string | undefined>(undefined);
  const [selectedEntityType, setSelectedEntityType] = useState<TILTEntities | undefined>(undefined);
  const [mode, setMode] = useState<string | undefined>(undefined);
  const [selectedEntities, setSelectedEntities] = useState<string[]>([]);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [resetClicked, setResetClicked] = useState(false);
  const [initialDrawerTab, setInitialDrawerTab] = useState<string | undefined>(undefined);
  const [initialEventDrawerTab, setInitialEventDrawerTab] = useState<string | undefined>(undefined);
  const [, setShowModLoader] = useState(false); // TODO: akhil remove this after testing finished
  const filters = props.ILTFilters;
  const isFiltered = !!Object.keys(prune(filters)).length;
  const currentPropsRef = useRef(props);
  const prevPropsRef = useRef(props);

  useEffect(() => {
    const queryParams = queryString.parse(window.location.search);
    const openDrawer = queryParams?.[OPEN_DRAWER_QUERY_PARAM];
    const eventId: string | undefined = Array.isArray(queryParams?.[EVENT_ID_QUERY_PARAM])
      ? queryParams?.[EVENT_ID_QUERY_PARAM][0] || undefined
      : queryParams?.[EVENT_ID_QUERY_PARAM] || undefined;

    if (openDrawer && openDrawer === CREATE_SESSION_QUERY_PARAM) {
      setMode(MODES.ADD);
      setSelected(undefined);
      if (eventId) {
        setSelectedEntityType(ILT_ENTITIES.SESSION_WITHIN_EVENT);
        setSelectedParent(eventId);
      } else {
        setSelectedEntityType(ILT_ENTITIES.SESSION);
        setSelectedParent(undefined);
      }
    } else if (openDrawer && openDrawer === CREATE_EVENT_QUERY_PARAM) {
      setMode(MODES.ADD);
      setSelected(undefined);
      setSelectedEntityType(ILT_ENTITIES.EVENT);
      setSelectedParent(undefined);
    }
  }, []);

  const tracker = useILTAdminSnowplowTracker();

  const handleChangedSessionSuccessfully = useCallback(() => {
    const { toastInfo } = getInfoAfterSingleSessionSaved({
      operationStatus: props.operationStatus,
    });
    toastInfo && multipleToast(toastInfo);
  }, [props.operationStatus]);

  useEffect(() => {
    // Set the current props to the prev props ref
    prevPropsRef.current = currentPropsRef.current;
    // Update the current props ref
    currentPropsRef.current = props;
  });

  const checkDataLoading = useCallback(
    (action: any) => {
      const {
        operationStatus: { isLoading, loadingData: { operation = {} } = {} },
      } = props;
      if (isUndefined(isLoading)) return;
      // eslint-disable-next-line eqeqeq
      if (action == operation) {
        return isLoading;
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps -- dependant props already included
    [props.operationStatus]
  );

  const changeSelected = ({ id, entityType, parentId }: TEntityWithBasicInfo) => {
    setSelected(id);
    setSelectedParent(parentId);
    setSelectedEntityType(entityType);
  };

  const tableActions: TTableActions = {
    onSelect: (selectedEntities: TEntitiesOnSelectInfo) => {
      setSelectedEntities(stateSelectedEntities => {
        stateSelectedEntities = [...stateSelectedEntities];
        const removeEntities: string[] = [];
        selectedEntities.forEach(entity => {
          entity.checked ? stateSelectedEntities.push(entity.id) : removeEntities.push(entity.id);
        });

        if (removeEntities.length) {
          stateSelectedEntities = stateSelectedEntities.filter(
            (id: string) => !removeEntities.includes(id)
          );
        }
        return stateSelectedEntities;
      });
    },
    deleteEntity: ({ id }: TEntityWithBasicInfo) => removeEntities([id]),
    editEntity: ({ id, entityType, parentId }: TEntityWithBasicInfo) => {
      setMode(MODES.EDIT);
      changeSelected({ id, entityType, parentId });
      setInitialDrawerTab(undefined);
      setInitialEventDrawerTab(undefined);
    },
    cancelEntity: ({ id, entityType, parentId }: TEntityWithBasicInfo) => {
      setMode(MODES.CANCEL);
      changeSelected({ id, entityType, parentId });
    },
    copyEntity: ({ id, entityType, parentId }: TEntityWithBasicInfo) => {
      setShowModLoader(true);
      changeSelected({ id, entityType, parentId });
      if (checkIsEvent(entityType)) {
        handleModeOperation({
          mode: MODES.COPY,
          data: [id],
          selectedEntity: { id, entityType, parentId },
        });
      } else {
        createSessionCopy(sessionsMap[id], { id, entityType, parentId });
      }
    },
    viewEntity: ({ id, entityType, parentId }: TEntityWithBasicInfo) => {
      setMode(MODES.VIEW);
      changeSelected({ id, entityType, parentId });
    },
    copyEntityUrl: ({ id, entityType, parentId }: TEntityWithBasicInfo) => {
      const isEvent = checkIsEvent(entityType);
      CopyToClipboard.copy({
        textToCopy: getIltSessionUrl(context, { id, entityType }),
        onCopy: (_text: string, result: any) =>
          result &&
          successToast({
            message: (
              <FormattedMessage
                {...(isEvent ? messages.EVENT_LINK_COPIED : messages.SESSION_LINK_COPIED)}
              />
            ),
          }),
      });
      sendEntityLinkCopyClicked(tracker, { pageName: trackerPageName, id, entityType, parentId });
    },
    sortColumn: (operation: any, sortOrder: TSortingOrder, sortField: TSupportedSortingField) => {
      setColSortField(sortField);
      setColSortOrder(sortOrder);
      handleOperation({ operation, sortOrder, sortField });
    },
    loadMoreEntities: () => handleOperation({ operation: LOAD_MORE }),
    manageCheckIn: ({ id, entityType, parentId }: TEntityWithBasicInfo) => {
      setMode(MODES.VIEW);
      changeSelected({ id, entityType, parentId });
      setInitialDrawerTab('attendance');
      sendSessionGetCheckinCodeClicked(tracker, {
        pageName: trackerPageName,
        id,
        name: props.sessionsMap[id].name,
      });
    },
    manageEventEnrollment: ({ id, entityType }: TTableActionsIdProp) => {
      setMode(MODES.VIEW);
      setSelected(id);
      setSelectedEntityType(entityType || ILT_ENTITIES.SESSION);
      setInitialEventDrawerTab('enrollment');
      sendEventSettingsClicked(tracker, { pageName: trackerPageName, id });
    },
    uploadSessionsWithinEvent: ({ parentId }: { parentId?: string } = {}) => {
      handleUploadSessions({ parentId });
    },
    createSessionWithinEvent: ({ parentId }: { parentId: string }) => {
      handleCreateSession({ parentId });
    },
    exportLiveChallengeReport: ({ id }: TTableActionsIdProp) => {
      setMode(MODE_EXPORT_LC_REPORT);
      setSelected(id);
      sendSessionEmailLCReportSubmitted(tracker, {
        pageName: trackerPageName,
        sessionId: id,
        sessionName: props.sessionsMap[id].name,
      });
    },
    openUploadRecordings: ({ id }: TTableActionsIdProp) => {
      props.actions.openUploadRecordings(props.sessionsMap[id]);
    },
  };

  // eslint-disable-next-line max-statements, complexity
  useEffect(() => {
    const {
      loaded: newLoaded,
      data: { operation = {} } = {},
      operation: currentOperation,
      error,
      hasError,
    } = props.operationStatus;
    const prevProps = prevPropsRef.current;
    const {
      operationStatus: { hasError: prevHasError },
    } = prevProps;
    const { loaded: oldLoaded } = prevProps.operationStatus;

    if (hasError && hasError !== prevHasError) {
      setShowModLoader(false);
      errorToast({ message: <FormattedMessage {...GET_ERROR_MESSAGES(error)} />, timeout: 3000 });
    }
    if (hasError && hasError !== prevHasError && [ADD, UPDATE, COPY].includes(currentOperation)) {
      if (error && error.data && error.data.WebConfErrorCode) {
        const message = <FormattedMessage {...GET_ERROR_MESSAGES(error.data.WebConfErrorCode)} />;
        if (message) {
          errorToast({ message, timeout: 3000 });
        }
      }
    }

    if (
      hasError &&
      hasError !== prevHasError &&
      (currentOperation === ADD_EVENT ||
        currentOperation === UPDATE_EVENT ||
        currentOperation === COPY_EVENT)
    ) {
      const message =
        currentOperation === COPY_EVENT
          ? messages.EVENT_COPY_FAILED
          : currentOperation === ADD_EVENT
          ? messages.EVENT_CREATE_FAILED
          : messages.EVENT_UPDATE_FAILED;
      errorToast({
        message: <FormattedMessage {...message} />,
        freeze: false,
        timeout: 3000,
      });
    }

    showOperationEffectMessages({
      trackerPageName,
      operationStatus: props.operationStatus,
      prevOperationStatus: prevProps.operationStatus,
      tracker,
    });

    switch (operation) {
      case ADD:
      case UPDATE:
      case COPY:
        if (newLoaded && oldLoaded !== newLoaded) {
          if (!hasError) {
            setMode(undefined);
            setShowModLoader(false);
            handleChangedSessionSuccessfully();
          }
        }
        break;
      case ADD_EVENT:
      case UPDATE_EVENT:
      case COPY_EVENT:
        if (newLoaded && oldLoaded !== newLoaded && !hasError) {
          setMode(undefined);
          setShowModLoader(false);
          const message =
            operation === COPY_EVENT
              ? messages.EVENT_COPIED_SUCCESS
              : operation === ADD_EVENT
              ? messages.EVENT_CREATED_SUCCESS
              : messages.EVENT_UPDATED_SUCCESS;
          successToast({
            message: <FormattedMessage {...message} />,
            freeze: false,
            timeout: 3000,
          });
        }
        break;
      default:
        break;
    }
    // call tracking events
    if (
      [
        ADD,
        ADD_EVENT,
        UPDATE,
        UPDATE_EVENT,
        COPY_EVENT,
        SEARCH,
        OPERATIONS.SHARE_CHECKIN_CODE,
      ].includes(operation) &&
      newLoaded &&
      oldLoaded !== newLoaded &&
      !hasError
    ) {
      const trackingParams = {
        operationStatus: props.operationStatus,
        extraTrackingProperties: {
          [SNOWPLOW_FIELD_NAMES.PAGE_NAME]: trackerPageName,
        },
      };
      if (COPY_EVENT === operation) {
        sendCopyIltEvent(tracker, trackingParams);
      } else {
        sendSessionOperation(tracker, trackingParams);
      }
    }
  }, [handleChangedSessionSuccessfully, props.operationStatus, tracker, trackerPageName]);

  const clearMode = () => {
    setMode(undefined);
    setSelected(undefined);
    setSelectedParent(undefined);
    setSelectedEntityType(undefined);
    setInitialDrawerTab(undefined);
  };

  const clearEventMode = () => {
    clearMode();
    setInitialEventDrawerTab(undefined);
  };

  const handleCreateSession = ({
    parentId,
    entityType,
  }: { parentId?: string; entityType?: TILTEntities } = {}) => {
    setMode(MODES.ADD);
    setSelected(undefined);
    setSelectedEntityType(
      entityType || (parentId ? ILT_ENTITIES.SESSION_WITHIN_EVENT : ILT_ENTITIES.SESSION)
    );
    setSelectedParent(parentId);
    sendCreateEntityClicked(tracker, { pageName: trackerPageName, entityType, parentId });
  };

  const handleCreateEvent = () => {
    handleCreateSession({ entityType: ILT_ENTITIES.EVENT });
  };

  const handleUploadSessions = ({ parentId }: { parentId?: string } = {}) => {
    if (parentId) {
      setSelectedParent(parentId);
    }
    setShowUploadModal(true);
    sendBulkUploadClickEvent(tracker, {
      pageName: trackerPageName,
      parentId,
    });
  };

  const createBulkSessions = (sessions: any, parentId?: string) => {
    handleModeOperation({
      mode: MODES.ADD_BULK,
      data: sessions,
      selectedEntity: { parentId },
    });
  };

  const handleModeOperation = ({
    mode,
    data,
    notifyLearnersOnCancellation = 'true',
    selectedEntity,
  }: handleModeOperationProps) => {
    let operation;
    const { entityType, parentId } = selectedEntity;
    const isEvent = checkIsEvent(entityType);
    switch (mode) {
      case MODES.EDIT:
        operation = isEvent ? UPDATE_EVENT : UPDATE;
        break;
      case MODES.ADD:
        operation = isEvent ? ADD_EVENT : ADD;
        break;
      case MODES.ADD_BULK:
        operation = ADD_BULK;
        break;
      case MODES.COPY:
        operation = isEvent ? COPY_EVENT : COPY;
        break;
      case MODES.CANCEL:
        operation = isEvent ? CANCEL_EVENT : CANCEL;
        break;
      default:
      // do nothings;
    }
    data = castArray(data);
    operation &&
      handleOperation({
        operation,
        sessions: data,
        parentId,
        notifyLearnersOnCancellation,
      });
  };

  const createSessionCopy = useCopySession({
    handleModeOperation,
    moduleDetails: props.moduleDetails,
  });

  const withConfirmation = ({
    operation,
    countInfo,
    callback,
  }: {
    operation: string;
    countInfo: any;
    callback: Function;
  }) => {
    let content = <></>,
      title = <></>,
      okButtonText = <FormattedMessage {...messages.OPERATION_CONFIRM_DEFAULT_OK_BUTTON_TEXT} />,
      confirmExtraProps: any = {};

    if (operation === REMOVE) {
      const {
        content: _content,
        okButtonText: _okButtonText,
        title: _title,
      } = getDeleteConfirmationMessages(countInfo);
      content = _content;
      okButtonText = _okButtonText;
      title = _title;
      confirmExtraProps = {
        isDestructiveAction: 'true',
        okButtonProps: { type: 'danger' },
        cancelButtonProps: { type: 'secondary' },
      };
    }

    Modal.confirm({
      prefixCls: THEME_PREFIX_CLS,
      content,
      centered: true,
      onOk: callback,
      okButtonText,
      title: title,
      ...confirmExtraProps,
    });
  };

  const handleOperation = ({
    operation,
    sessions = [],
    processIds = [],
    parentId,
    notifyLearnersOnCancellation = 'true',
    sortOrder = colSortOrder,
    sortField = colSortField,
    sessionsMap = {},
    isBulk = false, // for tracking purpose
  }: any) => {
    const { ILTFilters } = props;
    props.actions.manipulateData({
      operation,
      filters: prepareFilters(ILTFilters),
      sessions,
      processIds,
      parentId,
      notifyLearnersOnCancellation,
      sortOrder,
      sortField,
      sessionsMap,
      isBulk,
    });
  };
  const handleShareCheckInCode = ({
    sessionId,
    session,
    emailIds,
  }: {
    sessionId: string;
    session: any;
    emailIds: [string];
  }) => {
    props.actions.manipulateData({
      operation: OPERATIONS.SHARE_CHECKIN_CODE,
      sessionId,
      emailIds,
      sessions: [session],
    });
  };
  const multiSelectAction = {
    selectAll: (checked: boolean) => {
      setSelectedEntities(checked ? props.sessions.data : []);
    },
    unselectAll: () => {
      setSelectedEntities([]);
    },
    deleteEntities: () => {
      removeEntities(selectedEntities, { isBulk: true });
      sendBulkDeleteClicked(tracker, { pageName: trackerPageName });
    },
  };
  const removeEntities = (ids: string[] = [], { isBulk = false } = {}) => {
    const sessionsMap = props.sessionsMap;
    if (isEmpty(ids)) return;
    const {
      entitiesWithBasicInfo: entitiesWithInfo,
      allSessionsOfEventAreSelected,
      eventCount,
      sessionCount,
      sessionsWithinEventCount,
      allSessionCount,
    } = getSelectedEntitiesInfo({ ids, sessionsMap, eventSessionListMap });

    withConfirmation({
      operation: REMOVE,
      countInfo: {
        sessionCount,
        allSessionsOfEventAreSelected,
        allSessionCount,
        sessionsWithinEventCount,
        eventCount,
      },
      callback: () => {
        handleOperation({
          operation: REMOVE,
          processIds: entitiesWithInfo,
          sessionsMap: sessionsMap,
          isBulk, // for tracking purpose
        });
        multiSelectAction.unselectAll();
      },
    });
  };

  const onFilterSelect = useCallback(
    ({ filterType, data }: TOnFilterSelect) => {
      const updatedFilters = {
        ...ILTFilters,
        [filterType]: data,
      };
      updateILTFilters?.(updatedFilters);
      setSelectedEntities([]);
    },
    [ILTFilters, updateILTFilters]
  );

  const renderSessions = () => {
    const {
      sessions: { data: sessions, hasMore },
      moduleDetails: { invitedLearnersCount },
      haveBuildAccess,
      haveTrackAccess,
      sessionsMap,
      actions,
      ILTFilters,
      updateILTFilters,
    } = props;
    return (
      <StyledSessionWrapperContainer>
        <StyledFilterContainer>
          <SessionFilters
            ILTFilters={ILTFilters}
            updateILTFilters={updateILTFilters}
            manipulateData={actions.manipulateData}
            setResetClicked={setResetClicked}
            haveBuildAccess={haveBuildAccess}
            onFilterSelect={onFilterSelect}
            trackerPageName={trackerPageName}
          />
          <CreateEntitiesSplitButton
            onCreateSessionClick={handleCreateSession}
            onCreateEventClick={handleCreateEvent}
            onUploadSessionsClick={handleUploadSessions}
          />
        </StyledFilterContainer>
        <StyledSessionsTableContainer>
          <div className="marginT15 marginB50">
            <SessionsTable
              actions={tableActions}
              data={
                createSessionTableData({
                  sessions,
                  sessionsMap,
                  entityFilterHiddenMap,
                  eventSessionListMap,
                  entityStatsMap,
                }) as TTableEntityObject[]
              }
              hasMore={!!hasMore}
              loadMoreLoading={checkDataLoading(LOAD_MORE)}
              loading={checkDataLoading(SEARCH)}
              invitedLearnersCount={invitedLearnersCount}
              haveTrackAccess={haveTrackAccess}
              haveBuildAccess={haveBuildAccess}
              selectedSessions={selectedEntities}
              selectedCountInfo={getSelectionCountInfo({
                entityIds: selectedEntities,
                sessionsMap,
                eventSessionListMap,
              })}
              colSortField={colSortField}
              colSortOrder={colSortOrder}
              multiSelectAction={multiSelectAction}
            />
          </div>
        </StyledSessionsTableContainer>
      </StyledSessionWrapperContainer>
    );
  };

  const renderSessionEditDrawer = () => {
    const {
      sessionsMap,
      moduleDetails,
      operationStatus: { loadingData: { operation = {} } = {}, isLoading } = {},
      operationStatus,
      companyData,
      integrations,
      liveChallenge,
      context,
    } = props;
    return (
      <SessionEditDrawer
        visible={true}
        onClose={clearMode}
        integrations={integrations}
        data={(selected ? sessionsMap[selected as keyof typeof sessionsMap] : {}) || {}}
        mode={mode}
        companySettings={companySettings}
        moduleDetails={moduleDetails}
        domain={context.domain}
        liveChallenge={liveChallenge}
        resetNewLiveChallengeData={props.actions.resetNewLiveChallengeData}
        companyData={companyData}
        operationStatus={operationStatus}
        inProgress={[ADD, COPY, UPDATE].includes(operation) && isLoading}
        defaultScore={moduleDetails.scoring && moduleDetails.score}
        onShareCheckInCode={handleShareCheckInCode}
        update={(data: any) =>
          handleModeOperation({
            mode,
            data,
            selectedEntity: { parentId: selectedParent, entityType: selectedEntityType },
          })
        }
        initialDrawerTab={initialDrawerTab}
        triggerEditAction={tableActions.editEntity}
        defaultCompletionCriteria={moduleDetails.defaultSessionCompletionCriteria}
        setShowSessionModLoader={setShowModLoader}
        eventData={
          (selectedParent ? sessionsMap[selectedParent as keyof typeof sessionsMap] : {}) || {}
        }
        manageEventEnrollment={tableActions.manageEventEnrollment}
      />
    );
  };

  const renderEventEditDrawer = () => {
    const {
      sessionsMap,
      operationStatus: { loadingData: { operation = {} } = {}, isLoading } = {},
    } = props;
    return (
      <EventEditDrawer
        visible={true}
        onClose={clearEventMode}
        data={(selected ? sessionsMap[selected as keyof typeof sessionsMap] : {}) || {}}
        sessionsList={eventSessionListMap[selected || '']}
        mode={mode}
        inProgress={[ADD_EVENT, UPDATE_EVENT].includes(operation) && isLoading}
        update={(data: any) =>
          handleModeOperation({ mode, data, selectedEntity: { entityType: selectedEntityType } })
        }
        initialDrawerTab={initialEventDrawerTab}
        triggerEditAction={tableActions.editEntity}
        setShowEventModLoader={setShowModLoader}
      />
    );
  };

  const renderCancelModal = () => (
    <CancelSessionModal
      selectedCount={selectedEntities.length}
      sessionsMap={sessionsMap}
      selectedId={selected}
      inProgress={[CANCEL].includes(operation) && isLoading}
      isILTSessionCalendarAutoSyncEnabled={isILTSessionCalendarAutoSyncEnabled}
      handleModeOperation={handleModeOperation}
      clearMode={clearMode}
    />
  );

  const renderExportLCReportModal = () => {
    const session = sessionsMap[selected!];
    return (
      <ShareLiveChallengeModal
        sessionId={session.id}
        lcUniqueCode={session.liveChallenge!.uniqueCode}
        onCancel={clearMode}
        action={LC_MODAL_ACTIONS.EXPORT_REPORT}
      />
    );
  };

  const sessionActionMap = {
    [MODES.VIEW]: renderSessionEditDrawer,
    [MODES.CANCEL]: renderCancelModal,
    [MODES.EDIT]: renderSessionEditDrawer,
    [MODES.ADD]: renderSessionEditDrawer,
    [MODE_EXPORT_LC_REPORT]: renderExportLCReportModal,
  };

  const eventActionMap = {
    [MODES.VIEW]: renderEventEditDrawer,
    [MODES.CANCEL]: renderCancelModal,
    [MODES.EDIT]: renderEventEditDrawer,
    [MODES.ADD]: renderEventEditDrawer,
  };

  const renderSessionActionOverlay = () => {
    if (mode !== undefined && checkIsEvent(selectedEntityType)) {
      return eventActionMap[mode] && eventActionMap[mode]();
    }
    if (mode !== undefined) {
      return sessionActionMap[mode] && sessionActionMap[mode]();
    }
    return null;
  };

  const renderUploadModal = () =>
    showUploadModal && (
      <UploadSessions
        onClose={() => {
          setShowUploadModal(false);
          setSelectedParent(undefined);
        }}
        uploadSessions={createBulkSessions}
        integrations={props.integrations}
        defaultCompletionCriteria={props.moduleDetails.defaultSessionCompletionCriteria}
        trackerPageName={trackerPageName}
        eventData={
          (selectedParent ? sessionsMap[selectedParent as keyof typeof sessionsMap] : {}) || {}
        }
        sessionsList={eventSessionListMap[selectedParent || '']}
        moduleDetails={props.moduleDetails}
      />
    );

  //TODO(SUGATA): Check the actual logic when to show no data page, don't rely on SS logic
  return (
    <div>
      <EntitiesOperationModeLoader operationStatus={props.operationStatus} />
      {isEmpty(sessions) && !isFiltered && !resetClicked ? (
        <SessionsNoData
          onCreateSessionClick={handleCreateSession}
          onUploadSessionsClick={handleUploadSessions}
          onCreateEventClick={handleCreateEvent}
        />
      ) : (
        renderSessions()
      )}
      {renderSessionActionOverlay()}
      {renderUploadModal()}
    </div>
  );
};

SessionsWrapper.defaultProps = {
  operationStatus: {},
};

export default SessionsWrapper;
