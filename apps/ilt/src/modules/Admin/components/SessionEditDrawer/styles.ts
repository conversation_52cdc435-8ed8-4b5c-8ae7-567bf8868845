import styled, { css, createGlobalStyle } from 'styled-components';

import Drawer from '@mindtickle/drawer';
import { tokens, theme, mixins } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS } from '~/config/constants';

export const tabContainerCommonMixin = () => css`
  margin-bottom: 16px;
`;

// To fix body scroll happening after toast hidden
export const BodyStylesDuringDrawer = createGlobalStyle`
body {
  overflow: hidden !important;
}
`;

export const StyledSessionDrawer = styled(Drawer)`
  .${THEME_PREFIX_CLS}-drawer-content {
    border-radius: 0;
  }
  .${THEME_PREFIX_CLS}-drawer-footer {
    background: ${tokens.bgTokens.COLOR_BG_TERTIARY};
  }
`;

export const StyledFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  flex-direction: row-reverse;
  margin: 10px auto 10px 15px;
`;

export const StyledTabContainer = styled.div<{ attendanceTabHasErrors?: boolean }>`
  height: 100%;

  > .${THEME_PREFIX_CLS}-tabs-left {
    > .${THEME_PREFIX_CLS}-tabs-nav {
      & .${THEME_PREFIX_CLS}-tabs-tab {
        ${mixins.blackText()}
        padding-left: 24px;
        padding-right: 24px;
        margin: 0 0 8px 0;
        width: ${props => (props.attendanceTabHasErrors ? '258px' : '250px')};
        .${THEME_PREFIX_CLS}-tabs-tab-btn {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .tab-error {
            color: ${tokens.textTokens.COLOR_TEXT_DANGER};
          }
        }
        &:hover {
          background-color: ${tokens.bgTokens.COLOR_BG_ACCENT_SELECTED};
        }
      }
      & .${THEME_PREFIX_CLS}-tabs-tab-active {
        background: ${tokens.bgTokens.COLOR_BG_ACCENT_SELECTED};
      }
      & .${THEME_PREFIX_CLS}-tabs-ink-bar {
        display: none;
      }
      & .${THEME_PREFIX_CLS}-tabs-nav-wrap {
        padding: 0px;
      }
      & .${THEME_PREFIX_CLS}-form-item-label > label {
        display: block;
        font-size: 13px;
        margin-bottom: 7px;
        color: ${tokens.deprecatedTokens.COLOR_DEPRECATED_NAVBAR2};
        margin-bottom: 2px;
        line-height: 20px;
      }
    }
    > .${THEME_PREFIX_CLS}-tabs-content-holder {
      & .${THEME_PREFIX_CLS}-tabs-content {
        padding-left: 0px !important;
        padding-right: 0px !important;
        > .${THEME_PREFIX_CLS}-tabs-tabpane-active {
          padding-left: 32px;
        }
      }
    }
  }

  .${THEME_PREFIX_CLS}-form-item-label, .${THEME_PREFIX_CLS}-form-item-label > label {
    font-family: ${theme.fontFamily.DEFAULT};
    font-style: normal;
    font-weight: 600;
    font-size: 12px;
    line-height: 16px;
    color: ${tokens.textTokens.COLOR_TEXT_DEFAULT};
  }

  h3.${THEME_PREFIX_CLS}-typography {
    margin-bottom: 24px;
    margin-top: 24px;
  }
`;

export const StyledLabel = styled.label`
  font-family: ${theme.fontFamily.DEFAULT};
  font-style: normal;
  font-weight: 600;
  font-size: 12px;
  line-height: 16px;
  color: ${tokens.textTokens.COLOR_TEXT_DEFAULT};
  margin: 6px 0 4px 0;
  display: flex;
  align-items: center;
  & .infoHover {
    padding: 0 8px;
    color: ${tokens.iconTokens.COLOR_ICON_DEFAULT};
    font-size: 14px;
  }
`;

export const StyledToastWrapper = styled.div`
  position: absolute;
  top: 56px;
  left: 50%;
  transform: translate(-50%);
  z-index: 9990;
`;
