import _defer from 'lodash/defer';
import moment from 'moment';

import type { Moment } from 'moment';
export const dataURItoBlob = (dataURI: string): Blob => {
  // convert base64 to raw binary data held in a string
  // doesn't handle URLEncoded DataURIs - see SO answer #6850276 for code that does this
  const byteString = atob(dataURI.split(',')[1]);

  // separate out the mime component
  const mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0];

  // write the bytes of the string to an ArrayBuffer
  const ab = new ArrayBuffer(byteString.length);

  // create a view into the buffer
  const ia = new Uint8Array(ab);

  // set the bytes of the buffer to the correct values
  for (let i = 0; i < byteString.length; i++) {
    ia[i] = byteString.charCodeAt(i);
  }

  // write the ArrayBuffer to a blob, and you're done
  const blob = new Blob([ab], { type: mimeString });
  return blob;
};

export const generatePNGfromHTML = (...args: any[]): void => {
  _defer(_generatePNGfromHTML, ...args);
};
export const _generatePNGfromHTML = async (
  domElement: HTMLElement,
  fileName: string,
  onComplete: (data: any) => void
) => {
  const { default: html2canvas } = await import('html2canvas');
  const startTime = Date.now();
  html2canvas(domElement, {
    useCORS: true,
  }).then(canvas => {
    downloadImage({ imageData: canvas.toDataURL('image/png'), fileName, startTime }, onComplete);
  });
};

const downloadImage = (
  { imageData, fileName, startTime }: { imageData: string; fileName: string; startTime: number },
  onComplete: (data: any) => void
): void => {
  let link = document.createElement('a');
  link.download = `${fileName}.png`;
  let blob = dataURItoBlob(imageData);
  link.href = URL.createObjectURL(blob);
  link.click();
  URL.revokeObjectURL(link.href);
  const endTime = Date.now();
  const imageGenerationTime = endTime - startTime;
  onComplete({ imageGenerationTime });
};

export const convertTimeToSeconds = (hours: number, mins: number): number =>
  hours * 60 * 60 + mins * 60;

export const convertSecondsToTime = (secs: number): { hours: number; minutes: number } => {
  let minutes = Math.floor(secs / 60);
  secs = secs % 60;
  let hours = Math.floor(minutes / 60);
  minutes = minutes % 60;
  return { hours, minutes };
};

export const getSessionCheckinFormattedTime = (
  from: number = 0,
  to: number = 0,
  showEndTime: boolean = true
): string => {
  let fromTime: Moment | string = moment(from);
  let toTime: Moment | string = moment(to);

  if (fromTime.day() === toTime.day()) {
    toTime = toTime.format('  h:mm a');
  } else {
    toTime = toTime.format('MMM DD, h:mm a');
  }
  fromTime = fromTime.format('MMM DD, h:mm a');
  return `${!showEndTime ? 'Starts from ' : ''} ${fromTime} ${showEndTime ? ` - ${toTime}` : ''}`;
};

export const minutesToHourMinutes = (mins: number = 0): { hours: number; minutes: number } => ({
  hours: Math.floor(mins / 60),
  minutes: mins % 60,
});
