import emailValidator from 'email-validator';
import { FormattedMessage } from 'react-intl';

import Checkbox from '@mindtickle/checkbox';
import Divider from '@mindtickle/divider';
import Tabs from '@mindtickle/tabs';
import Tooltip from '@mindtickle/tooltip';
import { Title } from '@mindtickle/typography';

import {
  WEBINAR_MEETING_TYPES,
  ADD_AS_MEETING_COHOST_TEXT,
} from '~/modules/Admin/config/sessions.constants';
import {
  getWebinarMeetingSettingsKeyByLocationType,
  getWebinarMeetingMaxCohostLengthByLocationType,
} from '~/modules/Admin/utils/sessionEdit';

import { DRAWER_INSTRUCTOR_FACILITATOR_TABS } from '../../constants';

import FacilitatorSection from './components/FacilitatorSection';
import InstructorSection from './components/InstructorSection';
import messages from './messages';
import {
  StyledInstructorFacilitatorOverallTabContainer,
  StyledInstructorFacilitatorTabContainer,
  verticalDividerStyle,
} from './styles';

import type { TInstructorFacilitator } from './typeDefs';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';

const InstructorFacilitator = (props: TInstructorFacilitator) => {
  const {
    session,
    session: { instructors = [], facilitators = [], locationType },
    setSession,
    updateSessionState,
    userAuth,
    isPublishedOngoingPastSession,
    activeInstructorFacilitatorTab,
    setActiveInstructorFacilitatorTab,
    isReadOnlyModeEnabled,
    form,
  } = props;

  const meetingSettingsKey = getWebinarMeetingSettingsKeyByLocationType(locationType);
  const meetingSettings = session[meetingSettingsKey];
  const MAX_COHOST_LENGTH = getWebinarMeetingMaxCohostLengthByLocationType(locationType);

  const updateCohost = ({ email, updateType }: { email: string; updateType: string }) => {
    if (WEBINAR_MEETING_TYPES.includes(locationType) && !isPublishedOngoingPastSession) {
      const { coHosts = [] } = meetingSettings ?? {};
      const _email = email.toLowerCase();
      let _coHosts;
      if (updateType === 'remove') {
        _coHosts = coHosts.filter((value: any) => value.email.toLowerCase() !== _email);
      } else if (updateType === 'add') {
        _coHosts = [...coHosts, { email: _email }];
      }

      updateSessionState({
        key: meetingSettingsKey,
        value: {
          ...meetingSettings,
          coHosts: _coHosts,
        },
      });
    }
  };

  const removeCohost = (email: any) => {
    if (email) {
      if (typeof email !== 'string') {
        email = email.email;
      }
      email && updateCohost({ email, updateType: 'remove' });
    }
  };

  const isPresentInCohosts = (email: string) => {
    const { coHosts = [] } = meetingSettings ?? {};
    const _email = (email || '').toLowerCase();
    return coHosts.some((coHost: any) => coHost.email.toLowerCase() === _email);
  };

  const getCohostCheckbox = (
    email: string,
    checkboxName: string,
    { isFacilitator = false }: { isFacilitator?: boolean } = {}
  ) => {
    if (WEBINAR_MEETING_TYPES.includes(locationType)) {
      const { coHosts = [] } = meetingSettings ?? {};
      const checked = isPresentInCohosts(email);
      const cohostLimitExceeded = coHosts.length >= MAX_COHOST_LENGTH && !checked;
      const disabled =
        !emailValidator.validate(email) || isPublishedOngoingPastSession || cohostLimitExceeded;

      return (
        <div>
          <Divider style={verticalDividerStyle} type="vertical" />
          <Tooltip
            overlayClassName={'tooltipInfo'}
            placement="top"
            title={
              disabled ? (
                <p>
                  {isPublishedOngoingPastSession ? (
                    <FormattedMessage {...messages.COHOST_DISABLED_PAST_SESSION} />
                  ) : cohostLimitExceeded ? (
                    <FormattedMessage {...messages.MAX_COHOST_LIMIT_REACHED} />
                  ) : isFacilitator ? (
                    <FormattedMessage {...messages.COHOST_DISABLED_EMPTY_FACILITATOR} />
                  ) : (
                    <FormattedMessage {...messages.COHOST_DISABLED_EMPTY_INSTRUCTOR} />
                  )}
                </p>
              ) : null
            }
          >
            <Checkbox
              name={checkboxName}
              disabled={isReadOnlyModeEnabled || disabled}
              checked={checked}
              onChange={(e: CheckboxChangeEvent) => {
                updateCohost({
                  email: email,
                  updateType: e.target.checked ? 'add' : 'remove',
                });
              }}
            />
          </Tooltip>

          <span className="addCohostText">
            <FormattedMessage {...ADD_AS_MEETING_COHOST_TEXT} />
          </span>
        </div>
      );
    }
    return <></>;
  };

  return (
    <StyledInstructorFacilitatorOverallTabContainer className={'instructorContainer'}>
      <Title level={3}>Instructors and Facilitators</Title>
      <StyledInstructorFacilitatorTabContainer>
        <Tabs
          defaultActiveKey={DRAWER_INSTRUCTOR_FACILITATOR_TABS.INSTRUCTOR_SECTION}
          activeKey={activeInstructorFacilitatorTab}
          onChange={setActiveInstructorFacilitatorTab}
          tabPosition={'top'}
          tabBarGutter={24}
        >
          <Tabs.TabPane
            key={DRAWER_INSTRUCTOR_FACILITATOR_TABS.INSTRUCTOR_SECTION}
            tab={`Instructors (${instructors.length})`}
            forceRender={true}
          >
            <InstructorSection
              session={session}
              setSession={setSession}
              updateSessionState={updateSessionState}
              userAuth={userAuth}
              getCohostCheckbox={getCohostCheckbox}
              removeCohost={removeCohost}
              isReadOnlyModeEnabled={isReadOnlyModeEnabled}
              form={form}
            />
          </Tabs.TabPane>
          <Tabs.TabPane
            key={DRAWER_INSTRUCTOR_FACILITATOR_TABS.FACILITATOR_SECTION}
            tab={`Facilitators (${facilitators.length})`}
            forceRender={true}
          >
            <FacilitatorSection
              session={session}
              setSession={setSession}
              updateSessionState={updateSessionState}
              getCohostCheckbox={getCohostCheckbox}
              removeCohost={removeCohost}
              isReadOnlyModeEnabled={isReadOnlyModeEnabled}
              form={form}
            />
          </Tabs.TabPane>
        </Tabs>
      </StyledInstructorFacilitatorTabContainer>
    </StyledInstructorFacilitatorOverallTabContainer>
  );
};
export default InstructorFacilitator;
