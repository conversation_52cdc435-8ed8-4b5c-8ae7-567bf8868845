import messages from '../messages';

import { UseAuthModalState, AuthModalContent, AuthModalFlow } from './typeDefs';

export const LOADER_TIMEOUT = 1000 * 60 * 2;

export const authModalContent: Record<AuthModalFlow, AuthModalContent> = {
  hostEmail: {
    modalHeader: messages.ASSIGN_HOST,
    primaryCTAText: messages.CREATE_MEETING,
    secondaryCTAText: messages.AUTHENTICATE_ACCOUNT,
  },
  authenticate: {
    modalHeader: messages.AUTHENTICATE_ACCOUNT,
    primaryCTAText: messages.AUTHENTICATE_CTA,
    secondaryCTAText: messages.ENTER_HOST_EMAIL,
  },
};

// Define the initial state
export const initialState: UseAuthModalState = {
  flow: 'authenticate',
  content: authModalContent.authenticate,
  hostEmail: '',
  asyncStatus: 'idle',
  isSubmitDisabled: false,
  validationError: '',
};
