import { useCallback } from 'react';

import { FormattedMessage } from 'react-intl';

import { hoursToMiliseconds } from '@mindtickle/utils';

import { getTimezoneObject } from '~/modules/Admin/components/SessionsWrapper/utils';
import {
  WEBINAR_MEETING_TYPES,
  LOCATION_TYPE,
  ENROLLMENT_FREEZE_STATUSES,
  MESSAGES,
} from '~/modules/Admin/config/sessions.constants';
import { EVENT_ENROLLMENT_ENUM } from '~/modules/Admin/constants/events';
import { parseEventEnrollmentFreezeEpoch } from '~/modules/Admin/utils/eventEdit';
import { getWebinarValidationMessage } from '~/modules/Admin/utils/sessionEdit';
import { addOffsetDiff } from '~/utils/timezone';

import SessionTimingsSelector from './components/SessionTimingsSelector';

import type { TGetDateTimeValidationMessage, TSessionTime } from '../../typeDefs';

const getDateTimeValidationMessage = ({
  sessionStartTime,
  sessionTimezone,
  locationType,
  meetingValidationEnum,
  eventEnrollmentDetails,
  switchToEventDrawer,
}: TGetDateTimeValidationMessage) => {
  let dateTimeValidationErrors = [];
  const {
    enrollmentType,
    enrollmentFreezeStatus,
    enrollmentFreezeDaysBeforeEvent,
    enrollmentFreezeEpoch,
    enrollmentFreezeTimezone,
    eventStartTime,
  } = eventEnrollmentDetails;
  const enrollmentFreezeTimezoneObj = getTimezoneObject(enrollmentFreezeTimezone);
  const sessionTimezoneOffset = hoursToMiliseconds(sessionTimezone?.offset);
  const sessionStartTimeWithOffset = addOffsetDiff(sessionStartTime, sessionTimezoneOffset);
  const eventEnrollmentFreezeTime = parseEventEnrollmentFreezeEpoch({
    startTime: eventStartTime ? eventStartTime : sessionStartTimeWithOffset,
    enrollmentFreezeStatus,
    enrollmentFreezeEpoch,
    enrollmentFreezeDaysBeforeEvent,
    enrollmentFreezeTimezone: enrollmentFreezeTimezoneObj,
  });

  const addEventDateTimeValidationError = (message: any) => {
    dateTimeValidationErrors.push(
      <div key="freezeError">
        <FormattedMessage {...message} />
        <span className="drawer-change-cta" onClick={switchToEventDrawer}>
          Go to event settings
        </span>
      </div>
    );
  };

  if (WEBINAR_MEETING_TYPES.includes(locationType) && meetingValidationEnum) {
    dateTimeValidationErrors.push(
      <span key="webinarError">{getWebinarValidationMessage(meetingValidationEnum)}</span>
    );
  }
  if (
    enrollmentType === EVENT_ENROLLMENT_ENUM.EVENT &&
    enrollmentFreezeStatus !== ENROLLMENT_FREEZE_STATUSES.DISABLED
  ) {
    if (eventEnrollmentFreezeTime < new Date().getTime()) {
      addEventDateTimeValidationError(MESSAGES.INFO.FREEZE_ENROLLMENT_PAST_EVENT);
    } else if (sessionStartTimeWithOffset < eventEnrollmentFreezeTime) {
      addEventDateTimeValidationError(MESSAGES.INFO.ENROLLMENT_FREEZE_BEFORE_EVENT);
    }
  }

  if (dateTimeValidationErrors.length === 0) return undefined;

  return <>{dateTimeValidationErrors}</>;
};

const SessionTime = (props: TSessionTime) => {
  const {
    updateSessionState,
    isReadOnlyModeEnabled,
    eventData,
    switchToEventDrawer,
    setIsSessionTimeModified,
    isSessionTimeModified,
  } = props;
  const handleTiming = useCallback(
    (type: string, value: any, isTimeUpdated?: any) => {
      let key;
      switch (type) {
        case 'END':
          key = 'endTime';
          break;
        case 'START':
          key = 'startTime';
          break;
        case 'TIMEZONE':
          key = 'timezone';
          break;
        case 'MINSTART':
          key = 'minStartTime';
          break;
        default:
      }
      setIsSessionTimeModified(isSessionTimeModified || isTimeUpdated || false);
      key && updateSessionState({ key, value });
    },
    [updateSessionState, isSessionTimeModified]
  );

  const {
    session: {
      startTime,
      endTime,
      duration,
      timezone,
      currentOffset,
      minStartTime,
      enrollmentFreezeStatus,
      locationType,
    },
    changeTimezoneListLoadingState,
    webexMeetingStatusTrack: { meetingInvalidTimeEnum: webexMeetingInvalidTimeEnum },
    zoomMeetingStatusTrack: { meetingInvalidTimeEnum: zoomMeetingInvalidTimeEnum },
    msteamsMeetingStatusTrack: { meetingInvalidTimeEnum: msteamsMeetingInvalidTimeEnum },
    isPublishedOngoingPastSession,
  } = props;
  const sessionTimezoneObj = getTimezoneObject(timezone);

  const disableTimeEdit = isPublishedOngoingPastSession;

  // Generalize for zoom/webex
  const meetingValidationEnum =
    locationType === LOCATION_TYPE.WEBEX_MEETING
      ? webexMeetingInvalidTimeEnum
      : locationType === LOCATION_TYPE.MS_TEAMS_MEETING
      ? msteamsMeetingInvalidTimeEnum
      : zoomMeetingInvalidTimeEnum;
  const dateTimeValidationErrors = getDateTimeValidationMessage({
    sessionStartTime: startTime,
    sessionTimezone: sessionTimezoneObj,
    locationType,
    meetingValidationEnum,
    eventEnrollmentDetails: {
      enrollmentType: eventData?.enrollmentType,
      enrollmentFreezeEpoch: eventData?.enrollmentFreezeEpoch,
      enrollmentFreezeStatus: eventData?.enrollmentFreezeStatus,
      enrollmentFreezeDaysBeforeEvent: eventData?.enrollmentFreezeDaysBeforeEvent,
      enrollmentFreezeTimezone: eventData?.enrollmentFreezeTimezone,
      eventStartTime: eventData?.startTime,
    },
    switchToEventDrawer,
  });

  const enrollmentFreezeEnabled = enrollmentFreezeStatus !== ENROLLMENT_FREEZE_STATUSES.DISABLED;

  return (
    <SessionTimingsSelector
      startTime={startTime}
      endTime={endTime}
      duration={duration?.value}
      dateTimeValidationErrors={dateTimeValidationErrors}
      update={handleTiming}
      timezone={timezone}
      changeTimezoneListLoadingState={changeTimezoneListLoadingState}
      currentOffset={currentOffset}
      minStartTime={minStartTime}
      disableTimeEdit={disableTimeEdit}
      enrollmentFreezeEnabled={enrollmentFreezeEnabled}
      isReadOnlyModeEnabled={isReadOnlyModeEnabled}
    />
  );
};

export default SessionTime;
