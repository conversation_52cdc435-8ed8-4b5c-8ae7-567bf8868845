import styled from 'styled-components';

import TextButton from '@mindtickle/button/lib/TextButton';
import { tokens } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS } from '~/config/constants';

import { tabContainerCommonMixin } from '../../styles';

export const StyledSessionDetails = styled.div`
  ${tabContainerCommonMixin()}
  padding-right: 32px;
  .session-description {
    .ql-disabled {
      color: ${tokens.textTokens.COLOR_TEXT_SECONDARY};
      background-color: ${tokens.bgTokens.COLOR_BG_DISABLED} !important;
      border-color: ${tokens.borderTokens.COLOR_BORDER_DEFAULT} !important;
      box-shadow: none;
      cursor: not-allowed;
      opacity: 0.5;
    }
  }
  && .${THEME_PREFIX_CLS}-form-item {
    margin-bottom: 16px;
  }
  .${THEME_PREFIX_CLS}-form-item-label > label::before {
    display: none !important;
  }
  .score-input {
    width: 115px;
  }

  .session-details-divider {
    margin-bottom: 19px;
  }
`;

export const StyledSessionDateLocationWrapper = styled.div``;

export const StyledSessionDetailWrapper = styled.div`
  & .infoHover {
    padding: 0 12px;
    color: ${tokens.textTokens.COLOR_TEXT_SECONDARY};
    font-size: 14px;
  }
  .description-readonly-overlay {
    margin-top: 16px;
  }
`;

export const StyledTextButton = styled(TextButton)`
  &.${THEME_PREFIX_CLS}-btn {
    display: flex;
    font-size: 15px;
    justify-content: center;
    align-items: center;
  }

  &.${THEME_PREFIX_CLS}-btn-text {
    color: ${tokens.textTokens.COLOR_TEXT_ACCENT};
  }
`;

export const StyledHeader = styled.div`
  display: flex;
  justify-content: space-between;
`;
