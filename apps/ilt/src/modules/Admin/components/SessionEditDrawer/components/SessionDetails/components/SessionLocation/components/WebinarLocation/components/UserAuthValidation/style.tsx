import styled from 'styled-components';

import Icon from '@mindtickle/icon';
import { tokens } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS } from '~/config/constants';

export const StyledIcon = styled(Icon)`
  font-size: 16px;
  color: ${tokens.textTokens.COLOR_TEXT_DANGER};
  margin-right: 12px;
`;

export const LoaderWrapper = styled.div`
  display: flex;
  align-items: center;
  margin-top: 16px;
`;

export const Wrapper = styled.div`
  width: 100%;
  .error-wrapper {
    margin-top: 16px;
    background-color: ${tokens.bgTokens.COLOR_BG_DANGER};
    padding: 8px 16px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    .bold {
      font-weight: 600;
    }
    .icon-wrapper {
      display: flex;
      align-items: center;
    }
    .button-wrapper {
      margin-left: 8px;
      display: flex;
      .${THEME_PREFIX_CLS}-btn-text {
        color: ${tokens.textTokens.COLOR_TEXT_ACCENT};
      }
      .or {
        display: flex;
        align-items: center;
      }
    }
  }
`;
