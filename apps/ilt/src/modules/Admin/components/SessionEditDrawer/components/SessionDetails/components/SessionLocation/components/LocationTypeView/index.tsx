import InnerHTML from '@mindtickle/string-to-html';

import { LOCATION_TYPE, WEBINAR_MEETING_TYPES } from '~/modules/Admin/config/sessions.constants';
import {
  getWebinarSourceByLocationType,
  getWebinarMeetingSettingsKeyByLocationType,
} from '~/modules/Admin/utils/sessionEdit';
import { urlify } from '~/utils';

import ClassroomLocation from '../ClassroomLocation';
import WebinarLocation from '../WebinarLocation';
import { StyledAutoAttendanceToggle } from '../WebinarLocation/styles';
import WebinarMeeting from '../WebinarMeetings';

import type { TLocationTypeView } from '../../typeDefs';

const LocationTypeView = (props: TLocationTypeView) => {
  const {
    mode,
    session,
    updateSessionState,
    updateWebexMeetingStatus,
    updateZoomMeetingStatus,
    updateMSTeamsMeetingStatus,
    isOngoingOrPastSession,
    integrationsInfo,
    onUrlEligibleForValidation,
    onClear,
    manuallyFace2FaceToLink,
    setIsClassroomToLink,
    isReadOnlyModeEnabled,
  } = props;
  const { locationType, location, webAutoAttendanceSettings, instructors, facilitators } =
    session || {};

  const getUpdateMeetingStatus = (locationType: string) => {
    if (locationType === LOCATION_TYPE.WEBEX_MEETING) {
      return updateWebexMeetingStatus;
    }
    if (locationType === LOCATION_TYPE.MS_TEAMS_MEETING) {
      return updateMSTeamsMeetingStatus;
    }
    return updateZoomMeetingStatus;
  };

  if (locationType === LOCATION_TYPE.FACE_TO_FACE) {
    return (
      <>
        {isReadOnlyModeEnabled ? (
          <InnerHTML content={urlify(location)} className="session-location-view" />
        ) : (
          <ClassroomLocation
            session={session}
            updateSessionState={updateSessionState}
            integrationsInfo={integrationsInfo}
            onUrlEligibleForValidation={onUrlEligibleForValidation}
          />
        )}
      </>
    );
  }
  if (locationType === LOCATION_TYPE.LINK) {
    const { isAutoAttendanceEnabled, isLinkValidated, webinarSource } = webAutoAttendanceSettings;
    return (
      <>
        {isReadOnlyModeEnabled ? (
          <>
            <InnerHTML content={urlify(location)} className="session-location-view" />
            {isLinkValidated && (
              <StyledAutoAttendanceToggle
                isAutoAttendanceEnabled={isAutoAttendanceEnabled}
                webinarSource={webinarSource}
                isReadOnlyModeEnabled={isReadOnlyModeEnabled}
              />
            )}
          </>
        ) : (
          <WebinarLocation
            session={session}
            integrationsInfo={integrationsInfo}
            updateSessionState={updateSessionState}
            mode={mode}
            validateUrlOnMount={manuallyFace2FaceToLink}
            resetValidateUrlOnMount={setIsClassroomToLink}
            isReadOnlyModeEnabled={isReadOnlyModeEnabled}
          />
        )}
      </>
    );
  }
  if (WEBINAR_MEETING_TYPES.includes(locationType)) {
    const webinarSource = getWebinarSourceByLocationType(locationType);
    const meetingSettingsKey = getWebinarMeetingSettingsKeyByLocationType(locationType);
    const updateMeetingStatus = getUpdateMeetingStatus(locationType);
    const meetingSettings = session[meetingSettingsKey];

    return (
      <WebinarMeeting
        onClose={onClear}
        location={location}
        instructors={instructors}
        facilitators={facilitators}
        updateMeetingStatus={updateMeetingStatus}
        webAutoAttendanceSettings={webAutoAttendanceSettings}
        webinarMeetingSettings={meetingSettings}
        updateSessionState={updateSessionState}
        isOngoingOrPastSession={isOngoingOrPastSession}
        webinarSource={webinarSource}
        isReadOnlyModeEnabled={isReadOnlyModeEnabled}
        sessionStartDate={session.startTime}
        sessionEndDate={session.endTime}
        sessionName={session.name}
      />
    );
  }
  return <></>; // This will never execute
};

export default LocationTypeView;
