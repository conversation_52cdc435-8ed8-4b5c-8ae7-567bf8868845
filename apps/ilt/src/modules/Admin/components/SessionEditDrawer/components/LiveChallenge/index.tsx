// import React from 'react';
import { useEffect, useState } from 'react';

import Button from '@mindtickle/button';
import IconWithGradient, { ICON_MAP } from '@mindtickle/icon-with-gradient';
import { Title } from '@mindtickle/typography';

import { LC_OPERATIONS } from '../../../../config/live-challenges.constants';
import { MODES } from '../../../../config/sessions.constants';

import CreateLiveChallengeModal from './components/CreateLiveChallengeModal/index';
import DisplayLiveChallenge from './components/DisplayLiveChallenge';
import {
  StyledLCTabContainer,
  StyledCreateLiveChallengeContainer,
  liveChallengeIconStyle,
} from './styles';

import type { TLiveChallenge } from './typeDefs';

const LiveChallenge = (props: TLiveChallenge) => {
  const {
    mode,
    newLiveChallenge,
    liveChallenge,
    deleteLiveChallenge,
    hasSessionEnded,
    hasSessionStarted,
    isSessionPublished,
    sessionId,
    isLCAbsent,
    isReadOnlyModeEnabled,
    domain,
  } = props;

  const [isLCModalOpen, setLCModalOpen] = useState(false);

  useEffect(() => {
    if (newLiveChallenge.loaded || newLiveChallenge.hasError) {
      closeModal();
    }
  }, [newLiveChallenge.loaded, newLiveChallenge.hasError]);

  const closeModal = () => {
    setLCModalOpen(false);
  };

  const openModal = () => {
    setLCModalOpen(true);
  };

  const handleCreateLiveChallenge = () => {
    openModal();
  };

  const getLcOperation = (mode: string) => {
    let operation: string;
    switch (mode) {
      case MODES.ADD: {
        operation = newLiveChallenge.data ? LC_OPERATIONS.LC_EDIT : LC_OPERATIONS.LC_ADD;
        break;
      }
      case MODES.EDIT: {
        operation = liveChallenge ? LC_OPERATIONS.LC_EDIT : LC_OPERATIONS.LC_ADD;
        break;
      }
    }
    return operation!;
  };

  const hasLCEnded = !!(liveChallenge && liveChallenge.hasEnded);
  const lcData =
    newLiveChallenge.data ||
    (liveChallenge && {
      uniqueCode: liveChallenge.uniqueCode,
    });
  const isNewLcUploadProcessing = newLiveChallenge.isLoading;
  const isLcEdited = !!newLiveChallenge.data;

  return (
    <StyledLCTabContainer>
      <Title level={3}>In-session activity</Title>
      {!hasSessionStarted && isLCAbsent && (
        <StyledCreateLiveChallengeContainer>
          <IconWithGradient type={ICON_MAP.CREATE_LIVE_CHALLENGE} style={liveChallengeIconStyle} />
          <p>
            Create a live quiz game as an in-session activity to engage your audience. The moderator
            can start the quiz anytime during the session
          </p>
          {!isReadOnlyModeEnabled && (
            <Button type="primary" onClick={handleCreateLiveChallenge} className="createLCButton">
              Create live challenge
            </Button>
          )}
        </StyledCreateLiveChallengeContainer>
      )}

      {!isLCAbsent && (
        <DisplayLiveChallenge
          openModal={openModal}
          hasLCEnded={hasLCEnded}
          lcData={lcData}
          deleteLiveChallenge={deleteLiveChallenge}
          hasSessionEnded={hasSessionEnded}
          hasSessionStarted={hasSessionStarted}
          sessionId={sessionId}
          isSessionPublished={isSessionPublished}
          isLcEdited={isLcEdited}
          isReadOnlyModeEnabled={isReadOnlyModeEnabled}
          domain={domain}
        />
      )}

      <CreateLiveChallengeModal
        visible={isLCModalOpen}
        closeModal={closeModal}
        operation={getLcOperation(mode)}
        isProcessing={isNewLcUploadProcessing}
      />
    </StyledLCTabContainer>
  );
};

export default LiveChallenge;
