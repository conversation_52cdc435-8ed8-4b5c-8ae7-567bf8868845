import styled from 'styled-components';

import { mixins } from '@mindtickle/styles/lib';

import { tabContainerCommonMixin } from '../../styles';

export const StyledCompletionCriteriaContainer = styled.div``;

export const StyledRadioWrapper = styled.div`
  margin-top: 12px;
  margin-bottom: 15px;

  .completion-text {
    ${mixins.blackLink}
    margin-bottom: 16px;
  }

  .completion-criteria-radio-group {
    display: block;

    .radio-option-style {
      display: block;
      margin-bottom: 8px;
    }
  }
`;

export const StyledAttendanceCompletionWrapper = styled.div`
  ${tabContainerCommonMixin()}
  padding-right: 32px;

  .attendance-checkin-divider {
    margin-bottom: 19px;
  }
`;
