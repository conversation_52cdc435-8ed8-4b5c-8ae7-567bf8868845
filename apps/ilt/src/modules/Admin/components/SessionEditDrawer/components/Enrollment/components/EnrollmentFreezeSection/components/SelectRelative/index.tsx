import Form from '@mindtickle/form';
import InputNumber from '@mindtickle/input-number';

import { EnrollmentFreezeInput, StyledEnrollmentFreezeDaysView } from './styles';

import type { TSelectRelative } from '../../../../typeDefs';

function SelectRelative(props: TSelectRelative) {
  const { numDays, setNumDays, disableFreezeEdit, isFreezePast, isReadModeEnabled } = props;
  const isPast = !disableFreezeEdit && isFreezePast;
  const formatValue = (value: string) => {
    if (value !== '') {
      return Number(value);
    } else {
      return 1;
    }
  };

  return (
    <>
      {isReadModeEnabled ? (
        <StyledEnrollmentFreezeDaysView>
          <span className={'enrollment-freeze-days-view'}>{numDays}</span>
          <span> days before session date</span>
        </StyledEnrollmentFreezeDaysView>
      ) : (
        <EnrollmentFreezeInput isPast={isPast}>
          {disableFreezeEdit ? (
            <div className={'input-style-box'}>{numDays}</div>
          ) : (
            <Form.Item
              name={`enrollmentFreezeNumDays`}
              labelCol={{ span: 24 }}
              wrapperCol={{ span: 24 }}
              rules={[
                {
                  validator: (_: any, value: number) => {
                    if (isPast) {
                      return Promise.reject();
                    }
                    return Promise.resolve();
                  },
                },
              ]}
            >
              <InputNumber
                name="numDays"
                type="number"
                min={1}
                formatter={formatValue}
                value={numDays}
                defaultValue={numDays}
                onChange={setNumDays}
                precision={0}
              />
            </Form.Item>
          )}
          <div className={'input-side-text'}>days before session date</div>
        </EnrollmentFreezeInput>
      )}
    </>
  );
}

export default SelectRelative;
