import { renderHook, act } from '@testing-library/react-hooks';

import useTimeout from '../useSetTimeout';

describe('useTimeout', () => {
  jest.useFakeTimers();

  it('should execute callback after delay', () => {
    const callback = jest.fn();

    renderHook(() => useTimeout(callback, 1000));

    expect(callback).not.toBeCalled();

    act(() => {
      jest.advanceTimersByTime(1000);
    });

    expect(callback).toBeCalled();
  });

  it('should clear timeout on unmount', () => {
    const callback = jest.fn();

    const { unmount } = renderHook(() => useTimeout(callback, 1000));

    act(() => {
      jest.advanceTimersByTime(500);
    });

    unmount();

    act(() => {
      jest.advanceTimersByTime(500);
    });

    expect(callback).not.toBeCalled();
  });
});
