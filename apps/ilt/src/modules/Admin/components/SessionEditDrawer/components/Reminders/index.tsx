import { useCallback, useState } from 'react';

import Form from '@mindtickle/form';
import Grid from '@mindtickle/grid';
import Icon from '@mindtickle/icon';
import IconWithGradient, { ICON_MAP } from '@mindtickle/icon-with-gradient';
import Pill, { PILL_TYPES } from '@mindtickle/pill';
import { Title } from '@mindtickle/typography';

import { DEFAULT_REMINDER_SECONDS } from '~/modules/Admin/config/sessions.constants';
import { splitSeconds } from '~/utils';

import SetReminder from './components/SetReminder';
import { StyledRemindersView } from './styles';
import {
  StyledReminderTabContainer,
  StyledRemindersSection,
  StyledReminderTimes,
  StyledReminderRow,
  StyledInfoContainer,
  StyledEmptyReminderContainer,
  StyledEmptyReminderHeader,
  StyledEmptyReminderBody,
} from './styles';

import type { TReminder, THandleReminderChange } from './typeDefs';

const { Row } = Grid;

const Reminders = (props: TReminder) => {
  const {
    session: { reminders = [] },
    updateSessionState,
    isReadOnlyModeEnabled,
    form,
  } = props;
  const [initialReminderSeconds, setInitialReminderSeconds] = useState(
    reminders.length === 0
      ? DEFAULT_REMINDER_SECONDS
      : Math.max(...reminders) + DEFAULT_REMINDER_SECONDS
  );

  const addReminder = () => {
    setInitialReminderSeconds(initialReminderSeconds + DEFAULT_REMINDER_SECONDS);
    reminders.unshift(initialReminderSeconds);
    updateSessionState({
      key: 'reminders',
      value: reminders,
    });
  };

  const handleReminderChange = useCallback(
    ({ value = 0, index }: THandleReminderChange) => {
      reminders[index] = value;
      updateSessionState({ key: 'reminders', value: reminders });
    },
    [reminders, updateSessionState]
  );

  return (
    <StyledReminderTabContainer>
      <Title level={3}>Session reminders</Title>
      <StyledRemindersSection>
        <Row align="middle" className="action-section">
          {!isReadOnlyModeEnabled && (
            <Pill className="editTextFlexItem" onClick={addReminder} type={PILL_TYPES.ACTION}>
              +&nbsp;&nbsp;Add reminder
            </Pill>
          )}

          <StyledInfoContainer isReadOnlyModeEnabled={isReadOnlyModeEnabled}>
            <Icon type="info2" className="notify-info-icon" />
            <span className={'notify-text'}>
              Emails and notifications will be sent to the enrolled learners
            </span>
          </StyledInfoContainer>
        </Row>
        {reminders.length === 0 && (
          <StyledEmptyReminderContainer>
            <IconWithGradient style={{ width: 300, height: 280 }} type={ICON_MAP.ALARM_CLOCK} />
            <StyledEmptyReminderHeader>
              Reminders for this session will be shown here
            </StyledEmptyReminderHeader>
            <StyledEmptyReminderBody>
              <span
                className={isReadOnlyModeEnabled ? '' : 'session-link-color'}
                onClick={isReadOnlyModeEnabled ? undefined : addReminder}
              >
                Add reminders{' '}
              </span>
              <span>
                to send emails and notifications to the enrolled learners before the session
              </span>
            </StyledEmptyReminderBody>
          </StyledEmptyReminderContainer>
        )}
        <StyledReminderTimes>
          {reminders.map((reminder: number, index: number) => (
            <StyledReminderRow key={index}>
              {isReadOnlyModeEnabled ? (
                <StyledRemindersView>
                  <div className="time-block days">
                    <span className="time">{splitSeconds(reminder).days}</span>
                    <span className="label">{` day${
                      splitSeconds(reminder).days !== 1 ? 's' : '\u00a0\u00a0'
                    }`}</span>
                  </div>
                  <div className="time-block hours">
                    <span className="time">{splitSeconds(reminder).hours}</span>
                    <span className="label"> hours</span>
                  </div>
                  <div className="time-block minutes">
                    <span className="time">{splitSeconds(reminder).minutes}</span>
                    <span className="label"> minutes</span>
                  </div>
                  <div className="fixed-text">before the session</div>
                </StyledRemindersView>
              ) : (
                <>
                  <Form.Item
                    name={`reminder${index}`}
                    rules={[
                      {
                        validator: async (_: any, reminderSeconds: number) => {
                          const restReminders = [
                            ...reminders.slice(0, index),
                            ...reminders.slice(index + 1),
                          ];
                          if (restReminders.includes(reminderSeconds)) {
                            return Promise.reject('Another reminder exists at the same time');
                          } else {
                            return Promise.resolve();
                          }
                        },
                      },
                    ]}
                  >
                    <SetReminder
                      seconds={reminder}
                      onChange={value => handleReminderChange({ value, index })}
                      index={index}
                      reminders={reminders}
                      form={form}
                    />
                  </Form.Item>
                  <span>before the session</span>
                  <Icon
                    type="delete"
                    className="reminder-delete-icon"
                    onClick={() => {
                      reminders.length === 1 && setInitialReminderSeconds(DEFAULT_REMINDER_SECONDS);
                      updateSessionState({
                        key: 'reminders',
                        value: reminders.filter((_val: number, i: number) => i !== index),
                      });
                    }}
                  />
                </>
              )}
            </StyledReminderRow>
          ))}
        </StyledReminderTimes>
      </StyledRemindersSection>
    </StyledReminderTabContainer>
  );
};

export default Reminders;
