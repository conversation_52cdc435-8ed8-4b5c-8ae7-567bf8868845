import { injectIntl, FormattedMessage } from 'react-intl';

import TextButton from '@mindtickle/button/lib/TextButton';
import { ICON_MAP } from '@mindtickle/icon';
import Loader from '@mindtickle/loader';

import { WEBINAR_SOURCE_LABELS } from '~/modules/Admin/constants/sessions';

import HostAuthModal from '../../../WebinarMeetings/components/AuthModal';
import AUTH_MODAL_CONTENT from '../../../WebinarMeetings/components/AuthModal/messages';
import { StyledLoader } from '../../../WebinarMeetings/styles';

import useUserAuthValidation from './hooks/useUserAuthValidation';
import messages from './messages';
import { LoaderWrapper, StyledIcon, Wrapper } from './style';

import type { Props } from './typeDefs';
import type { InjectedIntlProps } from 'react-intl';

function UserAuthValidation({
  updateAutoAttendanceSettings,
  userEmailAddress,
  webinarSource,
  location,
  intl,
}: Props & InjectedIntlProps) {
  const {
    handleOnAuthPress: onAuthPress,
    handleValidationSuccessMessageAndData: onValidationSuccess,
    integrationSource,
    loading,
    isModalOpen,
    openAuthModal,
    closeAuthModal,
    validationInProgress,
    setValidationInProgress,
  } = useUserAuthValidation({
    updateAutoAttendanceSettings,
    location,
    webinarSource,
  });
  return (
    <>
      {validationInProgress ? (
        <StyledLoader
          size="sizeBig"
          type="Full"
          message={intl.formatMessage(messages.VALIDATION_IN_PROGRESS, {
            source: WEBINAR_SOURCE_LABELS[webinarSource],
          })}
        />
      ) : loading ? (
        <LoaderWrapper>
          <Loader size="sizeXSmall" />
          <span>{intl.formatMessage(messages.AUTH_INPROGRESS)}</span>
        </LoaderWrapper>
      ) : (
        <Wrapper>
          <div className="error-wrapper">
            <div className="icon-wrapper">
              <StyledIcon type={ICON_MAP.WARNING} />
            </div>
            <div>
              <span className="bold">{intl.formatMessage(messages.PLEASE_NOTE)} </span>
              {intl.formatMessage(messages.FAILED_TO_VALIDATE)}
            </div>
            <div className="button-wrapper">
              <TextButton loading={loading} onClick={onAuthPress}>
                <FormattedMessage {...AUTH_MODAL_CONTENT.AUTHENTICATE_CTA} />
              </TextButton>
              <span className="or">or</span>
              <TextButton disabled={loading} onClick={openAuthModal}>
                <FormattedMessage {...AUTH_MODAL_CONTENT.ENTER_HOST_EMAIL} />
              </TextButton>
            </div>
          </div>
        </Wrapper>
      )}

      {isModalOpen && (
        <HostAuthModal
          handleMeetingValidationSuccess={onValidationSuccess}
          hostEmail={userEmailAddress}
          onCancel={() => {
            closeAuthModal();
          }}
          webinarSource={webinarSource!}
          integrationSource={integrationSource}
          flow={'validate'}
          meetingUrl={location}
          initialFlow={'hostEmail'}
          setValidationInProgress={setValidationInProgress}
          validationInProgress={validationInProgress}
        />
      )}
    </>
  );
}

export default injectIntl(UserAuthValidation);
