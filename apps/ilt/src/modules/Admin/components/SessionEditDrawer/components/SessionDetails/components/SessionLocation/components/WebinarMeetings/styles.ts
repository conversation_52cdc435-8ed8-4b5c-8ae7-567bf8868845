import styled, { css } from 'styled-components';

import Divider from '@mindtickle/divider';
import Loader from '@mindtickle/loader';
import { tokens } from '@mindtickle/styles/lib';

export const StyledWebinarCard = styled.div`
  background: ${tokens.bgTokens.COLOR_BG_SECONDARY};
  border-radius: 8px;
  width: 80%;
  padding: 16px;
  margin-top: 5px;
  margin-bottom: 10px;
  position: relative;
  font-size: 12px;
`;

export const StyledCloseIcon = styled.div`
  top: 16px;
  right: 16px;
  cursor: pointer;
  color: ${tokens.textTokens.COLOR_TEXT_SECONDARY}; //$color-mildgrey;
  font-size: 12px;
  position: absolute;
`;

export const StyledWebinarHeading = styled.div`
  line-height: 20px;
  font-size: 14px;
  font-weight: 600;
  > a {
    color: ${tokens.textTokens.COLOR_TEXT_ACCENT};
  }
`;

export const StyledDivider = styled(Divider)`
  && {
    margin-top: 20px;
    margin-bottom: 14px;
  }
`;

const mixinGeneralLineStyle = css`
  line-height: 16px;
  margin-top: 8px;
`;

export const StyledWebinarPrimaryInfoText = styled.div`
  ${mixinGeneralLineStyle};
  color: ${tokens.textTokens.COLOR_TEXT_DEFAULT};
`;

export const StyledWebinarPrimaryHeading = styled.div`
  ${mixinGeneralLineStyle};
  margin-top: 20px;
  color: ${tokens.textTokens.COLOR_TEXT_SECONDARY};
`;

export const StyledAddCohost = styled.div`
  ${mixinGeneralLineStyle};
  color: ${tokens.textTokens.COLOR_TEXT_ACCENT};
  cursor: pointer;
  font-weight: 600;
  width: max-content;
`;

export const StyledMaxCohostInfo = styled.div`
  color: ${tokens.textTokens.COLOR_TEXT_SECONDARY};
  margin-top: 8px;
  line-height: 16px;
`;

export const StyledLoader = styled(Loader)`
  &&.fullPageloadingScreen {
    position: fixed;
    background-color: #ffffffb3;
    .loaderMessage {
      color: ${tokens.textTokens.COLOR_TEXT_DEFAULT};
    }
  }
`;

export const StyledCopyMeetingInfo = styled.div`
  display: flex;
  align-items: center;
  color: ${tokens.textTokens.COLOR_TEXT_ACCENT};
  cursor: pointer;
  font-weight: 600;
  line-height: 16px;
  margin-top: 16px;
  width: max-content;
  .copyIcon {
    margin-right: 8px;
  }
`;
