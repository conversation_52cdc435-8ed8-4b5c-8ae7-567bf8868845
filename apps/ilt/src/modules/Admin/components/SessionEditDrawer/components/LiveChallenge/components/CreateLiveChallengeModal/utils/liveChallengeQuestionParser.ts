import includes from 'lodash/includes';
import overEvery from 'lodash/overEvery';

import { requiredFields } from '../constants';

import type { TMtLiveChallengeFileData } from '../../../typeDefs';

export const parseMtLiveChallengeData = (mtLiveChallengeFileData: TMtLiveChallengeFileData[]) =>
  mtLiveChallengeFileData.map(mtLiveChallengeFileQuestion => {
    let titleImage = mtLiveChallengeFileQuestion.questionImage
      ? mtLiveChallengeFileQuestion.questionImage
      : '';
    if (titleImage.length > 0 && titleImage.includes('drive.google')) {
      const titleImageId = titleImage.match('/d/(.*)/')?.[1];
      titleImage = `https://drive.google.com/uc?export=view&id=${titleImageId}`;
    }

    const options = [
      mtLiveChallengeFileQuestion['answerChoice1'],
      mtLiveChallengeFileQuestion['answerChoice2'],
      mtLiveChallengeFileQuestion['answerChoice3'],
      mtLiveChallengeFileQuestion['answerChoice4'],
    ];
    if (mtLiveChallengeFileQuestion.questionType === 'MCQ_POLL') {
      return {
        text: encodeURI(mtLiveChallengeFileQuestion.questionText),
        type: mtLiveChallengeFileQuestion.questionType,
        titleImage: titleImage,
        properties: {
          options: options
            .map(optionText => {
              if (!optionText) return null;
              return {
                text: encodeURIComponent(
                  optionText.charAt(0) === '*' ? optionText.substr(1) : optionText
                ).replace(/[!'()*]/g, escape),
              };
            })
            .filter(o => !!o),
        },
        // adminText: mtLiveChallengeFileQuestion.cueText
        //     ? mtLiveChallengeFileQuestion.cueText
        //     : "",
      };
    } else {
      return {
        text: encodeURI(mtLiveChallengeFileQuestion.questionText),
        type: mtLiveChallengeFileQuestion.questionType,
        titleImage: titleImage,
        properties: {
          options: options
            .map(optionText => {
              if (!optionText) return null;
              return {
                text: encodeURIComponent(
                  optionText.charAt(0) === '*' ? optionText.substr(1) : optionText
                ).replace(/[!'()*]/g, escape),
                isCorrect: optionText.charAt(0) === '*',
              };
            })
            .filter(o => !!o),
        },
        // adminText: mtLiveChallengeFileQuestion.cueText
        //     ? mtLiveChallengeFileQuestion.cueText
        //     : "",
      };
    }
  });

const checkAllFieldsPresent = (mtLiveChallengeQuestionsData: TMtLiveChallengeFileData[]) => {
  const recordLength = mtLiveChallengeQuestionsData.length;

  return (
    mtLiveChallengeQuestionsData
      .map(mtLiveChallengeFileQuestion => {
        const allFields = Object.keys(mtLiveChallengeFileQuestion);
        return (
          requiredFields.map(field => allFields.includes(field)).filter(o => !!o).length ===
          requiredFields.length
        );
      })
      .filter(question => !!question).length === recordLength
  );
};

const checkQuestionTextPresent = (mtLiveChallengeQuestionsData: TMtLiveChallengeFileData[]) => {
  const recordLength = mtLiveChallengeQuestionsData.length;

  return (
    mtLiveChallengeQuestionsData
      .map(mtLiveChallengeFileQuestion => mtLiveChallengeFileQuestion.questionText)
      .filter(questionText => !!questionText).length === recordLength
  );
};

const validQuestionTypes = ['MCQ', 'MCQ_POLL'];

const checkQuestionTypeIsValid = (mtLiveChallengeQuestionsData: TMtLiveChallengeFileData[]) => {
  const recordLength = mtLiveChallengeQuestionsData.length;

  return (
    mtLiveChallengeQuestionsData
      .map(
        mtLiveChallengeFileQuestion =>
          mtLiveChallengeFileQuestion.questionType &&
          includes(validQuestionTypes, mtLiveChallengeFileQuestion.questionType)
      )
      .filter(questionType => !!questionType).length === recordLength
  );
};

const checkMinimumCorrectOptionPresent = (
  mtLiveChallengeQuestionsData: TMtLiveChallengeFileData[]
) => {
  const recordLength = mtLiveChallengeQuestionsData.length;

  return (
    mtLiveChallengeQuestionsData
      .map(mtLiveChallengeFileQuestion => {
        if (mtLiveChallengeFileQuestion.questionType === 'MCQ_POLL') {
          return true;
        } else {
          const options = [
            mtLiveChallengeFileQuestion['answerChoice1'],
            mtLiveChallengeFileQuestion['answerChoice2'],
            mtLiveChallengeFileQuestion['answerChoice3'],
            mtLiveChallengeFileQuestion['answerChoice4'],
          ];
          return (
            options
              .map(
                option =>
                  option &&
                  typeof option == 'string' &&
                  option.charAt(0) === '*' &&
                  option.length > 1
              )
              .filter(opt => !!opt).length > 0
          );
        }
      })
      .filter(question => !!question).length === recordLength
  );
};

const checkMinimumTwoOptionsPresent = (
  mtLiveChallengeQuestionsData: TMtLiveChallengeFileData[]
) => {
  const recordLength = mtLiveChallengeQuestionsData.length;

  return (
    mtLiveChallengeQuestionsData
      .map(mtLiveChallengeFileQuestion => {
        const options = [
          mtLiveChallengeFileQuestion['answerChoice1'],
          mtLiveChallengeFileQuestion['answerChoice2'],
          mtLiveChallengeFileQuestion['answerChoice3'],
          mtLiveChallengeFileQuestion['answerChoice4'],
        ];
        return options.filter(o => !!o).length > 1;
      })
      .filter(question => !!question).length === recordLength
  );
};

export const validateMtLiveChallengeData = overEvery([
  checkAllFieldsPresent,
  checkQuestionTextPresent,
  checkQuestionTypeIsValid,
  checkMinimumCorrectOptionPresent,
  checkMinimumTwoOptionsPresent,
]);
