import { useCallback, useEffect, useMemo, useState } from 'react';

import debounce from 'lodash/debounce';
import moment from 'moment/moment';
import { injectIntl, FormattedMessage } from 'react-intl';

import Divider from '@mindtickle/divider';
import { ICON_MAP } from '@mindtickle/icon';
import Icon from '@mindtickle/icon';
import InputNumber from '@mindtickle/input-number';
import Toggle from '@mindtickle/switch';

import messages from '~/modules/Admin/components/SessionEditDrawer/components/AttendanceCompletion/components/SessionEnrolmentEmailSettings/messages';
import EnrolmentThresholdReminderDetails from '~/modules/Admin/containers/Settings/components/Notifications/components/ReminderDetails/EnrolmentThresholdReminderDetails';
import { DEFAULT_TIME } from '~/modules/Admin/containers/Settings/components/Notifications/constants';
import {
  getDaysValue,
  getHasSpecificDate,
  getSchedule,
  getSelectedDate,
  getSelectedTime,
} from '~/modules/Admin/containers/Settings/components/Notifications/helpers';
import {
  StyledAddEnrolmentReminder,
  StyledAddIcon,
} from '~/modules/Admin/containers/Settings/components/Notifications/styles';
import {
  EmailAutomationTransformed,
  ReminderActionType,
} from '~/modules/Admin/containers/Settings/components/Notifications/typeDefs';
import {
  StyledEnrolmentReminderDateItem,
  StyledErrorText,
  StyledInput,
  StyledInputContainer,
  StyledText,
} from '~/modules/Admin/containers/Settings/styles';
import {
  getSummary,
  mixpanelConstants,
} from '~/modules/Admin/containers/Settings/utils/trackEvents';
import { useILTAdminSnowplowTracker } from '~/modules/Admin/hooks/useILTAdminSnowplowTracker';
import { MODULE_SETTINGS_EVENTS } from '~/modules/Admin/hooks/useILTAdminSnowplowTracker/constants/settings';
import { formatDateFromMilliseconds, validateEnrolmentThresholdReminder } from '~/utils';

import { TSessionEnrolmentEmailSettings } from '../../typeDefs';
import {
  StyledCheckInToggleText,
  StyledCheckInContainer,
  // StyledInfoContainer,
  // StyledCheckInNote,
  StyledErrorNote,
  StyledErrorContainer,
} from '../SessionCheckIn/styles';

const reminderOptions = [
  {
    //TODO:check this
    key: 'invited_but_not_enrolled',
    content: 'No. of days before session',
  },
  {
    key: 'specific_date',
    content: 'Specific date',
  },
];

function addCnameModuleId(input: string, strToAdd: string): string {
  const delimiter = '==';
  const index = input.indexOf(delimiter);

  if (index !== -1) {
    return input.slice(0, index) + strToAdd + input.slice(index);
  } else {
    return input + strToAdd;
  }
}

function getConditionFromReminderType(
  givenCondition: string,
  reminderType: string,
  companyId: string,
  moduleId: string,
  daysValue?: string
): string {
  let condition = givenCondition.split('==')[0];
  condition = condition.replace(/\([^)]*\)/g, '');
  condition = condition.trim();
  condition += `("${companyId}","${moduleId}")`;
  if (reminderType === 'specific_date') {
    return condition;
  }
  return condition + `==${daysValue}d`;
}

const defaultRecurrenceSettings = {
  recurring: false,
  recurrenceFrequency: 3,
  recurrenceType: 'DAYS',
  recurrenceEndType: 'STATE_CHANGE',
  recurrenceCount: 8,
  recurrenceDays: ['Tue'],
};

const enrolmentThresholdEmailTemplate = '_ilt_session_minimum_enrollment_settings_breach';
const enrolmentThresholdEmailTask = 'not_enrolled_to_session_by_date';

const modifyReminders = (
  reminders: any,
  isDraft: boolean,
  isEdited: boolean,
  companyId: string,
  moduleId: string,
  operation?: string,
  mailJobIndex?: string
  // eslint-disable-next-line max-params
): any =>
  reminders?.map((reminder: any, index: any) => ({
    templateId: reminder?.templateId,
    task: reminder?.templateId,
    schedule: reminder?.schedule,
    condition: addCnameModuleId(reminder?.condition, `("${companyId}","${moduleId}")`),
    recurrenceSettings: reminder?.recurrenceSettings,
    company: companyId,
    entity: moduleId,
    operation: operation === 'CREATE' ? 'CREATE' : operation,
    mailJobId: reminder?.mailJobId
      ? reminder?.mailJobId
      : `Temp-Reminder-${reminders?.length === 1 ? mailJobIndex : index}`,
    isDraft: isDraft,
    isEdited: isEdited,
    selectedTemplate: enrolmentThresholdEmailTemplate,
    selectedEmailReminderType: reminder?.condition?.includes('==')
      ? reminderOptions[0].key
      : reminderOptions[1].key,
    hasSpecificDate: getHasSpecificDate(reminder),
    daysValue: getDaysValue(reminder),
    selectedDate: getSelectedDate(reminder),
    selectedTime: getSelectedTime(reminder) || DEFAULT_TIME,
    reminderOptions: reminderOptions,
  }));

const SessionEnrolmentEmailSettings = (props: TSessionEnrolmentEmailSettings) => {
  const {
    intl,
    session,
    updateSessionState,
    isReadOnlyModeEnabled,
    sessionEnrolmentEmailSettings,
    moduleEnrolmentEmailSettings,
    mode,
    companyId,
    moduleId,
    isSessionTimeModified,
  } = props;

  const isPastSession = session?.endTime <= Date.now();
  const disableEditSettings = isReadOnlyModeEnabled || isPastSession;
  const tracker = useILTAdminSnowplowTracker();

  const [sessionEnrolmentEmailToggle, setSessionEnrolmentEmailToggle] = useState<boolean>(
    mode === 'add'
      ? moduleEnrolmentEmailSettings?.isEnabled
      : sessionEnrolmentEmailSettings?.isEnabled || false
  );
  const [sessionEnrolmentEmailThreshold, setSessionEnrolmentEmailThreshold] = useState<number>(
    mode === 'add'
      ? moduleEnrolmentEmailSettings?.enrolmentThreshold
      : !sessionEnrolmentEmailSettings?.enrolmentThreshold ||
        sessionEnrolmentEmailSettings?.enrolmentThreshold === 0
      ? moduleEnrolmentEmailSettings?.enrolmentThreshold
      : sessionEnrolmentEmailSettings?.enrolmentThreshold
  );

  const [enrolmentReminders, setEnrolmentReminders] = useState({
    key: '_reminder_not_enrolled_in_session',
    status: 'Not enrolled in any session or event',
    allowAddReminder: true,
    reminderDateText: undefined,
    emailTemplates: [
      {
        key: enrolmentThresholdEmailTemplate,
        // content: 'Room shared',
        content: 'ILT - Minimum Enrollment in a Session Unfulfilled',
      },
    ],
    reminderData: modifyReminders(
      mode === 'add'
        ? moduleEnrolmentEmailSettings?.reminders
        : sessionEnrolmentEmailSettings?.reminders || [],
      false,
      false,
      companyId,
      moduleId,
      mode === 'add' ? 'CREATE' : 'NO_CHANGE'
    ),
  });

  const debounceSendMixpanel = useMemo(
    () =>
      debounce(param => {
        tracker.trackStructuredEvent(param);
      }, 300),
    [tracker]
  );

  useEffect(() => {
    if (mode === 'add') {
      updateSessionState({
        key: 'enrolmentThresholdEmailSettings',
        value: {
          isEnabled: moduleEnrolmentEmailSettings?.isEnabled || false,
          enrolmentThreshold: moduleEnrolmentEmailSettings?.enrolmentThreshold || 0,
          reminders:
            moduleEnrolmentEmailSettings?.reminders?.map((reminder: any) => ({
              ...reminder,
              operation: 'CREATE',
              mailJobId: '',
            })) || [],
        },
      });
    }
  }, [mode]);

  const sessionEnrolmentEmailToggleHandler = (value: boolean) => {
    setSessionEnrolmentEmailToggle(value);
    setSessionEnrolmentEmailThreshold(sessionEnrolmentEmailThreshold);
    updateSessionState({
      key: 'enrolmentThresholdEmailSettings',
      value: {
        isEnabled: value,
        enrolmentThreshold: sessionEnrolmentEmailThreshold,
        reminders: transformEnrolmentReminders(enrolmentReminders?.reminderData || []),
      },
    });
    tracker.trackStructuredEvent({
      eventName: MODULE_SETTINGS_EVENTS.MODULE_ILT_SESSIONS_MINIMUM_ENROLLMENT_REMINDER_UPDATED,
      setting_type: mixpanelConstants.SCORING_AND_COMPLETION,
      sub_setting_type: mixpanelConstants.SET_ENROLLMENT_THRESHOLD_TOGGLE,
      sub_setting_value: value ? 'on' : 'off',
      enrollment_threshold: sessionEnrolmentEmailThreshold,
      ilt_session_id: session?.id || '',
    });
  };

  const sessionEnrolmentEmailThresholdHandler = (value: number) => {
    setSessionEnrolmentEmailThreshold(value);
    updateSessionState({
      key: 'enrolmentThresholdEmailSettings',
      value: {
        isEnabled: sessionEnrolmentEmailToggle,
        enrolmentThreshold: value,
        reminders: transformEnrolmentReminders(enrolmentReminders?.reminderData || []),
      },
    });
    tracker.trackStructuredEvent({
      eventName: MODULE_SETTINGS_EVENTS.MODULE_ILT_SESSIONS_MINIMUM_ENROLLMENT_REMINDER_UPDATED,
      setting_type: mixpanelConstants.SCORING_AND_COMPLETION,
      sub_setting_type: mixpanelConstants.SET_ENROLLMENT_THRESHOLD_TOGGLE,
      sub_setting_value: sessionEnrolmentEmailToggle ? 'on' : 'off',
      enrollment_threshold: value,
      ilt_session_id: session?.id || '',
    });
  };

  const transformEnrolmentReminders = (reminders: any) => {
    let transformedEnrolmentReminders = {
      reminders: reminders?.map((reminder: any) => ({
        operation: reminder?.operation,
        templateId: enrolmentThresholdEmailTemplate,
        task: enrolmentThresholdEmailTask,
        schedule: reminder?.schedule,
        condition: reminder?.condition?.replace(/\([^)]*\)/g, ''),
        mailJobId: reminder?.mailJobId?.includes('Temp-Reminder') ? '' : reminder?.mailJobId,
        recurrenceSettings: reminder?.recurrenceSettings
          ? reminder?.recurrenceSettings
          : defaultRecurrenceSettings,
      })),
    };
    transformedEnrolmentReminders = transformedEnrolmentReminders?.reminders?.filter(
      (item: any) => Object.keys(item).length > 0
    );
    return transformedEnrolmentReminders;
  };

  const onDeleteClick = useCallback(
    (prop: any) => {
      setEnrolmentReminders((prevState: any) => ({
        ...prevState,
        reminderData: prevState?.reminderData?.filter(
          (reminder: any) => reminder?.mailJobId !== prop?.mailJobId
        ),
      }));
      if (!prop?.isDraft) {
        const transformedEnrolmentReminders = transformEnrolmentReminders(
          enrolmentReminders?.reminderData?.map((reminder: any) =>
            reminder?.mailJobId === prop?.mailJobId
              ? { ...reminder, isEdited: false, isDraft: false, operation: 'DELETE' }
              : reminder
          )
        );
        updateSessionState({
          key: 'enrolmentThresholdEmailSettings',
          value: {
            isEnabled: sessionEnrolmentEmailToggle,
            enrolmentThreshold: sessionEnrolmentEmailThreshold,
            reminders: sessionEnrolmentEmailToggle
              ? transformEnrolmentReminders(transformedEnrolmentReminders)
              : [],
          },
        });
      }
      try {
        const updatedReminder = prop;

        let eventData: any = {
          enrollment_threshold: sessionEnrolmentEmailThreshold,
          recurrence: updatedReminder.recurrenceSettings.recurring ? 'Yes' : 'No',
          recurrence_Summary: getSummary(updatedReminder),
          reminder_operation: mixpanelConstants.OPERATION_DELETE,
        };
        if (updatedReminder?.hasSpecificDate) {
          eventData = {
            ...eventData,
            enrollment_reminder_specific_date: updatedReminder?.schedule,
          };
        } else {
          eventData = {
            ...eventData,
            enrollment_reminder_days_before_session: updatedReminder?.condition
              ?.split('==')[1]
              ?.split('d')[0],
          };
        }
        tracker.trackStructuredEvent({
          eventName: MODULE_SETTINGS_EVENTS.MODULE_ILT_SESSIONS_MINIMUM_ENROLLMENT_REMINDER_UPDATED,
          setting_type: mixpanelConstants.SCORING_AND_COMPLETION,
          sub_setting_type: mixpanelConstants.ENROLLMENT_THRESHOLD_REMINDER,
          ilt_session_id: session?.id || '',
          ...eventData,
        });
      } catch (e) {
        //e
      }
    },
    [enrolmentReminders]
  );

  const deleteDraftReminder = useCallback(
    (prop: any) => {
      const newEnrolmentReminders = {
        ...enrolmentReminders,
        reminderData: enrolmentReminders?.reminderData?.filter(
          (reminder: any) => reminder?.mailJobId !== prop?.mailJobId
        ),
      };
      setEnrolmentReminders(newEnrolmentReminders);
      updateSessionState({
        key: 'enrolmentThresholdEmailSettings',
        value: {
          isEnabled: sessionEnrolmentEmailToggle,
          enrolmentThreshold: sessionEnrolmentEmailThreshold,
          reminders: sessionEnrolmentEmailToggle
            ? transformEnrolmentReminders(newEnrolmentReminders?.reminderData)
            : [],
        },
      });
    },
    [enrolmentReminders]
  );

  const onSaveClick = useCallback(
    (prop: any) => {
      setEnrolmentReminders((prevState: any) => ({
        ...prevState,
        reminderData: prevState?.reminderData?.map((reminder: any) =>
          reminder?.mailJobId === prop?.mailJobId
            ? {
                ...reminder,
                schedule: prop?.schedule,
                recurrenceSettings: prop?.recurrenceSettings
                  ? prop?.recurrenceSettings
                  : defaultRecurrenceSettings,
                mailJobId: prop?.mailJobId,
                isDraft: false,
                isEdited: false,
                selectedTemplate: prop?.selectedTemplate,
                selectedEmailReminderType: prop?.selectedEmailReminderType,
                reminderOptions: prop?.reminderOptions,
                operation: reminder?.operation === 'CREATE' ? 'CREATE' : 'UPDATE',
                hasSpecificDate: getHasSpecificDate(prop),
                daysValue: prop?.daysValue || 1,
                condition: getConditionFromReminderType(
                  prop?.condition,
                  prop?.selectedEmailReminderType,
                  companyId,
                  moduleId,
                  prop?.daysValue || 1
                ),
              }
            : reminder
        ),
      }));
      const transformedEnrolmentReminders = transformEnrolmentReminders(
        enrolmentReminders?.reminderData?.map((reminder: any) =>
          reminder?.mailJobId === prop?.mailJobId
            ? {
                ...reminder,
                schedule: prop?.schedule,
                recurrenceSettings: prop?.recurrenceSettings
                  ? prop?.recurrenceSettings
                  : defaultRecurrenceSettings,
                mailJobId: prop?.mailJobId,
                isDraft: false,
                isEdited: false,
                selectedTemplate: prop?.selectedTemplate,
                templateId: enrolmentThresholdEmailTemplate,
                task: enrolmentThresholdEmailTask,
                selectedEmailReminderType: prop?.selectedEmailReminderType,
                reminderOptions: prop?.reminderOptions,
                operation: reminder?.operation === 'CREATE' ? 'CREATE' : 'UPDATE',
                hasSpecificDate: getHasSpecificDate(prop),
                daysValue: prop?.daysValue || 1,
                condition: getConditionFromReminderType(
                  prop?.condition,
                  prop?.selectedEmailReminderType,
                  companyId,
                  moduleId,
                  prop?.daysValue || 1
                ),
              }
            : reminder
        )
      );
      updateSessionState({
        key: 'enrolmentThresholdEmailSettings',
        value: {
          isEnabled: sessionEnrolmentEmailToggle,
          enrolmentThreshold: sessionEnrolmentEmailThreshold,
          reminders: sessionEnrolmentEmailToggle ? transformedEnrolmentReminders : [],
        },
      });
    },
    [enrolmentReminders]
  );

  const updateDraftReminders = useCallback(
    (prop: any) => {
      const automation = prop?.payload?.automation;

      const selectedEmailReminderType =
        prop?.type === ReminderActionType.UPDATE_REMINDER_TYPE
          ? prop?.payload?.selectedEmailReminderType
          : automation?.selectedEmailReminderType;

      const recurrenceSettings =
        prop?.type === ReminderActionType.UPDATE_RECURRENCE_SETTINGS
          ? prop?.payload?.recurrenceSettings
          : selectedEmailReminderType === 'specific_date'
          ? defaultRecurrenceSettings
          : automation?.recurrenceSettings ?? defaultRecurrenceSettings;

      const daysValue =
        prop?.type === ReminderActionType.UPDATE_REMINDER_DAY_VALUE
          ? prop?.payload?.daysValue
          : selectedEmailReminderType === 'specific_date'
          ? undefined
          : automation?.daysValue ?? 1;

      const selectedDate =
        prop?.type === ReminderActionType.UPDATE_REMINDER_DATE
          ? prop?.payload?.date
            ? prop?.payload?.date?.set({ hour: 0, minute: 0, second: 0, millisecond: 0 })
            : moment().add(3, 'days').startOf('day')
          : selectedEmailReminderType === 'specific_date'
          ? automation?.selectedDate ?? moment().add(3, 'days').startOf('day')
          : undefined;

      const selectedTime =
        prop?.type === ReminderActionType.UPDATE_REMINDER_TIME
          ? prop?.payload?.time
          : automation?.selectedTime || DEFAULT_TIME;

      const schedule = getSchedule({
        ...automation,
        selectedEmailReminderType,
        hasSpecificDate: selectedEmailReminderType === 'specific_date',
        selectedDate,
        selectedTime,
      });

      const newEnrolmentReminders = {
        ...enrolmentReminders,
        reminderData: enrolmentReminders?.reminderData?.map((reminder: any) =>
          reminder?.mailJobId === automation?.mailJobId
            ? {
                ...reminder,
                mailJobId: automation?.mailJobId,
                isDraft: false,
                isEdited: true,
                selectedTemplate: automation?.selectedTemplate,
                reminderOptions: automation?.reminderOptions,
                operation: automation?.operation === `CREATE` ? `CREATE` : `UPDATE`,

                schedule: schedule,
                recurrenceSettings: recurrenceSettings,
                selectedEmailReminderType: selectedEmailReminderType,
                hasSpecificDate: getHasSpecificDate({ ...automation, schedule: schedule }),
                daysValue: daysValue,
                condition: getConditionFromReminderType(
                  automation?.condition,
                  selectedEmailReminderType,
                  companyId,
                  moduleId,
                  daysValue
                ),
                selectedTime,
                selectedDate,
              }
            : reminder
        ),
      };
      setEnrolmentReminders(newEnrolmentReminders);
      updateSessionState({
        key: 'enrolmentThresholdEmailSettings',
        value: {
          isEnabled: sessionEnrolmentEmailToggle,
          enrolmentThreshold: sessionEnrolmentEmailThreshold,
          reminders: sessionEnrolmentEmailToggle
            ? transformEnrolmentReminders(newEnrolmentReminders?.reminderData)
            : [],
        },
      });
      try {
        const updatedReminder = newEnrolmentReminders.reminderData?.find(
          (eachRem: any) => eachRem.mailJobId === automation.mailJobId
        );
        let eventData: any = {
          enrollment_threshold: sessionEnrolmentEmailThreshold,
          reminder_operation: mixpanelConstants.OPERATION_UPDATE,
        };
        if (updatedReminder?.hasSpecificDate) {
          eventData = {
            ...eventData,
            enrollment_reminder_specific_date: updatedReminder?.schedule,
          };
        } else {
          eventData = {
            ...eventData,
            recurrence: updatedReminder.recurrenceSettings.recurring ? 'Yes' : 'No',
            recurrence_Summary: getSummary(updatedReminder),
            enrollment_reminder_days_before_session: updatedReminder?.condition
              ?.split('==')[1]
              ?.split('d')[0],
          };
        }
        debounceSendMixpanel({
          eventName: MODULE_SETTINGS_EVENTS.MODULE_ILT_SESSIONS_MINIMUM_ENROLLMENT_REMINDER_UPDATED,
          setting_type: mixpanelConstants.SCORING_AND_COMPLETION,
          sub_setting_type: mixpanelConstants.ENROLLMENT_THRESHOLD_REMINDER,
          ilt_session_id: session?.id || '',
          ...eventData,
        });
      } catch (e) {
        //e
      }
    },
    [enrolmentReminders]
  );

  const addDraftReminders = useCallback(() => {
    setEnrolmentReminders((prevState: any) => ({
      ...prevState,
      reminderData: [
        ...prevState?.reminderData,
        ...modifyReminders(
          [
            {
              task: enrolmentThresholdEmailTask,
              templateId: enrolmentThresholdEmailTemplate,
              condition: `${enrolmentThresholdEmailTask}==1d`,
              schedule: '0 0 0 * * ? *',
            },
          ],
          true,
          true,
          companyId,
          moduleId,
          'CREATE',
          prevState?.reminderData?.length
        ),
      ],
    }));
    updateSessionState({
      key: 'enrolmentThresholdEmailSettings',
      value: {
        isEnabled: sessionEnrolmentEmailToggle,
        enrolmentThreshold: sessionEnrolmentEmailThreshold,
        reminders: sessionEnrolmentEmailToggle
          ? transformEnrolmentReminders(
              {
                ...enrolmentReminders,
                reminderData: [
                  ...enrolmentReminders?.reminderData,
                  ...modifyReminders(
                    [
                      {
                        task: enrolmentThresholdEmailTask,
                        templateId: enrolmentThresholdEmailTemplate,
                        condition: 'invited_but_not_enrolled==1d',
                        schedule: '0 0 0 * * ? *',
                      },
                    ],
                    false,
                    false,
                    companyId,
                    moduleId,
                    'CREATE',
                    enrolmentReminders?.reminderData?.length
                  ),
                ],
              }?.reminderData
            )
          : [],
      },
    });

    try {
      let eventData: any = {
        enrollment_threshold: sessionEnrolmentEmailThreshold,
        recurrence: 'No',
        recurrence_Summary: getSummary(null),
        reminder_operation: mixpanelConstants.OPERATION_CREATE,
        enrollment_reminder_days_before_session: '1',
      };
      tracker.trackStructuredEvent({
        eventName: MODULE_SETTINGS_EVENTS.MODULE_ILT_SESSIONS_MINIMUM_ENROLLMENT_REMINDER_UPDATED,
        setting_type: mixpanelConstants.SCORING_AND_COMPLETION,
        sub_setting_type: mixpanelConstants.ENROLLMENT_THRESHOLD_REMINDER,
        ilt_session_id: session?.id || '',
        ...eventData,
      });
    } catch (e) {
      //e
    }
  }, [
    enrolmentReminders,
    sessionEnrolmentEmailThreshold,
    sessionEnrolmentEmailToggle,
    updateSessionState,
  ]);

  return (
    <StyledCheckInContainer>
      <StyledCheckInToggleText checked={sessionEnrolmentEmailToggle}>
        <FormattedMessage {...messages.ENROLMENT_EMAIL_TOGGLE_TEXT} />
      </StyledCheckInToggleText>
      <Toggle
        name="sessionEnrolmentEmailToggle"
        checked={sessionEnrolmentEmailToggle}
        onChange={sessionEnrolmentEmailToggleHandler}
        disabled={disableEditSettings}
      />
      {sessionEnrolmentEmailToggle && (
        <div>
          <div style={{ display: 'flex' }}>
            <StyledInputContainer>
              <StyledText>
                <FormattedMessage {...messages.MINIMUM_ENROLMENT_TEXT} />
              </StyledText>
              <StyledInput>
                <InputNumber
                  type="number"
                  min={0}
                  max={session?.maxSeatEnabled ? session?.maxSeats : 1000000}
                  step={1}
                  defaultValue={sessionEnrolmentEmailThreshold}
                  value={sessionEnrolmentEmailThreshold}
                  onChange={sessionEnrolmentEmailThresholdHandler}
                  style={{ width: '160px' }}
                  disabled={disableEditSettings}
                />
              </StyledInput>
              {session?.maxSeatEnabled &&
                session?.enrolmentThresholdEmailSettings?.enrolmentThreshold >
                  session?.maxSeats && (
                  <StyledErrorText>
                    <FormattedMessage {...messages.ENROLMENT_THRESHOLD_VALIDATION_ERROR} />
                  </StyledErrorText>
                )}
            </StyledInputContainer>
            <div style={{ marginTop: '24px', marginLeft: '32px' }}>
              <StyledText>
                <FormattedMessage {...messages.REMINDER_TEXT} />
              </StyledText>
              <div style={{ marginTop: '12px', width: '450px' }}>
                {enrolmentReminders?.reminderData?.map(
                  (reminder: EmailAutomationTransformed, index: any) => (
                    <div key={reminder?.mailJobId ? reminder?.mailJobId : index}>
                      <StyledEnrolmentReminderDateItem>
                        {enrolmentReminders?.reminderDateText || (
                          <EnrolmentThresholdReminderDetails
                            key={reminder?.id}
                            hasSpecificDate={reminder?.hasSpecificDate}
                            reminderOptions={reminder?.reminderOptions}
                            selectedDate={reminder?.selectedDate}
                            selectedEmailReminderType={reminder?.selectedEmailReminderType}
                            daysValue={reminder?.daysValue}
                            selectedTime={reminder?.selectedTime || DEFAULT_TIME}
                            updateDraftReminders={updateDraftReminders}
                            onDeleteClick={onDeleteClick}
                            automation={reminder}
                            onSaveClick={onSaveClick}
                            onDeleteDraftReminder={deleteDraftReminder}
                            disableSessionDrawerSettings={isPastSession || isReadOnlyModeEnabled}
                            isEnrolmentThresholdReminder={true}
                            isSessionDrawerReminder={true}
                            sessionId={session?.id || ''}
                          />
                        )}
                      </StyledEnrolmentReminderDateItem>
                      {!validateEnrolmentThresholdReminder(
                        session,
                        reminder,
                        isSessionTimeModified
                      ) && (
                        <StyledErrorText>
                          {intl.formatMessage(messages.ENROLMENT_REMINDER_VALIDATION_ERROR) +
                            formatDateFromMilliseconds(session?.startTime) +
                            ').'}
                        </StyledErrorText>
                      )}
                      {index !== (enrolmentReminders?.reminderData?.length || 0) - 1 && (
                        <Divider className="attendance-checkin-divider" />
                      )}
                    </div>
                  )
                )}
                {!disableEditSettings && enrolmentReminders?.allowAddReminder && (
                  <StyledAddEnrolmentReminder
                    emptyList={!enrolmentReminders?.reminderData?.length}
                    onClick={() => addDraftReminders()}
                  >
                    <StyledAddIcon type={ICON_MAP.ADD} />
                    Add reminder
                  </StyledAddEnrolmentReminder>
                )}
              </div>
            </div>
          </div>
          {isPastSession && (
            <StyledErrorContainer>
              <Icon type={ICON_MAP.WARNING} />
              <StyledErrorNote>
                <span className="noteHeading">Note: </span>
                {`Reminders are not applicable for past sessions.`}
              </StyledErrorNote>
            </StyledErrorContainer>
          )}
        </div>
      )}
    </StyledCheckInContainer>
  );
};

export default injectIntl(SessionEnrolmentEmailSettings);
