import { useCallback } from 'react';

import { FormattedMessage } from 'react-intl';

import Grid from '@mindtickle/grid';
import Icon, { ICON_MAP } from '@mindtickle/icon';

import AsyncTimezoneDropdown from '~/components/AsyncTimezoneDropdown';
import DateTimeSelector from '~/modules/Admin/components/DateTimeSelector';
import {
  DEFAULT_SESSION_DURATION,
  MINIMUM_SESSION_DURATION,
  MESSAGES,
  ENROLLMENT_FREEZE_STATUSES,
} from '~/modules/Admin/config/sessions.constants';
import { getSessionDateFormat, getSessionTimeFormat } from '~/modules/Admin/utils';
import { getTimezoneFromString } from '~/utils/timezone';

import { LabeledSessionInfo } from '../../../../../LabeledSessionInfo';

import {
  styleTimezoneViewOnlyRow,
  StyledSessionDateView,
  StyledTimeBlock,
  StyledTimezoneDropdownContainer,
} from './styles';

import type { TSessionTimingsSelector } from './typeDefs';
const { Row, Col } = Grid;

function SessionTimingsSelector(props: TSessionTimingsSelector) {
  let {
    startTime,
    endTime,
    timezone,
    minStartTime,
    disableTimeEdit,
    dateTimeValidationErrors,
    isReadOnlyModeEnabled,
    update,
    changeTimezoneListLoadingState,
    duration = DEFAULT_SESSION_DURATION,
    enrollmentFreezeEnabled,
  } = props;
  const updateTime = useCallback(
    (type: string, value: any) => {
      // eslint-disable-next-line default-case
      switch (type) {
        case 'START':
          if (value >= endTime) {
            update('START', value, true);
            update('END', value + duration, true);
          } else {
            update('START', value, true);
          }
          if (enrollmentFreezeEnabled && value < Date.now()) {
            update('ENROLLMENT_FREEZE_STATUS', ENROLLMENT_FREEZE_STATUSES.DISABLED, true);
            update('ENROLLMENT_FREEZE_EPOCH', 0, true);
          }
          break;
        case 'END':
          if (value <= startTime) {
            update('START', value - duration, true);
            update('END', value, true);
          } else {
            update('END', value, true);
          }
          break;
        case 'TIMEZONE': {
          update('TIMEZONE', value);
        }
      }
    },
    [startTime, endTime, update, duration, enrollmentFreezeEnabled]
  );

  const onTimezoneChange = useCallback(
    (data: any) => {
      updateTime('TIMEZONE', JSON.stringify(data));
    },
    [updateTime]
  );

  const onFirstTimeTimezoneListLoad = useCallback(
    (loading: boolean) => changeTimezoneListLoadingState(loading),
    [changeTimezoneListLoadingState]
  );
  const commonProps = {
    showTime: true,
    showToday: false,
    withBtns: false,
    disable: true,
  };
  const startTimeProps = {
    ...commonProps,
    min: minStartTime,
    value: startTime,
    disableTimeEdit: disableTimeEdit,
    ok: (value: number) => updateTime('START', value),
    id: 'sessionStart',
    datePickerWrapperClassName: 'date-picker-wrapper-class session-start-date-wrapper',
  };
  const endTimeProps = {
    ...commonProps,
    min: startTime + MINIMUM_SESSION_DURATION,
    value: endTime,
    disableTimeEdit: disableTimeEdit,
    ok: (value: number) => updateTime('END', value),
    id: 'sessionEnd',
  };

  try {
    let { timezone: parsedTimezone } = getTimezoneFromString(timezone);
    timezone = parsedTimezone;
  } catch (ex) {}

  return (
    <>
      {isReadOnlyModeEnabled ? (
        <>
          <Row gutter={12} align={'bottom'}>
            <Col>
              <StyledSessionDateView>
                <LabeledSessionInfo className={'time-label'} labelText="Start date">
                  <div>{getSessionDateFormat(startTime)}</div>
                </LabeledSessionInfo>
                <LabeledSessionInfo className={'time-label'} labelText="Start time">
                  <div>{getSessionTimeFormat(startTime)}</div>
                </LabeledSessionInfo>
              </StyledSessionDateView>
            </Col>
            <Col>
              <Icon type={ICON_MAP.STEP_ARROW} className="moreActionIcon" />
            </Col>
            <Col>
              <StyledSessionDateView>
                <LabeledSessionInfo className={'time-label'} labelText="End date">
                  <div>{getSessionDateFormat(endTime)}</div>
                </LabeledSessionInfo>
                <LabeledSessionInfo className={'time-label'} labelText="End time">
                  <div>{getSessionTimeFormat(endTime)}</div>
                </LabeledSessionInfo>
              </StyledSessionDateView>
            </Col>
          </Row>
          {/* TODO: Can use "moment.tz(JSON.parse(timezone.originalTimezoneString).id).zoneAbbr()" to get the abbreviation, but not reliable */}
          <Row style={styleTimezoneViewOnlyRow}>
            <LabeledSessionInfo style={{ marginTop: 0 }} labelText="Time zone">
              <div>{timezone.name}</div>
            </LabeledSessionInfo>
          </Row>
        </>
      ) : (
        <StyledTimeBlock>
          <Row align="middle" className={'session-date-row'}>
            <Col span={10}>
              <DateTimeSelector {...startTimeProps} errorStatus={!!dateTimeValidationErrors} />
            </Col>
            <Col span={1} className="arrow-icon-col">
              <Icon type={ICON_MAP.STEP_ARROW} className="more-action-icon" />
            </Col>
            <Col span={10}>
              <DateTimeSelector {...endTimeProps} errorStatus={!!dateTimeValidationErrors} />
            </Col>
          </Row>

          {!!dateTimeValidationErrors && (
            <div className={'date-time-error-message'}>{dateTimeValidationErrors}</div>
          )}
          <StyledTimezoneDropdownContainer>
            <div className={'timezone-dropdown-label'}>Time zone</div>
            <AsyncTimezoneDropdown
              startTime={startTime}
              onChange={onTimezoneChange}
              onFirstTimeTimezoneListLoad={onFirstTimeTimezoneListLoad}
              timezoneString={timezone?.originalTimezoneString}
              timezoneId={timezone?.id}
              disabled={!!disableTimeEdit}
            />
          </StyledTimezoneDropdownContainer>

          {timezone && timezone.hasdst && (
            <>
              <div key="timeBlockWarning" className={'timeBlockWarning'}>
                *The Time zone you have chosen is subject to daylight saving adjustments. <br />
                Choose the daylight saving alternative if daylight saving will be followed when the
                session occurs else choose the standard time alternative.
              </div>
            </>
          )}
          <div className="clear" />
          {disableTimeEdit && (
            <>
              <div key="timeBlockWarning" className={'timeDisabledWarning'}>
                {endTime - Date.now() > 0 ? (
                  <FormattedMessage {...MESSAGES.INFO.LIVE_EDIT_DISABLED} />
                ) : (
                  <FormattedMessage {...MESSAGES.INFO.PAST_EDIT_DISABLED} />
                )}
              </div>
            </>
          )}
          <div className="clear" />
        </StyledTimeBlock>
      )}
    </>
  );
}

SessionTimingsSelector.defaultProps = {
  duration: DEFAULT_SESSION_DURATION,
};

export default SessionTimingsSelector;
