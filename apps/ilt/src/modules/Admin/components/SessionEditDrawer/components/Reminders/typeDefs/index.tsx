import type { TSessionObject, TUpdateSessionState } from '../../../typeDefs';
import type { FormInstance } from 'antd/lib/form';
export interface TReminder {
  session: TSessionObject;
  updateSessionState: TUpdateSessionState;
  isReadOnlyModeEnabled: boolean;
  form: FormInstance;
}

export interface THandleReminderChange {
  value: number;
  index: number;
}

export type TSetReminder = {
  seconds: number;
  onChange: (seconds: number) => void;
  index: number;
  reminders: number[];
  form: FormInstance;
};

export interface TReminderTime {
  days: number;
  hours: number;
  minutes: number;
  seconds?: number;
}
