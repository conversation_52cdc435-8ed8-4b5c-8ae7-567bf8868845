import { FormattedMessage } from 'react-intl';

import Button from '@mindtickle/button';
import Icon, { ICON_MAP } from '@mindtickle/icon';

import { CREATE_LIVE_CHALLENGE } from '~/modules/Admin/config/live-challenges.constants';

import { SAMPLE_LIVE_CHALLENGE_FILE_URL } from '../../constants';

import { CreateLCModalTitle } from './styles';

const Title = ({ closeModal }: { closeModal: () => void }) => (
  <CreateLCModalTitle>
    <Icon className="backButton" type={ICON_MAP.LEFT_ARROW} onClick={closeModal} />
    <span className="title">
      <FormattedMessage {...CREATE_LIVE_CHALLENGE.CREATE_LIVE_CHALLENGE_TITLE} />
    </span>
    <a download href={SAMPLE_LIVE_CHALLENGE_FILE_URL} target="_blank" rel="noopener noreferrer">
      <Button type="secondary">
        <FormattedMessage {...CREATE_LIVE_CHALLENGE.DOWNLOAD_SAMPLE} />
      </Button>
    </a>
  </CreateLCModalTitle>
);

export default Title;
