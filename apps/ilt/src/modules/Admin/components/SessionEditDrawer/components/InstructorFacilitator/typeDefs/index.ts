import type { Dispatch, SetStateAction } from 'react';

import type { TSessionObject, TUpdateSessionState } from '../../../typeDefs';
import type { FormInstance } from 'antd/lib/form';
import type { DebouncedFunc } from 'lodash';

export interface TInstructorFacilitator {
  session: TSessionObject;
  setSession: Dispatch<SetStateAction<TSessionObject>>;
  updateSessionState: TUpdateSessionState;
  userAuth: object;
  isPublishedOngoingPastSession: boolean;
  activeInstructorFacilitatorTab: string;
  setActiveInstructorFacilitatorTab: Dispatch<SetStateAction<string>>;
  isReadOnlyModeEnabled: boolean;
  form: FormInstance;
}

export interface TFacilitatorSection {
  session: TSessionObject;
  setSession: Dispatch<SetStateAction<TSessionObject>>;
  updateSessionState: TUpdateSessionState;
  getCohostCheckbox: (
    email: string,
    checkboxName: string,
    extraInfo?: { isFacilitator?: boolean }
  ) => JSX.Element;
  removeCohost: (email: any) => void;
  isReadOnlyModeEnabled: boolean;
  form: FormInstance;
}

export interface TFacilitatorDetails {
  facilitatorEmail: string;
  index: number;
  updateFacilitator: DebouncedFunc<(_: any, index: number, value: string) => void>;
  removeFacilitator: (showCohost: boolean, facilitatorEmail: string, index: number) => void;
  getCohostCheckbox: (
    email: string,
    checkboxName: string,
    extraInfo?: { isFacilitator?: boolean }
  ) => JSX.Element;
  isReadOnlyModeEnabled: boolean;
  form: FormInstance;
}

export interface TInstructorSection {
  session: TSessionObject;
  setSession: Dispatch<SetStateAction<TSessionObject>>;
  updateSessionState: TUpdateSessionState;
  userAuth: any;
  getCohostCheckbox: (
    email: string,
    checkboxName: string,
    extraInfo?: { isFacilitator?: boolean }
  ) => JSX.Element;
  removeCohost: (email: any) => void;
  isReadOnlyModeEnabled: boolean;
  form: FormInstance;
}

export interface TInstructorDetails {
  instructor: any;
  index: number;
  updateInstructor: DebouncedFunc<(_: any, index: number, value: any) => void>;
  removeInstructor: (showCohost: boolean, instructorEmail: string, index: number) => void;
  getCohostCheckbox: (
    email: string,
    checkboxName: string,
    extraInfo?: { isFacilitator?: boolean }
  ) => JSX.Element;
  isReadOnlyModeEnabled: boolean;
  form: FormInstance;
}
