import { useCallback, useEffect, useState } from 'react';

import { FormattedMessage } from 'react-intl';

import Icon from '@mindtickle/icon';
import InputNumber from '@mindtickle/input-number';
import Toggle from '@mindtickle/switch';
import { Paragraph } from '@mindtickle/typography';

import messages from '~/modules/Admin/components/SessionEditDrawer/components/AttendanceCompletion/components/SessionAutoAttendance/messages';
import {
  StyledErrorNote,
  StyledInput,
  StyledInputContainer,
  StyledText,
} from '~/modules/Admin/containers/Settings/styles';
import { milliSecondsToHoursAndMinutes } from '~/utils';

import { TSessionAutoAttendanceIn } from '../../typeDefs';
import {
  StyledInfoContainer,
  StyledCheckInNote,
  StyledCheckInToggleText,
  StyledCheckInContainer,
} from '../SessionCheckIn/styles';

const SessionAutoAttendance = (props: TSessionAutoAttendanceIn) => {
  const {
    session,
    updateSessionState,
    isReadOnlyModeEnabled,
    companyAttendanceSettings,
    sessionAttendanceSettings,
    moduleAttendanceSettings,
    mode,
  } = props;

  const isPastSession = session?.endTime <= Date.now();
  const disableEditSettings =
    isReadOnlyModeEnabled ||
    !session?.webAutoAttendanceSettings?.isAutoAttendanceEnabled ||
    isPastSession ||
    !companyAttendanceSettings?.allowEditSettings;

  const [sessionAutoAttendanceToggle, setSessionAutoAttendanceToggle] = useState<boolean>(
    mode === 'add'
      ? moduleAttendanceSettings?.durationBasedAttendanceEnabled
      : sessionAttendanceSettings?.isEnabled || false
  );

  const [sessionAutoAttendanceThreshold, setSessionAutoAttendanceThreshold] = useState<number>(
    mode === 'add'
      ? moduleAttendanceSettings?.percentageThreshold
      : !sessionAttendanceSettings?.percentageThreshold ||
        sessionAttendanceSettings?.percentageThreshold === 0
      ? companyAttendanceSettings?.percentageThreshold
      : sessionAttendanceSettings?.percentageThreshold
  );
  const [sessionAttendanceError, setSessionAttendanceError] = useState<boolean>(false);

  useEffect(() => {
    if (mode === 'add') {
      updateSessionState({
        key: 'webAutoAttendanceSettings',
        value: {
          ...session?.webAutoAttendanceSettings,
          thresholdConfig: {
            isEnabled: moduleAttendanceSettings?.durationBasedAttendanceEnabled || false,
            percentageThreshold: moduleAttendanceSettings?.percentageThreshold || 0,
          },
        },
      });
    }
  }, [mode]);

  const sessionAutoAttendanceToggleHandler = (value: boolean) => {
    setSessionAttendanceError(false);
    setSessionAutoAttendanceToggle(value);
    setSessionAutoAttendanceThreshold(
      sessionAutoAttendanceThreshold === 0
        ? moduleAttendanceSettings?.percentageThreshold
        : sessionAutoAttendanceThreshold
    );
    updateSessionState({
      key: 'webAutoAttendanceSettings',
      value: {
        ...session?.webAutoAttendanceSettings,
        thresholdConfig: {
          isEnabled: value,
          percentageThreshold:
            sessionAutoAttendanceThreshold === 0
              ? moduleAttendanceSettings?.percentageThreshold
              : sessionAutoAttendanceThreshold,
        },
      },
    });
  };

  const sessionAutoAttendanceThresholdHandler = useCallback(
    (value: number) => {
      setSessionAutoAttendanceThreshold(
        value === 0 ? moduleAttendanceSettings?.percentageThreshold : value
      );
      setSessionAttendanceError(false);
      updateSessionState({
        key: 'webAutoAttendanceSettings',
        value: {
          ...session?.webAutoAttendanceSettings,
          thresholdConfig: {
            isEnabled: sessionAutoAttendanceToggle,
            percentageThreshold:
              value === 0 ? moduleAttendanceSettings?.percentageThreshold : value,
          },
        },
      });
    },
    [sessionAutoAttendanceThreshold]
  );

  return (
    <StyledCheckInContainer>
      <StyledCheckInToggleText checked={sessionAutoAttendanceToggle}>
        <FormattedMessage {...messages.AUTO_ATTENDANCE_TOGGLE_TEXT} />
      </StyledCheckInToggleText>
      <Toggle
        name="sessionAutoAttendanceToggle"
        checked={sessionAutoAttendanceToggle}
        onChange={sessionAutoAttendanceToggleHandler}
        disabled={disableEditSettings}
      />
      {sessionAutoAttendanceToggle && (
        <StyledInputContainer>
          <StyledText>
            <FormattedMessage {...messages.SET_MINIMUM_TIME_TEXT} />
          </StyledText>
          <StyledInput>
            <InputNumber
              type="number"
              min={1}
              max={100}
              step={1}
              disabled={disableEditSettings}
              defaultValue={sessionAutoAttendanceThreshold}
              value={sessionAutoAttendanceThreshold}
              onChange={sessionAutoAttendanceThresholdHandler}
              style={{ width: '60px' }}
              handleVisible={true}
            />
            <Paragraph className="default-max-score-text">%</Paragraph>
          </StyledInput>
          {sessionAttendanceError ? (
            <StyledErrorNote>
              <FormattedMessage {...messages.DEFAULT_ATTENDANCE_THRESHOLD_ERROR_TEXT} />
            </StyledErrorNote>
          ) : null}
          <StyledInfoContainer>
            <Icon type="info2" />
            <StyledCheckInNote>
              <span className="noteHeading">Note: </span>
              {isPastSession
                ? `Learners should have spent ${milliSecondsToHoursAndMinutes(
                    (Number(sessionAutoAttendanceThreshold) * (session?.duration?.value || 0)) / 100
                  )} (${sessionAutoAttendanceThreshold}% of ${milliSecondsToHoursAndMinutes(
                    session?.duration?.value || 0
                  )}) at least, to be marked as attended for this session.`
                : `Learners must attend at least ${milliSecondsToHoursAndMinutes(
                    (Number(sessionAutoAttendanceThreshold) * (session?.duration?.value || 0)) / 100
                  )} (${sessionAutoAttendanceThreshold}% of ${milliSecondsToHoursAndMinutes(
                    session?.duration?.value || 0
                  )}) to be marked as attended.`}
            </StyledCheckInNote>
          </StyledInfoContainer>
        </StyledInputContainer>
      )}
    </StyledCheckInContainer>
  );
};

export default SessionAutoAttendance;
