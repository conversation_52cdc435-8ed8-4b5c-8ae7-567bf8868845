import { injectIntl } from 'react-intl';

import Icon from '@mindtickle/icon';
import IconWithGradient from '@mindtickle/icon-with-gradient';

import { StyledDropdownElement, StyledSelectedTextwrapper } from '../../styles';

import { iconTypeImageMap } from './config';
import { IconComponent } from './constants';
import { optionIconStyle, selectedIconStyle } from './styles';

import type { TSelectedValueView, TDropdownElement, TIconRendererProps } from '../../typeDefs';

function IconRenderer({ type, componentType, style }: TIconRendererProps) {
  return componentType === IconComponent.ICON_GRADIENT ? (
    <IconWithGradient type={type} style={style} />
  ) : componentType === IconComponent.ICON ? (
    <Icon type={type} style={style} />
  ) : (
    <></>
  );
}

const RenderSelectedValueView = ({
  iconType = 'classroom',
  labelText,
  intl,
}: TSelectedValueView) => (
  <StyledSelectedTextwrapper>
    <IconRenderer
      componentType={iconTypeImageMap[iconType].componentType}
      type={iconTypeImageMap[iconType].type}
      style={iconTypeImageMap[iconType].selectedStyle || selectedIconStyle}
    />
    <span className="selected-label-text">{intl.formatMessage(labelText)}</span>
  </StyledSelectedTextwrapper>
);

export const SelectedValueView = injectIntl(RenderSelectedValueView);

const DropDownElement = ({
  iconType = 'classroom',
  primaryText,
  secondaryText,
  intl,
}: TDropdownElement) => (
  <StyledDropdownElement>
    <div className="location-icon">
      <IconRenderer
        componentType={iconTypeImageMap[iconType].componentType}
        type={iconTypeImageMap[iconType].type}
        style={iconTypeImageMap[iconType].optionStyle || optionIconStyle}
      />
    </div>
    <div className={'option-text'}>
      <div className="option-text-primary">{intl.formatMessage(primaryText)}</div>
      <div className="option-text-secondary">{intl.formatMessage(secondaryText)}</div>
    </div>
  </StyledDropdownElement>
);

export default injectIntl(DropDownElement);
