import { useMemo } from 'react';

import { FormattedMessage } from 'react-intl';

import {
  INTEGRATION_SOURCE,
  LOCATION_TYPE,
  SESSION_TYPES,
} from '~/modules/Admin/config/sessions.constants';

import type { TUseLocationOptions } from '../../typeDefs';

const sessionTypeGroupText = {
  [SESSION_TYPES.CLASSROOM.value]: <FormattedMessage {...SESSION_TYPES.CLASSROOM.displayValue} />,
  [SESSION_TYPES.WEBINAR.value]: <FormattedMessage {...SESSION_TYPES.WEBINAR.displayValue} />,
};

const useLocationOptions = ({ integrationsInfo }: TUseLocationOptions) => {
  const { isIntegrationAvailable, integrationDataMap } = integrationsInfo;
  return useMemo(() => {
    const webinarGroup = [];
    if (isIntegrationAvailable) {
      if (integrationDataMap[INTEGRATION_SOURCE.webex]) {
        webinarGroup.push({
          type: LOCATION_TYPE.WEBEX_MEETING,
        });
      }
      if (integrationDataMap[INTEGRATION_SOURCE.zoom]) {
        webinarGroup.push({
          type: LOCATION_TYPE.ZOOM_MEETING,
        });
      }
      if (integrationDataMap[INTEGRATION_SOURCE.ms_teams]) {
        webinarGroup.push({
          type: LOCATION_TYPE.MS_TEAMS_MEETING,
        });
      }
    }
    webinarGroup.push({
      type: LOCATION_TYPE.LINK,
    });

    return [
      {
        groupKey: SESSION_TYPES.CLASSROOM.value,
        groupText: sessionTypeGroupText[SESSION_TYPES.CLASSROOM.value],
        children: [{ type: LOCATION_TYPE.FACE_TO_FACE }],
      },
      {
        groupKey: SESSION_TYPES.WEBINAR.value,
        groupText: sessionTypeGroupText[SESSION_TYPES.WEBINAR.value],
        children: webinarGroup,
      },
    ];
  }, [isIntegrationAvailable, integrationDataMap]);
};

export default useLocationOptions;
