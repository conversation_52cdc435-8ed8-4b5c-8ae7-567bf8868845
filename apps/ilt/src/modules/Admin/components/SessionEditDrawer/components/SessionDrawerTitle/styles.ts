import styled from 'styled-components';

import { mixins } from '@mindtickle/styles/lib';
export const StyledSessionDrawerTitleContainer = styled.div`
  margin-left: 8px;
  .view-session-title,
  .edit-session-title,
  .create-session-title {
    font-weight: 600;
    font-size: 20px;
    line-height: 28px;
  }
`;

export const StyledSessionViewTitle = styled.div`
  display: flex;
  flex-direction: column;
  .edit-session-title {
    margin-right: 30px;
  }

  .event-title {
    margin-right: 30px;
    margin-top: 4px;
    ${mixins.smallBlackLink()}
  }
`;

export const StyledSessionEditTitle = styled.div`
  display: flex;
  align-items: center;
  .edit-session-button {
    margin-left: 24px;
    margin-right: 30px;
  }
`;
