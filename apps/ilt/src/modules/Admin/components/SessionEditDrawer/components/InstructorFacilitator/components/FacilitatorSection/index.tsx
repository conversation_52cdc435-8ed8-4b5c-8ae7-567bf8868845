import debounce from 'lodash/debounce';

import Grid from '@mindtickle/grid';
import Icon from '@mindtickle/icon';
import Pill, { PILL_TYPES } from '@mindtickle/pill';

import type { TSessionObject } from '~/modules/Admin/components/SessionEditDrawer/typeDefs';

import FacilitatorDetails from './components/FacilitatorDetails';
import {
  StyledFacilitatorsInfoWrapper,
  StyledFacilitatorsSection,
  StyledInfoContainer,
} from './styles';

import type { TFacilitatorSection } from '../../typeDefs';
const { Row } = Grid;

function FacilitatorSection(props: TFacilitatorSection) {
  const {
    session: { facilitators = [] },
    setSession,
    getCohostCheckbox,
    updateSessionState,
    removeCohost,
    isReadOnlyModeEnabled,
    form,
  } = props;

  const addFacilitator = () => {
    let {
      session: { facilitators = [] },
    } = props;
    facilitators = [...facilitators];
    facilitators.push('');
    updateSessionState({
      key: 'facilitators',
      value: facilitators,
    });
  };

  const removeFacilitator = (showCohost: boolean, facilitatorEmail: string, index: number) => {
    showCohost && removeCohost(facilitatorEmail);
    updateSessionState({
      key: 'facilitators',
      value: facilitators.filter((val: any, i: number) => i !== index),
    });
  };

  const updateFacilitator = debounce((_, index, value) => {
    setSession(prevState => {
      let session: TSessionObject = Object.assign({}, prevState);
      let facilitators = [...session.facilitators];
      facilitators[index] = value;
      session.facilitators = facilitators;
      return session;
    });
  }, 500);

  return (
    <StyledFacilitatorsSection>
      <Row align="middle" className="action-section">
        {!isReadOnlyModeEnabled && (
          <Pill className="editTextFlexItem" onClick={addFacilitator} type={PILL_TYPES.ACTION}>
            +&nbsp;&nbsp;Add facilitator
          </Pill>
        )}

        <StyledInfoContainer isReadOnlyModeEnabled={isReadOnlyModeEnabled}>
          <Icon type="info2" className="notify-info-icon" />
          <span className={'notifyText'}>Emails will be sent to the facilitators</span>
        </StyledInfoContainer>
      </Row>

      <StyledFacilitatorsInfoWrapper>
        {facilitators.map((facilitatorEmail: string, index: number) => (
          <FacilitatorDetails
            key={index}
            facilitatorEmail={facilitatorEmail}
            index={index}
            updateFacilitator={updateFacilitator}
            removeFacilitator={removeFacilitator}
            getCohostCheckbox={getCohostCheckbox}
            isReadOnlyModeEnabled={isReadOnlyModeEnabled}
            form={form}
          />
        ))}
      </StyledFacilitatorsInfoWrapper>
    </StyledFacilitatorsSection>
  );
}

export default FacilitatorSection;
