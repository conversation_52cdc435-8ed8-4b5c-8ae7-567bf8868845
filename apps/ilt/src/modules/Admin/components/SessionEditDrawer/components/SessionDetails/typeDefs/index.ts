import type { Dispatch, SetStateAction } from 'react';

import type {
  TMeetingStatusTrack,
  TOnLocationTypeChange,
  TSessionObject,
  TTimezone,
  TUpdateMeetingStatus,
  TUpdateSessionState,
} from '../../../typeDefs';
import type { FormInstance } from 'antd/lib/form';

export interface TSessionDetails {
  session: TSessionObject;
  changeTimezoneListLoadingState: (loading: boolean) => void;
  setSession: Dispatch<SetStateAction<TSessionObject>>;
  updateSessionState: TUpdateSessionState;
  webexMeetingStatusTrack: TMeetingStatusTrack;
  setWebexMeetingStatusTrack: Dispatch<SetStateAction<TMeetingStatusTrack>>;
  zoomMeetingStatusTrack: TMeetingStatusTrack;
  setZoomMeetingStatusTrack: Dispatch<SetStateAction<TMeetingStatusTrack>>;
  msteamsMeetingStatusTrack: TMeetingStatusTrack;
  setMSTeamsMeetingStatusTrack: Dispatch<SetStateAction<TMeetingStatusTrack>>;
  mode: string;
  integrations?: object;
  userAuth: object;
  addedClearedMeetingData: any;
  isPublishedOngoingPastSession: boolean;
  isReadOnlyModeEnabled: boolean;
  form: FormInstance;
  eventData: any;
  switchToEventDrawer: () => void;
  moduleDetails?: any;
  setIsSessionTimeModified?: any;
  isSessionTimeModified?: any;
}

export interface TSessionAttachment {
  attachments: any[];
  updateSessionState: TUpdateSessionState;
  isReadOnlyModeEnabled: boolean;
}

export interface TSessionDescription {
  description: string;
  updateSessionState: TUpdateSessionState;
  isReadOnlyModeEnabled: boolean;
}

export interface TSessionName {
  name: string;
  locationType: string;
  isPublishedOngoingPastSession: boolean;
  updateSessionState: TUpdateSessionState;
  isReadOnlyModeEnabled: boolean;
}

export interface TSessionScore {
  maxScore: number;
  updateSessionState: TUpdateSessionState;
  isReadOnlyModeEnabled: boolean;
}

export interface TSessionTime {
  session: TSessionObject;
  webexMeetingStatusTrack: TMeetingStatusTrack;
  zoomMeetingStatusTrack: TMeetingStatusTrack;
  msteamsMeetingStatusTrack: TMeetingStatusTrack;
  updateSessionState: TUpdateSessionState;
  isReadOnlyModeEnabled: boolean;
  isPublishedOngoingPastSession: boolean;
  eventData: any;
  switchToEventDrawer: () => void;
  changeTimezoneListLoadingState: (loading: boolean) => void;
  setIsSessionTimeModified?: any;
  isSessionTimeModified?: any;
}

export interface SessionLocationProps {
  session?: any;
  updateSessionState: TUpdateSessionState;
  integrations?: any;
  mode?: string;
  onLocationTypeChange: TOnLocationTypeChange;
  updateWebexMeetingStatus: TUpdateMeetingStatus;
  updateZoomMeetingStatus: TUpdateMeetingStatus;
  updateMSTeamsMeetingStatus: TUpdateMeetingStatus;
  isOngoingOrPastSession: boolean;
  webexMeetingStatusTrack?: any;
  zoomMeetingStatusTrack?: any;
  msteamsMeetingStatusTrack: TMeetingStatusTrack;
  userAuth?: object;
  isReadOnlyModeEnabled: boolean;
  moduleDetails?: any;
}

export interface TGetDateTimeValidationMessage {
  sessionStartTime: number;
  sessionTimezone?: TTimezone;
  locationType: string;
  meetingValidationEnum: string;
  eventEnrollmentDetails: {
    enrollmentType: string;
    enrollmentFreezeEpoch: number;
    enrollmentFreezeStatus: string;
    enrollmentFreezeDaysBeforeEvent?: number;
    enrollmentFreezeTimezone?: TTimezone;
    eventStartTime: number;
  };
  switchToEventDrawer: () => void;
}
