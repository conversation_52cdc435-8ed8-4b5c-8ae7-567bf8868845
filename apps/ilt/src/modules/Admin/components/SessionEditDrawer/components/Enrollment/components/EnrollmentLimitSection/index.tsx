import { useRef } from 'react';

import { FormattedMessage } from 'react-intl';

import Divider from '@mindtickle/divider';
import Form from '@mindtickle/form';
import Icon from '@mindtickle/icon';
import InputNumber from '@mindtickle/input-number';
import Toggle from '@mindtickle/switch';
import Tooltip from '@mindtickle/tooltip';
import { Title } from '@mindtickle/typography';

import { MESSAGES } from '~/modules/Admin/config/sessions.constants';
import ENROLLMENT_LIMIT_MESSAGES from '~/modules/Admin/messages/sessions/enrollment/enrollmentLimit';

import { StyledActionRow } from '../../styles';

import {
  StyledEnrollmentLimitContainer,
  StyledEnrollmentLimitText,
  StyledMaxSeatsContainer,
  StyledWaitingListContainer,
  StyledWaitingListText,
  StyledInfoCaption,
} from './styles';

import type { TEnrollmentLimitSection } from '../../typeDefs';

const EnrollmentLimitSection = (props: TEnrollmentLimitSection) => {
  const {
    session: {
      maxSeatEnabled: sessionMaxSeatEnabled,
      maxSeats: sessionMaxSeats,
      waitingListEnabled: sessionWaitingListEnabled,
      autoEnroll: sessionAutoEnroll,
      notifyWaitingList: sessionNotifyWaitingList,
    },
    updateSessionState,
    isReadOnlyModeEnabled,
    isEventLevelEnrollmentEnabled,
    event: {
      autoEnroll: eventAutoEnroll,
      maxSeatEnabled: eventMaxSeatEnabled,
      waitingListEnabled: eventWaitingListEnabled,
      maxSeats: eventMaxSeats,
      notifyWaitingList: eventNotifyWaitingList,
    },
  } = props;
  const scrollToSpanRef = useRef<HTMLSpanElement | null>(null);
  const maxSeatEnabled = !isEventLevelEnrollmentEnabled
    ? sessionMaxSeatEnabled
    : eventMaxSeatEnabled;
  const maxSeats = !isEventLevelEnrollmentEnabled ? sessionMaxSeats : eventMaxSeats;
  const waitingListEnabled = !isEventLevelEnrollmentEnabled
    ? sessionWaitingListEnabled
    : eventWaitingListEnabled;
  const autoEnroll = !isEventLevelEnrollmentEnabled ? sessionAutoEnroll : eventAutoEnroll;
  const notifyWaitingList = !isEventLevelEnrollmentEnabled
    ? sessionNotifyWaitingList
    : eventNotifyWaitingList;

  const formatValue = (value: string) => {
    if (value !== '') {
      return Number(value);
    } else {
      return 1;
    }
  };

  const onToggleChange = (data: { key: string; value: boolean }) => {
    updateSessionState(data);
    if (data && data.value) {
      setTimeout(() =>
        scrollToSpanRef.current?.scrollIntoView({
          behavior: 'smooth',
        })
      );
    }
  };

  const renderMaxSeats = () => (
    <StyledMaxSeatsContainer>
      <>
        {isReadOnlyModeEnabled ? (
          <div className="enrollment-limit-view">{maxSeats}</div>
        ) : (
          <Form.Item name={'maxSeats'} labelCol={{ span: 24 }} wrapperCol={{ span: 24 }}>
            <InputNumber
              name="maxSeats"
              type="number"
              min={1}
              max={10000}
              value={maxSeats}
              formatter={formatValue}
              className={'max-enroll-input'}
              defaultValue={maxSeats}
              onChange={(value: number) =>
                updateSessionState({
                  key: 'maxSeats',
                  value: value,
                })
              }
              precision={0}
              disabled={isEventLevelEnrollmentEnabled}
            />
          </Form.Item>
        )}
      </>
    </StyledMaxSeatsContainer>
  );

  const renderWaitingList = () => (
    <StyledWaitingListContainer>
      <Divider />
      <StyledActionRow>
        <StyledWaitingListText checked={!!waitingListEnabled}>
          <FormattedMessage {...ENROLLMENT_LIMIT_MESSAGES.ENABLE_WAITING_LIST} />
          <Tooltip
            overlayClassName={'tooltipInfo'}
            title={<FormattedMessage {...MESSAGES.INFO.WAITING_LIST} />}
            getPopupContainer={() => document.body}
          >
            <Icon type="info2" className={'infoHover'} />
          </Tooltip>
        </StyledWaitingListText>

        <Toggle
          name="waitingListEnabledToggle"
          checked={!!waitingListEnabled}
          onChange={(value: boolean) => onToggleChange({ key: 'waitingListEnabled', value })}
          disabled={isReadOnlyModeEnabled || isEventLevelEnrollmentEnabled}
        />
      </StyledActionRow>

      {waitingListEnabled && (
        <>
          <Divider />
          <StyledActionRow className="child-action-row">
            <StyledWaitingListText checked={!!autoEnroll}>
              <FormattedMessage {...ENROLLMENT_LIMIT_MESSAGES.AUTO_ENROLL_ON_UNENROLL} />
              <Tooltip
                overlayClassName={'tooltipInfo'}
                title={<FormattedMessage {...MESSAGES.INFO.AUTO_ENROLL} />}
              >
                <Icon type="info2" className={'infoHover'} />
              </Tooltip>
            </StyledWaitingListText>

            <Toggle
              name="autoEnrollToggle"
              onChange={(value: boolean) => onToggleChange({ key: 'autoEnroll', value })}
              checked={!!autoEnroll}
              disabled={isReadOnlyModeEnabled || isEventLevelEnrollmentEnabled}
            />
            {autoEnroll && (
              <StyledInfoCaption>
                <FormattedMessage {...ENROLLMENT_LIMIT_MESSAGES.AUTO_ENROLL_INFO} />
              </StyledInfoCaption>
            )}
          </StyledActionRow>
        </>
      )}

      {autoEnroll && waitingListEnabled && (
        <>
          <Divider />
          <StyledActionRow className="child-action-row">
            <StyledEnrollmentLimitText checked={!!notifyWaitingList}>
              <FormattedMessage {...ENROLLMENT_LIMIT_MESSAGES.NOTIFY_LEARNERS_ON_ENROLLMENT} />
            </StyledEnrollmentLimitText>
            <Toggle
              name="notifyWaitingListToggle"
              onChange={(value: boolean) => updateSessionState({ key: 'notifyWaitingList', value })}
              checked={!!notifyWaitingList}
              disabled={isReadOnlyModeEnabled || isEventLevelEnrollmentEnabled}
            />
          </StyledActionRow>
        </>
      )}
    </StyledWaitingListContainer>
  );

  return (
    <StyledEnrollmentLimitContainer>
      <Title level={3} className="enrollment-limit-header">
        <FormattedMessage {...ENROLLMENT_LIMIT_MESSAGES.ENROLLMENT_LIMIT} />
      </Title>
      <StyledActionRow>
        <StyledEnrollmentLimitText checked={!!maxSeatEnabled}>
          <FormattedMessage {...ENROLLMENT_LIMIT_MESSAGES.SET_ENROLLMENT_LIMIT} />
          <Tooltip
            overlayClassName={'tooltipInfo'}
            title={<FormattedMessage {...MESSAGES.INFO.MAX_SEATS} />}
          >
            <Icon type="info2" className={'infoHover'} />
          </Tooltip>
        </StyledEnrollmentLimitText>

        <Toggle
          name="enrollmentLimitToggle"
          checked={!!maxSeatEnabled}
          onChange={(value: boolean) => onToggleChange({ key: 'maxSeatEnabled', value })}
          disabled={isReadOnlyModeEnabled || isEventLevelEnrollmentEnabled}
        />
      </StyledActionRow>
      {maxSeatEnabled && renderMaxSeats()}

      {maxSeatEnabled && renderWaitingList()}
      <span ref={scrollToSpanRef}></span>
    </StyledEnrollmentLimitContainer>
  );
};
export default EnrollmentLimitSection;
