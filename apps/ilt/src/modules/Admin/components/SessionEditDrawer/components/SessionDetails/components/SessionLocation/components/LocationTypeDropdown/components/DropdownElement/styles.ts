import { tokens } from '@mindtickle/styles/lib';

export const optionIconStyle = {
  height: 24,
  width: 24,
};

export const selectedIconStyle = {
  height: 20,
  width: 20,
};

export const classroomSelectedIconStyle = {
  ...selectedIconStyle,
  color: tokens.iconTokens.COLOR_ICON_DANGER,
  fontSize: '12px',
  padding: '4px',
  textAlign: 'center',
};

export const classroomOptionIconStyle = {
  ...optionIconStyle,
  color: tokens.iconTokens.COLOR_ICON_DANGER,
  fontSize: '16px',
  display: 'flex',
  alignItems: 'center',
  padding: '8px',
};

export const linkSelectedIconStyle = {
  ...selectedIconStyle,
  color: tokens.iconTokens.COLOR_ICON_ACCENT,
  textAlign: 'center',
  padding: '4px',
  fontWeight: 800,
  fontSize: '12px',
};

export const linkOptionIconStyle = {
  ...optionIconStyle,
  fontSize: '16px',
  color: tokens.iconTokens.COLOR_ICON_ACCENT,
  display: 'flex',
  alignItems: 'center',
  fontWeight: 800,
};
