import type { Dispatch, ReactNode, SetStateAction } from 'react';

export interface TLiveChallenge {
  deleteLiveChallenge: Function;
  hasSessionEnded: boolean;
  hasSessionStarted: boolean;
  isSessionPublished?: boolean;
  mode: string;
  newLiveChallenge: {
    isLoading: boolean;
    loaded: boolean;
    hasError: boolean;
    partial: boolean;
    data?: object;
  };
  liveChallenge: any;
  sessionId?: string;
  isLCAbsent: boolean;
  isReadOnlyModeEnabled: boolean;
  domain?: string;
}

export interface TDisplayLiveChallenge {
  lcData?: {
    uniqueCode?: string;
  };
  openModal: Function;
  deleteLiveChallenge: Function;
  hasLCEnded: boolean;
  hasSessionEnded: boolean;
  hasSessionStarted: boolean;
  isLcEdited: boolean;
  isSessionPublished?: boolean;
  sessionId?: string;
  domain?: string;
  isReadOnlyModeEnabled: boolean;
}

export interface TLCLinks {
  uniqueCode?: string;
  domain?: string;
}

export interface TLCDeleteContent {
  onCancel: Function;
  onConfirm: Function;
}

export interface TFileActionsBar {
  deleteLiveChallenge: Function;
  hasSessionEnded: boolean;
  hasSessionStarted: boolean;
  isLcEdited: boolean;
  isSessionPublished?: boolean;
  openUpdateLCModal: Function;
  sessionId?: string;
  isReadOnlyModeEnabled: boolean;
}

export interface TEmailLinksPopover {
  children?: React.ReactNode;
  isVisible: boolean;
  onCancel: () => void;
  onVisibleChange: Dispatch<SetStateAction<boolean>>;
  popoverContainer?: HTMLElement | null;
  sessionId?: string;
}

export interface TDeletePopover {
  children?: ReactNode;
  confirmDeleteLc: Function;
  isDeleteVisible: boolean;
  onCancelDelete: Function;
  onVisibleChange: Dispatch<SetStateAction<boolean>>;
}

export type TCreateLiveChallengeModal = {
  closeModal: (...args: any[]) => any;
  isProcessing: boolean;
  operation: string;
  visible: boolean;
};

export type TIncorrectFileModal = {
  visible: boolean;
  onClose: Function;
  message?: string;
};

export interface TMtLiveChallengeFileData {
  questionText: string;
  questionImage?: string;
  questionType: string;
  answerChoice1: string;
  answerChoice2: string;
  answerChoice3: string;
  answerChoice4: string;
}
