import { useCallback, useState } from 'react';

import { FormattedMessage } from 'react-intl';

import Pill, { PILL_STATES, PILL_TYPES } from '@mindtickle/pill';

import ShareLiveChallengeModal from '~/modules/Admin/components/ShareLiveChallengeModal';
import { LC_MODAL_ACTIONS, LIVE_CHALLENGE } from '~/modules/Admin/config/live-challenges.constants';

// import DeletePopover from '../DeletePopover';
import DeleteModal from '../DeleteModal';

import { StyledActionsContainer } from './styles';

import type { TFileActionsBar } from '../../../../typeDefs';

const FileActionsBar = ({
  openUpdateLCModal,
  deleteLiveChallenge,
  hasSessionEnded,
  hasSessionStarted,
  isLcEdited,
  isSessionPublished,
  sessionId,
  isReadOnlyModeEnabled,
}: TFileActionsBar) => {
  const [isDeleteVisible, setIsDeleteVisible] = useState(false);
  const [isShareEmailVisible, setIsShareEmailVisible] = useState(false);

  const onCancelDelete = useCallback(() => {
    setIsDeleteVisible(false);
  }, []);

  const confirmDeleteLc = useCallback(() => {
    setIsDeleteVisible(false);
    deleteLiveChallenge();
  }, [deleteLiveChallenge]);

  const onCancelShare = useCallback(() => {
    setIsShareEmailVisible(false);
  }, []);

  return (
    <StyledActionsContainer>
      {isSessionPublished && !hasSessionEnded && !isLcEdited && (
        <div id={'ilt_lc_email'}>
          <Pill
            className="editTextFlexItem"
            onClick={() => setIsShareEmailVisible(!isShareEmailVisible)}
            disabled={false}
            type={PILL_TYPES.ACTION}
          >
            <FormattedMessage {...LIVE_CHALLENGE.EMAIL_LINKS} />
          </Pill>
          {isShareEmailVisible && (
            <ShareLiveChallengeModal
              onCancel={onCancelShare}
              sessionId={sessionId}
              action={LC_MODAL_ACTIONS.EMAIL_LINKS}
            />
          )}
        </div>
      )}
      {!hasSessionStarted && (
        <>
          <Pill
            className="editTextFlexItem"
            onClick={() => openUpdateLCModal()}
            id={'ilt_lc_replace'}
            disabled={isReadOnlyModeEnabled}
            tagState={isReadOnlyModeEnabled ? PILL_STATES.DISABLED : PILL_STATES.NORMAL}
            type={PILL_TYPES.ACTION}
          >
            <FormattedMessage {...LIVE_CHALLENGE.REPLACE} />
          </Pill>

          <Pill
            className="editTextFlexItem"
            onClick={() => setIsDeleteVisible(!isDeleteVisible)}
            disabled={isReadOnlyModeEnabled}
            tagState={isReadOnlyModeEnabled ? PILL_STATES.DISABLED : PILL_STATES.NORMAL}
            type={PILL_TYPES.NEGATIVE}
          >
            <FormattedMessage {...LIVE_CHALLENGE.DELETE} />
          </Pill>

          {isDeleteVisible && (
            <DeleteModal confirmDeleteLc={confirmDeleteLc} onCancelDelete={onCancelDelete} />
          )}
        </>
      )}
    </StyledActionsContainer>
  );
};

export default FileActionsBar;
