import { ICON_MAP } from '@mindtickle/icon';
import { ICON_MAP as GRADIENT_ICON_MAP } from '@mindtickle/icon-with-gradient';

import { IconComponent } from './constants';
import {
  classroomOptionIconStyle,
  classroomSelectedIconStyle,
  linkSelectedIconStyle,
  linkOptionIconStyle,
} from './styles';

import type { TIconTypeImageMap } from '../../typeDefs';

export const iconTypeImageMap: TIconTypeImageMap = {
  classroom: {
    componentType: IconComponent.ICON,
    type: ICON_MAP.LOCATION_FILLED,
    selectedStyle: classroomSelectedIconStyle,
    optionStyle: classroomOptionIconStyle,
  },
  webex: { componentType: IconComponent.ICON_GRADIENT, type: GRADIENT_ICON_MAP.WEBEX },
  zoom: { componentType: IconComponent.ICON_GRADIENT, type: GRADIENT_ICON_MAP.ZOOM_ICON },
  msteams: { componentType: IconComponent.ICON_GRADIENT, type: GRADIENT_ICON_MAP.MICROSOFT_TEAMS },
  link: {
    componentType: IconComponent.ICON,
    type: ICON_MAP.AUTOMATION_LINKS,
    selectedStyle: linkSelectedIconStyle,
    optionStyle: linkOptionIconStyle,
  },
};
