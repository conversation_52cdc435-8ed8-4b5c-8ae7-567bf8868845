import styled from 'styled-components';

import { tokens } from '@mindtickle/styles/lib';

export const StyledFacilitatorsSection = styled.div`
  min-height: 200px;
  margin-top: 24px;
  & .action-section {
    margin-bottom: 26px;
  }
`;

export const StyledInfoContainer = styled.div<{ isReadOnlyModeEnabled: boolean }>`
  display: flex;
  align-items: center;
  margin-left: ${props => (props.isReadOnlyModeEnabled ? '0px' : '16px')};
  .notifyText {
    margin-left: 8px;
  }
  .notify-info-icon {
    color: ${tokens.iconTokens.COLOR_ICON_DEFAULT};
    font-size: 14px;
  }
`;

export const StyledFacilitatorsInfoWrapper = styled.div`
  display: flex;
  flex-direction: column-reverse;
`;
