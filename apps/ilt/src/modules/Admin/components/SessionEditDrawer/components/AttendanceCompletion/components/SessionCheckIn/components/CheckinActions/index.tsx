import { Fragment, useState, useRef } from 'react';

//components
import { FormattedMessage } from 'react-intl';

import CopyToClipboard from '@mindtickle/copy-to-clipboard';
import Icon from '@mindtickle/icon';
import Loader from '@mindtickle/loader';
import { successToast } from '@mindtickle/toast';
import Tooltip from '@mindtickle/tooltip';

import { TOAST_WRAPPER_ID } from '~/modules/Admin/components/SessionEditDrawer/constants';
import type {
  TUpdateSessionState,
  TSessionObject,
} from '~/modules/Admin/components/SessionEditDrawer/typeDefs';
import { OPERATIONS } from '~/modules/Admin/config/sessions.constants';
import { useILTAdminSnowplowTracker } from '~/modules/Admin/hooks/useILTAdminSnowplowTracker';
import { MIXPANEL_UI_EVENTS } from '~/modules/Admin/hooks/useILTAdminSnowplowTracker/constants';

import { CHECKIN_UI_LABELS } from '../../constants';
//misc
import { generatePNGfromHTML } from '../../utils';
import CheckinDetail from '../CheckinDetail';
import ShareQRCode from '../ShareQRCode';

interface SessionCheckIn {
  session: TSessionObject;
  updateSessionState: TUpdateSessionState;
  operationStatus: any;
  onShareCheckInCode: (params: { sessionId: string; session: any; emailIds: [string] }) => void;
}

const CheckinActions = (props: SessionCheckIn) => {
  const {
    session: {
      checkInSettings: { selfCheckInCode = '', isSelfCheckInActivated = false } = {},
    } = {},
    session,
    operationStatus: {
      hasError = false,
      operation,
      data: { operation: successOperation = '' } = {},
    },
  } = props;
  const [showLoader, setShowLoader] = useState(false);
  const [qrShared, setQrShared] = useState(false);
  const checkinDetailsRef = useRef();
  const tracker = useILTAdminSnowplowTracker();

  const toggleCopied = (copyText?: string) => {
    const {
      session: { id: sessionId },
    } = props;
    const {
      session: {
        checkInSettings: { isSelfCheckInActivated },
      },
    } = props;
    if (!isSelfCheckInActivated) {
      return;
    }
    if (copyText) {
      successToast({
        message: 'Check-in code copied to clipboard',
        timeout: 3000,
        mountId: TOAST_WRAPPER_ID,
      });
      tracker.trackStructuredEvent({
        eventName: MIXPANEL_UI_EVENTS.MODULE_ILT_SESSIONS_CHECKIN_CODE_COPIED,
        ilt_session_id: sessionId,
      });
    }
  };

  const toggleQRShared = () => setQrShared(true);

  const onDownloadSuccess = ({ imageGenerationTime }: { imageGenerationTime: number }) => {
    const {
      session: { id: sessionId },
    } = props;
    setShowLoader(false);
    tracker.trackStructuredEvent({
      eventName: MIXPANEL_UI_EVENTS.MODULE_ILT_SESSIONS_CHECKIN_CODE_DOWNLOADED,
      sessionId,
      image_generation_time_in_ms: imageGenerationTime,
    });
  };

  const downloadCheckinDetails = () => {
    const {
      session: {
        name,
        checkInSettings: { isSelfCheckInActivated },
      },
    } = props;
    if (!isSelfCheckInActivated) {
      return;
    }
    setShowLoader(true);
    try {
      generatePNGfromHTML(checkinDetailsRef.current, `Check-In Code_${name}`, onDownloadSuccess);
    } catch (e) {
      setShowLoader(false);
    }
  };

  return (
    <Fragment>
      <div className="qr-code-actions">
        <CopyToClipboard
          textToCopy={selfCheckInCode}
          onCopy={toggleCopied}
          className="qr-copy-clipboard"
          tooltipProps={{ placement: 'bottom' }}
        />

        <Tooltip
          className="qr-action-tooltip"
          placement="bottom"
          getPopupContainer={() => document.body}
          title={
            isSelfCheckInActivated ? (
              <FormattedMessage {...CHECKIN_UI_LABELS.CHECKIN_CODE_DOWNLOAD} />
            ) : (
              <FormattedMessage {...CHECKIN_UI_LABELS.CHECKIN_ACTION_DISABLED} />
            )
          }
        >
          <div
            className={`qr-action ${showLoader && 'loading'} ${
              isSelfCheckInActivated ? '' : 'disabled-icons'
            }`}
            onClick={downloadCheckinDetails}
          >
            {showLoader ? <Loader size="sizeXSmall" /> : <Icon type="Download" />}
          </div>
        </Tooltip>

        <ShareQRCode {...props} onSuccess={toggleQRShared} />

        <CheckinDetail checkinDetailsRef={checkinDetailsRef} session={session} />
      </div>
      {
        //TODO(SUGATA): Kept original implementation of success or error on share QR via email with fix of an existing prod bug wherein success status is maintained across sessions
        //TODO: Tried using toast but showing unexpected behaviour(Vanish). Will check later
      }
      {qrShared &&
        (successOperation === OPERATIONS.SHARE_CHECKIN_CODE && !hasError ? (
          <div className="qr-shared success">
            <Icon type="filledTick" /> <FormattedMessage {...CHECKIN_UI_LABELS.SHARE_QR_SUCCESS} />
          </div>
        ) : operation === OPERATIONS.SHARE_CHECKIN_CODE && hasError ? (
          <div className="qr-shared error">
            <Icon type="Remove" /> <FormattedMessage {...CHECKIN_UI_LABELS.SHARE_QR_FAILURE} />
          </div>
        ) : null)}
    </Fragment>
  );
};

export default CheckinActions;
