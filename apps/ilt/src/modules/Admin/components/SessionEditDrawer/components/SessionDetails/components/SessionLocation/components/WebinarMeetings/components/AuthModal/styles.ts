import styled from 'styled-components';

import Icon from '@mindtickle/icon';
import { tokens, theme } from '@mindtickle/styles/lib';

export const StyledIcon = styled(Icon)`
  font-size: 16px;
  color: ${tokens.iconTokens.COLOR_ICON_DANGER};
  margin-right: 12px;
`;

export const ModalContentWrapper = styled.div`
  .secondary-cta {
    cursor: pointer;
    font-weight: ${theme.fontWeight.SEMIBOLD};
    color: ${tokens.textTokens.COLOR_TEXT_ACCENT};
  }

  .error-wrapper {
    margin-top: 0px;
    margin-bottom: 24px;
    background-color: ${tokens.bgTokens.COLOR_BG_DANGER};
    padding: 8px 16px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    .note-wrapper {
      width: 92%;
    }
    .bold {
      font-weight: ${theme.fontWeight.SEMIBOLD};
    }
  }

  .input-label {
    margin-top: 0px;
    margin-bottom: 16px;
  }
  .secondary-cta-wrapper {
    margin-top: 8px;
  }
`;
