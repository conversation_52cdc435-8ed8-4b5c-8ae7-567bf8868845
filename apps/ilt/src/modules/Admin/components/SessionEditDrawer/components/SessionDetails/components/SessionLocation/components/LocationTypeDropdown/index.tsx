import { useRef } from 'react';

import Select from '@mindtickle/select';
import Tooltip from '@mindtickle/tooltip';

import {
  LOCATION_TYPE,
  LOCATION_TYPE_ICON_TYPE as OPTION_ICON_TYPE,
  LOCATION_TYPE_PRIMARY_TEXT as OPTION_PRIMARY_TEXT,
  LOCATION_TYPE_SECONDARY_TEXT as OPTION_SECONDARY_TEXT,
  WEBINAR_MEETING_TYPES,
  WEBINAR_VALIDATION_ENUM,
  INTEGRATION_SOURCE,
  LOCATION_TYPE_VIEW_TEXT,
} from '~/modules/Admin/config/sessions.constants';
import { getWebinarValidationMessage } from '~/modules/Admin/utils/sessionEdit';

import DropdownElement, { SelectedValueView } from './components/DropdownElement';
import useLocationOptions from './components/UseLocationOptions';
import { StyledSelectWrapper, toolTipStyle } from './styles';

import type { TGetSessionTypeOptionMapFunction, TLocationTypeDropdown } from '../../typeDefs';

const getSessionTypeOptionMapFunction = (props: TGetSessionTypeOptionMapFunction) => {
  const {
    webexMeetingInvalidTimeEnum,
    zoomMeetingInvalidTimeEnum,
    msteamsMeetingInvalidTimeEnum,
    integrationsInfo,
  } = props;
  let isZoomWriteAccessProvided = false;
  const { isIntegrationAvailable, integrationDataMap } = integrationsInfo || {};
  if (isIntegrationAvailable && integrationDataMap && integrationDataMap[INTEGRATION_SOURCE.zoom]) {
    if (
      integrationDataMap[INTEGRATION_SOURCE.zoom].hasOwnProperty('paramsValue') &&
      integrationDataMap[INTEGRATION_SOURCE.zoom].paramsValue.hasOwnProperty(
        'isManageMeetingAllowed'
      )
    ) {
      isZoomWriteAccessProvided =
        integrationDataMap[INTEGRATION_SOURCE.zoom].paramsValue.isManageMeetingAllowed;
    }
  }
  const mapOptionChildren = (child: { type: string }) => {
    const { type } = child;
    let disabled = false;
    let optionContent = (
      <span className="option-content">
        <DropdownElement
          iconType={OPTION_ICON_TYPE[type]}
          primaryText={OPTION_PRIMARY_TEXT[type]}
          secondaryText={OPTION_SECONDARY_TEXT[type]}
        />
      </span>
    );
    // Generalize for zoom/webex
    if (WEBINAR_MEETING_TYPES.includes(type)) {
      let meetingValidationEnum =
        type === LOCATION_TYPE.WEBEX_MEETING
          ? webexMeetingInvalidTimeEnum
          : type === LOCATION_TYPE.MS_TEAMS_MEETING
          ? msteamsMeetingInvalidTimeEnum
          : zoomMeetingInvalidTimeEnum;
      if (type === LOCATION_TYPE.ZOOM_MEETING && !isZoomWriteAccessProvided) {
        meetingValidationEnum = WEBINAR_VALIDATION_ENUM.WRITE_ACCESS_NOT_GRANTED;
      }
      disabled = !!meetingValidationEnum;
      optionContent = (
        <Tooltip
          title={getWebinarValidationMessage(meetingValidationEnum) || ''}
          style={toolTipStyle}
          overlayClassName={'tooltipInfo'}
          placement="top"
          trigger="hover"
        >
          {optionContent}
        </Tooltip>
      );
    }

    return (
      <Select.Option
        key={type}
        value={type}
        label={
          <SelectedValueView
            iconType={OPTION_ICON_TYPE[type]}
            labelText={OPTION_PRIMARY_TEXT[type]}
          />
        }
        disabled={disabled}
        className="location-select-item"
      >
        {optionContent}
      </Select.Option>
    );
  };
  return mapOptionChildren;
};

const LocationTypeDropdown = ({
  locationType,
  onSelect,
  webexMeetingInvalidTimeEnum,
  zoomMeetingInvalidTimeEnum,
  msteamsMeetingInvalidTimeEnum,
  integrationsInfo,
  isReadOnlyModeEnabled,
}: TLocationTypeDropdown) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const optionList = useLocationOptions({ integrationsInfo });
  const sessionTypeOptionMapFunction = getSessionTypeOptionMapFunction({
    webexMeetingInvalidTimeEnum,
    zoomMeetingInvalidTimeEnum,
    msteamsMeetingInvalidTimeEnum,
    integrationsInfo,
  });

  return (
    <StyledSelectWrapper ref={containerRef}>
      {isReadOnlyModeEnabled ? (
        <SelectedValueView
          iconType={OPTION_ICON_TYPE[locationType]}
          labelText={LOCATION_TYPE_VIEW_TEXT[locationType]}
        />
      ) : (
        <Select
          onChange={onSelect}
          getPopupContainer={() => containerRef.current}
          value={locationType}
          showTick={true}
          optionLabelProp={'label'}
          listHeight={340} // gives max height to the list inside dropdown for y-scrolling
        >
          {optionList.map(({ groupKey, groupText, children }) => (
            <Select.OptGroup label={groupText} key={groupKey}>
              {children.map(sessionTypeOptionMapFunction)}
            </Select.OptGroup>
          ))}
        </Select>
      )}
    </StyledSelectWrapper>
  );
};

export default LocationTypeDropdown;
