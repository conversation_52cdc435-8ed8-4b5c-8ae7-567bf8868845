import styled from 'styled-components';

import { tokens, mixins } from '@mindtickle/styles/lib';

import { tabContainerCommonMixin } from '../../styles';

export const StyledEnrolmentTabContainer = styled.div`
  ${tabContainerCommonMixin()}
`;

export const StyledActionRow = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 5px;
  flex-wrap: wrap;
  justify-content: space-between;
  & .tooltipInfo {
    z-index: 999999 !important;
    cursor: pointer;
    max-width: 340px;
  }
  & .infoHover {
    cursor: pointer;
    margin-left: 8px;
    color: ${tokens.iconTokens.COLOR_ICON_DEFAULT};
    font-size: 14px;
  }
`;

export const StyledInfoContainer = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 8px 16px;
  gap: 8px;
  width: fit-content;
  height: 32px;
  border-radius: 8px;
  background: ${tokens.bgTokens.COLOR_BG_DISABLED};
  margin-top: 16px;
`;

export const StyledEventEnrollmentNote = styled.span`
  ${mixins.blackText()}
  margin-right: 10px;
  font-size: 12px;

  .noteHeading {
    font-weight: 600;
    font-size: 12px;
  }

  .drawer-change-cta {
    font-weight: 600;
    color: ${tokens.textTokens.COLOR_TEXT_ACCENT};
    cursor: pointer;
  }
`;
