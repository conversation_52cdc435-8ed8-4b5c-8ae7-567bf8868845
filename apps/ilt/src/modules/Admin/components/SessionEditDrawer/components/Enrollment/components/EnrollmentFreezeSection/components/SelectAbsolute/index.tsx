import DateTimeSelector from '~/modules/Admin/components/DateTimeSelector';
import { getSessionDateFormat, getSessionsTableTimeFormat } from '~/modules/Admin/utils';
import { getCurrTimeWithOffset } from '~/utils/timezone';

import { LabeledSessionInfo } from '../../../../../LabeledSessionInfo';

import { EnrollmentFreezeDatepicker, StyledEnrollmentFreezeDateView } from './styles';

import type { TSelectAbsolute } from '../../../../typeDefs';

function SelectAbsolute(props: TSelectAbsolute) {
  const {
    sessionStartTime,
    freezeDate,
    setFreezeDate,
    validateError,
    disableFreezeEdit,
    timezone,
    isFreezePast,
    isReadModeEnabled,
  } = props;
  let currentTimeWithOffset = getCurrTimeWithOffset(timezone?.offset);
  let isPast = !disableFreezeEdit && isFreezePast;

  return (
    <>
      {isReadModeEnabled ? (
        <StyledEnrollmentFreezeDateView>
          <LabeledSessionInfo labelText="Start date" overlayClassName="enrollment-freeze-date-view">
            <div className={'freeze-date'}>{getSessionDateFormat(freezeDate)}</div>
          </LabeledSessionInfo>
          <LabeledSessionInfo labelText="Start time" className="enrollment-freeze-date-view">
            <div className={'freeze-date'}>{getSessionsTableTimeFormat(freezeDate)}</div>
          </LabeledSessionInfo>
          <span className={'freeze-date'}>{timezone.shortDisplayName}</span>
        </StyledEnrollmentFreezeDateView>
      ) : (
        <EnrollmentFreezeDatepicker
          validateError={validateError}
          disabled={disableFreezeEdit}
          isPast={isPast}
        >
          <DateTimeSelector
            withBtns={false}
            disable={true}
            disableDateRange={true}
            disableTimeRange={true}
            disableTimeEdit={disableFreezeEdit}
            min={currentTimeWithOffset}
            max={sessionStartTime}
            value={freezeDate}
            ok={value => setFreezeDate(value)}
            id="enrollmentFreeze"
            errorStatus={validateError || isPast}
          />
          <div className={'shortDisplayName'}>{timezone.shortDisplayName}</div>
        </EnrollmentFreezeDatepicker>
      )}
    </>
  );
}

export default SelectAbsolute;
