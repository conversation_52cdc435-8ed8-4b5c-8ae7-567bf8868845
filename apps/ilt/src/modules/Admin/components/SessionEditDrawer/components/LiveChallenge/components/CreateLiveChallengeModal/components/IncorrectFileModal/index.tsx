import React from 'react';

import Button from '@mindtickle/button';
import Modal from '@mindtickle/modal';

import { ErrorModalContent, ErrorModalFooter } from './styles';

import type { TIncorrectFileModal } from '../../../../typeDefs';

const IncorrectFileModal: React.FC<TIncorrectFileModal> = ({ visible, onClose, message }) => (
  <>
    {visible && (
      <Modal
        visible={visible}
        destroyOnClose
        closable={false}
        centered
        footer={null}
        zIndex={9990}
        width={400}
      >
        <ErrorModalContent>
          <div>
            {message ||
              'There is something wrong with the file uploaded. Please download the sample file to make the appropriate changes.'}
          </div>
          <ErrorModalFooter>
            <Button onClick={onClose} shape="round">
              OK
            </Button>
          </ErrorModalFooter>
        </ErrorModalContent>
      </Modal>
    )}
  </>
);

export default IncorrectFileModal;
