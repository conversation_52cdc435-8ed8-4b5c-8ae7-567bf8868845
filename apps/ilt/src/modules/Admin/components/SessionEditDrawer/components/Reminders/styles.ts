import styled, { css } from 'styled-components';

import { tokens, mixins } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS } from '~/config/constants';

import { tabContainerCommonMixin } from '../../styles';

const CommonTextStyle = css`
  ${mixins.blackLink()};
  text-align: center;
`;

export const StyledReminderTabContainer = styled.div`
  ${tabContainerCommonMixin()}
`;

export const StyledRemindersSection = styled.div`
  min-height: 200px;
  margin-top: 18px;
  & .action-section {
    margin-bottom: 24px;
  }
`;

export const StyledReminderTimes = styled.div``;

export const StyledReminderRow = styled.div`
  display: flex;
  align-items: baseline;
  flex-wrap: wrap;
  margin-bottom: 11px;

  > .${THEME_PREFIX_CLS}-form-item {
    margin-bottom: 5px;
    > .${THEME_PREFIX_CLS}-form-item-row {
      margin-bottom: 10px;
      .${THEME_PREFIX_CLS}-form-item-explain-error {
        margin-bottom: 0px;
      }
    }
    .${THEME_PREFIX_CLS}-form-item-explain-error {
      margin-top: 4px;
    }
  }
  .reminder-delete-icon {
    margin-left: 10px;
    cursor: pointer;
  }
`;

export const StyledInfoContainer = styled.div<{ isReadOnlyModeEnabled: boolean }>`
  display: flex;
  align-items: center;
  margin-left: ${props => (props.isReadOnlyModeEnabled ? '0px' : '8px')};
  .notify-text {
    margin-left: 8px;
  }
  .notify-info-icon {
    color: ${tokens.iconTokens.COLOR_ICON_DEFAULT};
    font-size: 14px;
  }
`;

export const StyledEmptyReminderContainer = styled.div`
  display: flex;
  flex-basis: calc(100% / 3);
  flex-direction: column;
  align-items: center;
  width: 45%;
  margin: 20% auto 0;
`;

export const StyledEmptyReminderHeader = styled.div`
  ${CommonTextStyle}
  color: ${tokens.textTokens.COLOR_TEXT_DEFAULT};
`;

export const StyledEmptyReminderBody = styled.div`
  ${CommonTextStyle}
  font-weight: 400;
  color: ${tokens.textTokens.COLOR_TEXT_SECONDARY};
  margin-top: 12px;
  .session-link-color {
    font-weight: 600;
    color: ${tokens.textTokens.COLOR_TEXT_ACCENT};
    cursor: pointer;
  }
`;

export const StyledRemindersView = styled.div`
  padding-right: 5px;
  display: flex;
  justify-content: space-between;
  .time-block {
    display: flex;
    justify-content: space-evenly;
    margin-right: 10px;
    &.days {
      width: 50px;
    }
    &.hours {
      width: 60px;
    }
    &.minutes {
      width: 75px;
    }
  }

  > span {
    margin-right: 16px;
  }
`;
