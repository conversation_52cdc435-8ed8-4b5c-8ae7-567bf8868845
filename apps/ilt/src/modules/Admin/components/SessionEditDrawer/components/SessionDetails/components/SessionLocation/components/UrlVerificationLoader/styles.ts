import styled from 'styled-components';

import { tokens } from '@mindtickle/styles/lib';
export const StyledActionArea = styled.div`
  margin-top: 16px;
  .urlLoader {
    display: inline-flex;
    height: 20px;
  }
  .loading {
    color: ${tokens.deprecatedTokens.COLOR_DEPRECATED_NAVBAR2};
  }

  .warning {
    color: ${tokens.textTokens.COLOR_TEXT_WARNING};
  }

  .success {
    color: ${tokens.textTokens.COLOR_TEXT_SUCCESS};
  }
  .retryButton {
    color: ${tokens.textTokens.COLOR_TEXT_ACCENT};
  }
`;
