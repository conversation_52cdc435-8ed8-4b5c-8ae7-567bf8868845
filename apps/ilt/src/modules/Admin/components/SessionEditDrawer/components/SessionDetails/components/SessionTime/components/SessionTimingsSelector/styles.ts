import styled from 'styled-components';

import { tokens, mixins } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS } from '~/config/constants';
export const StyledSessionDateView = styled.div`
  display: flex;
  align-items: center;
  column-gap: 12px;
  .time-label {
    margin-top: 0px;
  }
`;

export const StyledTimezoneDropdownContainer = styled.div`
  .timezone-dropdown-label {
    ${mixins.activeBlackLink()}
    padding-bottom: 8px;
  }
`;

export const StyledTimeBlock = styled.div`
  .date-time-error-message {
    color: ${tokens.textTokens.COLOR_TEXT_DANGER};
    margin-bottom: 16px;
    .drawer-change-cta {
      color: ${tokens.textTokens.COLOR_TEXT_ACCENT};
      cursor: pointer;
      margin-left: 8px;
    }
  }
  .session-date-row {
    height: 70px;
    margin-top: 5px;
  }

  .arrow-icon-col {
    margin-top: 15px;
  }

  .sessionTimezone button {
    width: 100%;
    text-align: left;
  }

  /* This is a workaround. To find a solution to this. need refactoring */
  .sessionTimezone :global(#timezones) {
    width: 100%;
  }

  .sessionTimezoneDisabled {
    color: rgba(0, 0, 0, 0.25);
    background-color: ${tokens.bgTokens.COLOR_BG_DEFAULT};
    cursor: unset;
  }

  .label-custom-margin label {
    color: ${tokens.deprecatedTokens.COLOR_DEPRECATED_NAVBAR2};
    margin-bottom: 2px;
    line-height: 20px;
  }

  .timeBlockWarning {
    float: left;
    font-style: italic;
    color: ${tokens.textTokens.COLOR_TEXT_SECONDARY};
    line-height: 18px;
    margin-top: 5px;
    margin-bottom: 5px;
  }

  .clear {
    clear: both;
  }

  .timeDisabledWarning {
    float: left;
    font-style: italic;
    line-height: 18px;
    margin-top: 5px;
  }

  :global(.${THEME_PREFIX_CLS}-calendar-picker) {
    margin-right: 5px;
  }

  :global(.${THEME_PREFIX_CLS}-calendar-picker-input) {
    width: 105px;
  }

  :global(.${THEME_PREFIX_CLS}-time-picker) {
    width: 75px;
  }

  .date-picker-wrapper-class.session-start-date-wrapper {
    .${THEME_PREFIX_CLS}-picker-panel-container {
      margin-left: 1px;
    }
  }
`;

export const styleTimezoneViewOnlyRow = {
  marginTop: '16px',
};
