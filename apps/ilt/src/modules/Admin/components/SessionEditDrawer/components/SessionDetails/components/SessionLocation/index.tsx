/* eslint-disable max-statements */
import { useCallback, useMemo, useState } from 'react';

import classnames from 'classnames';
//components
import { FormattedMessage } from 'react-intl';

import Icon from '@mindtickle/icon';
import Tooltip from '@mindtickle/tooltip';

import {
  INTEGRATION_SOURCE,
  WEBINAR_SOURCE,
  LOCATION_TYPE,
  LOCATION_DROPDOWN_LABEL,
  WEBINAR_MEETING_TYPES,
} from '~/modules/Admin/config/sessions.constants';
import {
  getClassroomLocationTooltip,
  getStartEndTimeWithOffset,
} from '~/modules/Admin/utils/sessionEdit';

import { StyledLabel } from '../../../../styles';
import { LOCATION_TYPE_ACTION } from '../../constants';

import LocationTypeChangeConfirm from './components/LocationTypeChangeConfirm';
import LocationTypeDropdown from './components/LocationTypeDropdown';
import LocationTypeView from './components/LocationTypeView';
import {
  WebexListenerComponent,
  ZoomListenerComponent,
  MSTeamsListenerComponent,
} from './components/WebinarMeetings';
import { StyledSessionLocationWrapper } from './styles';

import type { SessionLocationProps } from '../../typeDefs';

export default function SessionLocation(props: SessionLocationProps) {
  // This state variable holds future value of locationtype.
  // ex: in webexmeeting creation, by using this state var we show meeting creation flow, if meeting created, we assign location type the value stored in this state otherwise discard it so that selection before meeting creation is retained.
  const [preChangeLocationType, setPreChangeLocationType] = useState('');
  const [updateConfirmData, setUpdateConfirmData] = useState<any>();
  const [isClassroomToLink, setIsClassroomToLink] = useState(false);
  const {
    session,
    updateSessionState,
    integrations,
    mode,
    onLocationTypeChange,
    updateWebexMeetingStatus,
    updateZoomMeetingStatus,
    updateMSTeamsMeetingStatus,
    webexMeetingStatusTrack,
    zoomMeetingStatusTrack,
    msteamsMeetingStatusTrack,
    isOngoingOrPastSession,
    userAuth,
    isReadOnlyModeEnabled,
    moduleDetails,
  } = props;

  const integrationsInfo = useMemo(() => {
    const { data: integrationDataMap = {} } = integrations || {};
    const integrationSourceList = Object.keys(integrationDataMap);
    const isIntegrationAvailable = integrationSourceList.length > 0;
    return {
      isIntegrationAvailable,
      integrationSourceList,
      integrationDataMap,
    };
  }, [integrations]);

  const { meetingInvalidTimeEnum: webexMeetingInValidTimeEnum } = webexMeetingStatusTrack;
  const { meetingInvalidTimeEnum: zoomMeetingInvalidTimeEnum } = zoomMeetingStatusTrack;
  const { meetingInvalidTimeEnum: msteamsMeetingInvalidTimeEnum } = msteamsMeetingStatusTrack || {};
  const {
    locationType,
    location,
    startTime,
    endTime,
    timezone,
    name: sessionName,
    webAutoAttendanceSettings,
  } = session;

  const { endTimeWithOffSet, startTimeWithOffSet } = getStartEndTimeWithOffset({
    startTime,
    endTime,
    timezone,
  });

  const onUrlEligibleForValidation = useCallback(() => {
    setIsClassroomToLink(true);
    onLocationTypeChange?.(LOCATION_TYPE_ACTION.CHANGE, LOCATION_TYPE.LINK, undefined);
  }, [onLocationTypeChange]);

  const handleManuallyChangedLocationType = (
    operation: string,
    value: any,
    throughConfirm: any
  ) => {
    if (operation === LOCATION_TYPE_ACTION.CHANGE) {
      if (WEBINAR_MEETING_TYPES.includes(value)) {
        setPreChangeLocationType(value);
      } else {
        onLocationTypeChange?.(operation, value, undefined);
      }
    } else {
      // operation === clear
      onLocationTypeChange?.(operation, value, undefined);
    }
    if (throughConfirm) {
      setUpdateConfirmData(undefined);
      if (locationType === LOCATION_TYPE.FACE_TO_FACE && value === LOCATION_TYPE.LINK) {
        setIsClassroomToLink(true);
      }
    }
  };

  const onSelect = (value: any) => {
    const operation = LOCATION_TYPE_ACTION.CHANGE;
    if (location) {
      setUpdateConfirmData({ operation, value });
    } else {
      handleManuallyChangedLocationType(operation, value, undefined);
    }
  };

  const onClear = () => {
    setUpdateConfirmData({ operation: LOCATION_TYPE_ACTION.CLEAR, value: undefined });
  };

  const onOkUpdateConfirm = () => {
    handleManuallyChangedLocationType(updateConfirmData.operation, updateConfirmData.value, true);
  };

  const onCancelUpdateConfirm = () => setUpdateConfirmData(undefined);

  const clearPreChangeLocationType = () => setPreChangeLocationType('');

  const webexListenerComponent = !isReadOnlyModeEnabled &&
    !!integrationsInfo.integrationDataMap[INTEGRATION_SOURCE.webex] && (
      <WebexListenerComponent
        mode={mode}
        meetingStatusTrack={webexMeetingStatusTrack}
        sessionName={sessionName}
        locationType={locationType}
        integrationSource={INTEGRATION_SOURCE.webex}
        webinarSource={WEBINAR_SOURCE.WEBEX}
        listenerLocationType={LOCATION_TYPE.WEBEX_MEETING}
        webAutoAttendanceSettings={webAutoAttendanceSettings}
        userAuth={userAuth}
        onLocationTypeChange={onLocationTypeChange}
        isOngoingOrPastSession={isOngoingOrPastSession}
        integrationsInfo={integrationsInfo}
        updateMeetingStatus={updateWebexMeetingStatus}
        endTimeWithOffSet={endTimeWithOffSet}
        startTimeWithOffSet={startTimeWithOffSet}
        clearPreChangeLocationType={clearPreChangeLocationType}
        preChangeLocationType={preChangeLocationType}
        setPreChangeLocationType={setPreChangeLocationType}
        session={session}
        moduleDetails={moduleDetails}
      />
    );

  const zoomListenerComponent = !isReadOnlyModeEnabled &&
    !!integrationsInfo.integrationDataMap[INTEGRATION_SOURCE.zoom] && (
      <ZoomListenerComponent
        mode={mode}
        meetingStatusTrack={zoomMeetingStatusTrack}
        sessionName={sessionName}
        locationType={locationType}
        integrationSource={INTEGRATION_SOURCE.zoom}
        webinarSource={WEBINAR_SOURCE.ZOOM}
        listenerLocationType={LOCATION_TYPE.ZOOM_MEETING}
        webAutoAttendanceSettings={webAutoAttendanceSettings}
        userAuth={userAuth}
        onLocationTypeChange={onLocationTypeChange}
        isOngoingOrPastSession={isOngoingOrPastSession}
        integrationsInfo={integrationsInfo}
        updateMeetingStatus={updateZoomMeetingStatus}
        endTimeWithOffSet={endTimeWithOffSet}
        startTimeWithOffSet={startTimeWithOffSet}
        clearPreChangeLocationType={clearPreChangeLocationType}
        preChangeLocationType={preChangeLocationType}
        setPreChangeLocationType={setPreChangeLocationType}
        session={session}
        moduleDetails={moduleDetails}
      />
    );

  const msteamsListenerComponent = !isReadOnlyModeEnabled &&
    !!integrationsInfo.integrationDataMap[INTEGRATION_SOURCE.ms_teams] && (
      <MSTeamsListenerComponent
        mode={mode}
        meetingStatusTrack={msteamsMeetingStatusTrack}
        sessionName={sessionName}
        locationType={locationType}
        integrationSource={INTEGRATION_SOURCE.ms_teams}
        webinarSource={WEBINAR_SOURCE.MS_TEAMS}
        listenerLocationType={LOCATION_TYPE.MS_TEAMS_MEETING}
        webAutoAttendanceSettings={webAutoAttendanceSettings}
        userAuth={userAuth}
        onLocationTypeChange={onLocationTypeChange}
        isOngoingOrPastSession={isOngoingOrPastSession}
        integrationsInfo={integrationsInfo}
        updateMeetingStatus={updateMSTeamsMeetingStatus}
        endTimeWithOffSet={endTimeWithOffSet}
        startTimeWithOffSet={startTimeWithOffSet}
        clearPreChangeLocationType={clearPreChangeLocationType}
        preChangeLocationType={preChangeLocationType}
        setPreChangeLocationType={setPreChangeLocationType}
        session={session}
        moduleDetails={moduleDetails}
      />
    );

  const tooltipTexts = getClassroomLocationTooltip(integrationsInfo.integrationSourceList);

  const tooltip = (
    <Tooltip
      overlayClassName={classnames('tooltipInfo')}
      title={
        <span>
          <p>{tooltipTexts.primaryText}</p>
          <p>{tooltipTexts.secondaryText}</p>
        </span>
      }
    >
      <Icon type="info2" className={'infoHover'} />
    </Tooltip>
  );

  return (
    <StyledSessionLocationWrapper
      className={classnames('label-custom-margin', 'common-margin-top')}
    >
      <StyledLabel>
        <FormattedMessage {...LOCATION_DROPDOWN_LABEL} />
        {integrationsInfo.isIntegrationAvailable && tooltip}
      </StyledLabel>
      <LocationTypeDropdown
        onSelect={onSelect}
        webexMeetingInvalidTimeEnum={webexMeetingInValidTimeEnum}
        zoomMeetingInvalidTimeEnum={zoomMeetingInvalidTimeEnum}
        msteamsMeetingInvalidTimeEnum={msteamsMeetingInvalidTimeEnum}
        locationType={locationType}
        integrationsInfo={integrationsInfo}
        isReadOnlyModeEnabled={isReadOnlyModeEnabled}
      />
      <LocationTypeView
        mode={mode}
        session={session}
        updateSessionState={updateSessionState}
        updateWebexMeetingStatus={updateWebexMeetingStatus}
        updateZoomMeetingStatus={updateZoomMeetingStatus}
        updateMSTeamsMeetingStatus={updateMSTeamsMeetingStatus}
        isOngoingOrPastSession={isOngoingOrPastSession}
        integrationsInfo={integrationsInfo}
        startTimeWithOffSet={startTimeWithOffSet}
        endTimeWithOffSet={endTimeWithOffSet}
        onUrlEligibleForValidation={onUrlEligibleForValidation}
        onClear={onClear}
        manuallyFace2FaceToLink={isClassroomToLink}
        setIsClassroomToLink={setIsClassroomToLink}
        isReadOnlyModeEnabled={isReadOnlyModeEnabled}
      />
      {webexListenerComponent}
      {zoomListenerComponent}
      {msteamsListenerComponent}
      {!!updateConfirmData && (
        // To use Modal.confirm for this in revamp as Modal.confirm has close icon in >v4.9
        <LocationTypeChangeConfirm
          onCancelUpdateConfirm={onCancelUpdateConfirm}
          onOkUpdateConfirm={onOkUpdateConfirm}
          locationType={locationType}
          newLocationType={updateConfirmData.value}
        />
      )}
    </StyledSessionLocationWrapper>
  );
}
