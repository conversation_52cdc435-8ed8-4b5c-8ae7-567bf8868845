import Description from '@mindtickle/description';
import Form from '@mindtickle/form';
import InnerHTML from '@mindtickle/string-to-html';

import { LabeledSessionInfo } from '../../../LabeledSessionInfo';

import type { TSessionDescription } from '../../typeDefs';

const SessionDescription = ({
  description,
  updateSessionState,
  isReadOnlyModeEnabled,
}: TSessionDescription) => {
  let childProps = {
    maxLength: 1000,
    content: description,
    onChange: (value: string) => {
      updateSessionState({ key: 'description', value });
    },
    id: 'sessionDescription',
    className: 'session-description-content',
    bounds: `.session-description-content`,
  };
  const strippedDescription = isReadOnlyModeEnabled ? description.replace(/<[^>]+>/g, '') : '';

  return (
    <>
      {isReadOnlyModeEnabled ? (
        <>
          {strippedDescription.length > 0 && (
            <LabeledSessionInfo
              overlayClassName={'description-readonly-overlay'}
              labelText="Description"
              htmlFor="session-description-view"
            >
              <InnerHTML content={description} id="session-description-view" />
            </LabeledSessionInfo>
          )}
        </>
      ) : (
        <Form.Item
          label="Description"
          labelCol={{ span: 24 }}
          wrapperCol={{ span: 24 }}
          name="sessionDescription"
          className={'session-description'}
        >
          <Description placeholder="Enter session description" {...childProps} />
        </Form.Item>
      )}
    </>
  );
};

export default SessionDescription;
