import type { Dispatch, SetStateAction } from 'react';

import type {
  TSessionObject,
  TUpdateSessionState,
  TUpdateMeetingStatus,
  TIntegrationInfo,
  TInstructor,
  TWebAutoAttendanceSettings,
  TWebinarMeetingSettings,
} from '~/modules/Admin/components/SessionEditDrawer/typeDefs';

import type { InjectedIntlProps } from 'react-intl';

export type TAutoAttendanceToggle = {
  className?: string;
  isAutoAttendanceEnabled?: boolean;
  updateAutoAttendanceStatus?: (value: any) => void;
  webinarSource?: string;
  isReadOnlyModeEnabled: boolean;
};

export type TClassroomLocation = {
  session: TSessionObject;
  updateSessionState: TUpdateSessionState;
  integrationsInfo: TIntegrationInfo;
  onUrlEligibleForValidation: Function;
} & InjectedIntlProps;

export type TModalFooter = {
  onCancel?: () => void;
  onOk?: () => void;
};

export type TLocationTypeChangeConfirmModal = {
  locationType?: string;
  newLocationType?: string;
  onCancelUpdateConfirm?: () => void;
  onOkUpdateConfirm?: () => void;
};

export interface TGetSessionTypeOptionMapFunction {
  onSelect?: (locationType: string) => void;
  webexMeetingInvalidTimeEnum?: string;
  zoomMeetingInvalidTimeEnum?: string;
  msteamsMeetingInvalidTimeEnum?: string;
  locationType?: string;
  integrationsInfo?: TIntegrationInfo;
}

export interface TLocationTypeDropdown {
  locationType: string;
  onSelect?: (value: string) => void;
  webexMeetingInvalidTimeEnum: string;
  zoomMeetingInvalidTimeEnum: string;
  msteamsMeetingInvalidTimeEnum: string;
  integrationsInfo: TIntegrationInfo;
  isReadOnlyModeEnabled: boolean;
}

export interface TLocationTypeView {
  mode?: string;
  session: TSessionObject;
  updateSessionState: TUpdateSessionState;
  updateWebexMeetingStatus: TUpdateMeetingStatus;
  updateZoomMeetingStatus: TUpdateMeetingStatus;
  updateMSTeamsMeetingStatus: TUpdateMeetingStatus;
  isOngoingOrPastSession: boolean;
  integrationsInfo: TIntegrationInfo;
  startTimeWithOffSet: number;
  endTimeWithOffSet: number;
  onUrlEligibleForValidation: Function;
  onClear: () => void;
  manuallyFace2FaceToLink?: boolean;
  setIsClassroomToLink?: Dispatch<SetStateAction<boolean>>;
  isReadOnlyModeEnabled: boolean;
}

export interface TUrlVerificationLoader {
  loading?: boolean;
  integrationSource?: string;
}

export interface TUseUrlValidation {
  integrationsInfo: any;
  onValidationSuccess?: (data: any) => void;
  onValidationNoSuccess?: (data: any) => void;
  debounceTimeout?: number;
}

export type TValidateURLFunction = ({
  value,
  password,
  userEmail,
  signal,
}: {
  value: string;
  password?: string | undefined;
  userEmail?: string;
  signal: AbortSignal;
}) => Promise<void>;

export interface TCheckToShowPasswordSection {
  isIntegrationAvailable: boolean;
  webinarSource?: string;
  lastValidityStatus: string;
  integrationDataMap: TIntegrationInfo['integrationDataMap'];
}

export interface TCheckToShowAuthSection {
  isIntegrationAvailable: boolean;
  webinarSource?: string;
  lastValidityStatus: string;
  integrationDataMap: TIntegrationInfo['integrationDataMap'];
}

export interface TCheckToShowHostEmailSection {
  isIntegrationAvailable: boolean;
  webinarSource?: string;
  lastValidityStatus: string;
  isWebexUserLevelAuthEnabled: boolean;
}

export type TWebinarLocation = {
  session: TSessionObject;
  integrationsInfo: TIntegrationInfo;
  updateSessionState: TUpdateSessionState;
  mode?: string;
  validateUrlOnMount?: boolean;
  resetValidateUrlOnMount?: Dispatch<SetStateAction<boolean>>;
  isReadOnlyModeEnabled: boolean;
} & InjectedIntlProps;

export interface TUpdateAutoAttendanceSettings {
  validationData: any;
  canRetry?: boolean;
  data?: object;
}

export type TPasswordSection = {
  validationMessage?: string;
  password?: string;
  onSave: (value: any) => any;
  onClear: () => void;
  isLoading?: boolean;
  loadingState?: any;
};

export type TWebinarMeeting = {
  location: string;
  webinarMeetingSettings?: TWebinarMeetingSettings;
  webAutoAttendanceSettings?: TWebAutoAttendanceSettings;
  onClose: () => void;
  updateMeetingStatus: TUpdateMeetingStatus;
  updateSessionState: TUpdateSessionState;
  isOngoingOrPastSession: boolean;
  instructors?: TInstructor[];
  facilitators?: string[];
  webinarSource: string;
  isReadOnlyModeEnabled: boolean;
  sessionStartDate: number;
  sessionEndDate: number;
  sessionName: string;
};
