import React, { useState } from 'react';

import { SecondaryDescription } from '@mindtickle/description';
import Divider from '@mindtickle/divider';
import Form from '@mindtickle/form';
import Grid from '@mindtickle/grid';
import Icon from '@mindtickle/icon';
import Input from '@mindtickle/input';
import InnerHTML from '@mindtickle/string-to-html';

import { LabeledSessionInfo } from '../../../../../LabeledSessionInfo';
import { verticalDividerStyle } from '../../../../styles';

import { StyledInstructorSection, StyledInstructorHeader, StyledInstructorContent } from './styles';

import type { TInstructorDetails } from '../../../../typeDefs';
const { Row, Col } = Grid;

const InstructorDetails = (props: TInstructorDetails) => {
  const {
    instructor,
    index,
    updateInstructor,
    removeInstructor,
    getCohostCheckbox,
    isReadOnlyModeEnabled,
    form,
  } = props;
  const [showDescriptionAfterMount, setShowDescriptionAfterMount] = useState(false);
  const cohostCheckbox = getCohostCheckbox(instructor.email, `instructorAddAsCoHost${index}`);
  const showCohost = !!cohostCheckbox;

  React.useEffect(() => {
    setShowDescriptionAfterMount(true);
  }, []);

  React.useEffect(() => {
    form.setFieldsValue({
      [`instructorName${index}`]: instructor.name,
      [`instructorEmail${index}`]: instructor.email,
      [`instructorDescription${index}`]: instructor.description,
    });
  }, [form, index, instructor]);

  const renderInstructorName = () => {
    const childProps = {
      name: `instructorName${index}`,
      value: instructor.name,
      onChange: (event: React.FormEvent<HTMLInputElement>) =>
        updateInstructor('name', index, (event.target as HTMLInputElement).value),
    };

    return (
      <>
        {isReadOnlyModeEnabled ? (
          <LabeledSessionInfo labelText="Instructor name" htmlFor="instructor-name-view">
            <div id="instructor-name-view">{instructor.name}</div>
          </LabeledSessionInfo>
        ) : (
          <Form.Item
            label="Instructor name*"
            labelCol={{ span: 24 }}
            wrapperCol={{ span: 24 }}
            name={`instructorName${index}`}
            rules={[{ required: true, message: 'Please enter instructor name' }]}
          >
            <Input placeholder="Instructor name" {...childProps} />
          </Form.Item>
        )}
      </>
    );
  };

  const renderInstructorEmail = () => {
    const childProps = {
      name: `instructorEmail${index}`,
      value: instructor.email,
      onChange: (event: React.FormEvent<HTMLInputElement>) =>
        updateInstructor('email', index, (event.target as HTMLInputElement).value),
    };

    return (
      <>
        {isReadOnlyModeEnabled ? (
          <LabeledSessionInfo labelText="Instructor email id" htmlFor="instructor-email-view">
            <div id="instructor-email-view">{instructor.email}</div>
          </LabeledSessionInfo>
        ) : (
          <Form.Item
            label="Instructor email id*"
            labelCol={{ span: 24 }}
            wrapperCol={{ span: 24 }}
            name={`instructorEmail${index}`}
            rules={[
              { required: true, message: 'Please enter instructor email' },
              { type: 'email', message: 'Please enter a valid email' },
            ]}
          >
            <Input placeholder="Instructor email id" {...childProps} />
          </Form.Item>
        )}
      </>
    );
  };

  const renderInstructorDescription = () => {
    const childProps = {
      maxLength: 1000,
      content: instructor.description,
      onChange: (value: any) => updateInstructor('description', index, value),
      id: `instructorDescription${index}`,
      className: `instructor-description instructor-description-identifier-${index}`,
      bounds: `.instructor-description-identifier-${index}`,
    };

    return (
      <>
        {isReadOnlyModeEnabled ? (
          <>
            {instructor.description && (
              <LabeledSessionInfo
                labelText="Instructor description"
                htmlFor={'instructor-description-view' + index}
              >
                <InnerHTML
                  id={'instructor-description-view' + index}
                  content={instructor.description}
                />
              </LabeledSessionInfo>
            )}
          </>
        ) : (
          <Form.Item
            label="Instructor description"
            labelCol={{ span: 24 }}
            wrapperCol={{ span: 24 }}
            name={`instructorDescription${index}`}
          >
            <SecondaryDescription placeholder="Instructor description" {...childProps} />
          </Form.Item>
        )}
      </>
    );
  };

  return (
    <StyledInstructorSection>
      <StyledInstructorHeader isReadOnlyModeEnabled={isReadOnlyModeEnabled}>
        <span>{`Instructor ${index + 1}`}</span>
        {cohostCheckbox}
        {!isReadOnlyModeEnabled && (
          <>
            <Divider style={verticalDividerStyle} type="vertical" />
            <Icon
              type="delete"
              className="deleteInstructorIcon"
              onClick={() => removeInstructor(showCohost, instructor.email, index)}
            />
          </>
        )}
      </StyledInstructorHeader>
      <StyledInstructorContent>
        <Row align="middle" className={'instructor-input-row'}>
          <Col span={12} className={'instructor-input-col'}>
            {renderInstructorName()}
          </Col>
          <Col span={12} className={'instructor-input-col'}>
            {renderInstructorEmail()}
          </Col>
        </Row>
        <Row align="middle">
          <Col span={24}>{showDescriptionAfterMount && renderInstructorDescription()}</Col>
        </Row>
      </StyledInstructorContent>
      {index > 0 && <Divider className="instructor-section-divider" />}
    </StyledInstructorSection>
  );
};

export default InstructorDetails;
