import styled, { css } from 'styled-components';

import { tokens } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS } from '~/config/constants';
const mixinPadding = css`
  padding: 9px 10px;
`;

export const StyledSelectWrapper = styled.div`
  margin-bottom: 8px;
  margin-top: 6px;
  & .${THEME_PREFIX_CLS}-select {
    width: 100%;
  }
  & .${THEME_PREFIX_CLS}-select-item {
    padding: 6px 8px;
  }
  & .${THEME_PREFIX_CLS}-select-item-option {
    & .tooltipInfo {
      z-index: 999999 !important;
      cursor: pointer;
      max-width: 360px;
      .${THEME_PREFIX_CLS}-tooltip-inner {
        white-space: pre-wrap;
      }
    }
  }
  .${THEME_PREFIX_CLS}-select-selector {
    border-color: ${tokens.iconTokens.COLOR_ICON_INVERSE_TERTIARY};
  }
  .${THEME_PREFIX_CLS}-select-dropdown {
    border: 1px solid ${tokens.borderTokens.COLOR_BORDER_DEFAULT};
  }
  .${THEME_PREFIX_CLS}-select-item-group {
    ${mixinPadding};
    margin: 0;
    font-weight: 600;
    font-size: 11px;
    line-height: 16px;
    color: ${tokens.textTokens.COLOR_TEXT_DEFAULT};
  }
  .${THEME_PREFIX_CLS}-select-item-option-grouped {
    ${mixinPadding};
    padding-left: 16px;
    margin: 0;
  }

  .${THEME_PREFIX_CLS}-select-item-option-active:not(.${THEME_PREFIX_CLS}-select-item-option-disabled):not(
      .${THEME_PREFIX_CLS}-select-item-option-selected
    ) {
    background-color: transparent;
  }

  .${THEME_PREFIX_CLS}-select-item.${THEME_PREFIX_CLS}-select-item-option-disabled {
    pointer-events: auto;
    color: inherit;
    .option-content {
      .location-icon {
        filter: grayscale(1);
      }
      .option-text {
        opacity: 0.5;
      }
    }
    &:hover {
      background-color: ${tokens.bgTokens.COLOR_BG_DEFAULT};
      color: inherit;
      cursor: not-allowed;
    }
  }
  .${THEME_PREFIX_CLS}-select-dropdown {
    padding: 0;
    .${THEME_PREFIX_CLS}-select-item-option-selected .icon-tick2 {
      color: ${tokens.textTokens.COLOR_TEXT_ACCENT};
      top: 50%;
      transform: translateY(-50%);
      font-size: 10px;
      right: 24px;
    }
    .location-select-item {
      border-radius: 4px;
      &:hover {
        text-decoration: none;
        background-color: ${tokens.bgTokens.COLOR_BG_TERTIARY};
        color: inherit;
      }
    }
  }
`;

const primaryTextMixin = css`
  font-size: 13px;
  color: ${tokens.textTokens.COLOR_TEXT_DEFAULT};
  line-height: 18px;
`;

export const StyledSelectedTextwrapper = styled.div`
  display: flex;
  align-items: center;
  height: 100%;
  .selected-label-text {
    ${primaryTextMixin};
    margin-left: 8px;
  }
`;

export const StyledDropdownElement = styled.div`
  display: flex;
  align-items: center;
  .location-icon {
    width: 24px;
    height: 24px;
  }

  .option-text {
    font-weight: 400;
    margin-left: 8px;
    .option-text-primary {
      ${primaryTextMixin};
    }
    .option-text-secondary {
      font-size: 12px;
      color: ${tokens.textTokens.COLOR_TEXT_SECONDARY};
      line-height: 16px;
      margin-top: 2px;
    }
  }
`;

export const toolTipStyle = { zIndex: 999999 };
