import { FunctionComponent } from 'react';

import { FormattedMessage } from 'react-intl';
import { useSelector } from 'react-redux';

import Icon, { ICON_MAP } from '@mindtickle/icon';
import { successToast } from '@mindtickle/toast';
import Tooltip from '@mindtickle/tooltip';
import { Link } from '@mindtickle/typography';
import { getSubDomain } from '@mindtickle/utils/url';

import { TOAST_WRAPPER_ID } from '~/modules/Admin/components/SessionEditDrawer/constants';
import {
  LIVE_CHALLENGE,
  LIVE_CHALLENGE_PATH,
  LIVE_CHALLENGE_SUB_DOMAIN,
} from '~/modules/Admin/config/live-challenges.constants';

import { LCLink, StyledLiveChallengeLinkContainer } from './styles';

import type { TLCLinks } from '../../../../typeDefs';

const LCLinks: FunctionComponent<TLCLinks> = ({ uniqueCode, domain }) => {
  const companyUrl = useSelector((state: any) => state.ilt.context.companyUrl);
  const playerLink = `${companyUrl}/${LIVE_CHALLENGE_PATH}/${uniqueCode}`;
  const domainFromUrl = getSubDomain(window.location.host);
  let moderatorLink: string | null = null;
  if (domain === 'us-east-1') {
    moderatorLink = `${LIVE_CHALLENGE_SUB_DOMAIN}.prod-us.mindtickle.com/moderator?code=dab&gameCode=${uniqueCode}`;
  } else {
    moderatorLink = `${LIVE_CHALLENGE_SUB_DOMAIN}.${domainFromUrl}/moderator?code=dab&gameCode=${uniqueCode}`;
  }

  const copyToClipboard = (text: string | null, successMessage: JSX.Element) => {
    if (text) {
      navigator.clipboard.writeText(text);
      successToast({ message: successMessage, timeout: 3000, mountId: TOAST_WRAPPER_ID });
    }
  };

  return (
    <StyledLiveChallengeLinkContainer>
      <LCLink>
        <span className="lcLinkType">
          <FormattedMessage {...LIVE_CHALLENGE.MODERATOR_LINK} />
        </span>
        <span className="lcLink">
          <Link ellipsis href={'https://' + moderatorLink} target="_blank">
            {moderatorLink}
          </Link>

          <Tooltip
            title={<FormattedMessage {...LIVE_CHALLENGE.COPY_MODERATOR_LINK} />}
            overlayClassName={'tooltipInfo'}
          >
            <Icon
              type={ICON_MAP.COPYTO_CLIPBOARD}
              className="copyIcon"
              onClick={() =>
                copyToClipboard(
                  moderatorLink,
                  <FormattedMessage {...LIVE_CHALLENGE.MODERATOR_LINK_COPIED} />
                )
              }
            />
          </Tooltip>
        </span>
      </LCLink>
      <LCLink>
        <span className="lcLinkType">
          <FormattedMessage {...LIVE_CHALLENGE.PLAYER_LINK} />
        </span>
        <span className="lcLink">
          <Link ellipsis href={'https://' + playerLink} target="_blank">
            {playerLink}
          </Link>
          <Tooltip
            title={<FormattedMessage {...LIVE_CHALLENGE.COPY_PLAYER_LINK} />}
            overlayClassName={'tooltipInfo'}
          >
            <Icon
              type={ICON_MAP.COPYTO_CLIPBOARD}
              className="copyIcon"
              onClick={() =>
                copyToClipboard(
                  playerLink,
                  <FormattedMessage {...LIVE_CHALLENGE.PLAYER_LINK_COPIED} />
                )
              }
            />
          </Tooltip>
        </span>
      </LCLink>
    </StyledLiveChallengeLinkContainer>
  );
};

export default LCLinks;
