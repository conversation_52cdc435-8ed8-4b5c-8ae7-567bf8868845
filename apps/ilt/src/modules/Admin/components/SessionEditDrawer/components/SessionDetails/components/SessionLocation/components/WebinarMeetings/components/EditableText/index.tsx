import { useCallback, useEffect, useRef, useState, ReactElement } from 'react';

import { FormattedMessage } from 'react-intl';

import AutoComplete from '@mindtickle/auto-complete';
import Icon from '@mindtickle/icon';
import Pill, { PILL_TYPES } from '@mindtickle/pill';

import { EDIT_HOST_COHOST_VALIDATION_MESSAGES } from '~/modules/Admin/config/sessions.constants';
import {
  required as checkRequired,
  email as checkValidEmail,
} from '~/modules/Admin/utils/formValidator';
import { EMPTY_ARRAY_READONLY } from '~/mt-ui-core/config/global.config';

import { StyledEdit, StyledFormItem } from '../../../../styles';

import type { TOnMountObject, TEditableText } from '../../typeDefs';
const {
  INVALID_EMAIL_MESSAGE,
  REQUIRED_EMAIL_MESSAGE,
  EXISTING_COHOST_EMAIL_MESSAGE: EXISTING_EMAIL_MESSAGE,
  SAVE_CHANGES_MESSAGE,
} = EDIT_HOST_COHOST_VALIDATION_MESSAGES;
const filterOptionFunc = (inputValue: string, option: any) =>
  option.value.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1;

function useEditableTextState({ defaultEdit }: { defaultEdit: boolean }) {
  const [edit, setEdit] = useState(defaultEdit);
  const [saveClicked, setSaveClicked] = useState(false);
  const [validationMessage, setValidationMessage] = useState<string | ReactElement>('');
  const [stateEmailId, setStateEmailId] = useState<string>();
  const autoCompleteContainerRef = useRef<HTMLDivElement>(null);
  return {
    edit,
    setEdit,
    saveClicked,
    setSaveClicked,
    validationMessage,
    setValidationMessage,
    stateEmailId,
    setStateEmailId,
    autoCompleteContainerRef,
  };
}

const EditableText = ({
  emailId,
  onSave,
  onCancel,
  onDelete,
  defaultEdit = false,
  onEdit,
  onEditValidate,
  noDelete,
  noEdit,
  optionList = EMPTY_ARRAY_READONLY,
  onMount,
}: TEditableText) => {
  const {
    edit,
    setEdit,
    saveClicked,
    setSaveClicked,
    validationMessage,
    setValidationMessage,
    stateEmailId,
    setStateEmailId,
    autoCompleteContainerRef,
  } = useEditableTextState({ defaultEdit });
  const handleCancel = () => {
    setSaveClicked(false);
    setEdit(false);
    setValidationMessage('');
    onCancel && onCancel(emailId);
  };
  const handleSave = () => {
    setSaveClicked(true);
    if (!validateValue(stateEmailId)) {
      onSave &&
        onSave(stateEmailId, emailId, () => {
          setEdit(false);
          setSaveClicked(false);
        });
    }
  };
  const handlOnChange = (value: string) => {
    setStateEmailId(value.trim());
  };
  const handleDelete = () => onDelete && onDelete(emailId);
  // this will be called from parent only when this container is in edit mode
  const handleOnSubmit = () => {
    if (!validateValue(stateEmailId)) {
      setValidationMessage(<FormattedMessage {...SAVE_CHANGES_MESSAGE} />);
    }
  };
  const handleOnSubmitRef = useRef(handleOnSubmit);
  const handleOnEdit = () => {
    setEdit(true);
    onEdit && onEdit(emailId, handleOnSubmitRef);
  };
  const handleOnSubmitForm = () => {
    if (!validateValue(stateEmailId)) {
      setValidationMessage(<FormattedMessage {...SAVE_CHANGES_MESSAGE} />);
    }
    return true;
  };
  const validateValue = useCallback(
    (value: string | undefined) => {
      let validationMessage: string | ReactElement = '';
      if (checkRequired(value)) {
        validationMessage = <FormattedMessage {...REQUIRED_EMAIL_MESSAGE} />;
      } else if (checkValidEmail(value)) {
        validationMessage = <FormattedMessage {...INVALID_EMAIL_MESSAGE} />;
      } else if (onEditValidate && !onEditValidate(value, emailId)) {
        validationMessage = <FormattedMessage {...EXISTING_EMAIL_MESSAGE} />;
      }
      setValidationMessage(validationMessage);
      return validationMessage;
    },
    [emailId, onEditValidate, setValidationMessage]
  );
  useEffect(() => {
    if (emailId) {
      setStateEmailId(emailId);
    }
  }, [emailId, setStateEmailId]);
  useEffect(() => {
    if (saveClicked) {
      validateValue(stateEmailId);
    }
  }, [saveClicked, stateEmailId, validateValue]);
  /// intentionally kept no dependency array
  useEffect(() => {
    handleOnSubmitRef.current = handleOnSubmit;
  });
  useEffect(() => {
    if (onMount) {
      let obj: TOnMountObject = {};
      if (defaultEdit) {
        obj.onSubmitTrigger = handleOnSubmitRef.current;
      }
      onMount(obj);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  if (edit) {
    return (
      <StyledFormItem
        validateStatus={validationMessage ? 'error' : 'success'}
        help={validationMessage}
        name={'cohostEmail'}
        rules={[
          {
            validator: (_: any, value: any) => {
              if (handleOnSubmitForm()) {
                return Promise.reject();
              }
              return Promise.resolve();
            },
            validateTrigger: 'onSubmit',
          },
        ]}
      >
        <StyledEdit ref={autoCompleteContainerRef}>
          <AutoComplete
            wrapperClassName={'editTextFlexItem name-input'}
            defaultValue={emailId}
            filterOption={filterOptionFunc}
            onChange={handlOnChange}
            options={optionList}
            getPopupContainer={() => autoCompleteContainerRef.current}
          />

          <Pill className="editTextFlexItem" onClick={handleSave} type={PILL_TYPES.ACTION}>
            Save
          </Pill>

          <Pill className="editTextFlexItem" onClick={handleCancel} type={PILL_TYPES.NEGATIVE}>
            Cancel
          </Pill>
        </StyledEdit>
      </StyledFormItem>
    );
  }
  return (
    <StyledEdit>
      <span className="editTextStaticFlexItem">{emailId}</span>
      {noEdit ? null : (
        <Icon
          className="editTextStaticFlexItem cursor-pointer"
          type="edit"
          onClick={handleOnEdit}
        />
      )}
      {noDelete ? null : (
        <Icon
          className="editTextStaticFlexItem cursor-pointer"
          type="delete"
          onClick={handleDelete}
        />
      )}
    </StyledEdit>
  );
};
export default EditableText;
