import { useState, useEffect } from 'react';

import { FormattedMessage } from 'react-intl';

import Checkbox from '@mindtickle/checkbox';
import Icon from '@mindtickle/icon';
import Toggle from '@mindtickle/switch';
import Tooltip from '@mindtickle/tooltip';
import { Title } from '@mindtickle/typography';

import type { TCheckInSettings } from '~/modules/Admin/components/SessionEditDrawer/typeDefs';
import { CHECKIN_TEXTS, MODES } from '~/modules/Admin/config/sessions.constants';
import { LOCATION_TYPE_TO_DISABLED_CHECKIN_MESSAGE } from '~/modules/Admin/constants/sessions';
import { useILTAdminSnowplowTracker } from '~/modules/Admin/hooks/useILTAdminSnowplowTracker';
import { MIXPANEL_UI_EVENTS } from '~/modules/Admin/hooks/useILTAdminSnowplowTracker/constants';
import { EMPTY_OBJECT_READONLY } from '~/mt-ui-core/config/global.config';

import CheckinActions from './components/CheckinActions';
import TimeSelector from './components/TimeSelector';
import { CHECKIN_UI_LABELS, EMAIL_TYPES } from './constants';
import {
  StyledInfoContainer,
  StyledCheckInNote,
  StyledCheckInToggleText,
  StyledCheckInContainer,
  StyledInformCheckbox,
  StyledCheckinActionsWrapper,
} from './styles';
import { convertSecondsToTime, convertTimeToSeconds } from './utils';

import type { TSessionCheckIn } from '../../typeDefs';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';

const SessionCheckIn = (props: TSessionCheckIn) => {
  const {
    session,
    updateSessionState,
    onShareCheckInCode,
    mode,
    isReadOnlyModeEnabled,
    operationStatus,
    form,
  } = props;
  const {
    checkInSettings: {
      isSelfCheckInActivated = session.startTime > Date.now(),
      informInstructorsAboutCheckIn,
      informFacilitatorsAboutCheckin,
      secondsRelativeToSessionStartToAllowCheckIn = -1800,
      secondsRelativeToSessionStopToAllowCheckIn = 1800,
      selfCheckInQrCodeUrl,
      selfCheckInCode,
    } = EMPTY_OBJECT_READONLY as TCheckInSettings,
    checkInSettings = EMPTY_OBJECT_READONLY as TCheckInSettings,
    webAutoAttendanceSettings: { isAutoAttendanceEnabled = false },
    locationType,
  } = session;
  const isCheckInEnabled = isAutoAttendanceEnabled ? false : !!isSelfCheckInActivated;
  const autoAttendanceVendor = LOCATION_TYPE_TO_DISABLED_CHECKIN_MESSAGE[locationType];
  const [checkinTimeBeforeSession, setCheckinTimeBeforeSession] = useState(
    convertSecondsToTime(Math.abs(secondsRelativeToSessionStartToAllowCheckIn))
  );
  const [checkinTimeAfterSession, setCheckinTimeAfterSession] = useState(
    convertSecondsToTime(secondsRelativeToSessionStopToAllowCheckIn)
  );
  const tracker = useILTAdminSnowplowTracker();
  const toggleCheckin = (value: boolean) => {
    let newCheckInSettings = Object.assign({}, checkInSettings);
    newCheckInSettings.isSelfCheckInActivated = value;
    const {
      session: { id: sessionId },
    } = props;
    updateSessionState({ key: 'checkInSettings', value: newCheckInSettings });
    tracker.trackStructuredEvent({
      eventName: MIXPANEL_UI_EVENTS.MODULE_ILT_SESSIONS_ENABLE_CHECKIN_TOGGLE_CLICKED,
      sessionId,
    });
  };

  useEffect(() => {
    let newCheckInSettings = Object.assign({}, checkInSettings);
    newCheckInSettings.secondsRelativeToSessionStartToAllowCheckIn =
      convertTimeToSeconds(checkinTimeBeforeSession.hours, checkinTimeBeforeSession.minutes) * -1; // make it negative as it is time before session starts
    newCheckInSettings.secondsRelativeToSessionStopToAllowCheckIn = convertTimeToSeconds(
      checkinTimeAfterSession.hours,
      checkinTimeAfterSession.minutes
    );
    updateSessionState({ key: 'checkInSettings', value: newCheckInSettings });
    // eslint-disable-next-line react-hooks/exhaustive-deps -- only on change of checkinTimeBeforeSession and checkinTimeAfterSession values
  }, [checkinTimeBeforeSession, checkinTimeAfterSession]);

  const renderCheckinTimeSelector = () => (
    <div className="checkin-form-container">
      <div className="checkin-time-preferences">
        <TimeSelector
          checkinTimeBeforeSession={checkinTimeBeforeSession}
          checkinTimeAfterSession={checkinTimeAfterSession}
          setCheckinTimeBeforeSession={setCheckinTimeBeforeSession}
          setCheckinTimeAfterSession={setCheckinTimeAfterSession}
          sessionId={session.id}
          updateSessionState={updateSessionState}
          checkInSettings={checkInSettings}
          isReadOnlyModeEnabled={isReadOnlyModeEnabled}
          form={form}
        />
      </div>
    </div>
  );

  const toggleInformInstructorStatus = (e: CheckboxChangeEvent) => {
    const {
      session: { id: sessionId },
    } = props;
    let newCheckInSettings = Object.assign({}, checkInSettings);
    newCheckInSettings.informInstructorsAboutCheckIn = e.target.checked;
    updateSessionState({ key: 'checkInSettings', value: newCheckInSettings });
    tracker.trackStructuredEvent({
      eventName: MIXPANEL_UI_EVENTS.MODULE_ILT_SESSIONS_CHECKIN_EMAIL_CLICKED,
      ilt_session_id: sessionId,
      email_type: EMAIL_TYPES.INSTRUCTOR,
    });
  };

  const toggleInformFacilitatorStatus = (e: CheckboxChangeEvent) => {
    const {
      session: { id: sessionId },
    } = props;
    let newCheckInSettings = Object.assign({}, checkInSettings);
    newCheckInSettings.informFacilitatorsAboutCheckin = e.target.checked;
    updateSessionState({ key: 'checkInSettings', value: newCheckInSettings });
    tracker.trackStructuredEvent({
      eventName: MIXPANEL_UI_EVENTS.MODULE_ILT_SESSIONS_CHECKIN_EMAIL_CLICKED,
      ilt_session_id: sessionId,
      email_type: EMAIL_TYPES.FACILITATORS,
    });
  };

  const renderNotifyInstructor = () => (
    <StyledInformCheckbox>
      <Checkbox
        checked={informInstructorsAboutCheckIn}
        onChange={toggleInformInstructorStatus}
        className="checkin-time-checkbox"
        disabled={isReadOnlyModeEnabled}
      >
        <FormattedMessage {...CHECKIN_UI_LABELS.SEND_EMAIL_TO_INSTRUCTORS} />
      </Checkbox>
      <Tooltip
        title={<FormattedMessage {...CHECKIN_UI_LABELS.SEND_EMAIL_TO_INSTRUCTORS_TOOLTIP} />}
      >
        <Icon type="info2" />
      </Tooltip>
    </StyledInformCheckbox>
  );

  const renderNotifyFacilitator = () => (
    <StyledInformCheckbox>
      <Checkbox
        checked={informFacilitatorsAboutCheckin}
        onChange={toggleInformFacilitatorStatus}
        className="checkin-time-checkbox"
        disabled={isReadOnlyModeEnabled}
      >
        <FormattedMessage {...CHECKIN_UI_LABELS.SEND_EMAIL_TO_FACILITATORS} />
      </Checkbox>
      <Tooltip
        title={<FormattedMessage {...CHECKIN_UI_LABELS.SEND_EMAIL_TO_FACILITATORS_TOOLTIP} />}
      >
        <Icon type="info2" />
      </Tooltip>
    </StyledInformCheckbox>
  );

  return (
    <StyledCheckInContainer>
      <Title level={3}>Session check-in</Title>
      <StyledCheckInToggleText checked={isCheckInEnabled}>
        <FormattedMessage {...CHECKIN_TEXTS.ENABLE_CHECKIN_TOGGLE} />
      </StyledCheckInToggleText>
      <Toggle
        name="checkInToggle"
        checked={isCheckInEnabled}
        onChange={toggleCheckin}
        disabled={isReadOnlyModeEnabled || isAutoAttendanceEnabled}
      />
      {isAutoAttendanceEnabled && (
        <StyledInfoContainer>
          <Icon type="info2" />
          <StyledCheckInNote>
            <span className="noteHeading">Please note: </span>
            <FormattedMessage
              {...CHECKIN_TEXTS.AUTO_ATTENDANCE_ENABLED_NOTE}
              values={{ autoAttendanceVendor }}
            />
          </StyledCheckInNote>
        </StyledInfoContainer>
      )}
      {isCheckInEnabled && (
        <StyledCheckinActionsWrapper>
          {renderCheckinTimeSelector()}
          {renderNotifyInstructor()}
          {renderNotifyFacilitator()}
          {mode === MODES.ADD ? (
            <StyledInfoContainer>
              <Icon type="info2" />
              <StyledCheckInNote>
                <span className="noteHeading">Please note: </span>
                <FormattedMessage {...CHECKIN_TEXTS.CODE_UNAVAILABLE_NOTE} />
              </StyledCheckInNote>
            </StyledInfoContainer>
          ) : (
            <div className="checkin-prefs">
              <div className="qr-container">
                <img width="133px" height="133px" src={selfCheckInQrCodeUrl} alt={'checkinQR'} />
                <div className="checkin-code-title">
                  <FormattedMessage {...CHECKIN_UI_LABELS.CHECKIN_CODE_LABEL} />
                </div>
                <div className="checkin-code">{selfCheckInCode}</div>
                <CheckinActions
                  session={session}
                  updateSessionState={updateSessionState}
                  operationStatus={operationStatus}
                  onShareCheckInCode={onShareCheckInCode}
                />
              </div>
            </div>
          )}
        </StyledCheckinActionsWrapper>
      )}
    </StyledCheckInContainer>
  );
};

export default SessionCheckIn;
