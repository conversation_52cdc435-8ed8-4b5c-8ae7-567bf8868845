export interface TLiveChallengeModalFooter {
  onCancel?: () => void;
  onOk?: () => void;
  action: string;
}

export interface LCActionModal {
  liveChallengeModalAction: string;
  onCancelUpdateConfirm: () => void;
  onOkUpdateConfirm: () => void;
}

export interface TDeleteModal {
  confirmDeleteLc: Function;
  onCancelDelete: Function;
}

export interface TModalFooter {
  onCancel?: Function;
  onOk?: Function;
}
