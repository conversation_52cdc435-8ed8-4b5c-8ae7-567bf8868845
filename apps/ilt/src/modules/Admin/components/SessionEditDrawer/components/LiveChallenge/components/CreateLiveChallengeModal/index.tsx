import { useEffect, useState, FunctionComponent } from 'react';

import { FormattedMessage } from 'react-intl';
import { useDispatch } from 'react-redux';

import Button from '@mindtickle/button';
import Upload from '@mindtickle/file-uploader';
import IconWithGradient, { ICON_MAP } from '@mindtickle/icon-with-gradient';
import Loader from '@mindtickle/loader';
import { getActions } from '@mindtickle/medux/Action';
import Modal from '@mindtickle/modal';

import { MANAGE_LIVE_CHALLENGE } from '~/modules/Admin/actionTypes';
import {
  CREATE_LIVE_CHALLENGE,
  LIVE_CHALLENGE,
} from '~/modules/Admin/config/live-challenges.constants';
import { getDataFromSheet } from '~/utils/xlsx';

import { CreateLCModalSubHeader, UploadLCCsv, uploadIconStyle } from '../../styles';

import IncorrectFileModal from './components/IncorrectFileModal';
import Title from './components/Title';
import { SheetJSFT } from './constants';
import {
  validateMtLiveChallengeData,
  parseMtLiveChallengeData,
} from './utils/liveChallengeQuestionParser';

import type { TCreateLiveChallengeModal } from '../../typeDefs';
const { Dragger } = Upload;

const CreateLiveChallengeModal: FunctionComponent<TCreateLiveChallengeModal> = ({
  closeModal,
  visible,
  operation,
  isProcessing,
}) => {
  const [fileHasErrors, setFileHasErrors] = useState(false);
  const [fileErrorMessage, setFileErrorMessage] = useState<string | undefined>(undefined);
  const [loadingFile, setLoadingFile] = useState(false);
  const isLoading = isProcessing || loadingFile;
  const dispatch = useDispatch();
  const SIZE_4MB = 4 * 1024 * 1024;
  useEffect(() => {
    if (!visible) {
      setFileHasErrors(false);
      setFileErrorMessage(undefined);
      setLoadingFile(false);
    }
  }, [visible]);
  const dispatchLcUpdate = ({ lcData, operation }: { lcData: any; operation: string }) => {
    dispatch(
      getActions(MANAGE_LIVE_CHALLENGE)(
        { lcData, operation },
        {
          loadingData: { operation },
        }
      )
    );
  };
  const updateLiveChallengeData = (parsedLcData: any) => {
    const lcData = {
      name: `Live Challenge`,
      uniqueCode: '',
      rounds: [
        {
          name: 'Round',
          questions: parsedLcData,
        },
      ],
    };
    dispatchLcUpdate({ lcData, operation });
  };
  const onDataParsed = (liveChallengeData: any, fileName: string, fileType: string) => {
    const mtLiveChallengeQuestionsData: any = Object.values(liveChallengeData)[0];
    if (mtLiveChallengeQuestionsData.length > 0) {
      const doesEveryCheckPass = validateMtLiveChallengeData(mtLiveChallengeQuestionsData);
      if (doesEveryCheckPass) {
        const parsedMtLiveChallengeData = parseMtLiveChallengeData(mtLiveChallengeQuestionsData);
        updateLiveChallengeData(parsedMtLiveChallengeData);
      } else {
        setFileHasErrors(true);
        setFileErrorMessage(undefined);
        setLoadingFile(false);
      }
    } else {
      setFileHasErrors(true);
      setFileErrorMessage(undefined);
      setLoadingFile(false);
    }
  };

  // eslint-disable-next-line max-statements
  const handleFileRead = (file: any, fileList: any[]) => {
    if (isLoading) {
      return;
    }
    if (fileList.length !== 1) {
      setFileHasErrors(true);
      setFileErrorMessage('Please select exactly 1 supported file.');
      return;
    }
    if (file.size > SIZE_4MB) {
      setFileHasErrors(true);
      setFileErrorMessage('Size of the file should not be more than 4MB.');
      return;
    }
    setLoadingFile(true);
    const reader = new FileReader();
    const readAsBinaryString = !!reader.readAsBinaryString;
    reader.onload = (event: any) => {
      const binaryString = event.target.result;
      const parseOptions = { raw: false };
      const data = getDataFromSheet(binaryString, readAsBinaryString, parseOptions);
      onDataParsed(data, file.name, file.type);
    };
    if (readAsBinaryString) {
      reader.readAsBinaryString(file);
    } else {
      reader.readAsArrayBuffer(file);
    }
    // Required to cancel upload by antd
    return false;
  };
  const onErrorModalClosed = () => {
    setFileHasErrors(false);
    setFileErrorMessage(undefined);
  };
  const close = () => {
    if (isLoading) {
      return;
    }
    setLoadingFile(false);
    closeModal();
  };
  return (
    <>
      <Modal
        visible={visible}
        onCancel={close}
        destroyOnClose
        zIndex={9980}
        title={<Title closeModal={close} />}
        footer={null}
        maskClosable={false}
        width={650}
      >
        <CreateLCModalSubHeader>
          <FormattedMessage {...CREATE_LIVE_CHALLENGE.CREATE_LC_MODAL_SUB_HEADER} />{' '}
          <a target="_blank" rel="noopener noreferrer" href={CREATE_LIVE_CHALLENGE.LEARN_HOW_LINK}>
            <FormattedMessage {...CREATE_LIVE_CHALLENGE.LEARN_HOW} />
          </a>
        </CreateLCModalSubHeader>

        <UploadLCCsv>
          <Dragger
            className="dragger"
            disabled={isLoading}
            accept={SheetJSFT}
            multiple
            beforeUpload={handleFileRead}
            openFileDialogOnClick={false}
            showUploadList={false}
          >
            <div className="draggerContent">
              {isLoading ? (
                <Loader
                  message={<FormattedMessage {...LIVE_CHALLENGE.CREATING_LC_LOADER_MESSAGE} />}
                />
              ) : (
                <>
                  <IconWithGradient type={ICON_MAP.UPLOAD} style={uploadIconStyle} />
                  <div className="addCsvText">
                    <FormattedMessage {...CREATE_LIVE_CHALLENGE.DROP_FILE_TEXT} />
                    <div>or</div>
                  </div>
                  <Upload
                    className="uploadButton"
                    disabled={isLoading}
                    accept={SheetJSFT}
                    beforeUpload={handleFileRead}
                    showUploadList={false}
                  >
                    <Button type="primary">Upload</Button>
                  </Upload>
                  <div className="fileInfo">
                    <FormattedMessage {...CREATE_LIVE_CHALLENGE.SUPPORTED_FILE_TYPES} />
                    <br />
                    <FormattedMessage {...CREATE_LIVE_CHALLENGE.MAX_4_MB} />
                  </div>
                </>
              )}
            </div>
          </Dragger>
        </UploadLCCsv>
      </Modal>
      <IncorrectFileModal
        visible={fileHasErrors}
        message={fileErrorMessage}
        onClose={onErrorModalClosed}
      />
    </>
  );
};
export default CreateLiveChallengeModal;
