import { Flow } from '../typeDefs';

export interface UseAuthParams {
  onSuccessfulAuth: ({ hostEmail }: { hostEmail: string }) => void;
  onError: () => void;
  integrationSource: string;
  onAbruptAuthWindowClose: () => void;
  isWaitingForAuth: boolean;
}

export type AuthModalFlow = 'hostEmail' | 'authenticate';

export interface AuthModalContent {
  modalHeader: ReactIntl.FormattedMessage.MessageDescriptor;
  primaryCTAText: ReactIntl.FormattedMessage.MessageDescriptor;
  secondaryCTAText: ReactIntl.FormattedMessage.MessageDescriptor;
}
// Define the types for the flows
export type UseAuthModalState = {
  flow: AuthModalFlow;
  content: AuthModalContent;
  hostEmail: string;
  asyncStatus: 'idle' | 'loading' | 'success' | 'error';
  isSubmitDisabled: boolean;
  validationError: string;
};

export type UseAuthModalStateAction =
  | { type: 'SWITCH_TO_HOST_EMAIL' }
  | { type: 'SWITCH_TO_AUTHENTICATE' }
  | {
      type: 'ADD_HOST_EMAIL';
      payload: string;
    }
  | {
      type: 'ASYNC_STATUS';
      payload: 'idle' | 'loading' | 'success' | 'error';
    }
  | {
      type: 'SET_SUBMIT_DISABLED';
      payload: boolean;
    }
  | {
      type: 'SET_VALIDATION_ERROR';
      payload: string;
    };

export type UseAuthModalParams = {
  webinarSource: string;
  integrationSource: string;
  endTimeWithOffSet: number;
  startTimeWithOffSet: number;
  meetingTitle?: string;
  hostEmail: string;
  onSuccessfulMeetingCreation: (meetingData: any) => void;
  creationInProgress: boolean;
  setCreationInProgress: (isInProgress: boolean) => void;
  handleMeetingValidationSuccess?: (meetingData: any) => void;
  validationInProgress: boolean;
  setValidationInProgress: (value: boolean) => void;
  meetingUrl?: string;
  initialFlow?: AuthModalFlow;
  flow: Flow;
};

export interface APIParams {
  webinarSource: string;
  endTimeWithOffSet: number;
  startTimeWithOffSet: number;
  meetingTitle?: string;
  hostEmail: string;
}
