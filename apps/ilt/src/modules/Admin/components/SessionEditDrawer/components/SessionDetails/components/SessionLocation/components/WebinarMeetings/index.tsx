import { useEffect } from 'react';

import { injectIntl } from 'react-intl';

import CopyToClipboard from '@mindtickle/copy-to-clipboard';
import Icon, { ICON_MAP } from '@mindtickle/icon';
import { successToast } from '@mindtickle/toast';

import { TOAST_WRAPPER_ID } from '~/modules/Admin/components/SessionEditDrawer/constants';
import { WEBINAR_SOURCE, WEBINAR_SOURCE_LABELS } from '~/modules/Admin/config/sessions.constants';
import { sessionTimeFormatter } from '~/modules/Admin/utils';
import { getWebinarMeetingMaxCohostLengthByWebinarSource } from '~/modules/Admin/utils/sessionEdit';
import { EMPTY_ARRAY_READONLY } from '~/mt-ui-core/config/global.config';

import { WEBINAR_MEETING_STATUS_OPERATION } from '../../../../constants';
import <PERSON>AttendanceToggle from '../AutoAttendanceToggle';

import EditableText from './components/EditableText';
import WebinarMeetingListener from './components/WebinarMeetingListener';
import { useWebinarCohosts, useUniqueOptionsList } from './hooks';
import MEETING_TEXTS from './messages';
import {
  StyledWebinarCard,
  StyledAddCohost,
  StyledWebinarHeading,
  StyledWebinarPrimaryInfoText,
  StyledWebinarPrimaryHeading,
  StyledDivider,
  StyledCloseIcon,
  StyledMaxCohostInfo,
  StyledCopyMeetingInfo,
} from './styles';

import type { TWebinarMeeting } from '../../typeDefs';
import type { InjectedIntlProps } from 'react-intl';

export {
  WebinarMeetingListener as WebexListenerComponent,
  WebinarMeetingListener as ZoomListenerComponent,
  WebinarMeetingListener as MSTeamsListenerComponent,
};

const WebinarMeeting = ({
  onClose,
  updateSessionState,
  webAutoAttendanceSettings = {
    isAutoAttendanceEnabled: false,
    isLinkValidated: false,
    lastValidityStatus: 'NONE',
  },
  webinarMeetingSettings,
  updateMeetingStatus,
  isOngoingOrPastSession,
  location,
  instructors,
  facilitators,
  webinarSource,
  isReadOnlyModeEnabled,
  sessionStartDate,
  sessionEndDate,
  sessionName,
  intl,
}: TWebinarMeeting & InjectedIntlProps) => {
  const { isAutoAttendanceEnabled } = webAutoAttendanceSettings;
  const {
    meetingNumber = '',
    password = '',
    hostKey = '',
    hostEmail = '',
    coHosts = EMPTY_ARRAY_READONLY,
  } = webinarMeetingSettings ?? {};
  const MAX_COHOST_LENGTH = getWebinarMeetingMaxCohostLengthByWebinarSource(webinarSource);

  const {
    coHostsInEditMode,
    newCoHost,
    editCoHost,
    cancelCoHost,
    deleteCoHost,
    updateCoHost,
    addCoHost,
    addNewCoHost,
    onNewCohostEditMount,
    cancelNewCoHost,
    validateCoHost,
  } = useWebinarCohosts({
    webinarMeetingSettings,
    updateSessionState,
    webinarSource,
  });
  const uniqueOptionList = useUniqueOptionsList({
    instructors,
    facilitators,
    coHosts,
    hostEmail,
  });
  const updateAutoAttendanceStatus = (value: boolean) => {
    updateSessionState({
      key: 'webAutoAttendanceSettings',
      value: {
        ...webAutoAttendanceSettings,
        isAutoAttendanceEnabled: value,
      },
    });
  };
  useEffect(() => {
    const onSubmitTrigger = () => {
      const triggerOnsubmit = ({
        onSubmitTrigger,
      }: { onSubmitTrigger?: { current?: () => void } } = {}) => {
        onSubmitTrigger && onSubmitTrigger.current && onSubmitTrigger.current();
      };
      if (coHostsInEditMode.length) {
        for (const eachCoHostEdit of coHostsInEditMode) {
          triggerOnsubmit(eachCoHostEdit);
        }
      }
      triggerOnsubmit(newCoHost);
    };
    updateMeetingStatus(WEBINAR_MEETING_STATUS_OPERATION.IN_PROGRESS, {
      value: !!(coHostsInEditMode.length || newCoHost),
      onSubmitTrigger,
    });
    return () => updateMeetingStatus(WEBINAR_MEETING_STATUS_OPERATION.IN_PROGRESS, false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [coHostsInEditMode, newCoHost]);

  const getMeetingInfoToCopy = (webinarSource: string) => {
    const meetingInfo = [
      `${sessionName ? sessionName : ''}`,
      `${sessionTimeFormatter(sessionStartDate)} - ${sessionTimeFormatter(sessionEndDate)}`,
      `${WEBINAR_SOURCE_LABELS[webinarSource]} joining info:`,
      `${intl.formatMessage(MEETING_TEXTS.meetingNumber)}: ${meetingNumber}`,
      `${intl.formatMessage(MEETING_TEXTS.password)}: ${password}`,
    ];
    if (webinarSource === WEBINAR_SOURCE.WEBEX) {
      meetingInfo.push(`${intl.formatMessage(MEETING_TEXTS.hostKey)}: ${hostKey}`);
    }
    meetingInfo.push(`Meeting link: ${location}`);

    return meetingInfo.join('\n');
  };

  const copyMeetingInfoToClipboardWithToast = (webinarSource: string) => {
    CopyToClipboard.copy({
      textToCopy: getMeetingInfoToCopy(webinarSource),
    });

    successToast({
      message: intl.formatMessage(MEETING_TEXTS.copyMeetingInfoToast),
      timeout: 3000,
      mountId: TOAST_WRAPPER_ID,
    });
  };

  return (
    <StyledWebinarCard>
      {!isReadOnlyModeEnabled && (
        <StyledCloseIcon onClick={onClose}>
          <Icon type="close" />
        </StyledCloseIcon>
      )}

      <StyledWebinarHeading>
        <a href={location} target="_blank" rel="noopener noreferrer">
          {intl.formatMessage(MEETING_TEXTS.webinarLinkHeading, {
            sourceLabel: WEBINAR_SOURCE_LABELS[webinarSource],
          })}
        </a>
      </StyledWebinarHeading>

      <StyledWebinarPrimaryInfoText>
        {intl.formatMessage(MEETING_TEXTS.meetingNumber)}: {meetingNumber}
      </StyledWebinarPrimaryInfoText>
      {password && (
        <StyledWebinarPrimaryInfoText>
          {intl.formatMessage(MEETING_TEXTS.password)}: {password}
        </StyledWebinarPrimaryInfoText>
      )}
      {hostKey && (
        <StyledWebinarPrimaryInfoText>
          {intl.formatMessage(MEETING_TEXTS.hostKey)}: {hostKey}
        </StyledWebinarPrimaryInfoText>
      )}
      <StyledCopyMeetingInfo onClick={() => copyMeetingInfoToClipboardWithToast(webinarSource)}>
        <Icon type={ICON_MAP.COPYTO_CLIPBOARD} className="copyIcon" />
        <span>{intl.formatMessage(MEETING_TEXTS.copyMeetingInfo)}</span>
      </StyledCopyMeetingInfo>

      <StyledDivider />

      <StyledWebinarPrimaryHeading>
        {intl.formatMessage(MEETING_TEXTS.host)}
      </StyledWebinarPrimaryHeading>
      <EditableText emailId={hostEmail} noDelete={true} noEdit={true} />

      <StyledWebinarPrimaryHeading>
        {intl.formatMessage(MEETING_TEXTS.coHost)}
      </StyledWebinarPrimaryHeading>
      {coHosts.map((value = {}, index) => (
        <EditableText
          key={value.email + '_' + index + '_' + coHosts.length}
          optionList={uniqueOptionList}
          emailId={value.email}
          onCancel={cancelCoHost}
          onSave={updateCoHost}
          onEdit={editCoHost}
          onEditValidate={validateCoHost}
          onDelete={deleteCoHost}
          noDelete={isReadOnlyModeEnabled || isOngoingOrPastSession}
          noEdit={isReadOnlyModeEnabled || isOngoingOrPastSession}
        />
      ))}

      {!isReadOnlyModeEnabled && !isOngoingOrPastSession && coHosts.length < MAX_COHOST_LENGTH ? (
        !newCoHost ? (
          <StyledAddCohost onClick={addNewCoHost}>
            {intl.formatMessage(MEETING_TEXTS.addCohost)}
          </StyledAddCohost>
        ) : (
          <EditableText
            optionList={uniqueOptionList}
            onEditValidate={validateCoHost}
            defaultEdit={true}
            onMount={onNewCohostEditMount}
            onCancel={cancelNewCoHost}
            onSave={addCoHost}
          />
        )
      ) : null}

      {coHosts.length >= MAX_COHOST_LENGTH && (
        <StyledMaxCohostInfo>
          {intl.formatMessage(MEETING_TEXTS.maxCohostLimitReached)}
        </StyledMaxCohostInfo>
      )}

      <StyledDivider />

      <AutoAttendanceToggle
        isAutoAttendanceEnabled={isAutoAttendanceEnabled}
        updateAutoAttendanceStatus={updateAutoAttendanceStatus}
        webinarSource={webinarSource}
        isReadOnlyModeEnabled={isReadOnlyModeEnabled}
      />
    </StyledWebinarCard>
  );
};
export default injectIntl(WebinarMeeting);
