import { FormattedMessage } from 'react-intl';

import Divider from '@mindtickle/divider';
import Icon from '@mindtickle/icon';
import Toggle from '@mindtickle/switch';
import Tooltip from '@mindtickle/tooltip';
import { Title } from '@mindtickle/typography';
import { hoursToMiliseconds } from '@mindtickle/utils';

import { getTimezoneObject } from '~/modules/Admin/components/SessionsWrapper/utils';
import {
  ENROLLMENT_FREEZE_STATUSES,
  ENROLLMENT_LOCK_WARNING,
  MESSAGES,
} from '~/modules/Admin/config/sessions.constants';
import { sessionTimeFormatter } from '~/modules/Admin/utils';
import { parseEventEnrollmentFreezeEpoch } from '~/modules/Admin/utils/eventEdit';
import { addOffsetDiff, subtractOffsetDiff } from '~/utils/timezone';

import { StyledActionRow } from '../../styles';

import EnrollmentFreeze from './components/EnrollmentFreeze';
import { StyledEnrollmentFreezeContainer, StyledEnrollmentLockText } from './styles';

import type { TEnrollmentFreezeSection } from '../../typeDefs';

const EnrollmentFreezeSection = (props: TEnrollmentFreezeSection) => {
  const {
    updateSessionState,
    session: { enrollmentFreezeStatus, startTime, enrollmentFreezeEpoch, timezone },
    isReadOnlyModeEnabled,
    isPublishedOngoingPastSession,
    isEventLevelEnrollmentEnabled,
    event: {
      enrollmentFreezeDaysBeforeEvent: eventEnrollmentFreezeDaysBeforeEvent,
      enrollmentFreezeStatus: eventEnrollmentFreezeStatus,
      enrollmentFreezeEpoch: eventEnrollmentFreezeEpoch,
      enrollmentFreezeTimezone,
      startTime: eventStartTime,
    },
  } = props;

  const timezoneObj = getTimezoneObject(timezone);
  let offset = hoursToMiliseconds(timezoneObj?.offset);
  const startTimeWithOffset = addOffsetDiff(startTime, offset);
  const enrollmentFreezeEnabled = enrollmentFreezeStatus !== ENROLLMENT_FREEZE_STATUSES.DISABLED;
  const eventEnrollmentFreezeEnabled =
    eventEnrollmentFreezeStatus !== ENROLLMENT_FREEZE_STATUSES.DISABLED;

  //Handling case when copy session is being created, also unpublished case
  const disableFreezeEdit = isPublishedOngoingPastSession;

  let hideSectionWhenPast = Date.now() - startTimeWithOffset >= 0;

  const eventEnrollmentFreezeTime = parseEventEnrollmentFreezeEpoch({
    startTime: eventStartTime ? eventStartTime : startTimeWithOffset,
    enrollmentFreezeStatus: eventEnrollmentFreezeStatus,
    enrollmentFreezeEpoch: eventEnrollmentFreezeEpoch,
    enrollmentFreezeDaysBeforeEvent: eventEnrollmentFreezeDaysBeforeEvent,
    enrollmentFreezeTimezone,
  });
  const eventEnrollmentFreezeTimeWithOffset = subtractOffsetDiff(eventEnrollmentFreezeTime, offset);

  const hideEnrollmentFreeze = hideSectionWhenPast === true && enrollmentFreezeEnabled === false;

  const setFreezeStatus = (value: boolean) => {
    if (value) {
      updateSessionState({
        key: 'enrollmentFreezeStatus',
        value: ENROLLMENT_FREEZE_STATUSES.RELATIVE,
      });
    } else {
      updateSessionState({
        key: 'enrollmentFreezeStatus',
        value: ENROLLMENT_FREEZE_STATUSES.DISABLED,
      });
      updateSessionState({ key: 'enrollmentFreezeEpoch', value: 0 });
    }
  };

  return (
    <>
      {!hideEnrollmentFreeze && (
        <StyledEnrollmentFreezeContainer>
          <div className="entity-enrollment-row">
            <Title level={3}>Enrollment lock</Title>
            {isEventLevelEnrollmentEnabled && (
              // Just a placeholder toggle to depict event level enrollment lock settings
              <Toggle
                name="eventEnrollmentFreezeToggle"
                checked={!!eventEnrollmentFreezeEnabled}
                disabled={true}
              />
            )}
          </div>

          {!isEventLevelEnrollmentEnabled && (
            <StyledActionRow>
              <StyledEnrollmentLockText checked={!!enrollmentFreezeEnabled}>
                Set a date after which enrollments will be locked
                <Tooltip
                  overlayClassName={'tooltipInfo'}
                  title={<FormattedMessage {...MESSAGES.INFO.FREEZE_ENROLLMENT} />}
                >
                  <Icon type="info2" className={'infoHover'} />
                </Tooltip>
              </StyledEnrollmentLockText>

              <Toggle
                name="enrollmentFreezeToggle"
                checked={!!enrollmentFreezeEnabled}
                onChange={setFreezeStatus}
                disabled={isReadOnlyModeEnabled || disableFreezeEdit}
              />
            </StyledActionRow>
          )}

          {isEventLevelEnrollmentEnabled ? (
            eventEnrollmentFreezeEnabled && (
              <div className={'date-display'}>
                {disableFreezeEdit ? ENROLLMENT_LOCK_WARNING.PAST : ENROLLMENT_LOCK_WARNING.FUTURE}
                {sessionTimeFormatter(eventEnrollmentFreezeTimeWithOffset) +
                  timezoneObj?.shortDisplayName}
              </div>
            )
          ) : enrollmentFreezeEnabled ? (
            <EnrollmentFreeze
              sessionStartTime={startTime}
              enrollmentFreezeStatus={enrollmentFreezeStatus}
              updateSessionState={updateSessionState}
              enrollmentFreezeTime={enrollmentFreezeEpoch}
              disableFreezeEdit={disableFreezeEdit}
              timezone={timezoneObj!}
              isReadOnlyModeEnabled={isReadOnlyModeEnabled}
            />
          ) : null}

          <Divider className="enrollment-freeze-divider" />
        </StyledEnrollmentFreezeContainer>
      )}
    </>
  );
};

export default EnrollmentFreezeSection;
