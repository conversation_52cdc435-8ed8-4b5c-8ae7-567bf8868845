export const MENU_PLACEMENT_TOP = 'top';

export const TIMEZONE_INTER_FILTER_STYLES = (props: any) => {
  const isTopPlacementMenu = props.menuPlacement === MENU_PLACEMENT_TOP;
  return {
    control: (base: any, state: any) => {
      let styleforTopMenu = isTopPlacementMenu
        ? {
            boxShadow: state.menuIsOpen ? `0 -4px 8px 0 rgba(0, 0, 0, 0.08)` : 'none',
            borderRadius: state.menuIsOpen ? '0 0 4px 4px' : '4px',
          }
        : {};
      return {
        ...base,
        width: '432px',
        ...styleforTopMenu,
      };
    },
    container: (base: any) => ({
      ...base,
      marginTop: '8px',
      width: '432px',
      minWidth: '432px',
    }),
    menu: (base: any) => {
      let styleforTopMenu = isTopPlacementMenu
        ? {
            boxShadow: `0 -4px 8px 0 rgba(0, 0, 0, 0.08)`,
            borderRadius: '4px 4px 0 0',
          }
        : {};
      return {
        ...base,
        width: '432px',
        ...styleforTopMenu,
      };
    },
  };
};
export const FILTER_STYLE = {
  style: TIMEZONE_INTER_FILTER_STYLES({}),
  minMenuHeight: 150,
  maxMenuHeight: 200,
  minMenuWidth: 70,
};
