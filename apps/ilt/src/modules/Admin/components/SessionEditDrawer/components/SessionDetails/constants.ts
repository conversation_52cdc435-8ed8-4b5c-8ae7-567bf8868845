export const SUBHEADINGS = {
  ENROLLMENT_FREEZE: 'Disable learners from enrolling or unenrolling after a set date',
};

export const LOCATION_TYPE_ACTION = {
  CHANGE: 'change',
  C<PERSON>AR: 'clear',
};

export const WEBINAR_MEETING_STATUS_OPERATION = {
  IN_PROGRESS: 'inprogress',
  MEETING_INVALID_TIME: 'meetinginvalidtime',
  CLEAR_MODE_DATA: 'clearmodedata',
};

export const WEBEX_PASSWORD_SECTION_TEXTS = {
  SECTION_HEADING: 'Enter password for the Webex meeting added above to enable auto attendance',
  INPUT_PLACEHOLDER: 'Webex password',
  SAVE_BUTTON_TEXT: 'Save',
  CLEAR_BUTTON_TEXT: 'Clear',
  INVALID_PASSWORD_VALIDATION_TEXT: 'Incorrect password. Please check and try again',
};

export const PRE_WEBINAR_MEETING_CREATION_TEXTS = {
  CREATING_WEBINAR_MEETING_MESSAGE: (webinarSource: string) => `Creating ${webinarSource} meeting`,
  INVALID_HOST_TITLE: (webinarSource: string) => `Assign host for ${webinarSource} meeting`,
  INVALID_HOST_CONTENT: (webinarSource: string) =>
    `Add the email ID of the host for this ${webinarSource} meeting`,
  INVALID_HOST_MULTIPLE_TIME_TITLE: 'Unable to add host',
  INVALID_HOST_MULTIPLE_TIME_CONTENT: (webinarSource: string) =>
    `The email ID you entered previously does not have a ${webinarSource} account linked to it. Please add a valid email ID.`,
  MEETING_CREATION_FAILED_MESSAGE: (webinarSource: string) =>
    `Failed to create ${webinarSource} meeting. Please try again`,
  WEBINAR_AUTH_FAILED_MESSAGE: (webinarSource: string) =>
    `Failed to create ${webinarSource} meeting. Please try again`,
  WEBINAR_AUTH_VALIDATE_FAILED_MESSAGE: (webinarSource: string) =>
    `Failed to validate ${webinarSource} meeting. Please try again`,
  MEETING_CREATION_FAILED_AUTH_ERROR: (webinarSource: string) =>
    `Failed to create ${webinarSource} meeting. Please re-authorize and try again.`,
  OK_BUTTON_TEXT: 'Confirm host',
  CANCEL_BUTTON_TEXT: 'Discard meeting',
  ENTERED_HOST_RATE_LIMIT_BREACHED:
    'The email ID you entered previously has exceeded its daily meeting creation limit. Please add another email ID to create this meeting.',
  CURRENT_HOST_RATE_LIMIT_BREACHED:
    'You have exceeded daily meeting creation limit. Please add another email ID to creating this meeting.',
  CURRENT_HOST_RATE_LIMIT_BREACHED_TITLE: (webinarSource: string) =>
    `Assign alternate host for ${webinarSource} meeting`,
  MEETING_CREATED_SUCCESSFULLY: (webinarSource: string) =>
    `${webinarSource} meeting has been created successfully`,
  ULR_VALIDATED_SUCCESSFULLY: (webinarSource: string) =>
    `${webinarSource} meeting has been validated successfully`,
};
