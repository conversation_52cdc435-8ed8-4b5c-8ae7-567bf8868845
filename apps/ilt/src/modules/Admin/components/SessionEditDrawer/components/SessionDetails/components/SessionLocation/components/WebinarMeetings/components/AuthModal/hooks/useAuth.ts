import { useEffect, useRef, useState } from 'react';

import { useUserAuth } from 'ui_shell/Auth';

import { getWebConfOAuthUrl } from '~/modules/Admin/utils/sessionEdit';
import {
  IntegrationAuthChannel,
  IntegrationAuthChannelData,
  IntegrationAuthChannelDataStatus,
} from '~/utils/broadcastChannel';

import { UseAuthParams } from './typeDefs';

function clearWindowClosedInterval(intervalTimer: NodeJS.Timer | null) {
  if (intervalTimer !== null) {
    clearInterval(intervalTimer);
  }
}

function closeAbandonedWindows(abandonedWindows: React.MutableRefObject<any[]>) {
  abandonedWindows.current.forEach(abandonedWindow => {
    if (abandonedWindow && !abandonedWindow.closed && abandonedWindow.close) {
      abandonedWindow.close();
    }
  });
}

export function useAuth({
  onSuccessfulAuth = () => {},
  onError = () => {},
  integrationSource,
  onAbruptAuthWindowClose,
  isWaitingForAuth,
}: UseAuthParams) {
  const [oauthUrl, setOAuthUrl] = useState('');
  const user = useUserAuth();
  const authWindow = useRef<any>(null);
  const abandonedAuthWindows = useRef<any[]>([]);
  const intervalRef = useRef<NodeJS.Timer | null>(null);
  const onAbruptAuthWindowCloseRef = useRef(onAbruptAuthWindowClose);
  onAbruptAuthWindowCloseRef.current = onAbruptAuthWindowClose;

  useEffect(() => {
    let isUnmounted = false;
    const api = async () => {
      const data = await getWebConfOAuthUrl({ companyId: user.company.id, integrationSource });
      if (!isUnmounted) {
        setOAuthUrl(data);
      }
    };
    api();
    let timeoutRef: NodeJS.Timeout;
    const integrationAuthChannelListenerCallback = (event: MessageEvent) => {
      if (!authWindow.current) {
        return;
      }
      const data = JSON.parse(event.data) as IntegrationAuthChannelData;
      clearWindowClosedInterval(intervalRef.current);
      if (authWindow.current) {
        abandonedAuthWindows.current.push(authWindow.current);
      }
      if (data.payload.status === IntegrationAuthChannelDataStatus.SUCCESS) {
        onSuccessfulAuth({ hostEmail: data.payload.hostEmail });
      } else {
        onError();
      }
      if (timeoutRef) clearTimeout(timeoutRef);
      timeoutRef = setTimeout(() => closeAbandonedWindows(abandonedAuthWindows), 150); // to fix unnecessary issue raised https://mindtickle.atlassian.net/browse/LA2-1583
    };
    IntegrationAuthChannel.addEventListener('message', integrationAuthChannelListenerCallback);
    return () => {
      isUnmounted = true;
      clearWindowClosedInterval(intervalRef.current);
      if (authWindow.current) authWindow.current.close();
      if (timeoutRef) clearTimeout(timeoutRef);
      closeAbandonedWindows(abandonedAuthWindows);
      IntegrationAuthChannel.removeEventListener('message', integrationAuthChannelListenerCallback);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (!isWaitingForAuth) {
      if (authWindow.current) {
        abandonedAuthWindows.current.push(authWindow.current);
      }
      authWindow.current = null;
      clearWindowClosedInterval(intervalRef.current);
    }
  }, [isWaitingForAuth]);

  const checkAuthWindowIsStillOpen = () => {
    clearWindowClosedInterval(intervalRef.current);
    intervalRef.current = setInterval(() => {
      if (authWindow.current && authWindow.current.closed) {
        onAbruptAuthWindowCloseRef.current();
        clearWindowClosedInterval(intervalRef.current);
      }
    }, 2000);
  };

  const startAuth = () => {
    if (!oauthUrl) return;
    if (authWindow.current) {
      abandonedAuthWindows.current.push(authWindow.current);
    }
    closeAbandonedWindows(abandonedAuthWindows);
    authWindow.current = window.open(oauthUrl, '_blank');
    checkAuthWindowIsStillOpen();
  };

  return {
    startAuth,
  };
}
