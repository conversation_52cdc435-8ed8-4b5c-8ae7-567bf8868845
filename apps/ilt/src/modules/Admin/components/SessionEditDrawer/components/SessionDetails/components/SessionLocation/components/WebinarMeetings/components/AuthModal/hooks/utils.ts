import { validateWebinarURL } from '~/modules/Admin/utils';
import {
  required as checkRequired,
  email as checkValidEmail,
} from '~/modules/Admin/utils/formValidator';
import { createWebinarMeeting } from '~/modules/Admin/utils/sessionEdit';

import { authModalContent } from './constants';
import { APIParams, UseAuthModalState, UseAuthModalStateAction } from './typeDefs';

export const useAuthModalReducer = (
  state: UseAuthModalState,
  action: UseAuthModalStateAction
): UseAuthModalState => {
  switch (action.type) {
    case 'SWITCH_TO_HOST_EMAIL':
      return {
        ...state,
        flow: 'hostEmail',
        content: authModalContent.hostEmail,
        validationError: '',
        hostEmail: '',
        asyncStatus: 'idle',
        isSubmitDisabled: true,
      };
    case 'SWITCH_TO_AUTHENTICATE':
      return {
        ...state,
        flow: 'authenticate',
        content: authModalContent.authenticate,
        isSubmitDisabled: false,
        asyncStatus: 'idle',
      };
    case 'ADD_HOST_EMAIL':
      const isEmailValid = !checkValidEmail(action.payload) && !checkRequired(action.payload);
      return {
        ...state,
        hostEmail: action.payload,
        isSubmitDisabled: !isEmailValid,
        validationError: '',
      };
    case 'ASYNC_STATUS':
      return { ...state, asyncStatus: action.payload };
    case 'SET_SUBMIT_DISABLED':
      return { ...state, isSubmitDisabled: action.payload };
    case 'SET_VALIDATION_ERROR':
      return { ...state, validationError: action.payload };
    default:
      return state;
  }
};

export const createMeeting = async ({
  webinarSource,
  endTimeWithOffSet,
  startTimeWithOffSet,
  meetingTitle,
  hostEmail,
}: APIParams) => {
  // create the meeting
  let error: any;
  let meetingData;
  try {
    const response = await createWebinarMeeting({
      webinarSource: webinarSource,
      endTimeWithOffSet,
      startTimeWithOffSet,
      meetingTitle,
      hostEmail,
    });
    if (response && !response.errorCode && response.status === true) {
      meetingData = response.meetingVo;
    } else {
      error = response;
    }
  } catch (err) {
    error = err;
  }
  return { meetingData, error };
};

export const validateMeeting = async ({
  location,
  webinarSource,
  hostEmail,
}: {
  location: string;
  webinarSource: string;
  hostEmail: string;
}) =>
  validateWebinarURL({
    location,
    webinarSource: webinarSource,
    hostEmail,
    password: '',
    signal: undefined,
  });
