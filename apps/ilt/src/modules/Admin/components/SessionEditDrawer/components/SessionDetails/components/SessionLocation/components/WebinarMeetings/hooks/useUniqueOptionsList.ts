import { useMemo } from 'react';

import emailValidator from 'email-validator';

import type { TUseUniqueOptionsList, TOption } from '../typeDefs';

const useUniqueOptionsList = ({
  instructors = [],
  facilitators = [],
  coHosts = [],
  hostEmail,
}: TUseUniqueOptionsList): TOption[] =>
  useMemo(() => {
    const instructorEmails = instructors.reduce<string[]>((aggregated, instructor) => {
      if (instructor && instructor.email) {
        aggregated.push(instructor.email);
      }
      return aggregated;
    }, []);

    const facilitatorEmails = facilitators;

    const rawCombinedEmails = [...instructorEmails, ...facilitatorEmails];

    const coHostEmails = coHosts.map(item => item.email);

    return rawCombinedEmails
      .filter(
        (email, index, self) =>
          self.indexOf(email) === index &&
          emailValidator.validate(email) &&
          coHostEmails.indexOf(email) === -1 &&
          hostEmail !== email
      )
      .map(email => ({ value: email }));
  }, [instructors, facilitators, coHosts, hostEmail]);

export default useUniqueOptionsList;
