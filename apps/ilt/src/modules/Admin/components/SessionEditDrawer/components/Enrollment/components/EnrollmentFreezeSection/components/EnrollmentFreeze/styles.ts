import styled from 'styled-components';

import Select from '@mindtickle/select';
import { tokens } from '@mindtickle/styles/lib';
import { mixins, theme } from '@mindtickle/styles/lib';

export const EnrollmentFreezeContainer = styled.div`
  font-family: ${theme.fontFamily.DEFAULT};
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  margin-bottom: -17px;
  margin-top: 16px;
  .enrollment-freeze-type-view {
    ${mixins.darkText()}
  }

  .date-display {
    padding-bottom: 18px;
  }
`;

export const EnrollmentFreezeDropdown = styled(Select)`
  margin-right: 4px;

  > .dropdown {
    button {
      font-size: 13px;
      padding-left: 0px;
      color: ${tokens.textTokens.COLOR_TEXT_DEFAULT};
      text-align: left;
      width: 100%;
      max-width: 240px;
      padding: 0 5px;

      .titileDropdown {
        ${mixins.smallBlackLink()}
      }
    }

    .dropdown-menu {
      top: 22px;
      left: 0px;
      width: 100%;
      max-height: 180px;
      overflow: auto;
    }

    .caret {
      position: absolute;
      top: 12px;
      right: 12px;
    }
  }
`;

export const ErrorMessage = styled.div`
  float: left;
  color: ${tokens.textTokens.COLOR_TEXT_DANGER};
  margin-bottom: 21px;
  &.absolute-select-error {
    margin-top: -9px;
  }
`;

export const EnrollmentTypeContainer = styled.div`
  display: flex;
`;

export const DisabledMessage = styled.div`
  font-style: italic;
  margin-top: 4px;
`;

export const DROPDOWN_STYLE = { width: '320px' };
