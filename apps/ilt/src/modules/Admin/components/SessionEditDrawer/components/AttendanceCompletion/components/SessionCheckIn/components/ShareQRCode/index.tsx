import { useState, useRef } from 'react';

// import { OPERATIONS } from '@app/modules/ilt/config/sessions.constants';
import emailValidator from 'email-validator';
import { FormattedMessage } from 'react-intl';
//components

import Button from '@mindtickle/button';
import EllipsisTooltip from '@mindtickle/ellipsis-tooltip';
import Form from '@mindtickle/form';
import Icon from '@mindtickle/icon';
import Input from '@mindtickle/input';
import Popover from '@mindtickle/popover';

import { CHECKIN_UI_LABELS } from '../../constants';
import { StyledPopupForm } from '../../styles';

import type { TShareQRCode } from '../../../../typeDefs';

const ShareQRCode = (props: TShareQRCode) => {
  const [showEmailPopover, setShowEmailPopover] = useState(false);
  const [emailToShareQR, setEmailToShareQR] = useState('');
  const [invalidEmailToShare, setInvalidEmailToShare] = useState(false);
  const popoverContainer = useRef<HTMLDivElement>(null);
  const {
    session: { checkInSettings: { isSelfCheckInActivated = false } = {}, isPublished } = {},
    onShareCheckInCode,
  } = props;

  const updateEmailToShareQR = (e: any, email = '') => {
    setInvalidEmailToShare(false);
    setEmailToShareQR(email.trim());
  };

  const shareQRcode = () => {
    const {
      session: { id: sessionId },
    } = props;
    const isInValidEmail = !emailValidator.validate(emailToShareQR);

    if (!isInValidEmail) {
      onShareCheckInCode({
        sessionId: sessionId || '',
        emailIds: [emailToShareQR],
        session: props.session,
      });
      props.onSuccess();
      toggleShareQRcode();
    }
    setInvalidEmailToShare(isInValidEmail);
  };

  const toggleShareQRcode = () => {
    const {
      session: {
        checkInSettings: { isSelfCheckInActivated },
      },
    } = props;
    isSelfCheckInActivated && setShowEmailPopover(!showEmailPopover);
  };

  const getPopupForm = () => (
    <StyledPopupForm>
      <FormattedMessage {...CHECKIN_UI_LABELS.SHARE_QR_LABEL} />

      <Form.Item
        name={'shareCheckinEmail'}
        validateStatus={invalidEmailToShare ? 'error' : null}
        help={invalidEmailToShare ? 'Please enter a valid email address' : null}
      >
        <Input
          name={'shareCheckinEmail'}
          onChange={updateEmailToShareQR}
          wrapperClassName={`share-qr-email-input ${invalidEmailToShare ? 'hasError' : ''}`}
          value={emailToShareQR}
        />
      </Form.Item>
      <span className="qr-share-subtitle">
        <FormattedMessage {...CHECKIN_UI_LABELS.SHARE_QR_INFO} />
      </span>

      <div className="share-qr-actions">
        <Button type="text" onClick={toggleShareQRcode}>
          <FormattedMessage {...CHECKIN_UI_LABELS.CANCEL_BUTTON_LABEL} />
        </Button>
        <Button type="primary" onClick={shareQRcode}>
          <FormattedMessage {...CHECKIN_UI_LABELS.SHARE_BUTTON_LABEL} />
        </Button>
      </div>
    </StyledPopupForm>
  );
  return (
    <EllipsisTooltip
      wrapperClassName="qr-action-tooltip"
      align={{ targetOffset: [-8, 0] }}
      placement="bottom"
      title={
        isSelfCheckInActivated ? (
          !isPublished ? (
            <FormattedMessage {...CHECKIN_UI_LABELS.CHECKIN_SHARE_VIA_EMAIL_DISABLED_UNPUBLISHED} />
          ) : (
            <FormattedMessage {...CHECKIN_UI_LABELS.SHARE_QR_TOOLTIP} />
          )
        ) : (
          <FormattedMessage {...CHECKIN_UI_LABELS.CHECKIN_ACTION_DISABLED} />
        )
      }
      showTooltipWhenEllipsis={false}
    >
      <Popover
        content={getPopupForm()}
        getPopupContainer={() => popoverContainer.current}
        trigger="click"
        placement="bottom"
        visible={isPublished && showEmailPopover}
        onVisibleChange={toggleShareQRcode}
      >
        <div
          className={`qr-action ${
            isSelfCheckInActivated ? (!isPublished ? 'disabled-icons' : '') : 'disabled-icons'
          }`}
        >
          <div ref={popoverContainer}>
            <Icon type="message" />
          </div>
        </div>
      </Popover>
    </EllipsisTooltip>
  );
};

export default ShareQRCode;
