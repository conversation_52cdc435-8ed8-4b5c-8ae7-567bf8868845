import type { TIntegrationInfo } from '~/modules/Admin/components/SessionEditDrawer/typeDefs';

import type { IconComponent } from '../components/DropdownElement/constants';
import type { InjectedIntlProps } from 'react-intl';

export type TSelectedValueView = {
  iconType: string;
  labelText: ReactIntl.FormattedMessage.MessageDescriptor;
} & InjectedIntlProps;

export type TDropdownElement = {
  iconType: string;
  primaryText: ReactIntl.FormattedMessage.MessageDescriptor;
  secondaryText: ReactIntl.FormattedMessage.MessageDescriptor;
} & InjectedIntlProps;

export type TUseLocationOptions = {
  integrationsInfo: TIntegrationInfo;
};

export type TIconRendererProps = {
  type: string;
  componentType: IconComponent;
  style: Record<string, any>;
};

export type TIconTypeImageMap = Record<
  string,
  {
    componentType: IconComponent;
    type: string;
    selectedStyle?: Record<string, any>;
    optionStyle?: Record<string, any>;
  }
>;
