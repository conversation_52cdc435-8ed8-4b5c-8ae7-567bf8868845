import { useEffect } from 'react';

import flatten from 'lodash/flatten';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';

import Loader from '@mindtickle/loader';
import SelectWithSearch from '@mindtickle/select-with-search';
import { deepEqual } from '@mindtickle/utils';

import { LabeledSessionInfo } from '~/modules/Admin/components/SessionEditDrawer/components/LabeledSessionInfo';

import { FILTER_STYLE, MENU_PLACEMENT_TOP, TIMEZONE_INTER_FILTER_STYLES } from './constants';
import { StyledSessionTimezone } from './styles';

import type { TimezoneDropdownProps } from '../../typeDefs';

const TimezoneDropdown = (props: TimezoneDropdownProps) => {
  const { dropdownProps, currentOffset, timezones, originalTimezoneString } = props;
  let { title, ...rest } = dropdownProps;

  const updateTimezoneOnDateChange = () => {
    try {
      let timezoneObject = JSON.parse(originalTimezoneString);
      let newTimezoneObject = flatten(Object.values(timezones)).find(
        (timezone: any) => timezone.id === timezoneObject.id
      );
      if (deepEqual(newTimezoneObject, timezoneObject)) return;
      dropdownProps.onChange(JSON.stringify(newTimezoneObject));
    } catch (ex) {} //eslint-disable-line
  };

  useEffect(() => {
    const timezoneDropdownOptions: any = get(Object.values(props.timezoneDropdownOptions), 0, []);
    if (!dropdownProps.title && timezoneDropdownOptions.length > 0) {
      let timezoneSelection = timezoneDropdownOptions.find((timezone: any) => {
        let origOffset = currentOffset * 60 * 1000 * -1;
        if (timezone.offset === origOffset) {
          return true;
        }
        return false;
      });
      if (!timezoneSelection)
        timezoneDropdownOptions.find((timezone: any) => {
          if (timezone.offset === 0) {
            return true;
          }
          return false;
        });
      dropdownProps.onChange(timezoneSelection.value);
    }
    if (!isEmpty(timezones)) updateTimezoneOnDateChange();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dropdownProps, props.timezoneDropdownOptions, currentOffset, timezones]);

  const childProps = {
    fixedButtonLabel: title ? title : <Loader size="sizeXSmall" />,
    hasNone: false,
    menuPlacement: MENU_PLACEMENT_TOP,
    ...rest,
    options: flatten(Object.values(props.timezoneDropdownOptions)),
    className: 'session-timezone-dropdown',
    id: 'session-timezone-dropdown',
  };

  const styleProp = TIMEZONE_INTER_FILTER_STYLES(childProps);

  return (
    <StyledSessionTimezone>
      <LabeledSessionInfo labelText="Time zone" htmlFor="session-timezone-dropdown">
        <SelectWithSearch {...childProps} {...FILTER_STYLE} style={styleProp} />
      </LabeledSessionInfo>
    </StyledSessionTimezone>
  );
};
TimezoneDropdown.defaultProps = {
  timezoneDropdownOptions: {},
};

export default TimezoneDropdown;
