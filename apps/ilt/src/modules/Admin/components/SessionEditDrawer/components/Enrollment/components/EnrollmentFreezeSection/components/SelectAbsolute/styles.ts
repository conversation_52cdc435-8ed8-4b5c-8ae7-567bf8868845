import styled from 'styled-components';

import { tokens, mixins } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS } from '~/config/constants';

import type { TEnrollmentFreezeDatepicker } from '../../../../typeDefs';

export const EnrollmentFreezeDatepicker = styled.div<TEnrollmentFreezeDatepicker>`
  margin-top: 16px;
  margin-bottom: 1px;
  display: flex;
  align-items: center;

  > .datePicker .${THEME_PREFIX_CLS}-input {
    width: 106px;
    ${({ validateError, isPast }) =>
      (validateError || isPast) &&
      `
            border: solid 1.5px ${tokens.borderTokens.COLOR_BORDER_DANGER};
        `}
  }

  > .datePicker .${THEME_PREFIX_CLS}-time-picker-input {
    ${({ validateError, isPast }) =>
      (validateError || isPast) &&
      `
            border: solid 1.5px ${tokens.borderTokens.COLOR_BORDER_DANGER};
        `}
  }

  > .shortDisplayName {
    margin-top: 6px;
    ${({ disabled }) => disabled && `color: ${tokens.textTokens.COLOR_TEXT_DISABLED};`}
  }

  .${THEME_PREFIX_CLS}-form-item {
    margin-bottom: 16px;
  }
`;

export const StyledEnrollmentFreezeDateView = styled.div`
  display: flex;
  align-items: end;
  margin-top: 16px;
  margin-bottom: 16px;
  & .enrollment-freeze-date-view {
    margin-right: 15px;
  }
  .freeze-date {
    ${mixins.darkText()}
  }
`;
