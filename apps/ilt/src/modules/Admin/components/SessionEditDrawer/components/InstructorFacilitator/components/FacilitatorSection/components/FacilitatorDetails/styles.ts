import styled from 'styled-components';

import { THEME_PREFIX_CLS } from '~/config/constants';

export const StyledFacilitatorHeader = styled.div<{ isReadOnlyModeEnabled: boolean }>`
  display: flex;
  align-items: center;
  margin-bottom: ${props => (props.isReadOnlyModeEnabled ? '0px' : '16px')};
  & .addCohostText {
    margin: 1px 8px 0px 8px;
  }
  .deleteFacilitatorIcon {
    cursor: pointer;
  }
`;

export const StyledFacilitatorContent = styled.div`
  & .facilitatorInputCol {
    max-width: 45%;
  }
  & .facilitatorInputRow {
    justify-content: space-between;
  }
  & .${THEME_PREFIX_CLS}-form-item-label > label::before {
    display: none !important;
  }
`;

export const StyledFacilitatorSection = styled.div<{ isReadOnlyModeEnabled: boolean }>`
  .facilitator-section-divider {
    margin-top: ${props => (props.isReadOnlyModeEnabled ? '24px' : '0px')};
    margin-bottom: 18px;
  }
`;
