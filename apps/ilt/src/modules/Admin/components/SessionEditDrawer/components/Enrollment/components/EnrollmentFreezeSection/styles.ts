import styled from 'styled-components';

import { tokens } from '@mindtickle/styles/lib';
export const StyledEnrollmentFreezeContainer = styled.div`
  padding-right: 32px;

  .enrollment-freeze-divider {
    margin-bottom: 23px;
    margin-top: 23px;
  }

  .entity-enrollment-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
`;

export const StyledEnrollmentLockText = styled.span<{ checked: boolean }>`
  margin-right: 6px;
  font-weight: 600;
  font-size: 14px;
  color: ${props =>
    props.checked ? tokens.textTokens.COLOR_TEXT_DEFAULT : tokens.textTokens.COLOR_TEXT_TERTIARY};
`;
