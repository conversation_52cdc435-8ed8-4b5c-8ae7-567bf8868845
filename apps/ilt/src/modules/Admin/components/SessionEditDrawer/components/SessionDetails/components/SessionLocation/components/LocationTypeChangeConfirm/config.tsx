import { FormattedMessage } from 'react-intl';

import { LOCATION_TYPE, WEBINAR_SOURCE_LABELS } from '~/modules/Admin/config/sessions.constants';
import { getWebinarSourceByLocationType } from '~/modules/Admin/utils/sessionEdit';

import messages from './messages';

export const OK_BUTTON_TEXT = 'Update';

export const CONFIRMATION_TITLE_LABELS = Object.values(LOCATION_TYPE).reduce(
  (acc, locationType) => {
    acc[locationType] =
      locationType === LOCATION_TYPE.FACE_TO_FACE
        ? 'classroom location'
        : locationType === LOCATION_TYPE.LINK
        ? 'meeting link'
        : `${WEBINAR_SOURCE_LABELS[getWebinarSourceByLocationType(locationType)]} meeting`;
    return acc;
  },
  {} as { [key: string]: string }
);

const getConfirmationContent = (
  label: ReactIntl.FormattedMessage.MessageDescriptor,
  from: string,
  to: string
) => <FormattedMessage {...label} values={{ from, to }} />;

// read this JIRA comment for logic https://mindtickle.atlassian.net/browse/LA2-303?focusedCommentId=283333
function generateAllLocationTypeCombinationLabels() {
  const confirmationContent: { [key: string]: React.ReactNode } = {};
  const locationTypes = Object.values(LOCATION_TYPE);
  for (const fromValue of locationTypes) {
    for (const toValue of locationTypes) {
      if (fromValue === toValue) {
        continue;
      }
      confirmationContent[`${fromValue}_${toValue}`] =
        fromValue === LOCATION_TYPE.FACE_TO_FACE && toValue === LOCATION_TYPE.LINK
          ? getConfirmationContent(messages.LOCATION_NO_DISCARD, 'classroom', 'a meeting link')
          : fromValue === LOCATION_TYPE.FACE_TO_FACE
          ? getConfirmationContent(
              messages.LOCATION,
              'classroom',
              `${WEBINAR_SOURCE_LABELS[getWebinarSourceByLocationType(toValue)]} meeting`
            )
          : fromValue === LOCATION_TYPE.LINK && toValue === LOCATION_TYPE.FACE_TO_FACE
          ? getConfirmationContent(messages.LOCATION, 'meeting link', 'classroom')
          : fromValue === LOCATION_TYPE.LINK
          ? getConfirmationContent(
              messages.VIDEO_CONFERENCING,
              'meeting link',
              `${WEBINAR_SOURCE_LABELS[getWebinarSourceByLocationType(toValue)]} meeting`
            )
          : toValue === LOCATION_TYPE.FACE_TO_FACE
          ? getConfirmationContent(
              messages.LOCATION,
              `the ${WEBINAR_SOURCE_LABELS[getWebinarSourceByLocationType(fromValue)]} meeting`,
              'classroom'
            )
          : toValue === LOCATION_TYPE.LINK
          ? getConfirmationContent(
              messages.VIDEO_CONFERENCING,
              `${WEBINAR_SOURCE_LABELS[getWebinarSourceByLocationType(fromValue)]}`,
              `another meeting link`
            )
          : getConfirmationContent(
              messages.VIDEO_CONFERENCING,
              `${WEBINAR_SOURCE_LABELS[getWebinarSourceByLocationType(fromValue)]}`,
              `${WEBINAR_SOURCE_LABELS[getWebinarSourceByLocationType(toValue)]}`
            );
    }
  }
  return confirmationContent;
}

export const CONFIRMATION_CONTENT = generateAllLocationTypeCombinationLabels();
