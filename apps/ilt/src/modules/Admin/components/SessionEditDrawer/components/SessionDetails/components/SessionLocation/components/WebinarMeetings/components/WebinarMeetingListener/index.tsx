import { useEffect, useState } from 'react';

import { MODES } from '~/modules/Admin/config/sessions.constants';
import {
  checkIfInValidWebinarMeetingTime,
  checkIntegrationUserAuthEnabled,
} from '~/modules/Admin/utils/sessionEdit';

import {
  LOCATION_TYPE_ACTION,
  WEBINAR_MEETING_STATUS_OPERATION,
} from '../../../../../../constants';
import PreWebinarMeetingView from '../PreWebinarMeetingView';

import type { TWebinarMeetingListener } from '../../typeDefs';

const WebinarMeetingListener = ({
  mode,
  sessionName,
  locationType,
  integrationSource,
  webinarSource,
  listenerLocationType,
  webAutoAttendanceSettings,
  userAuth,
  onLocationTypeChange,
  isOngoingOrPastSession,
  integrationsInfo,
  meetingStatusTrack,
  updateMeetingStatus,
  endTimeWithOffSet,
  startTimeWithOffSet,
  clearPreChangeLocationType,
  preChangeLocationType,
  setPreChangeLocationType,
  session,
  moduleDetails,
}: TWebinarMeetingListener) => {
  const [autoTrigger, setAutoTrigger] = useState(false);
  const [autoTriggerData, setAutoTriggerData] = useState<any>();
  const { integrationDataMap } = integrationsInfo;
  const { meetingInvalidTimeEnum, createNewOnTimeCorrection } = meetingStatusTrack ?? {};
  const { isAutoAttendanceEnabled } = webAutoAttendanceSettings;
  const clear = () => {
    if (autoTrigger) {
      updateMeetingStatus(WEBINAR_MEETING_STATUS_OPERATION.CLEAR_MODE_DATA);
      setAutoTrigger(false);
      setAutoTriggerData(undefined);
    }
    clearPreChangeLocationType();
  };

  const onMeetingCreation = (meetingData: any) => {
    clear();
    let autoAttendanceObj = {
      isAutoAttendanceEnabled: true,
      isLinkValidated: true,
      lastValidityStatus: 'SUCCESS',
      thresholdConfig: {
        // isEnabled: session?.webAutoAttendanceSettings?.thresholdConfig?.isEnabled,
        // percentageThreshold:
        //   session?.webAutoAttendanceSettings?.thresholdConfig?.percentageThreshold,
        isEnabled: moduleDetails?.attendanceSettings?.durationBasedAttendanceEnabled,
        percentageThreshold: moduleDetails?.attendanceSettings?.percentageThreshold,
      },
    };

    if (autoTrigger) {
      const {
        autoAttendanceSettings: originalAttendanceSettings,
        meetingInfo: originalMeetingInfo,
      } = autoTriggerData;
      autoAttendanceObj = Object.assign({}, autoAttendanceObj, {
        isAutoAttendanceEnabled: !!originalAttendanceSettings.isAutoAttendanceEnabled,
      });
      meetingData = Object.assign({}, originalMeetingInfo, meetingData);
    }

    onLocationTypeChange(LOCATION_TYPE_ACTION.CHANGE, listenerLocationType, {
      webAutoAttendanceSettings: autoAttendanceObj,
      meetingSettings: meetingData,
      location: meetingData.meetingLink,
    });
  };

  useEffect(() => {
    const { integrationDataMap } = integrationsInfo;
    let timeoutRef: NodeJS.Timer;
    const inValidMeetingTimeCheck = () => {
      const latestInvalidMeetingTimeEnum = checkIfInValidWebinarMeetingTime(
        startTimeWithOffSet,
        endTimeWithOffSet,
        webinarSource
      );
      if (latestInvalidMeetingTimeEnum || meetingInvalidTimeEnum) {
        updateMeetingStatus(
          WEBINAR_MEETING_STATUS_OPERATION.MEETING_INVALID_TIME,
          latestInvalidMeetingTimeEnum
        );
      }
      return latestInvalidMeetingTimeEnum;
    };
    if (integrationDataMap[integrationSource] && !isOngoingOrPastSession) {
      if (!inValidMeetingTimeCheck()) {
        timeoutRef = setInterval(() => {
          if (inValidMeetingTimeCheck()) {
            clearInterval(timeoutRef);
          }
        }, 1000);
      }
    } else if (
      integrationDataMap[integrationSource] &&
      isOngoingOrPastSession &&
      locationType !== listenerLocationType
    ) {
      inValidMeetingTimeCheck();
    }
    return () => {
      clearInterval(timeoutRef);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    integrationsInfo,
    endTimeWithOffSet,
    startTimeWithOffSet,
    isOngoingOrPastSession,
    meetingInvalidTimeEnum,
    locationType,
  ]);

  // In case of edit unpublished session which was ongoing or past, create new meeting as soon as time get corrected
  useEffect(() => {
    if (mode === MODES.EDIT && createNewOnTimeCorrection) {
      if (locationType === listenerLocationType && !meetingInvalidTimeEnum) {
        setAutoTrigger(true);
        setAutoTriggerData({
          autoAttendanceSettings: { isAutoAttendanceEnabled },
          meetingInfo: createNewOnTimeCorrection.meetingInfo,
        });
        setPreChangeLocationType(listenerLocationType);
      } else if (locationType !== listenerLocationType) {
        // without correcting time changed location type
        updateMeetingStatus(WEBINAR_MEETING_STATUS_OPERATION.CLEAR_MODE_DATA);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [createNewOnTimeCorrection, meetingInvalidTimeEnum, isAutoAttendanceEnabled, locationType]);

  return preChangeLocationType === listenerLocationType ? (
    <PreWebinarMeetingView
      startTimeWithOffSet={startTimeWithOffSet}
      endTimeWithOffSet={endTimeWithOffSet}
      meetingTitle={sessionName}
      onCancel={clear}
      onMeetingCreation={onMeetingCreation}
      initialHostEmail={
        autoTrigger
          ? autoTriggerData.meetingInfo.hostEmail || userAuth.primaryEmail
          : userAuth.primaryEmail
      }
      webinarSource={webinarSource}
      isUserLevelAuthEnabled={checkIntegrationUserAuthEnabled(
        integrationDataMap,
        integrationSource
      )}
      integrationSource={integrationSource}
    />
  ) : null;
};

export default WebinarMeetingListener;
