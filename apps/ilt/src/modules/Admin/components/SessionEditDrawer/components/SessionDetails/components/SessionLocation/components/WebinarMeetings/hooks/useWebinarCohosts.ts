import { useCallback, useState } from 'react';

import { getWebinarMeetingSettingsKeyByWebinarSource as getWebinarMeetingKey } from '~/modules/Admin/utils/sessionEdit';
import { EMPTY_ARRAY_READONLY } from '~/mt-ui-core/config/global.config';

import type { TUseWebinarCohosts, TSetCoHostsInEditMode } from '../typeDefs';

const useWebinarCohosts = ({
  webinarMeetingSettings,
  updateSessionState,
  webinarSource,
}: TUseWebinarCohosts) => {
  const [coHostsInEditMode, setCoHostsInEditMode] = useState<TSetCoHostsInEditMode[]>([]);
  const [newCoHost, setNewCoHost] = useState<any>();
  const webinarMeetingKey = getWebinarMeetingKey(webinarSource);

  const { coHosts = EMPTY_ARRAY_READONLY } = webinarMeetingSettings ?? {};

  const editCoHost = (value: string, onSubmitTrigger: { current?: () => void }) => {
    setCoHostsInEditMode(prev => [...prev, { email: value, onSubmitTrigger }]);
  };

  const cancelCoHost = (cancelledValue: string) => {
    setCoHostsInEditMode(prev => prev.filter(value => value.email !== cancelledValue));
  };

  const deleteCoHost = (emailId: string) => {
    if (emailId) {
      let _coHosts = coHosts.filter(value => value.email !== emailId);
      updateSessionState({
        key: webinarMeetingKey,
        value: {
          ...webinarMeetingSettings,
          coHosts: _coHosts,
        },
      });
    }
  };

  const updateCoHost = (newValue: string, oldValue: string, callbackSetEdit: Function) => {
    if (newValue === oldValue) {
      cancelCoHost(oldValue);
      callbackSetEdit(false);
    } else {
      let { coHosts = [] } = webinarMeetingSettings ?? {};
      const indexOfNewValue = coHosts.findIndex(value => value.email === newValue);
      if (indexOfNewValue > -1) {
        return false;
      }
      cancelCoHost(oldValue);
      callbackSetEdit(false);

      coHosts = [...coHosts];
      const indexOfOldValue = coHosts.findIndex(value => value.email === oldValue);
      if (indexOfOldValue > -1) {
        coHosts[indexOfOldValue] = { email: newValue };
      } else {
        coHosts.push({ email: newValue });
      }

      updateSessionState({
        key: webinarMeetingKey,
        value: {
          ...webinarMeetingSettings,
          coHosts,
        },
      });
    }
  };

  const addCoHost = (newValue: string) => {
    if (newValue) {
      let { coHosts = [] } = webinarMeetingSettings ?? {};

      if (coHosts.some(coHost => coHost.email === newValue)) {
        return;
      }
      setNewCoHost(undefined);
      coHosts = coHosts.concat([{ email: newValue }]);

      updateSessionState({
        key: webinarMeetingKey,
        value: {
          ...webinarMeetingSettings,
          coHosts,
        },
      });
    }
  };

  const addNewCoHost = () => {
    setNewCoHost({});
  };

  const onNewCohostEditMount = ({ onSubmitTrigger }: { onSubmitTrigger: Function }) => {
    setNewCoHost({ onSubmitTrigger });
  };

  const cancelNewCoHost = () => {
    setNewCoHost(undefined);
  };

  const validateCoHost = useCallback(
    (value: string, originalValue: string) =>
      !coHosts.some(item => item.email === value && item.email !== originalValue),
    [coHosts]
  );
  return {
    coHostsInEditMode,
    newCoHost,
    editCoHost,
    cancelCoHost,
    deleteCoHost,
    updateCoHost,
    addCoHost,
    addNewCoHost,
    onNewCohostEditMount,
    cancelNewCoHost,
    validateCoHost,
  };
};

export default useWebinarCohosts;
