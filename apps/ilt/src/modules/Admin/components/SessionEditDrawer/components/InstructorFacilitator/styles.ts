import styled from 'styled-components';

import { tokens } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS } from '~/config/constants';

import { tabContainerCommonMixin } from '../../styles';

export const StyledInstructorFacilitatorOverallTabContainer = styled.div`
  ${tabContainerCommonMixin()}
`;

export const StyledInstructorFacilitatorTabContainer = styled.div`
  padding-right: 32px;
  & .${THEME_PREFIX_CLS}-tabs-content {
    padding-left: 0px !important;
  }
  & .${THEME_PREFIX_CLS}-tabs-nav-wrap {
    padding: 0px;
  }
  > .${THEME_PREFIX_CLS}-tabs {
    > .${THEME_PREFIX_CLS}-tabs-content-holder {
      & .${THEME_PREFIX_CLS}-tabs-content {
        > .${THEME_PREFIX_CLS}-tabs-tabpane-active {
          padding-left: 0px !important;
        }
      }
    }
  }
`;

export const verticalDividerStyle = {
  borderLeftColor: tokens.borderTokens.COLOR_BORDER_STRONG,
};
