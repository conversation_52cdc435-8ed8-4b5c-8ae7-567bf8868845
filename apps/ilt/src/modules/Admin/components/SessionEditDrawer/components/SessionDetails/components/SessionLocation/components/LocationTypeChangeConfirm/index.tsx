import { useRef } from 'react';

import { FormattedMessage } from 'react-intl';

import Button from '@mindtickle/button';
import Modal from '@mindtickle/modal';

import { MASKED_MODAL_INDEX } from '~/config/constants';
import { LOCATION_TYPE } from '~/modules/Admin/config/sessions.constants';

import { StyledConfirmModalWrapper } from '../../styles';

import { CONFIRMATION_TITLE_LABELS, CONFIRMATION_CONTENT } from './config';
import messages from './messages';

import type { TLocationTypeChangeConfirmModal, TModalFooter } from '../../typeDefs';

// read this JIRA comment for logic https://mindtickle.atlassian.net/browse/LA2-303?focusedCommentId=283333
const getConfirmTexts = (currentLocationType: string, newLocationType?: string) => {
  if (!newLocationType) {
    newLocationType = LOCATION_TYPE.FACE_TO_FACE;
  }
  const title = (
    <FormattedMessage
      {...messages.CONFIRMATION_TITLE}
      values={{ to: CONFIRMATION_TITLE_LABELS[newLocationType] }}
    />
  );

  const content = CONFIRMATION_CONTENT[`${currentLocationType}_${newLocationType}`];

  if (!content) {
    throw Error('One of location type combination is not added');
  }
  return {
    title,
    content,
  };
};

const ModalFooter = ({ onCancel, onOk }: TModalFooter) => (
  <>
    <Button type="text" onClick={onCancel} className={'cancelButton'}>
      <FormattedMessage {...messages.CANCEL_BUTTON_TEXT} />
    </Button>
    <Button type="primary" size="small" onClick={onOk}>
      <FormattedMessage {...messages.OK_BUTTON_TEXT} />
    </Button>
  </>
);

const LocationTypeChangeConfirmModal = ({
  locationType,
  newLocationType,
  onCancelUpdateConfirm,
  onOkUpdateConfirm,
}: TLocationTypeChangeConfirmModal) => {
  const modalContainerRef = useRef<HTMLDivElement>(null);
  const { title, content } = getConfirmTexts(locationType || '', newLocationType);
  return (
    <StyledConfirmModalWrapper ref={modalContainerRef}>
      <Modal
        title={title}
        visible={true}
        onOk={onOkUpdateConfirm}
        onCancel={onCancelUpdateConfirm}
        centered={true}
        getPopupContainer={() => modalContainerRef.current}
        maskClosable={false}
        keyboard={false}
        type={'small'}
        zIndex={MASKED_MODAL_INDEX}
        footer={<ModalFooter onCancel={onCancelUpdateConfirm} onOk={onOkUpdateConfirm} />}
      >
        <div>{content}</div>
      </Modal>
    </StyledConfirmModalWrapper>
  );
};

export default LocationTypeChangeConfirmModal;
