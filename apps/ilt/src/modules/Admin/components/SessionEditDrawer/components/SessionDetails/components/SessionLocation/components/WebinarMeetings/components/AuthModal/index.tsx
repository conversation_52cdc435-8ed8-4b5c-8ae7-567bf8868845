import { useRef } from 'react';

import { FormattedMessage } from 'react-intl';

import Button from '@mindtickle/button';
import EllipsisTooltip from '@mindtickle/ellipsis-tooltip';
import { ICON_MAP } from '@mindtickle/icon';
import Input from '@mindtickle/input';
import Modal from '@mindtickle/modal';

import { MASKED_MODAL_INDEX } from '~/config/constants';
import { WEBINAR_SOURCE_LABELS } from '~/modules/Admin/constants/sessions';

import {
  StyledFormItemCustomMargin,
  StyledConfirmModalWrapper as StyledModalWrapper,
} from '../../../../styles';

import useWebinarAuthFlow from './hooks/useAuthModel';
import AUTH_MODAL_CONTENT from './messages';
import { ModalContentWrapper, StyledIcon } from './styles';

import type { IHostAuthModalProps, IAuthModalFooterProps } from './typeDefs';

const ModalFooter = ({
  onCancel,
  onOk,
  okButtonText,
  okButtonDisabled,
  loading,
}: IAuthModalFooterProps) => (
  <>
    <Button type="text" onClick={onCancel} className={'cancel-button'}>
      <FormattedMessage {...AUTH_MODAL_CONTENT.DISCARD_TEXT} />
    </Button>
    <Button type="primary" onClick={onOk} disabled={okButtonDisabled} loading={loading}>
      {okButtonText}
    </Button>
  </>
);

const HostInputEmailModal = ({
  onCancel,
  hostEmail,
  handleMeetingCreationSuccess,
  webinarSource,
  integrationSource,
  endTimeWithOffSet,
  startTimeWithOffSet,
  meetingTitle,
  creationInProgress = false,
  setCreationInProgress,
  flow,
  handleMeetingValidationSuccess,
  validationInProgress = false,
  setValidationInProgress,
  meetingUrl,
  initialFlow,
}: IHostAuthModalProps) => {
  const webinarSourceLabel = WEBINAR_SOURCE_LABELS[webinarSource];
  const {
    state,
    switchToHostEmail,
    startWebFlowAuth,
    addHostEmail,
    // setAsyncStatus,
    onPressEnter,
    onPressOk,
  } = useWebinarAuthFlow({
    hostEmail,
    webinarSource,
    integrationSource,
    endTimeWithOffSet: endTimeWithOffSet!,
    startTimeWithOffSet: startTimeWithOffSet!,
    meetingTitle,
    onSuccessfulMeetingCreation: handleMeetingCreationSuccess!,
    creationInProgress,
    setCreationInProgress: setCreationInProgress!,
    handleMeetingValidationSuccess: handleMeetingValidationSuccess!,
    validationInProgress,
    setValidationInProgress: setValidationInProgress!,
    flow,
    meetingUrl,
    initialFlow,
  });

  const modalContainerRef = useRef<HTMLDivElement>(null);

  const hostEmailToDisplay: string = state.hostEmail?.length ? state.hostEmail : hostEmail;

  const primaryCTAText =
    flow === 'validate' && state.flow === 'hostEmail'
      ? AUTH_MODAL_CONTENT.SAVE
      : state.content.primaryCTAText;

  const secondaryCTATextPrefix =
    flow === 'create' ? (
      <FormattedMessage {...AUTH_MODAL_CONTENT.ALREADY_HAVE_ACCOUNT} />
    ) : flow === 'validate' ? (
      <FormattedMessage
        {...AUTH_MODAL_CONTENT.IS_NOT_HOST}
        values={{
          hostEmail: <strong>{hostEmailToDisplay}</strong>,
        }}
      />
    ) : (
      <></>
    );

  if (validationInProgress || creationInProgress) {
    return null;
  }

  return (
    <StyledModalWrapper ref={modalContainerRef}>
      <Modal
        title={
          <FormattedMessage
            {...state.content.modalHeader}
            values={{ sourceLabel: webinarSourceLabel }}
          />
        }
        visible={true}
        onCancel={onCancel} // for x button
        centered={true}
        getPopupContainer={() => modalContainerRef.current}
        maskClosable={false}
        keyboard={false}
        type={'small'}
        footer={
          <ModalFooter
            onCancel={onCancel}
            onOk={onPressOk}
            okButtonDisabled={state.isSubmitDisabled && state.asyncStatus !== 'loading'}
            loading={state.asyncStatus === 'loading'}
            okButtonText={<FormattedMessage {...primaryCTAText} />}
          />
        }
        zIndex={MASKED_MODAL_INDEX}
      >
        <ModalContentWrapper>
          {state.flow === 'authenticate' ? (
            <>
              <div className="error-wrapper">
                <div>
                  <StyledIcon type={ICON_MAP.WARNING} />
                </div>
                <div className="note-wrapper">
                  <span className="bold">Please note: </span>
                  {
                    <FormattedMessage
                      {...AUTH_MODAL_CONTENT.AUTHENTICATE_ERROR}
                      values={{ sourceLabel: webinarSourceLabel }}
                    />
                  }
                  <br />{' '}
                  <EllipsisTooltip title={hostEmailToDisplay}>{hostEmailToDisplay}</EllipsisTooltip>
                </div>
              </div>
              <div>
                {secondaryCTATextPrefix}{' '}
                <span className="secondary-cta" onClick={switchToHostEmail}>
                  <FormattedMessage
                    {...state.content.secondaryCTAText}
                    values={{ sourceLabel: webinarSourceLabel }}
                  />
                </span>
              </div>
            </>
          ) : (
            <>
              <div className="input-label">
                <FormattedMessage
                  {...AUTH_MODAL_CONTENT.INPUT_LABEL}
                  values={{ sourceLabel: webinarSourceLabel }}
                />
              </div>
              <StyledFormItemCustomMargin
                validateStatus={state.validationError ? 'error' : 'success'}
                help={state.validationError}
              >
                <Input
                  name="hostemail"
                  value={state.hostEmail}
                  onChange={(event: any) => {
                    addHostEmail(event.target.value);
                  }}
                  placeholder={'<EMAIL>'}
                  onPressEnter={onPressEnter}
                />
              </StyledFormItemCustomMargin>
              <div className="secondary-cta-wrapper">
                Or{' '}
                <span className="secondary-cta" onClick={startWebFlowAuth}>
                  <FormattedMessage
                    {...state.content.secondaryCTAText}
                    values={{ sourceLabel: webinarSourceLabel }}
                  />
                </span>
              </div>
            </>
          )}
        </ModalContentWrapper>
      </Modal>
    </StyledModalWrapper>
  );
};

export default HostInputEmailModal;
