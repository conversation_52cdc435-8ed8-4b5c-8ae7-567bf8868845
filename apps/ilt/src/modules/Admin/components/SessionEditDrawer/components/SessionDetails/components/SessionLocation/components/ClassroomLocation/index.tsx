import { useMemo } from 'react';

import _debounce from 'lodash/debounce';
//components
import { injectIntl } from 'react-intl';

import Input from '@mindtickle/input';

import { LOCATION_TYPE_PLACEHOLDERS } from '~/modules/Admin/config/sessions.constants';
import { checkUrlEligibilityForIntegrations } from '~/modules/Admin/utils';

import { TClassroomLocation } from '../../typeDefs';

const ClassroomLocation = ({
  session: { location },
  updateSessionState,
  integrationsInfo,
  onUrlEligibleForValidation,
  intl,
}: TClassroomLocation) => {
  const { isIntegrationAvailable, integrationDataMap } = integrationsInfo;

  const onChangeHandler = useMemo(() => {
    const onChange = _debounce(value => {
      updateSessionState({ key: 'location', value });
      if (isIntegrationAvailable) {
        const { eligible } = checkUrlEligibilityForIntegrations({
          location: value,
          integrations: integrationDataMap,
        });
        if (eligible) {
          onUrlEligibleForValidation();
        }
      }
    }, 500);
    return (_: any, value: string) => {
      value = value ? value.trim() : value;
      onChange(value);
    };
  }, [updateSessionState, isIntegrationAvailable, integrationDataMap, onUrlEligibleForValidation]);

  const childProps = {
    name: 'location',
    value: location,
    placeholder: intl.formatMessage(LOCATION_TYPE_PLACEHOLDERS.CLASSROOM_LOCATION),
    className: 'locationWidth',
    wrapperClassName: 'locationWrapper',
    onChange: onChangeHandler,
  };

  return (
    <div className="classroom-location-wrapper">
      <Input {...childProps} />
    </div>
  );
};

export default injectIntl(ClassroomLocation);
