import { useState } from 'react';

import Input from '@mindtickle/input';
import Pill, { PILL_STATES, PILL_TYPES } from '@mindtickle/pill';

import { WEBEX_PASSWORD_SECTION_TEXTS as SECTION_TEXTS } from '../../../../../../constants';
import { StyledEdit, StyledFormItem } from '../../../../styles';
import { StyledPasswordSection, StyledPasswordSectionHeading } from '../../styles';

import type { TPasswordSection } from '../../../../typeDefs';

const PasswordSection = ({
  validationMessage,
  password,
  onSave,
  onClear,
  isLoading,
  loadingState,
}: TPasswordSection) => {
  const [statePassword, setStatePassword] = useState(password);
  const handleOnChange = (_: any, value: any) => {
    setStatePassword(value);
  };
  const handleSave = () => {
    onSave(statePassword);
  };
  const onEnter = (_e: any) => {
    handleSave();
  };
  const handleClear = () => {
    setStatePassword('');
    onClear();
  };
  return (
    <StyledPasswordSection>
      <StyledPasswordSectionHeading>{SECTION_TEXTS.SECTION_HEADING}</StyledPasswordSectionHeading>
      <StyledFormItem
        validateStatus={validationMessage ? 'error' : 'success'}
        help={validationMessage}
      >
        <StyledEdit>
          <Input
            name="meetingpassword"
            placeholder={SECTION_TEXTS.INPUT_PLACEHOLDER}
            wrapperClassName={'editTextFlexItem'}
            value={statePassword}
            onChange={handleOnChange}
            onPressEnter={onEnter}
          />

          <Pill
            className="editTextFlexItem save-button"
            onClick={handleSave}
            disabled={!statePassword}
            tagState={!statePassword ? PILL_STATES.DISABLED : PILL_STATES.NORMAL}
            type={PILL_TYPES.ACTION}
          >
            {SECTION_TEXTS.SAVE_BUTTON_TEXT}
          </Pill>

          <Pill
            className="editTextFlexItem clear-button"
            disabled={isLoading || !statePassword}
            onClick={handleClear}
            tagState={isLoading || !statePassword ? PILL_STATES.DISABLED : PILL_STATES.NORMAL}
            type={PILL_TYPES.NEGATIVE}
          >
            {SECTION_TEXTS.CLEAR_BUTTON_TEXT}
          </Pill>
        </StyledEdit>
      </StyledFormItem>
      {loadingState}
    </StyledPasswordSection>
  );
};
export default PasswordSection;
