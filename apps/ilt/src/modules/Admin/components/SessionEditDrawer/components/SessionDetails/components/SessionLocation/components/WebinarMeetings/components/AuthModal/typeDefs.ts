import { AuthModalFlow } from './hooks/typeDefs';

export interface IAuthModalFooterProps {
  onOk: (value: string | undefined) => void;
  onCancel: () => void;
  okButtonDisabled: boolean;
  okButtonText: React.ReactNode;
  loading: boolean;
}

export type Flow = 'create' | 'validate';

export type IHostAuthModalProps = {
  flow: Flow;
  handleMeetingCreationSuccess?: (meetingData: any) => void;
  creationInProgress?: boolean;
  setCreationInProgress?: (value: boolean) => void;
  endTimeWithOffSet?: number;
  startTimeWithOffSet?: number;
  meetingTitle?: string;
  handleMeetingValidationSuccess?: (meetingData: any) => void;
  validationInProgress?: boolean;
  setValidationInProgress?: (value: boolean) => void;
  hostEmail: string;
  webinarSource: string;
  integrationSource: string;
  onCancel: () => void;
  meetingUrl?: string;
  initialFlow?: AuthModalFlow;
};
