export interface TSessionTimingsSelector {
  startTime: number;
  endTime: number;
  minStartTime?: number;
  update: (type: string, value: any, isTimeUpdated?: any) => void;
  duration?: number;
  timezone?: any;
  dateTimeValidationErrors?: JSX.Element;
  currentOffset?: number;
  disableTimeEdit?: boolean;
  enrollmentFreezeEnabled?: boolean;
  isReadOnlyModeEnabled: boolean;
  changeTimezoneListLoadingState: (loading: boolean) => void;
}

export type TimezoneDropdownProps = {
  dropdownProps: {
    id: string;
    title: string;
    setTitle: boolean;
    className: any;
    search: boolean;
    isDisabled: boolean;
    value: any;
    onChange: (data: any) => void;
  };
  style?: object;
  timezones?: any;
  timezoneDropdownOptions?: any;
  originalTimezoneString?: any;
  currentOffset: number;
};
