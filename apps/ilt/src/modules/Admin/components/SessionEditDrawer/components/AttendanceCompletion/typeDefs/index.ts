import type { TUpdateSessionState, TSessionObject } from '../../../typeDefs';
import type { FormInstance } from 'antd/lib/form';
export interface TAttendanceCompletion {
  session: TSessionObject;
  updateSessionState: TUpdateSessionState;
  mode: string;
  isReadOnlyModeEnabled: boolean;
  operationStatus: any;
  form: FormInstance;
  onShareCheckInCode: (params: { sessionId: string; session: any; emailIds: [string] }) => void;
  moduleDetails?: any;
  companySettings?: any;
  isSessionTimeModified?: any;
}

export interface TSessionCheckIn {
  session: TSessionObject;
  updateSessionState: TUpdateSessionState;
  mode: string;
  isReadOnlyModeEnabled: boolean;
  operationStatus: any;
  form: FormInstance;
  onShareCheckInCode: (params: { sessionId: string; session: any; emailIds: [string] }) => void;
}

export interface TSessionAutoAttendanceIn {
  companyAttendanceSettings?: {
    durationBasedAttendanceEnabled: boolean;
    allowEditSettings: boolean;
    defaultILTSetting: boolean;
    percentageThreshold: number;
  };
  sessionAttendanceSettings?: {
    isEnabled: boolean;
    percentageThreshold: number;
  };
  moduleAttendanceSettings?: any;
  session: TSessionObject;
  updateSessionState: TUpdateSessionState;
  mode: string;
  isReadOnlyModeEnabled: boolean;
}

export interface TSessionEnrolmentEmailSettings {
  intl?: any;
  sessionEnrolmentEmailSettings: any;
  moduleEnrolmentEmailSettings: any;
  session: TSessionObject;
  updateSessionState: TUpdateSessionState;
  mode: string;
  isReadOnlyModeEnabled: boolean;
  companyId: string;
  moduleId: string;
  isSessionTimeModified?: any;
}

export interface TCheckinDetails {
  session: TSessionObject;
  checkinDetailsRef: any;
}

export interface TShareQRCode {
  session: TSessionObject;
  onShareCheckInCode: (params: { sessionId: string; session: any; emailIds: [string] }) => void;
  onSuccess: Function;
}

interface checkInTime {
  hours: number;
  minutes: number;
  hasError?: boolean;
}

export interface TTimeSelector {
  checkinTimeBeforeSession: checkInTime;
  checkinTimeAfterSession: checkInTime;
  setCheckinTimeBeforeSession: Function;
  setCheckinTimeAfterSession: Function;
  sessionId?: string;
  updateSessionState: TUpdateSessionState;
  checkInSettings: any;
  isReadOnlyModeEnabled: boolean;
  form: FormInstance;
}
