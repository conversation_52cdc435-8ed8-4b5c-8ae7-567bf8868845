import styled from 'styled-components';

import { tokens } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS } from '~/config/constants';

export const StyledAutoAttendanceHeadingRow = styled.div`
  display: flex;
  align-items: center;
  & .autoAttendanceHeading {
    font-weight: 600;
  }
  & .infoHover {
    padding: 0 12px;
    color: ${tokens.iconTokens.COLOR_ICON_DEFAULT};
    font-size: 14px;
  }
  & .tooltipInfo {
    z-index: 999999 !important;
    cursor: pointer;
    max-width: 340px;
    & .${THEME_PREFIX_CLS}-tooltip-arrow {
      left: 38.5%;
      transform: translateX(-38.5%);
    }
  }
`;

export const StyledAutoAttendanceContainer = styled.div`
  font-size: 12px;
  line-height: 16px;
  color: ${tokens.textTokens.COLOR_TEXT_SECONDARY};
`;

export const StyledCheckinWarning = styled.div`
  color: ${tokens.textTokens.COLOR_TEXT_SECONDARY};
  margin-top: 8px;
  line-height: 16px;
`;
