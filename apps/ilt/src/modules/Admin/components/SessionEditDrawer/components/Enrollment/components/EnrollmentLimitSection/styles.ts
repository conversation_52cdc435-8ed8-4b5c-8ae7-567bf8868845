import styled, { css } from 'styled-components';

import { tokens } from '@mindtickle/styles/lib';
const CommonTextStyle = css`
  font-weight: 600;
  font-size: 14px;
  color: ${tokens.textTokens.COLOR_TEXT_DEFAULT};
`;

export const StyledEnrollmentLimitContainer = styled.div`
  padding-right: 32px;
`;

export const StyledEnrollmentLimitText = styled.span<{ checked: boolean }>`
  margin-right: 10px;
  display: flex;
  align-items: center;
  ${CommonTextStyle}
  color: ${props =>
    props.checked ? tokens.textTokens.COLOR_TEXT_DEFAULT : tokens.textTokens.COLOR_TEXT_TERTIARY};
`;

export const StyledWaitingListText = styled.span<{ checked: boolean }>`
  margin-right: 6px;
  display: flex;
  align-items: center;
  ${CommonTextStyle}
  color: ${props =>
    props.checked ? tokens.textTokens.COLOR_TEXT_DEFAULT : tokens.textTokens.COLOR_TEXT_TERTIARY};
`;

export const StyledMaxSeatsContainer = styled.div`
  margin-bottom: 16px;
  margin-top: 16px;
`;

export const StyledWaitingListContainer = styled.div``;

export const StyledInfoCaption = styled.div`
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  flex-basis: 100%;
  margin-top: 4px;
`;
