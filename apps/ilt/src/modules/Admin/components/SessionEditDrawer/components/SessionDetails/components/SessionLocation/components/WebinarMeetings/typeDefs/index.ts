import type { Dispatch, SetStateAction } from 'react';

import type {
  TOnLocationTypeChange,
  TIntegrationInfo,
  TMeetingStatusTrack,
  TUpdateMeetingStatus,
  TWebAutoAttendanceSettings,
  TWebinarMeetingSettings,
  TCoHost,
  TInstructor,
  TUpdateSessionState,
} from '~/modules/Admin/components/SessionEditDrawer/typeDefs';
import type {
  INTEGRATION_SOURCE,
  WEBINAR_SOURCE,
  LOCATION_TYPE,
} from '~/modules/Admin/config/sessions.constants';
export interface TOnMountObject {
  onSubmitTrigger?: () => void;
}

export type TEditableText = {
  emailId?: string;
  onSave?: Function;
  onCancel?: Function;
  onDelete?: Function;
  onEdit?: Function;
  onEditValidate?: Function;
  defaultEdit?: boolean;
  noDelete?: boolean;
  noEdit?: boolean;
  optionList?: any[];
  onMount?: Function;
};

export interface THostInputEmailModal {
  modalTitle?: string;
  modalContent?: string;
  okText?: string;
  cancelText?: string;
  loading?: boolean;
  onOk: (value: string) => void;
  onCancel: () => void;
  hostEmail: string;
  setHostEmail: Dispatch<SetStateAction<string>>;
}

export interface TPreWebinarMeetingView {
  endTimeWithOffSet: number;
  startTimeWithOffSet: number;
  meetingTitle?: string;
  initialHostEmail: string;
  onMeetingCreation: (meetingData: any) => void;
  onCancel: () => void;
  webinarSource: string;
  isUserLevelAuthEnabled: boolean;
  integrationSource: string;
}

export interface TWebinarMeetingListener {
  mode?: string;
  sessionName?: string;
  locationType?: (typeof LOCATION_TYPE)[keyof typeof LOCATION_TYPE];
  userAuth?: any;
  onLocationTypeChange: TOnLocationTypeChange;
  isOngoingOrPastSession?: boolean;
  integrationsInfo: TIntegrationInfo;
  meetingStatusTrack?: TMeetingStatusTrack;
  updateMeetingStatus: TUpdateMeetingStatus;
  endTimeWithOffSet: number;
  startTimeWithOffSet: number;
  clearPreChangeLocationType: () => void;
  preChangeLocationType?: string;
  setPreChangeLocationType: Dispatch<SetStateAction<string>>;
  webAutoAttendanceSettings: TWebAutoAttendanceSettings;
  integrationSource: (typeof INTEGRATION_SOURCE)[keyof typeof INTEGRATION_SOURCE];
  webinarSource: (typeof WEBINAR_SOURCE)[keyof typeof WEBINAR_SOURCE];
  listenerLocationType: (typeof LOCATION_TYPE)[keyof typeof LOCATION_TYPE];
  session?: any;
  moduleDetails?: any;
}

export interface TUseUniqueOptionsList {
  instructors?: TInstructor[];
  facilitators?: string[];
  coHosts: TCoHost[];
  hostEmail: string;
}

export interface TOption {
  value: string;
}

export interface TUseWebinarCohosts {
  webinarMeetingSettings?: TWebinarMeetingSettings;
  updateSessionState: TUpdateSessionState;
  webinarSource: string;
}

export interface TSetCoHostsInEditMode {
  email: string;
  onSubmitTrigger: { current?: () => void };
}

export interface ICreateMeetingParams {
  hostEmail: string;
  webinarSource: string;
  endTimeWithOffSet: number;
  startTimeWithOffSet: number;
  meetingTitle?: string;
  handleMeetingCreationSuccess: Function;
  setHostEmail: Function;
  handleHostError: Function;
  handleMeetingError: Function;
  setCreationInProgress: Function;
  setCreationErrInvalidHost: Function;
  isUnmounted: any;
  onCancel: Function;
}
