import styled from 'styled-components';

import { tokens } from '@mindtickle/styles/lib';
import { theme } from '@mindtickle/styles/lib';

export const StyledInstructorsSection = styled.div`
  min-height: 200px;
  margin-top: 24px;
  .info-container-row {
    margin-bottom: 10px;
  }
  > .action-row {
    margin-bottom: 16px;
    & .actionContainer {
      display: flex;
    }
  }
  & .tooltipInfo {
    z-index: 999999 !important;
    cursor: pointer;
    max-width: 340px;
  }
  & .infoHover {
    cursor: pointer;
    color: ${tokens.iconTokens.COLOR_ICON_DEFAULT};
    font-size: 14px;
  }
`;

export const StyledCheckboxContainer = styled.div<{ isReadOnlyModeEnabled: boolean }>`
  margin-left: ${props => (props.isReadOnlyModeEnabled ? '0px' : '16px')};
  margin-right: 3px;
  display: flex;
  align-items: center;
  & .notifyText {
    margin-right: 8px;
  }
  & .notify-instructors-checkbox {
    padding-bottom: 6px;
    margin-right: 8px;
  }
`;

export const StyledAutoSyncMessageContainer = styled.div`
  background-color: ${tokens.bgTokens.COLOR_BG_WARNING};
  border-radius: 4px;
  width: fit-content;
  padding: 8px 16px;
  border-radius: 8px;

  & .infoContainerText {
    font-family: ${theme.fontFamily.DEFAULT};
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 16px;
    color: ${tokens.textTokens.COLOR_TEXT_DEFAULT};
    display: flex;
    align-items: center;
  }

  & .warningIcon {
    color: ${tokens.iconTokens.COLOR_ICON_DANGER};
    margin-right: 9px;
  }

  & .noteHeading {
    font-weight: 600;
    font-size: 12px;
    margin-right: 3px;
  }
`;

export const StyledInstructorsInfoWrapper = styled.div`
  display: flex;
  flex-direction: column-reverse;
`;
