import { Fragment } from 'react';

import debounce from 'lodash/debounce';
import { FormattedMessage } from 'react-intl';

import Checkbox from '@mindtickle/checkbox';
import Grid from '@mindtickle/grid';
import Icon, { ICON_MAP } from '@mindtickle/icon';
import Pill, { PILL_TYPES } from '@mindtickle/pill';
import Tooltip, { TOOLTIP_SIZE } from '@mindtickle/tooltip';

import {
  ILT_CALENDAR_AUTO_SYNC_CONFIG_KEY,
  INSTRUCTOR_CALENDAR_AUTO_SYNC_INFO,
} from '~/modules/Admin/config/sessions.constants';

import InstructorDetails from './components/InstructorDetails';
import {
  StyledInstructorsSection,
  StyledCheckboxContainer,
  StyledAutoSyncMessageContainer,
  StyledInstructorsInfoWrapper,
} from './styles';

import type { TInstructorSection } from '../../typeDefs';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';
const { Row } = Grid;

function InstructorSection(props: TInstructorSection) {
  const {
    session: { instructors = [], sendInstructorMail = true },
    userAuth,
    setSession,
    getCohostCheckbox,
    updateSessionState,
    removeCohost,
    isReadOnlyModeEnabled,
    form,
  } = props;

  const isCalendarAutoSyncEnabled =
    ILT_CALENDAR_AUTO_SYNC_CONFIG_KEY in userAuth.tempFeatureConfig
      ? userAuth.tempFeatureConfig.isILTSessionCalendarAutoSyncEnabled
      : true;

  const updateInstructor = debounce((type, index, value) => {
    setSession(prevState => {
      let session = Object.assign({}, prevState);
      let instructors = [...session.instructors];
      instructors[index] = Object.assign({}, instructors[index], { [type]: value });
      session.instructors = instructors;
      return session;
    });
  }, 500);

  const addInstructor = () => {
    let {
      session: { instructors = [] },
    } = props;
    instructors = [...instructors];
    instructors.push({
      description: '',
      name: '',
      email: '',
    });
    updateSessionState({
      key: 'instructors',
      value: instructors,
    });
  };

  const removeInstructor = (showCohost: boolean, instructorEmail: string, index: number) => {
    showCohost && removeCohost(instructorEmail);
    updateSessionState({
      key: 'instructors',
      value: instructors.filter((val: any, i: number) => i !== index),
    });
  };

  return (
    <StyledInstructorsSection>
      <Row align="middle" className={'action-row'}>
        {!isReadOnlyModeEnabled && (
          <Pill className="editTextFlexItem" onClick={addInstructor} type={PILL_TYPES.ACTION}>
            +&nbsp;&nbsp;Add instructor
          </Pill>
        )}
        <StyledCheckboxContainer isReadOnlyModeEnabled={isReadOnlyModeEnabled}>
          <Checkbox
            name="sendInstructorMail"
            checked={!!sendInstructorMail}
            onChange={(e: CheckboxChangeEvent) =>
              updateSessionState({
                key: 'sendInstructorMail',
                value: e.target.checked,
              })
            }
            disabled={isReadOnlyModeEnabled}
            className="notify-instructors-checkbox"
          />
          <span className={'notifyText'}>Notify instructors via email</span>
          <Tooltip
            overlayClassName={'tooltipInfo'}
            size={TOOLTIP_SIZE.LARGE}
            placement="bottom"
            title={
              <p>
                If enabled, instructors will get update emails when:
                <br />
                <br />
                &bull; A module is published
                <br />
                &bull; The session time is updated
                <br />
                &bull; The session location is updated
                <br />
                &bull; An instructor is added or removed
                {userAuth.tempFeatureConfig.isIltLcIntegrationEnabled && (
                  <Fragment>
                    <br />
                    &bull; In-session activity is updated
                  </Fragment>
                )}
              </p>
            }
          >
            <Icon type="info2" className={'infoHover'} />
          </Tooltip>
        </StyledCheckboxContainer>
      </Row>
      <Row className={'info-container-row'}>
        {!sendInstructorMail && isCalendarAutoSyncEnabled && (
          <StyledAutoSyncMessageContainer>
            <span className={'infoContainerText'}>
              <Icon type={ICON_MAP.WARNING} className={'warningIcon'} />
              <span className="noteHeading">Please note: </span>
              <FormattedMessage {...INSTRUCTOR_CALENDAR_AUTO_SYNC_INFO} />
            </span>
          </StyledAutoSyncMessageContainer>
        )}
      </Row>

      <StyledInstructorsInfoWrapper>
        {instructors.map((instructor: any, index: number) => (
          <InstructorDetails
            key={index}
            instructor={instructor}
            index={index}
            updateInstructor={updateInstructor}
            removeInstructor={removeInstructor}
            getCohostCheckbox={getCohostCheckbox}
            isReadOnlyModeEnabled={isReadOnlyModeEnabled}
            form={form}
          />
        ))}
      </StyledInstructorsInfoWrapper>
    </StyledInstructorsSection>
  );
}

export default InstructorSection;
