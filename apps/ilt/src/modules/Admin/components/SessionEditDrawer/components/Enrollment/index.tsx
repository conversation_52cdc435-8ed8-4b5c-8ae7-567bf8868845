import { FormattedMessage } from 'react-intl';

import Icon from '@mindtickle/icon';

import { MESSAGES } from '~/modules/Admin/config/sessions.constants';
import { EVENT_ENROLLMENT_ENUM } from '~/modules/Admin/constants/events';

import EnrollmentFreezeSection from './components/EnrollmentFreezeSection';
import EnrollmentLimitSection from './components/EnrollmentLimitSection';
import {
  StyledEnrolmentTabContainer,
  StyledEventEnrollmentNote,
  StyledInfoContainer,
} from './styles';

import type { TEnrollment } from './typeDefs';

function Enrollment(props: TEnrollment) {
  const {
    session,
    updateSessionState,
    isReadOnlyModeEnabled,
    isPublishedOngoingPastSession,
    switchToEventDrawer,
    eventData,
  } = props;
  const isEventLevelEnrollmentEnabled = eventData?.enrollmentType === EVENT_ENROLLMENT_ENUM.EVENT;
  return (
    <StyledEnrolmentTabContainer>
      {isEventLevelEnrollmentEnabled && (
        <StyledInfoContainer>
          <Icon type="info2" />
          <StyledEventEnrollmentNote>
            <span className="noteHeading">Please note: </span>
            <FormattedMessage {...MESSAGES.INFO.EVENT_LEVEL_ENROLLMENT_ENABLED} />
            <span className="drawer-change-cta" onClick={switchToEventDrawer}>
              {' '}
              go to event settings
            </span>
          </StyledEventEnrollmentNote>
        </StyledInfoContainer>
      )}
      <EnrollmentFreezeSection
        session={session}
        updateSessionState={updateSessionState}
        isReadOnlyModeEnabled={isReadOnlyModeEnabled}
        isPublishedOngoingPastSession={isPublishedOngoingPastSession}
        isEventLevelEnrollmentEnabled={isEventLevelEnrollmentEnabled}
        event={eventData}
      />
      <EnrollmentLimitSection
        session={session}
        updateSessionState={updateSessionState}
        isReadOnlyModeEnabled={isReadOnlyModeEnabled}
        isEventLevelEnrollmentEnabled={isEventLevelEnrollmentEnabled}
        event={eventData}
      />
    </StyledEnrolmentTabContainer>
  );
}

export default Enrollment;
