import { Fragment } from 'react';

//components
import { FormattedMessage } from 'react-intl';

import Checkbox from '@mindtickle/checkbox';
import Form from '@mindtickle/form';
import Icon from '@mindtickle/icon';
import InputNumber from '@mindtickle/input-number';
import Tooltip from '@mindtickle/tooltip';

import { useILTAdminSnowplowTracker } from '~/modules/Admin/hooks/useILTAdminSnowplowTracker';
import { MIXPANEL_UI_EVENTS } from '~/modules/Admin/hooks/useILTAdminSnowplowTracker/constants';

import {
  CHECKIN_HOURS_ALLOWED_AFTER_SESSION,
  CHECKIN_HOURS_ALLOWED_BEFORE_SESSION,
  CHECKIN_UI_LABELS,
} from '../../constants';
import { minutesToHourMinutes } from '../../utils';

import { StyledCheckinTime } from './styles';

import type { TTimeSelector } from '../../../../typeDefs';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';

const TimeSelector = (props: TTimeSelector) => {
  const {
    checkinTimeBeforeSession,
    checkinTimeAfterSession,
    setCheckinTimeBeforeSession,
    setCheckinTimeAfterSession,
    checkInSettings,
    updateSessionState,
    isReadOnlyModeEnabled,
    form,
    sessionId,
  } = props;
  const {
    checkinTimeBeforeSession: { hasError: invalidTimeBeforeSession = false },
    checkinTimeAfterSession: { hasError: invalidTimeAfterSession = false },
  } = props;
  const tracker = useILTAdminSnowplowTracker();
  const { isSelfCheckInTimeRestricted = true } = checkInSettings;

  const toggleCheckinTimeRestriction = (e: CheckboxChangeEvent) => {
    let newCheckInSettings = Object.assign({}, checkInSettings);
    newCheckInSettings.isSelfCheckInTimeRestricted = e.target.checked;
    updateSessionState({ key: 'checkInSettings', value: newCheckInSettings });
    tracker.trackStructuredEvent({
      eventName: MIXPANEL_UI_EVENTS.MODULE_ILT_SESSIONS_CHECKIN_DURATION_CLICKED,
      ilt_session_id: sessionId,
    });
  };

  const updateHoursBeforeSession = (hours: number) => {
    if (isNaN(hours)) return;

    form.setFieldsValue({
      checkinHoursBeforeSession: hours,
    });
    setCheckinTimeBeforeSession({
      ...checkinTimeBeforeSession,
      hours,
    });
  };

  const updateAndValidateHoursBeforeSession = (e: any) => {
    const { minutes, hours } = checkinTimeBeforeSession;
    let hoursValue = parseInt(e.target.value) || 0;
    hoursValue = isNaN(hoursValue) ? hours : hoursValue;

    form.setFieldsValue({
      checkinHoursBeforeSession: hoursValue,
    });
    setCheckinTimeBeforeSession({
      ...checkinTimeBeforeSession,
      hours: hoursValue,
      hasError:
        hoursValue > CHECKIN_HOURS_ALLOWED_BEFORE_SESSION ||
        (hoursValue >= CHECKIN_HOURS_ALLOWED_BEFORE_SESSION && minutes > 0),
    });
  };

  const updateMinsBeforeSession = (minutes: number) => {
    if (isNaN(minutes)) return;

    form.setFieldsValue({
      checkinMinsBeforeSession: minutes,
    });
    setCheckinTimeBeforeSession({
      ...checkinTimeBeforeSession,
      minutes,
    });
  };

  const updateAndValidateMinutesBeforeSession = (e: any) => {
    const { hours: currentHours, minutes: currentMins } = checkinTimeBeforeSession;
    let minutesValue = parseInt(e.target.value);
    minutesValue = isNaN(minutesValue) ? currentMins : minutesValue;

    const { hours, minutes } = minutesToHourMinutes(minutesValue);
    const updatedHours = currentHours + hours;

    form.setFieldsValue({
      checkinHoursBeforeSession: updatedHours,
      checkinMinsBeforeSession: minutes,
    });
    setCheckinTimeBeforeSession({
      hours: updatedHours,
      minutes,
      hasError:
        updatedHours > CHECKIN_HOURS_ALLOWED_BEFORE_SESSION ||
        (updatedHours >= CHECKIN_HOURS_ALLOWED_BEFORE_SESSION && minutes > 0),
    });
  };

  const updateHoursAfterSession = (hours: number) => {
    if (isNaN(hours)) return;

    form.setFieldsValue({
      checkinHoursAfterSession: hours,
    });
    setCheckinTimeAfterSession({
      ...checkinTimeAfterSession,
      hours,
    });
  };

  const updateAndValidateHoursAfterSession = (e: any) => {
    const { minutes, hours } = checkinTimeAfterSession;
    let hoursValue = parseInt(e.target.value) || 0;
    hoursValue = isNaN(hoursValue) ? hours : hoursValue;

    form.setFieldsValue({
      checkinHoursAfterSession: hoursValue,
    });
    setCheckinTimeAfterSession({
      ...checkinTimeAfterSession,
      hours: hoursValue,
      hasError:
        hoursValue > CHECKIN_HOURS_ALLOWED_AFTER_SESSION ||
        (hoursValue >= CHECKIN_HOURS_ALLOWED_AFTER_SESSION && minutes > 0),
    });
  };

  const updateMinsAfterSession = (minutes: number) => {
    if (isNaN(minutes)) return;

    form.setFieldsValue({
      checkinMinsAfterSession: minutes,
    });
    setCheckinTimeAfterSession({
      ...checkinTimeAfterSession,
      minutes,
    });
  };

  const updateAndValidateMinutesAfterSession = (e: any) => {
    const { hours: currentHours, minutes: currentMins } = checkinTimeAfterSession;
    let minutesValue = parseInt(e.target.value);
    minutesValue = isNaN(minutesValue) ? currentMins : minutesValue;

    const { hours, minutes } = minutesToHourMinutes(minutesValue);
    const updatedHours = currentHours + hours;

    form.setFieldsValue({
      checkinHoursAfterSession: updatedHours,
      checkinMinsAfterSession: minutes,
    });
    setCheckinTimeAfterSession({
      hours: updatedHours,
      minutes,
      hasError:
        updatedHours > CHECKIN_HOURS_ALLOWED_AFTER_SESSION ||
        (updatedHours >= CHECKIN_HOURS_ALLOWED_AFTER_SESSION && minutes > 0),
    });
  };

  const formatValue = (value: string) => {
    if (value !== '') {
      return Number(value);
    } else {
      return 0;
    }
  };

  return (
    <Fragment>
      <div>
        <Checkbox
          onChange={toggleCheckinTimeRestriction}
          checked={isSelfCheckInTimeRestricted}
          className="checkin-time-checkbox"
          disabled={isReadOnlyModeEnabled}
        >
          <FormattedMessage {...CHECKIN_UI_LABELS.CHECKIN_TIME_DURATION_LABEL} />
        </Checkbox>
        <Tooltip title={<FormattedMessage {...CHECKIN_UI_LABELS.CHECKIN_TIME_DURATION_TOOLTIP} />}>
          <Icon type="info2" />
        </Tooltip>
      </div>
      {isSelfCheckInTimeRestricted && (
        <Fragment>
          {isReadOnlyModeEnabled ? (
            <>
              <StyledCheckinTime>
                {checkinTimeBeforeSession.hours + ' Hr ' + checkinTimeBeforeSession.minutes + ' '}
                <FormattedMessage {...CHECKIN_UI_LABELS.CHECKIN_TIME_BEFORE_SESSION} />
              </StyledCheckinTime>
              <StyledCheckinTime>
                {checkinTimeAfterSession.hours + ' Hr ' + checkinTimeAfterSession.minutes + ' '}
                <FormattedMessage {...CHECKIN_UI_LABELS.CHECKIN_TIME_AFTER_SESSION} />
              </StyledCheckinTime>
            </>
          ) : (
            <>
              <Form.Item
                name={'checkinTimeBeforeSession'}
                validateStatus={invalidTimeBeforeSession ? 'error' : null}
                help={
                  invalidTimeBeforeSession ? (
                    <FormattedMessage {...CHECKIN_UI_LABELS.CHECKIN_MAX_TIME_BEFORE_SESSION} />
                  ) : null
                }
                rules={[
                  {
                    validator: async (_: any, value: any) => {
                      if (invalidTimeBeforeSession) {
                        return Promise.reject();
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
                className={invalidTimeBeforeSession ? 'has-error' : 'no-error'}
              >
                <div className="checkin-time-pref-form">
                  <Form.Item
                    name={'checkinHoursBeforeSession'}
                    validateStatus={invalidTimeBeforeSession ? 'error' : null}
                  >
                    <InputNumber
                      name={'checkinHoursBeforeSession'}
                      min={0}
                      type="number"
                      defaultValue={checkinTimeBeforeSession.hours}
                      value={checkinTimeBeforeSession.hours}
                      wrapperClassName="checkin-time-input"
                      onChange={updateHoursBeforeSession}
                      onBlur={updateAndValidateHoursBeforeSession}
                      precision={0}
                      formatter={formatValue}
                    />
                  </Form.Item>
                  Hr
                  <Form.Item
                    name={'checkinMinsBeforeSession'}
                    validateStatus={invalidTimeBeforeSession ? 'error' : null}
                    className={'checkin-mins-before-session'}
                  >
                    <InputNumber
                      name={'checkinMinsBeforeSession'}
                      min={0}
                      type="number"
                      defaultValue={checkinTimeBeforeSession.minutes}
                      value={checkinTimeBeforeSession.minutes}
                      wrapperClassName="checkin-time-input"
                      onChange={updateMinsBeforeSession}
                      onBlur={updateAndValidateMinutesBeforeSession}
                      precision={0}
                      formatter={formatValue}
                    />
                  </Form.Item>
                  <FormattedMessage {...CHECKIN_UI_LABELS.CHECKIN_TIME_BEFORE_SESSION} />
                </div>
              </Form.Item>

              <Form.Item
                name={'checkinTimeAfterSession'}
                validateStatus={invalidTimeAfterSession ? 'error' : null}
                help={
                  invalidTimeAfterSession ? (
                    <FormattedMessage {...CHECKIN_UI_LABELS.CHECKIN_MAX_TIME_AFTER_SESSION} />
                  ) : null
                }
                rules={[
                  {
                    validator: async (_: any, value: any) => {
                      if (invalidTimeAfterSession) {
                        return Promise.reject();
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
                className={invalidTimeAfterSession ? 'has-error' : 'no-error'}
              >
                <div className="checkin-time-pref-form">
                  <Form.Item
                    name={'checkinHoursAfterSession'}
                    validateStatus={invalidTimeAfterSession ? 'error' : null}
                  >
                    <InputNumber
                      name={'checkinHoursAfterSession'}
                      min={0}
                      type="number"
                      defaultValue={checkinTimeAfterSession.hours}
                      value={checkinTimeAfterSession.hours}
                      wrapperClassName="checkin-time-input"
                      onChange={updateHoursAfterSession}
                      onBlur={updateAndValidateHoursAfterSession}
                      precision={0}
                      formatter={formatValue}
                    />
                  </Form.Item>
                  Hr
                  <Form.Item
                    name={'checkinMinsAfterSession'}
                    validateStatus={invalidTimeAfterSession ? 'error' : null}
                    className={'checkin-mins-after-session'}
                  >
                    <InputNumber
                      name={'checkinMinsAfterSession'}
                      min={0}
                      type="number"
                      defaultValue={checkinTimeAfterSession.minutes}
                      value={checkinTimeAfterSession.minutes}
                      wrapperClassName="checkin-time-input"
                      onChange={updateMinsAfterSession}
                      onBlur={updateAndValidateMinutesAfterSession}
                      precision={0}
                      formatter={formatValue}
                    />
                  </Form.Item>
                  <FormattedMessage {...CHECKIN_UI_LABELS.CHECKIN_TIME_AFTER_SESSION} />
                </div>
              </Form.Item>
            </>
          )}
        </Fragment>
      )}
    </Fragment>
  );
};

export default TimeSelector;
