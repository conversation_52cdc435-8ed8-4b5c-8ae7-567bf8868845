import { useState, useEffect } from 'react';

import moment from 'moment';
import { FormattedMessage } from 'react-intl';

import Divider from '@mindtickle/divider';
import { Title } from '@mindtickle/typography';

import { getWebinarMeetingSettingsKeyByLocationType } from '~/modules/Admin/utils/sessionEdit';
import { sessionsViewPageURL } from '~/utils';
import { SCROLL_TO_DATE_QUERY_PARAM } from '~/utils/constants';

import {
  WEBINAR_MEETING_TYPES,
  DEFAULT_WEBINAR_SETTINGS,
  ZOOM_MEETING_SETTING_KEY,
  WEBEX_MEETING_SETTING_KEY,
  SESSION_TYPES,
  LOCATION_TYPE,
  LOCATION_SESSION_TYPE_MAP,
  MS_TEAMS_MEETING_SETTING_KEY,
} from '../../../../config/sessions.constants';

import SessionAttachments from './components/SessionAttachments';
import SessionDescription from './components/SessionDescription';
import SessionLocation from './components/SessionLocation';
import SessionName from './components/SessionName';
import SessionScore from './components/SessionScore';
import SessionTime from './components/SessionTime';
import { LOCATION_TYPE_ACTION } from './constants';
import messages from './messages';
import { StyledHeader, StyledTextButton } from './styles';
import {
  StyledSessionDateLocationWrapper,
  StyledSessionDetailWrapper,
  StyledSessionDetails,
} from './styles';
import { modifyMeetingStatusTrack } from './utils';

import type { TSessionDetails } from './typeDefs';
import type { TSessionObject } from '../../typeDefs';

function SessionDetails(props: TSessionDetails) {
  const {
    session,
    updateSessionState,
    changeTimezoneListLoadingState,
    setSession,
    isPublishedOngoingPastSession,
    isReadOnlyModeEnabled,
    form,
    eventData,
    switchToEventDrawer,
    setIsSessionTimeModified,
    isSessionTimeModified,
  } = props;
  const [showDescriptionAfterMount, setShowDescriptionAfterMount] = useState(false);

  const goToSessionView = () => {
    window.open(
      window.location.origin +
        sessionsViewPageURL +
        `?${SCROLL_TO_DATE_QUERY_PARAM}=${session.startTime}`,
      '_blank'
    );
  };

  const handleLocationTypeChange = (action: string, value: any, extraValue: any) => {
    const { addedClearedMeetingData } = props;
    // eslint-disable-next-line max-statements
    setSession(prevState => {
      let session: TSessionObject = Object.assign({}, prevState);
      const { locationType: prevLocationType } = session;
      // clear data related to previous selection
      if (LOCATION_SESSION_TYPE_MAP[prevLocationType] === SESSION_TYPES.WEBINAR.value) {
        if (prevLocationType === LOCATION_TYPE.LINK) {
          if (session[WEBEX_MEETING_SETTING_KEY]) {
            session[WEBEX_MEETING_SETTING_KEY] = undefined;
          }
          if (session[ZOOM_MEETING_SETTING_KEY]) {
            session[ZOOM_MEETING_SETTING_KEY] = undefined;
          }
          if (session[MS_TEAMS_MEETING_SETTING_KEY]) {
            session[MS_TEAMS_MEETING_SETTING_KEY] = undefined;
          }
          session.webAutoAttendanceSettings = DEFAULT_WEBINAR_SETTINGS;
        } else if (WEBINAR_MEETING_TYPES.includes(prevLocationType)) {
          const meetingSettingsKey = getWebinarMeetingSettingsKeyByLocationType(prevLocationType);
          // if anything changed for this, dont' forget to revisit delete meeting func
          // To handle meeting removed in edit mode, this may create muliple entry of same meeting in the array if meeting was created and cleared
          addedClearedMeetingData.current.push({
            locationType: prevLocationType,
            location: session.location,
            meetingInfo: session[meetingSettingsKey],
          });
          session.webAutoAttendanceSettings = DEFAULT_WEBINAR_SETTINGS;
          session[meetingSettingsKey] = undefined;
          session.location = '';
        }
      }

      if (action === LOCATION_TYPE_ACTION.CHANGE) {
        session.locationType = value;
        session.type = LOCATION_SESSION_TYPE_MAP[value];
        // assign new data related to selection if it is be done along with location change
        if (extraValue) {
          const { webAutoAttendanceSettings, location: locationUrl, meetingSettings } = extraValue;
          if (WEBINAR_MEETING_TYPES.includes(value)) {
            const meetingSettingsKey = getWebinarMeetingSettingsKeyByLocationType(value);

            session.webAutoAttendanceSettings = webAutoAttendanceSettings;
            session[meetingSettingsKey] = meetingSettings;
            session.location = locationUrl;
            // if anything changed for this, dont' forget to revisit delete meeting func
            addedClearedMeetingData.current.push({
              locationType: value,
              location: session.location,
              meetingInfo: session[meetingSettingsKey],
            });
          }
        }
      } else if (action === LOCATION_TYPE_ACTION.CLEAR) {
        session.locationType = LOCATION_TYPE.FACE_TO_FACE;
        session.type = LOCATION_SESSION_TYPE_MAP[session.locationType];
        session.location = '';
      }

      return session;
    });
  };

  const handleWebexMeetingStatus = (operation: string, value?: any) => {
    const { webexMeetingStatusTrack: meetingStatusTrack, setWebexMeetingStatusTrack } = props;
    setWebexMeetingStatusTrack(modifyMeetingStatusTrack(meetingStatusTrack, operation, value));
  };

  const handleZoomMeetingStatus = (operation: string, value: any) => {
    const { zoomMeetingStatusTrack: meetingStatusTrack, setZoomMeetingStatusTrack } = props;
    setZoomMeetingStatusTrack(modifyMeetingStatusTrack(meetingStatusTrack, operation, value));
  };

  const handleMSTeamsMeetingStatus = (operation: string, value: any) => {
    const { msteamsMeetingStatusTrack: meetingStatusTrack, setMSTeamsMeetingStatusTrack } = props;
    setMSTeamsMeetingStatusTrack(modifyMeetingStatusTrack(meetingStatusTrack, operation, value));
  };

  const renderLocation = () => {
    const {
      session,
      updateSessionState,
      webexMeetingStatusTrack,
      zoomMeetingStatusTrack,
      msteamsMeetingStatusTrack,
      moduleDetails,
    } = props;
    const { mode, integrations, userAuth } = props;

    return (
      <SessionLocation
        mode={mode}
        updateSessionState={updateSessionState}
        onLocationTypeChange={handleLocationTypeChange}
        updateWebexMeetingStatus={handleWebexMeetingStatus}
        updateZoomMeetingStatus={handleZoomMeetingStatus}
        updateMSTeamsMeetingStatus={handleMSTeamsMeetingStatus}
        webexMeetingStatusTrack={webexMeetingStatusTrack}
        zoomMeetingStatusTrack={zoomMeetingStatusTrack}
        msteamsMeetingStatusTrack={msteamsMeetingStatusTrack}
        session={session}
        integrations={integrations}
        isOngoingOrPastSession={isPublishedOngoingPastSession}
        userAuth={userAuth}
        isReadOnlyModeEnabled={isReadOnlyModeEnabled}
        moduleDetails={moduleDetails}
      />
    );
  };

  useEffect(() => {
    setShowDescriptionAfterMount(true);
  }, []);

  useEffect(() => {
    form.setFieldsValue({
      attachments: session?.attachments,
      sessionStartDate: moment(session.startTime),
      sessionEndDate: moment(session.endTime),
      sessionStartTime: moment(session.startTime),
      sessionEndTime: moment(session.endTime),
      // enrollmentFreezeDate: moment(session.enrollmentFreezeDate),
      // enrollmentFreezeTime: moment(session.enrollmentFreezeDate),
    });
  }, [session, form]);

  return (
    <StyledSessionDetails>
      <Title level={3}>Enter session details</Title>
      <StyledSessionDetailWrapper>
        <SessionName
          name={session.name}
          locationType={session.locationType}
          updateSessionState={updateSessionState}
          isPublishedOngoingPastSession={isPublishedOngoingPastSession}
          isReadOnlyModeEnabled={isReadOnlyModeEnabled}
        />
        {showDescriptionAfterMount && (
          <SessionDescription
            description={session.description}
            updateSessionState={updateSessionState}
            isReadOnlyModeEnabled={isReadOnlyModeEnabled}
          />
        )}
        <SessionAttachments
          attachments={session.attachments}
          updateSessionState={updateSessionState}
          isReadOnlyModeEnabled={isReadOnlyModeEnabled}
        />
        <SessionScore
          maxScore={session.maxScore}
          updateSessionState={updateSessionState}
          isReadOnlyModeEnabled={isReadOnlyModeEnabled}
        />
      </StyledSessionDetailWrapper>
      <Divider className="session-details-divider" />
      <StyledHeader>
        <Title level={3} className="date-location-title">
          Date and location
        </Title>
        {!isReadOnlyModeEnabled && (
          <StyledTextButton onClick={goToSessionView}>
            <FormattedMessage {...messages.VIEW_ALL_SESSIONS} />
          </StyledTextButton>
        )}
      </StyledHeader>
      <StyledSessionDateLocationWrapper>
        <SessionTime
          session={session}
          webexMeetingStatusTrack={props.webexMeetingStatusTrack}
          zoomMeetingStatusTrack={props.zoomMeetingStatusTrack}
          msteamsMeetingStatusTrack={props.msteamsMeetingStatusTrack}
          updateSessionState={updateSessionState}
          changeTimezoneListLoadingState={changeTimezoneListLoadingState}
          isReadOnlyModeEnabled={isReadOnlyModeEnabled}
          isPublishedOngoingPastSession={isPublishedOngoingPastSession}
          eventData={eventData}
          switchToEventDrawer={switchToEventDrawer}
          setIsSessionTimeModified={setIsSessionTimeModified}
          isSessionTimeModified={isSessionTimeModified}
        />
        {renderLocation()}
      </StyledSessionDateLocationWrapper>
    </StyledSessionDetails>
  );
}

export default SessionDetails;
