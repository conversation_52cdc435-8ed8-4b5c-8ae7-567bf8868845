import { defineMessages } from 'react-intl';
export default defineMessages({
  webinarLinkHeading: `{sourceLabel} meeting link`,
  meetingNumber: 'Meeting number',
  password: 'Password',
  hostKey: 'Host key',
  host: 'Host',
  coHost: 'Co-host',
  addCohost: 'Add co-host',
  maxCohostLimitReached: 'Maximum number of co-hosts have been added for this meeting.',
  copyMeetingInfoToast: 'Meeting information copied to clipboard',
  copyMeetingInfo: 'Copy meeting information',
});
