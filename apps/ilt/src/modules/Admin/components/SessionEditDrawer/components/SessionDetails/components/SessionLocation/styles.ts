import styled from 'styled-components';

import Form from '@mindtickle/form';
import Icon from '@mindtickle/icon';
import { tokens } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS } from '~/config/constants';

export const StyledIcon = styled(Icon)`
  font-size: 7px;
`;

export const StyledConfirmModalWrapper = styled.div`
  .${THEME_PREFIX_CLS}-modal .${THEME_PREFIX_CLS}-modal-content .${THEME_PREFIX_CLS}-modal-footer {
    margin-top: 10px;
    background: none;
    border-top-width: 0;
  }
  .${THEME_PREFIX_CLS}-modal
    .${THEME_PREFIX_CLS}-modal-footer
    .${THEME_PREFIX_CLS}-btn.cancelButton {
    color: ${tokens.textTokens.COLOR_TEXT_SECONDARY};
    border-width: 0;
  }
`;

export const StyledSessionLocationWrapper = styled.div`
  &.label-custom-margin {
    & label {
      color: ${tokens.deprecatedTokens.COLOR_DEPRECATED_NAVBAR2};
      margin-bottom: 2px;
      line-height: 20px;
    }
  }
  &.common-margin-top {
    margin-top: 16px;
    line-height: 24px;
    color: ${tokens.textTokens.COLOR_TEXT_DEFAULT};
  }
  & .tooltipInfo {
    z-index: 999999 !important;
    cursor: pointer;
    max-width: 340px;
  }
  & .classroom-location-wrapper {
    width: 100%;
    margin-bottom: 10px;
  }
`;

export const StyledEdit = styled.div`
  display: flex;
  align-items: center;
  margin-top: 8px;
  .editTextFlexItem {
    margin-left: 8px;
    &:first-child {
      flex-grow: 0.6;
      margin-left: 0;
    }

    &.name-input {
      width: 100%;
    }
  }
  .editTextStaticFlexItem {
    margin-left: 13px;
    &:first-child {
      margin-left: 0;
      color: ${tokens.textTokens.COLOR_TEXT_DEFAULT};
      font-weight: 600;
    }
  }
  .cursor-pointer {
    cursor: pointer;
  }
`;

const errorColor = `${tokens.textTokens.COLOR_TEXT_DANGER}`;

export const StyledFormItem = styled(Form.Item)`
  &&&.${THEME_PREFIX_CLS}-form-item {
    margin-bottom: 0;
  }
  .${THEME_PREFIX_CLS}-form-explain {
    font-size: 12px;
  }
  .has-error {
    .${THEME_PREFIX_CLS}-form-explain {
      color: ${errorColor};
      font-size: 12px;
    }
    .${THEME_PREFIX_CLS}-input {
      border-color: ${errorColor};
      &:hover {
        border-color: ${errorColor};
      }
    }
  }
`;

export const StyledFormItemCustomMargin = styled(StyledFormItem)`
  margin-top: 8px !important;
`;

export const StyledFormItemWrapper = styled(StyledFormItem)`
  margin-top: 16px !important;
`;

export const StyledActionArea = styled.div`
  margin-top: 8px;
  & .urlLoader {
    display: inline-flex;
    height: 20px;
  }
  & .loading {
    color: ${tokens.deprecatedTokens.COLOR_DEPRECATED_NAVBAR2};
  }

  & .warning {
    color: ${tokens.textTokens.COLOR_TEXT_WARNING};
  }

  & .success {
    color: ${tokens.textTokens.COLOR_TEXT_SUCCESS};
  }
  & .retryButton {
    color: ${tokens.textTokens.COLOR_TEXT_ACCENT};
  }
  & .success,
  .warning {
    font-weight: 600;
  }
`;
