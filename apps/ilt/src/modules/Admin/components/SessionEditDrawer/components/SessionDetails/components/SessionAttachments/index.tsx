import _findIndex from 'lodash/findIndex';

import Form from '@mindtickle/form';

import Attachments from '~/modules/Admin/components/Attachment';

import { TSessionAttachment } from '../../typeDefs';

import { StyledAttachmentsView } from './styles';

const SessionAttachments = ({
  attachments,
  updateSessionState,
  isReadOnlyModeEnabled,
}: TSessionAttachment) => {
  let key = 'attachments';

  const addAttachment = (value: any) => {
    let newAttachments = [...attachments, value];
    updateSessionState({ key, value: newAttachments });
  };

  const removeAttachment = (value: any) => {
    const attachmentIndex = _findIndex(
      attachments,
      (attachment: any) => attachment.id === value.id
    );

    let newAttachments = attachments.filter((attachment: any, i: number) => i !== attachmentIndex);
    updateSessionState({ key, value: newAttachments });
  };

  const attachmentRenderer = (
    <Attachments
      attachments={attachments}
      add={addAttachment}
      remove={removeAttachment}
      isReadOnly={isReadOnlyModeEnabled}
    />
  );

  return isReadOnlyModeEnabled ? (
    <StyledAttachmentsView>{attachmentRenderer}</StyledAttachmentsView>
  ) : (
    <Form.Item name="attachments">{attachmentRenderer}</Form.Item>
  );
};

export default SessionAttachments;
