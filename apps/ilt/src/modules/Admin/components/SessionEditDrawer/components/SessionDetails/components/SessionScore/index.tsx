import { FormattedMessage } from 'react-intl';

import Form from '@mindtickle/form';
import Icon from '@mindtickle/icon';
import InputNumber from '@mindtickle/input-number';
import Tooltip from '@mindtickle/tooltip';

import { MESSAGES } from '~/modules/Admin/config/sessions.constants';

import { LabeledSessionInfo } from '../../../LabeledSessionInfo';

import { StyledScoreLabel, StyledSessionScore } from './styles';

import type { TSessionScore } from '../../typeDefs';

const SessionScoreLabel = () => (
  <StyledScoreLabel>
    <span>Score</span>
    <Tooltip
      overlayClassName={'tooltipInfo'}
      title={<FormattedMessage {...MESSAGES.INFO.MAX_SCORE} />}
      getPopupContainer={() => document.body}
    >
      <Icon type="info2" className={'infoHover'} />
    </Tooltip>
  </StyledScoreLabel>
);

const SessionScore = ({ maxScore, updateSessionState, isReadOnlyModeEnabled }: TSessionScore) => {
  const formatValue = (value: string) => {
    if (value !== '') {
      return Number(value);
    }
    return 0;
  };

  return (
    <StyledSessionScore>
      {isReadOnlyModeEnabled ? (
        <LabeledSessionInfo labelText={<SessionScoreLabel />} htmlFor="session-score-view">
          <div id="session-score-view">{maxScore}</div>
        </LabeledSessionInfo>
      ) : (
        <Form.Item
          label={<SessionScoreLabel />}
          labelCol={{ span: 24 }}
          wrapperCol={{ span: 24 }}
          name="maxScore"
          className="session-score"
        >
          <InputNumber
            name="maxScore"
            type="number"
            min={0}
            step={50}
            max={1000}
            formatter={formatValue}
            value={maxScore}
            defaultValue={maxScore !== 0 ? maxScore : ''}
            onChange={(value: number) =>
              updateSessionState({ key: 'maxScore', value: value ? value : 0 })
            }
            placeholder="Enter score"
            className="score-input"
            precision={0}
          />
        </Form.Item>
      )}
    </StyledSessionScore>
  );
};

export default SessionScore;
