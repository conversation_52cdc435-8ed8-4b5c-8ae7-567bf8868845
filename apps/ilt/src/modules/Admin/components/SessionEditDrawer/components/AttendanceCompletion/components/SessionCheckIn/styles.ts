import styled, { css } from 'styled-components';

import { tokens } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS } from '~/config/constants';
const CommonTextStyle = css`
  font-weight: 600;
  font-size: 14px;
  color: ${tokens.textTokens.COLOR_TEXT_DEFAULT};
`;

export const StyledInfoContainer = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 8px 16px;
  gap: 8px;
  width: fit-content;
  border-radius: 8px;
  background: ${tokens.bgTokens.COLOR_BG_DISABLED};
  margin-top: 8px;
`;

export const StyledErrorContainer = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 8px 16px;
  gap: 8px;
  width: fit-content;
  border-radius: 8px;
  background: ${tokens.bgTokens.COLOR_BG_DANGER};
  margin-top: 8px;
`;

export const StyledCheckInNote = styled.span`
  ${CommonTextStyle}
  margin-right: 10px;
  font-size: 12px;
  font-weight: 400;

  .noteHeading {
    font-weight: 600;
    font-size: 12px;
  }
`;

export const StyledErrorNote = styled.span`
  color: ${tokens.textTokens.COLOR_TEXT_DEFAULT};
  margin-right: 10px;
  font-size: 12px;
  font-weight: 400;

  .noteHeading {
    font-weight: 600;
    font-size: 12px;
  }
`;

export const StyledCheckInToggleText = styled.span<{ checked: boolean }>`
  ${CommonTextStyle}
  margin-right: 10px;
  color: ${props =>
    props.checked ? tokens.textTokens.COLOR_TEXT_DEFAULT : tokens.textTokens.COLOR_TEXT_TERTIARY};
`;

export const StyledCheckInContainer = styled.div`
  .icon-info2 {
    color: ${tokens.iconTokens.COLOR_ICON_DEFAULT};
  }

  .icon-Download {
    font-weight: 600;
  }

  .mt-8 {
    margin-top: 8px;
  }

  .mt-12 {
    margin-top: 12px;
  }

  .mt-20 {
    margin-top: 20px;
  }

  .mr-12 {
    margin-right: 12px;
  }

  .ml-8 {
    margin-left: 8px;
  }

  .checkin-prefs {
    padding-top: 16px;
    display: flex;
    flex-direction: column;
  }

  .checkin-form-container {
    width: calc(100% - 138px);
    display: inline-block;
    vertical-align: top;
    margin-top: 16px;
    margin-bottom: 2px;

    .checkin-time-preferences {
      .${THEME_PREFIX_CLS}-form-item {
        margin-bottom: 0;
      }
      .${THEME_PREFIX_CLS}-form-item.no-error {
        .${THEME_PREFIX_CLS}-form-item-explain-error {
          display: none;
        }
      }

      .checkin-time-pref-form {
        display: flex;
        align-items: baseline;
        padding-top: 16px;
        margin-bottom: 0;

        .error {
          color: ${tokens.textTokens.COLOR_TEXT_DANGER};
          padding: 0 0 0 8px;
        }

        .${THEME_PREFIX_CLS}-input-number {
          width: 42px;
          height: 32px;
          margin: 0px 8px;

          .${THEME_PREFIX_CLS}-input-number-handler-wrap {
            display: none;
          }

          .${THEME_PREFIX_CLS}-input-number-input {
            padding: 0 11px;
          }
        }

        .${THEME_PREFIX_CLS}-input-number:first-child {
          margin-left: 0;
        }

        .checkin-time-input {
          display: inline-block;

          .error {
            width: 200px;
          }
        }

        .checkin-mins-before-session,
        .checkin-mins-after-session {
          margin-left: 8px;
        }
      }
    }
  }

  .qr-container {
    width: 132px;
    display: inline-block;
    text-align: right;

    img {
      border: 1px solid ${tokens.borderTokens.COLOR_BORDER_DEFAULT};
      border-radius: 4px;
      height: 132px;
      width: 132px;
    }

    .checkin-code-title {
      color: ${tokens.textTokens.COLOR_TEXT_TERTIARY};
      letter-spacing: 0;
      line-height: 16px;
      text-align: center;
      padding-top: 4px;
    }

    .checkin-code {
      color: ${tokens.textTokens.COLOR_TEXT_DEFAULT};
      font-size: 14px;
      font-weight: 600;
      letter-spacing: 0;
      line-height: 20px;
      text-align: center;
    }

    .qr-code-actions {
      padding-top: 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .qr-copy-clipboard {
        margin: 0 8px 0 0;
        border-radius: 4px;
        padding: 7px;
        background-color: ${tokens.bgTokens.COLOR_BG_TERTIARY};

        .copy-icon {
          font-weight: 400;
          color: ${tokens.textTokens.COLOR_TEXT_ACCENT};
        }
      }

      .qr-action {
        margin: 0 8px;
        cursor: pointer;
        display: inline-block;
        color: ${tokens.textTokens.COLOR_TEXT_ACCENT};
        border-radius: 4px;
        padding: 8px;
        background-color: ${tokens.bgTokens.COLOR_BG_TERTIARY};

        &:last-of-type {
          margin-right: 0;
        }

        &:hover {
          background-color: ${tokens.bgTokens.COLOR_BG_ACCENT_SELECTED};
        }

        &.loading {
          vertical-align: bottom;
        }

        .spinner {
          width: 16px;
          height: 20px;

          div {
            height: 14px;
            width: 14px;
            margin: 0;
          }
        }

        &.disabled-icons {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }

      .qr-copy-clipboard,
      .qr-action,
      .qr-action-tooltip {
        width: 32px;
        height: 32px;
      }
    }

    .qr-shared {
      text-align: left;
      padding: 15px 0 0 12px;
      font-size: 12px;
      letter-spacing: 0;
      line-height: 16px;

      &.success {
        color: ${tokens.moduleTokens.COLOR_MODULE_COURSE};
      }

      &.error {
        color: ${tokens.textTokens.COLOR_TEXT_DANGER};
      }
    }
  }

  .checkin-toggles {
    padding-top: 40px;

    .toggle-checkin-switch {
      display: inline-block;
      margin-left: 12px;
    }
  }

  .${THEME_PREFIX_CLS}-switch {
    float: right;
  }
`;

export const StyledCheckinDetails = styled.div`
  opacity: 0;

  .checkin-details-container {
    border: solid 1px ${tokens.borderTokens.COLOR_BORDER_DEFAULT};
    width: 800px;
    text-align: center;
    border-radius: 8px;
    margin: 0 auto;

    .header {
      padding: 32px 64px;
      border-bottom: solid 1px ${tokens.borderTokens.COLOR_BORDER_DEFAULT};

      .session-title {
        font-size: 20px;
        font-weight: bold;
        padding-bottom: 16px;
      }

      .session-time {
        font-weight: bold;
      }

      .session-location {
        padding: 8px 0;
      }
    }
  }

  .timezone {
    padding: 0;
    font-weight: bold;
  }

  .body {
    padding: 32px;
    border-bottom: solid 1px ${tokens.borderTokens.COLOR_BORDER_DEFAULT};

    .qr-code-img {
      border: solid 1px ${tokens.borderTokens.COLOR_BORDER_DEFAULT};
      border-radius: 4px;
      height: 257px;
      width: 257px;
    }

    .checkin-code {
      padding: 8px;
      font-size: 20px;
      font-weight: bold;
      border: dashed 2px ${tokens.borderTokens.COLOR_BORDER_DEFAULT};
      width: 360px;
      border-radius: 4px;
      margin: 24px auto;
    }

    .qr-info-label {
      padding: 8px 24px 24px 24px;
      font-weight: bold;
    }

    .checkin-validity {
      font-weight: bold;
    }
  }

  .footer {
    color: ${tokens.textTokens.COLOR_TEXT_DISABLED};
    padding: 16px 8px;
    font-size: 12px;
  }
`;

export const StyledPopupForm = styled.div`
  .share-qr-email-input {
    width: 348px;
    margin-top: 16px;
    color: ${tokens.textTokens.COLOR_TEXT_DEFAULT};
    font-weight: 600;
    letter-spacing: 0;

    &.hasError {
      margin-bottom: 24px;
    }
  }

  .qr-share-subtitle {
    font-size: 12px;
    color: ${tokens.textTokens.COLOR_TEXT_TERTIARY};
  }

  .share-qr-actions {
    text-align: right;
    margin-top: 25px;
  }

  .${THEME_PREFIX_CLS}-form-item {
    margin-bottom: 0;
  }
`;

export const StyledInformCheckbox = styled.div`
  margin-top: 9px;
`;

export const StyledCheckinActionsWrapper = styled.div``;
