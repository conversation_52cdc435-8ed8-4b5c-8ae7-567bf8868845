import { renderHook, act } from '@testing-library/react-hooks';

import useWebinarAuthFlow from '../useAuthModel';

jest.mock('ui_shell/Auth', () => ({ useUserAuth: () => {} }), { virtual: true });
jest.mock(
  'ui_shell/GlobalConstants',
  () => ({
    LIFECYCLE_STAGES: {
      BUILD: 'build',
      SETTINGS: 'settings',
      PUBLISH: 'publish',
      INVITE: 'invite',
    },
  }),
  {
    virtual: true,
  }
);

describe('useAuthFlow', () => {
  it('should handle switchToHostEmail', () => {
    const { result } = renderHook(() =>
      useWebinarAuthFlow({
        hostEmail: '<EMAIL>',
        webinarSource: 'source',
        integrationSource: 'webex',
        meetingTitle: 'title',
        startTimeWithOffSet: 123456789,
        endTimeWithOffSet: 123456789,
        onSuccessfulMeetingCreation: () => {},
        setCreationInProgress: () => {},
        flow: 'create',
        meetingUrl: 'url',
        handleMeetingValidationSuccess: () => {},
        initialFlow: 'authenticate',
      })
    );

    act(() => {
      result.current.switchToHostEmail();
    });

    expect(result.current.state.flow).toBe('hostEmail');
  });

  it('should handle switchToAuthenticate', () => {
    const { result } = renderHook(() =>
      useWebinarAuthFlow({
        hostEmail: '<EMAIL>',
        webinarSource: 'source',
        integrationSource: 'webex',
        meetingTitle: 'title',
        startTimeWithOffSet: 123456789,
        endTimeWithOffSet: 123456789,
        onSuccessfulMeetingCreation: () => {},
        setCreationInProgress: () => {},
        flow: 'create',
        meetingUrl: 'url',
        handleMeetingValidationSuccess: () => {},
        initialFlow: 'authenticate',
      })
    );

    act(() => {
      result.current.switchToAuthenticate();
    });

    expect(result.current.state.flow).toBe('authenticate');
  });

  it('should handle addHostEmail', () => {
    const { result } = renderHook(() =>
      useWebinarAuthFlow({
        hostEmail: '<EMAIL>',
        webinarSource: 'source',
        integrationSource: 'webex',
        meetingTitle: 'title',
        startTimeWithOffSet: 123456789,
        endTimeWithOffSet: 123456789,
        onSuccessfulMeetingCreation: () => {},
        setCreationInProgress: () => {},
        flow: 'create',
        meetingUrl: 'url',
        handleMeetingValidationSuccess: () => {},
        initialFlow: 'authenticate',
      })
    );

    act(() => {
      result.current.addHostEmail('<EMAIL>');
    });

    expect(result.current.state.hostEmail).toBe('<EMAIL>');
  });

  it('should handle setAsyncStatus', () => {
    const { result } = renderHook(() =>
      useWebinarAuthFlow({
        hostEmail: '<EMAIL>',
        webinarSource: 'source',
        integrationSource: 'webex',
        meetingTitle: 'title',
        startTimeWithOffSet: 123456789,
        endTimeWithOffSet: 123456789,
        onSuccessfulMeetingCreation: () => {},
        setCreationInProgress: () => {},
        flow: 'create',
        meetingUrl: 'url',
        handleMeetingValidationSuccess: () => {},
        initialFlow: 'authenticate',
      })
    );

    act(() => {
      result.current.setAsyncStatus('loading');
    });

    expect(result.current.state.asyncStatus).toBe('loading');
  });

  it('should handle setSubmitDisabled', () => {
    const { result } = renderHook(() =>
      useWebinarAuthFlow({
        hostEmail: '<EMAIL>',
        webinarSource: 'source',
        integrationSource: 'webex',
        meetingTitle: 'title',
        startTimeWithOffSet: 123456789,
        endTimeWithOffSet: 123456789,
        onSuccessfulMeetingCreation: () => {},
        setCreationInProgress: () => {},
        flow: 'create',
        meetingUrl: 'url',
        handleMeetingValidationSuccess: () => {},
        initialFlow: 'authenticate',
      })
    );

    act(() => {
      result.current.setSubmitDisabled(true);
    });

    expect(result.current.state.isSubmitDisabled).toBe(true);
  });

  it('should handle setValidationError', () => {
    const { result } = renderHook(() =>
      useWebinarAuthFlow({
        hostEmail: '<EMAIL>',
        webinarSource: 'source',
        integrationSource: 'webex',
        meetingTitle: 'title',
        startTimeWithOffSet: 123456789,
        endTimeWithOffSet: 123456789,
        onSuccessfulMeetingCreation: () => {},
        setCreationInProgress: () => {},
        flow: 'create',
        meetingUrl: 'url',
        handleMeetingValidationSuccess: () => {},
        initialFlow: 'authenticate',
      })
    );

    act(() => {
      result.current.setValidationError('Error message');
    });

    expect(result.current.state.validationError).toBe('Error message');
  });

  it('should handle onPressEnter', () => {
    const { result } = renderHook(() =>
      useWebinarAuthFlow({
        hostEmail: '<EMAIL>',
        webinarSource: 'source',
        integrationSource: 'webex',
        meetingTitle: 'title',
        startTimeWithOffSet: 123456789,
        endTimeWithOffSet: 123456789,
        onSuccessfulMeetingCreation: () => {},
        setCreationInProgress: () => {},
        flow: 'create',
        meetingUrl: 'url',
        handleMeetingValidationSuccess: () => {},
        initialFlow: 'authenticate',
      })
    );

    act(() => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      result.current.onPressEnter(event as any);
    });

    expect(result.current.state.asyncStatus).toBe('loading');
    expect(result.current.state.isSubmitDisabled).toBe(false);
  });
});
