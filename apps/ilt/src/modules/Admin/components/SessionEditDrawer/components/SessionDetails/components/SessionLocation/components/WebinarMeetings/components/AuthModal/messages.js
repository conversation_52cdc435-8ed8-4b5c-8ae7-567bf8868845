import { defineMessages } from 'react-intl';

export default defineMessages({
  DISCARD_TEXT: 'Discard',
  AUTHENTICATE_ERROR: ' No authenticated {sourceLabel} account found for',
  INPUT_LABEL: 'Add email ID of an authenticated host for this {sourceLabel} meeting',
  SAVE: 'Save',
  ALREADY_HAVE_ACCOUNT: 'Already have an authenticated account? ',
  IS_NOT_HOST: '{hostEmail} is not the host?',
  //
  ASSIGN_HOST: 'Assign host for {sourceLabel} meeting',
  CREATE_MEETING: 'Create meeting',
  AUTHENTICATE_ACCOUNT: 'Authenticate your {sourceLabel} account',
  AUTHENTICATE_CTA: 'Authenticate',
  ENTER_HOST_EMAIL: 'Enter host email ID',
});
