import Form from '@mindtickle/form';
import Input from '@mindtickle/input';

import { MAX_SESSION_NAME_LENGTH } from '~/modules/Admin/config/sessions.constants';
import { WEBINAR_MEETING_TYPES } from '~/modules/Admin/constants/sessions';

import { LabeledSessionInfo } from '../../../LabeledSessionInfo';

import type { TSessionName } from '../../typeDefs';

const SessionName = ({
  name,
  locationType,
  isPublishedOngoingPastSession,
  updateSessionState,
  isReadOnlyModeEnabled,
}: TSessionName) => {
  let disabled = false;
  if (WEBINAR_MEETING_TYPES.includes(locationType)) {
    disabled = isPublishedOngoingPastSession;
  }

  const childProps = {
    name: 'title',
    value: name,
    maxLength: MAX_SESSION_NAME_LENGTH,
    onChange: (event: React.FormEvent<HTMLInputElement>) =>
      updateSessionState({
        key: 'name',
        value: (event.target as HTMLInputElement).value.substring(0, MAX_SESSION_NAME_LENGTH),
      }),
    disabled: disabled,
    showMaxLengthInAddon: true,
  };
  return (
    <>
      {isReadOnlyModeEnabled ? (
        <LabeledSessionInfo labelText="Session name" htmlFor="session-name-view">
          <div id="session-name-view">{name}</div>
        </LabeledSessionInfo>
      ) : (
        <Form.Item
          label="Session name*"
          labelCol={{ span: 24 }}
          wrapperCol={{ span: 24 }}
          name="sessionName"
          rules={[{ required: true, message: 'Please enter a session name' }]}
        >
          <Input placeholder="Enter session name" {...childProps} />
        </Form.Item>
      )}
    </>
  );
};

export default SessionName;
