import { useRef, useState, useMemo } from 'react';

import debounce from 'lodash/debounce';

import { DEFAULT_WEBINAR_SETTINGS } from '~/modules/Admin/config/sessions.constants';
import { checkUrlEligibilityForIntegrations, validateWebinarURL } from '~/modules/Admin/utils';
import { checkIntegrationUserAuthEnabled } from '~/modules/Admin/utils/sessionEdit';

import type { TUseUrlValidation, TValidateURLFunction } from '../../../typeDefs';

const useUrlValidation = ({
  integrationsInfo,
  onValidationSuccess,
  onValidationNoSuccess,
  debounceTimeout,
}: TUseUrlValidation) => {
  const [validationInProgress, setValidationInProgress] = useState<boolean>(false);
  const [verificationSource, setVerificationSource] = useState<string | undefined>('');
  const abortController = useRef<AbortController | null>(null);

  const {
    isIntegrationAvailable,
    integrationDataMap,
  }: { isIntegrationAvailable: boolean; integrationDataMap: any } = integrationsInfo;

  const validateUrl = useMemo(() => {
    let _validateURL: TValidateURLFunction = async ({
      value: location,
      password,
      userEmail,
      signal,
    }: {
      value: string;
      password?: string;
      userEmail?: string;
      signal: AbortSignal;
    }) => {
      let validationData: any = DEFAULT_WEBINAR_SETTINGS;
      let canRetry: boolean = false;
      const { eligible, webinarSource, integrationSource } = checkUrlEligibilityForIntegrations({
        location,
        integrations: integrationDataMap,
      });
      if (eligible) {
        setValidationInProgress(true);
        setVerificationSource(integrationSource);
        const shouldUseHostEmail = checkIntegrationUserAuthEnabled(
          integrationDataMap,
          integrationSource || ''
        );
        let { canRetry: _canRetry, ...updatedState } = (await validateWebinarURL({
          location,
          webinarSource,
          password: shouldUseHostEmail ? undefined : password,
          signal,
          hostEmail: shouldUseHostEmail ? userEmail : undefined,
        })) as {
          isLinkValidated: boolean;
          lastValidityStatus: string;
          thresholdConfig: any;
          isAutoAttendanceEnabled: boolean;
          canRetry: boolean;
          webinarSource: string;
          errorCode?: string;
          data: any;
        };
        canRetry = _canRetry;
        validationData = updatedState;
      }
      if (!signal.aborted) {
        if (validationData.isLinkValidated) {
          const { data, ...restValidationData } = validationData;
          onValidationSuccess &&
            onValidationSuccess({
              validationData: restValidationData,
              data,
              canRetry,
            });
        } else {
          onValidationNoSuccess &&
            onValidationNoSuccess({
              validationData: validationData,
              canRetry,
            });
        }
        setValidationInProgress(false);
        setVerificationSource('');
      }
    };
    if (debounceTimeout) {
      _validateURL = debounce(_validateURL, debounceTimeout) as TValidateURLFunction;
    }

    return (
      value: string,
      { password, userEmail }: { password?: string; userEmail?: string } = {}
    ) => {
      if (isIntegrationAvailable) {
        if (abortController.current && !abortController.current.signal.aborted) {
          abortController.current.abort();
        }
        abortController.current = new AbortController();
        _validateURL({ value, password, signal: abortController.current.signal, userEmail });
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isIntegrationAvailable, integrationDataMap, onValidationSuccess]);

  return {
    validationInProgress,
    verificationSource,
    validateUrl,
    abortController,
  };
};

export default useUrlValidation;
