import { FunctionComponent } from 'react';

import FileActionsBar from './components/FileActionsBar';
import LCLinks from './components/LCLinks';

import type { TDisplayLiveChallenge } from '../../typeDefs';

const DisplayLiveChallenge: FunctionComponent<TDisplayLiveChallenge> = ({
  lcData,
  openModal,
  deleteLiveChallenge,
  hasSessionEnded,
  hasSessionStarted,
  isLcEdited,
  isSessionPublished,
  sessionId,
  domain,
  isReadOnlyModeEnabled,
}) => (
  <>
    <FileActionsBar
      openUpdateLCModal={openModal}
      deleteLiveChallenge={deleteLiveChallenge}
      hasSessionEnded={hasSessionEnded}
      hasSessionStarted={hasSessionStarted}
      isLcEdited={isLcEdited}
      isSessionPublished={isSessionPublished}
      sessionId={sessionId}
      isReadOnlyModeEnabled={isReadOnlyModeEnabled}
    />
    <LCLinks uniqueCode={lcData?.uniqueCode} domain={domain} />
  </>
);

export default DisplayLiveChallenge;
