import React from 'react';

import Divider from '@mindtickle/divider';
import Form from '@mindtickle/form';
import Grid from '@mindtickle/grid';
import Icon from '@mindtickle/icon';
import Input from '@mindtickle/input';

import { LabeledSessionInfo } from '../../../../../LabeledSessionInfo';
import { verticalDividerStyle } from '../../../../styles';

import {
  StyledFacilitatorHeader,
  StyledFacilitatorContent,
  StyledFacilitatorSection,
} from './styles';

import type { TFacilitatorDetails } from '../../../../typeDefs';

const { Row, Col } = Grid;

const FacilitatorDetails = (props: TFacilitatorDetails) => {
  const {
    getCohostCheckbox,
    facilitatorEmail,
    index,
    updateFacilitator,
    removeFacilitator,
    isReadOnlyModeEnabled,
    form,
  } = props;

  const cohostCheckbox = getCohostCheckbox(facilitatorEmail, `facilitatorAddAsCohost${index}`, {
    isFacilitator: true,
  });
  const showCohost = !!cohostCheckbox;

  React.useEffect(() => {
    form.setFieldsValue({
      [`facilitatorEmail${index}`]: facilitatorEmail,
    });
  }, [facilitatorEmail, form, index]);

  const renderFacilitatorEmail = () => {
    const childProps = {
      name: `facilitatorEmail${index}`,
      value: facilitatorEmail,
      onChange: (event: React.FormEvent<HTMLInputElement>) =>
        updateFacilitator('email', index, (event.target as HTMLInputElement).value),
    };

    return (
      <>
        {isReadOnlyModeEnabled ? (
          <LabeledSessionInfo labelText="Facilitator email id" htmlFor="facilitator-email-view">
            <div id="facilitator-email-view">{facilitatorEmail}</div>
          </LabeledSessionInfo>
        ) : (
          <Form.Item
            label="Facilitator email id*"
            labelCol={{ span: 24 }}
            wrapperCol={{ span: 24 }}
            name={`facilitatorEmail${index}`}
            rules={[
              { required: true, message: 'Please enter facilitator email' },
              { type: 'email', message: 'Please enter a valid email' },
            ]}
          >
            <Input placeholder="Facilitator email id" {...childProps} />
          </Form.Item>
        )}
      </>
    );
  };

  return (
    <StyledFacilitatorSection isReadOnlyModeEnabled={isReadOnlyModeEnabled}>
      <StyledFacilitatorHeader isReadOnlyModeEnabled={isReadOnlyModeEnabled}>
        <span>Facilitator {index + 1}</span>
        {cohostCheckbox}
        {!isReadOnlyModeEnabled && (
          <>
            <Divider style={verticalDividerStyle} type="vertical" />
            <Icon
              type="delete"
              className={'deleteFacilitatorIcon'}
              onClick={() => removeFacilitator(showCohost, facilitatorEmail, index)}
            />
          </>
        )}
      </StyledFacilitatorHeader>
      <StyledFacilitatorContent>
        <Row align="middle" className={'facilitatorInputRow'}>
          <Col span={12} className={'facilitatorInputCol'}>
            {renderFacilitatorEmail()}
          </Col>
        </Row>
      </StyledFacilitatorContent>
      {index > 0 && <Divider className="facilitator-section-divider" />}
    </StyledFacilitatorSection>
  );
};

export default FacilitatorDetails;
