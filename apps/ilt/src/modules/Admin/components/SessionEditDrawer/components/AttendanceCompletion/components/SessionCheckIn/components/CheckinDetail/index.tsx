import ReactDOM from 'react-dom';
import { FormattedMessage } from 'react-intl';

import { CHECKIN_UI_LABELS } from '../../constants';
import { StyledCheckinDetails } from '../../styles';
import { getSessionCheckinFormattedTime } from '../../utils';

import type { TCheckinDetails } from '../../../../typeDefs';

const CheckinDetails = (props: TCheckinDetails) => {
  const {
    name,
    startTime,
    endTime,
    location,
    checkInSettings: {
      secondsRelativeToSessionStopToAllowCheckIn = 1800,
      secondsRelativeToSessionStartToAllowCheckIn = -1800,
      selfCheckInCode = '',
      selfCheckInQrCodeUrl = '',
      isSelfCheckInTimeRestricted = true,
    } = {},
    timezone: { displayName: timezoneString = '' } = {},
  } = props.session;
  const { checkinDetailsRef } = props;

  const checkinFrom = !isSelfCheckInTimeRestricted
    ? startTime - 1800000
    : startTime + secondsRelativeToSessionStartToAllowCheckIn * 1000;
  const checkinTo = endTime + secondsRelativeToSessionStopToAllowCheckIn * 1000;
  const selfCheckInQrCodeUrlWithTimestamp = `${selfCheckInQrCodeUrl}&`; // intentionally kept '&' in the end to fool browser to download the file from s3 server instead of cache. removed timestamp as aws was throwing error.

  return ReactDOM.createPortal(
    <StyledCheckinDetails id="checkin-details-portal">
      <div className="checkin-details-container">
        <div id="checkin-details" ref={checkinDetailsRef}>
          <div className="header">
            <div className="session-title">{name}</div>
            <div className="session-time">{getSessionCheckinFormattedTime(startTime, endTime)}</div>
            <div className="timezone">{timezoneString}</div>
            <div className="session-location">{location}</div>
          </div>
          <div className="body">
            <div className="qr-info-label">
              <FormattedMessage {...CHECKIN_UI_LABELS.USE_CHECKIN_CODE_INFO} />
            </div>
            <img
              crossOrigin="anonymous"
              className="qr-code-img"
              alt="qr-code-img"
              src={selfCheckInQrCodeUrlWithTimestamp}
            />
            <div className="checkin-code">
              <FormattedMessage {...CHECKIN_UI_LABELS.CHECKIN_CODE_LABEL} />: {selfCheckInCode}
            </div>
            <div className="checkin-validity">
              <FormattedMessage {...CHECKIN_UI_LABELS.VALIDITY_PERIOD} />
              {getSessionCheckinFormattedTime(checkinFrom, checkinTo, isSelfCheckInTimeRestricted)}
            </div>
            <div className="timezone">{timezoneString}</div>
          </div>
          <div className="footer">
            <FormattedMessage {...CHECKIN_UI_LABELS.CHECKIN_DETAILS_EMAIL_INFO} />
            <a href={`mailto:${CHECKIN_UI_LABELS.CHECKIN_DETAILS_SUPPORT_EMAIL}`}>
              {CHECKIN_UI_LABELS.CHECKIN_DETAILS_SUPPORT_EMAIL}
            </a>
          </div>
        </div>
      </div>
    </StyledCheckinDetails>,
    document.getElementById('portals')!
  );
};

export default CheckinDetails;
