import EllipsisTooltip from '@mindtickle/ellipsis-tooltip';
import Pill, { PILL_TYPES } from '@mindtickle/pill';

import { MODES } from '~/modules/Admin/config/sessions.constants';
import { ILT_ENTITIES } from '~/modules/Admin/constants/module';

import {
  StyledSessionDrawerTitleContainer,
  StyledSessionEditTitle,
  StyledSessionViewTitle,
} from './styles';

import type { TSessionDrawerTitle } from '../../typeDefs';

const SessionDrawerTitle = (props: TSessionDrawerTitle) => {
  const {
    mode,
    sessionName,
    sessionId,
    triggerEditAction,
    isReadOnlyModeEnabled,
    eventName,
    eventId,
    showEditButton,
  } = props;
  const renderViewTitle = () => (
    <StyledSessionViewTitle>
      <StyledSessionEditTitle>
        <EllipsisTooltip
          wrapperClassName="view-session-title"
          placement="bottom"
          title={sessionName}
          showTooltipWhenEllipsis={true}
        >
          {sessionName}
        </EllipsisTooltip>{' '}
        {showEditButton && (
          <Pill
            className={'edit-session-button'}
            onClick={() =>
              triggerEditAction({
                id: sessionId!,
                entityType: eventId ? ILT_ENTITIES.SESSION_WITHIN_EVENT : ILT_ENTITIES.SESSION,
                parentId: eventId,
              })
            }
            disabled={false}
            type={PILL_TYPES.ACTION}
          >
            Edit session
          </Pill>
        )}
      </StyledSessionEditTitle>

      {eventName && (
        <EllipsisTooltip
          wrapperClassName="event-title"
          placement="bottom"
          title={'Event: ' + eventName}
          showTooltipWhenEllipsis={true}
        >
          {'Event: ' + eventName}
        </EllipsisTooltip>
      )}
    </StyledSessionViewTitle>
  );

  const renderEditTitle = () => (
    <StyledSessionViewTitle>
      <EllipsisTooltip
        wrapperClassName="edit-session-title"
        placement="bottom"
        title={sessionName}
        showTooltipWhenEllipsis={true}
      >
        {sessionName}
      </EllipsisTooltip>
      {eventName && (
        <EllipsisTooltip
          wrapperClassName="event-title"
          placement="bottom"
          title={'Event: ' + eventName}
          showTooltipWhenEllipsis={true}
        >
          {'Event: ' + eventName}
        </EllipsisTooltip>
      )}
    </StyledSessionViewTitle>
  );

  const renderCreateTitle = () => (
    <StyledSessionViewTitle>
      <div className="create-session-title">Create session</div>
      {eventName && (
        <EllipsisTooltip
          wrapperClassName="event-title"
          placement="bottom"
          title={'Event: ' + eventName}
          showTooltipWhenEllipsis={true}
        >
          {'Event: ' + eventName}
        </EllipsisTooltip>
      )}
    </StyledSessionViewTitle>
  );

  return (
    <StyledSessionDrawerTitleContainer>
      {mode === MODES.EDIT
        ? renderEditTitle()
        : isReadOnlyModeEnabled
        ? renderViewTitle()
        : renderCreateTitle()}
    </StyledSessionDrawerTitleContainer>
  );
};

export default SessionDrawerTitle;
