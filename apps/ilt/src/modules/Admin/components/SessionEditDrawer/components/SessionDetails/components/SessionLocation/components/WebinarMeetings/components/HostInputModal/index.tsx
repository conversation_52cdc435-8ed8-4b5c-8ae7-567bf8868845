import { useCallback, useRef, useState } from 'react';

import { injectIntl, InjectedIntlProps } from 'react-intl';

import Button from '@mindtickle/button';
import Input from '@mindtickle/input';
import Modal from '@mindtickle/modal';

import { MASKED_MODAL_INDEX } from '~/config/constants';
import { GENERIC_VALIDATION_MESSAGES } from '~/modules/Admin/config/sessions.constants';
import {
  required as checkRequired,
  email as checkValidEmail,
} from '~/modules/Admin/utils/formValidator';

import { StyledConfirmModalWrapper as StyledModalWrapper } from '../../../../styles';
import { StyledFormItemWrapper } from '../../../../styles';

import type { THostInputEmailModal } from '../../typeDefs';

const REQUIRED_EMAIL_MESSAGE = GENERIC_VALIDATION_MESSAGES.REQUIRED_EMAIL_ID;
const INVALID_EMAIL_MESSAGE = GENERIC_VALIDATION_MESSAGES.ENTER_VALID_EMAIL;

const HostInputEmailModal = ({
  onCancel,
  modalTitle,
  modalContent,
  onOk: onOkProp,
  okText,
  cancelText,
  hostEmail,
  setHostEmail,
  intl,
}: THostInputEmailModal & InjectedIntlProps) => {
  const modalContainerRef = useRef<HTMLDivElement>(null);
  const [validationMessage, setValidationMessage] = useState<
    string | ReactIntl.FormattedMessage.MessageDescriptor
  >('');
  const handleOnCancel = () => {
    onCancel();
  };
  const onOk = () => {
    let localValidation;
    if (checkRequired(hostEmail)) {
      localValidation = intl.formatMessage(REQUIRED_EMAIL_MESSAGE);
    } else if (checkValidEmail(hostEmail)) {
      localValidation = intl.formatMessage(INVALID_EMAIL_MESSAGE);
    }

    if (localValidation) {
      setValidationMessage(localValidation);
    } else {
      setValidationMessage('');
      onOkProp(hostEmail);
    }
  };
  const onEnter = (e: any) => {
    if (e) {
      e.preventDefault();
    }
    onOk();
  };
  const onHostChange = useCallback(
    (_: any, value: string) => {
      setHostEmail(value.trim());
    },
    [setHostEmail]
  );
  const ModalFooter = ({
    onCancel,
    onOk,
  }: {
    onOk: (value: string | undefined) => void;
    onCancel: () => void;
  }) => (
    <>
      <Button type="text" onClick={onCancel} className={'cancelButton'}>
        {cancelText}
      </Button>
      <Button type="primary" size="small" onClick={onOk}>
        {okText}
      </Button>
    </>
  );
  return (
    <StyledModalWrapper ref={modalContainerRef}>
      <Modal
        title={modalTitle}
        visible={true}
        onCancel={handleOnCancel}
        onOk={onOk}
        okText={okText}
        cancelText={cancelText}
        cancelButtonProps={{ className: 'cancelButton' }}
        centered={true}
        getPopupContainer={() => modalContainerRef.current}
        maskClosable={false}
        keyboard={false}
        type={'small'}
        footer={<ModalFooter onCancel={handleOnCancel} onOk={onOk} />}
        zIndex={MASKED_MODAL_INDEX}
      >
        <div>{modalContent}</div>
        <StyledFormItemWrapper
          validateStatus={validationMessage ? 'error' : 'success'}
          help={validationMessage}
        >
          <Input
            name="hostemail"
            value={hostEmail}
            onChange={onHostChange}
            placeholder={'<EMAIL>'}
            onPressEnter={onEnter}
          />
        </StyledFormItemWrapper>
      </Modal>
    </StyledModalWrapper>
  );
};

export default injectIntl(HostInputEmailModal);
