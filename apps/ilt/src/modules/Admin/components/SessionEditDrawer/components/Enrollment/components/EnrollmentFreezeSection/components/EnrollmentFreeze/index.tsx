import { useState, useEffect } from 'react';

import moment from 'moment';
import { FormattedMessage } from 'react-intl';

import {
  ENROLLMENT_FREEZE_TYPE_OPTIONS,
  ENROLLMENT_FREEZE_STATUSES,
  ENROLLMENT_LOCK_WARNING,
  MESSAGES,
} from '~/modules/Admin/config/sessions.constants';
import { getEnrollmentFreezeTitle, sessionTimeFormatter } from '~/modules/Admin/utils';
import { getCurrTimeWithOffset } from '~/utils/timezone';

import SelectAbsolute from '../SelectAbsolute';
import SelectRelative from '../SelectRelative';

import {
  EnrollmentFreezeContainer,
  EnrollmentFreezeDropdown,
  ErrorMessage,
  EnrollmentTypeContainer,
  DROPDOWN_STYLE,
} from './styles';

import type { TEnrollmentFreeze } from '../../../../typeDefs';

function EnrollmentFreeze({
  sessionStartTime,
  enrollmentFreezeStatus,
  updateSessionState,
  enrollmentFreezeTime,
  disableFreezeEdit,
  timezone,
  isReadOnlyModeEnabled,
}: TEnrollmentFreeze) {
  const [numDays, setNumDays] = useState(
    enrollmentFreezeTime !== 0
      ? moment(sessionStartTime).diff(moment(enrollmentFreezeTime), 'days')
      : 5
  );
  const [freezeDate, setFreezeDate] = useState(
    enrollmentFreezeTime !== 0 ? enrollmentFreezeTime : sessionStartTime
  );
  const [validateError, setValidateError] = useState(false);
  let currentTimeWithOffset = getCurrTimeWithOffset(timezone?.offset);
  const isRelativeFreezePast =
    currentTimeWithOffset - moment(sessionStartTime).subtract(numDays, 'days').valueOf() > 0;
  const isAbsoluteFreezePast = currentTimeWithOffset - moment(freezeDate).valueOf() > 0;

  useEffect(() => {
    switch (enrollmentFreezeStatus) {
      case ENROLLMENT_FREEZE_STATUSES.RELATIVE:
        setValidateError(false);
        if (numDays === 0) setNumDays(1);
        updateSessionState({
          key: 'enrollmentFreezeEpoch',
          value: moment(sessionStartTime).subtract(numDays, 'days').valueOf(),
        });
        break;
      case ENROLLMENT_FREEZE_STATUSES.ABSOLUTE:
        if (freezeDate > sessionStartTime) setValidateError(true);
        else setValidateError(false);
        updateSessionState({ key: 'enrollmentFreezeEpoch', value: moment(freezeDate).valueOf() });
        break;
      default:
        updateSessionState({ key: 'enrollmentFreezeEpoch', value: 0 });
        break;
    }
  }, [sessionStartTime, freezeDate, numDays, enrollmentFreezeStatus, updateSessionState]);

  return (
    <EnrollmentFreezeContainer>
      {isReadOnlyModeEnabled ? (
        <div className="enrollment-freeze-type-view">
          {getEnrollmentFreezeTitle(enrollmentFreezeStatus).displayValue}
        </div>
      ) : (
        <EnrollmentFreezeDropdown
          id="enrollmentFreezeType"
          options={ENROLLMENT_FREEZE_TYPE_OPTIONS}
          onSelect={(value: string) => updateSessionState({ key: 'enrollmentFreezeStatus', value })}
          value={getEnrollmentFreezeTitle(enrollmentFreezeStatus).value}
          validateError={validateError}
          disabled={disableFreezeEdit}
          dropdownMatchSelectWidth={false}
          dropdownStyle={DROPDOWN_STYLE}
        />
      )}

      <EnrollmentTypeContainer>
        {enrollmentFreezeStatus === ENROLLMENT_FREEZE_STATUSES.RELATIVE ? (
          <SelectRelative
            numDays={numDays}
            setNumDays={setNumDays}
            disableFreezeEdit={disableFreezeEdit}
            isFreezePast={isRelativeFreezePast}
            isReadModeEnabled={isReadOnlyModeEnabled}
          />
        ) : (
          <SelectAbsolute
            sessionStartTime={sessionStartTime}
            freezeDate={freezeDate}
            setFreezeDate={setFreezeDate}
            validateError={validateError}
            disableFreezeEdit={disableFreezeEdit}
            timezone={timezone}
            isFreezePast={isAbsoluteFreezePast}
            isReadModeEnabled={isReadOnlyModeEnabled}
          />
        )}
      </EnrollmentTypeContainer>

      {enrollmentFreezeStatus === ENROLLMENT_FREEZE_STATUSES.RELATIVE &&
        (!isReadOnlyModeEnabled && !disableFreezeEdit && isRelativeFreezePast ? (
          <ErrorMessage>
            <FormattedMessage {...MESSAGES.INFO.FREEZE_ENROLLMENT_PAST} />
          </ErrorMessage>
        ) : (
          <div className={'date-display'}>
            {disableFreezeEdit ? ENROLLMENT_LOCK_WARNING.PAST : ENROLLMENT_LOCK_WARNING.FUTURE}
            {sessionTimeFormatter(enrollmentFreezeTime) + timezone.shortDisplayName}
          </div>
        ))}

      {enrollmentFreezeStatus === ENROLLMENT_FREEZE_STATUSES.ABSOLUTE &&
        (validateError ? (
          <ErrorMessage className="absolute-select-error">
            <FormattedMessage {...MESSAGES.INFO.FREEZE_ENROLLMENT_WARNING} />
          </ErrorMessage>
        ) : !isReadOnlyModeEnabled && !disableFreezeEdit && isAbsoluteFreezePast ? (
          <ErrorMessage className="absolute-select-error">
            <FormattedMessage {...MESSAGES.INFO.FREEZE_ENROLLMENT_PAST} />
          </ErrorMessage>
        ) : null)}
    </EnrollmentFreezeContainer>
  );
}

export default EnrollmentFreeze;
