/* eslint-disable max-statements */
import React, { useCallback, useEffect, useMemo, useState, useRef } from 'react';

import classnames from 'classnames';
import _debounce from 'lodash/debounce';
import { injectIntl } from 'react-intl';
import { useUserAuth } from 'ui_shell/Auth';

import Button from '@mindtickle/button';
import Input from '@mindtickle/input';

import {
  SESSION_WEBINAR_URL_VALIDATION_STATUS_TYPES,
  SESSION_WEBINAR_STATUS_TYPES as WEBEX_STATUS_TYPES,
  LOCATION_TYPE_PLACEHOLDERS,
  WEBEX_MEETING_SETTING_KEY,
  MS_TEAMS_MEETING_SETTING_KEY,
  WEBINAR_SOURCE,
  INTEGRATION_SOURCE,
} from '~/modules/Admin/config/sessions.constants';
import { SESSION_WEBINAR_URL_LAST_VALIDATION_STATUS_ERROR_TYPES } from '~/modules/Admin/constants/sessions';
import { getValidationMessage } from '~/modules/Admin/utils';
import {
  checkIntegrationUserAuthEnabled,
  checkUrlValidationWebinarSourceHasSettingKey,
} from '~/modules/Admin/utils/sessionEdit';

import { WEBEX_PASSWORD_SECTION_TEXTS } from '../../../../constants';
import { StyledActionArea } from '../../styles';
import UrlVerificationLoader, { useUrlValidation } from '../UrlVerificationLoader';

import PasswordSection from './components/PasswordSection';
import UserAuthValidation from './components/UserAuthValidation';
import { StyledAutoAttendanceToggle, StyledSessionLocationWrapper } from './styles';

import type {
  TCheckToShowPasswordSection,
  TCheckToShowAuthSection,
  TWebinarLocation,
  TUpdateAutoAttendanceSettings,
} from '../../typeDefs';
const DEBOUNCED_TIMEOUT = 500;
const checkToShowPasswordSection = ({
  isIntegrationAvailable,
  webinarSource,
  lastValidityStatus,
  integrationDataMap,
}: TCheckToShowPasswordSection) =>
  isIntegrationAvailable &&
  webinarSource === WEBINAR_SOURCE.WEBEX &&
  lastValidityStatus === WEBEX_STATUS_TYPES.UNAUTHORIZED_ACTION &&
  !checkIntegrationUserAuthEnabled(integrationDataMap, INTEGRATION_SOURCE.webex);

const checkToShowAuthSection = ({
  isIntegrationAvailable,
  webinarSource,
  lastValidityStatus,
  integrationDataMap,
}: TCheckToShowAuthSection) =>
  isIntegrationAvailable &&
  SESSION_WEBINAR_URL_LAST_VALIDATION_STATUS_ERROR_TYPES.includes(lastValidityStatus) &&
  ((webinarSource === WEBINAR_SOURCE.WEBEX &&
    checkIntegrationUserAuthEnabled(integrationDataMap, INTEGRATION_SOURCE.webex)) ||
    (webinarSource === WEBINAR_SOURCE.MS_TEAMS &&
      checkIntegrationUserAuthEnabled(integrationDataMap, INTEGRATION_SOURCE.ms_teams)));

function WebinarLocation(props: TWebinarLocation) {
  const [canRetry, setCanRetry] = useState<boolean>();
  const [showPasswordSection, setShowPasswordSection] = useState(false);
  const [showErrorMessageWithCTA, setShowErrorMessageWithCTA] = useState(false);
  const isComponentUnmounted = useRef(false);
  const user = useUserAuth();
  const userEmailAddress = user.primaryEmail;
  const {
    integrationsInfo,
    updateSessionState,
    session,
    validateUrlOnMount,
    resetValidateUrlOnMount,
    isReadOnlyModeEnabled,
  } = props;
  const {
    webAutoAttendanceSettings,
    location = '',
    [WEBEX_MEETING_SETTING_KEY]: webexMeetingSettings,
    [MS_TEAMS_MEETING_SETTING_KEY]: msteamsMeetingSettings,
  } = session;
  const { password: webexMeetingPassword = '' } = webexMeetingSettings || {};
  const { isAutoAttendanceEnabled, isLinkValidated, lastValidityStatus, webinarSource } =
    webAutoAttendanceSettings;
  const { isIntegrationAvailable, integrationDataMap } = integrationsInfo;

  const isLocationEmpty = location.length === 0;
  const updateAutoAttendanceSettings = useCallback(
    ({
      validationData: newState,
      canRetry = false,
      data = undefined,
    }: TUpdateAutoAttendanceSettings) => {
      if (isComponentUnmounted.current === true) {
        return;
      }
      setCanRetry(canRetry);
      updateSessionState({
        key: 'webAutoAttendanceSettings',
        value: {
          ...webAutoAttendanceSettings,
          ...newState,
        },
      });
      const webinarSettingsKey = checkUrlValidationWebinarSourceHasSettingKey(
        newState.webinarSource
      );
      if (newState.isLinkValidated && webinarSettingsKey && data) {
        updateSessionState({
          key: webinarSettingsKey,
          value: data,
        });
      }
      setShowPasswordSection(
        checkToShowPasswordSection({
          isIntegrationAvailable,
          webinarSource: newState.webinarSource,
          lastValidityStatus: newState.lastValidityStatus,
          integrationDataMap,
        })
      );
      setShowErrorMessageWithCTA(
        checkToShowAuthSection({
          isIntegrationAvailable,
          webinarSource: newState.webinarSource,
          lastValidityStatus: newState.lastValidityStatus,
          integrationDataMap,
        })
      );
    },
    [integrationDataMap, isIntegrationAvailable, updateSessionState, webAutoAttendanceSettings]
  );
  const { verificationSource, validationInProgress, validateUrl, abortController } =
    useUrlValidation({
      integrationsInfo,
      onValidationSuccess: updateAutoAttendanceSettings,
      onValidationNoSuccess: updateAutoAttendanceSettings,
      debounceTimeout: undefined,
    });
  const updateAutoAttendanceStatus = (value: boolean) => {
    updateAutoAttendanceSettings({ validationData: { isAutoAttendanceEnabled: value } });
  };
  const renderLoadingState = () => (
    <UrlVerificationLoader loading={validationInProgress} integrationSource={verificationSource} />
  );
  const updateLocationWithDebounce = useMemo(() => {
    const updateLocation = (value: string) => {
      if (isComponentUnmounted.current === true) {
        return;
      }
      updateSessionState({ key: 'location', value: value });
      setShowPasswordSection(false);
      setShowErrorMessageWithCTA(false);
      validateUrl(value, {
        userEmail: userEmailAddress,
      });
    };
    return _debounce(updateLocation, DEBOUNCED_TIMEOUT);
  }, [
    updateSessionState,
    isComponentUnmounted,
    setShowPasswordSection,
    validateUrl,
    userEmailAddress,
  ]);
  const handleInputChange = useCallback(
    (event: any, value: string) => {
      value = value ? value.trim() : value;
      if (isIntegrationAvailable) {
        if (abortController.current && !abortController.current.signal.aborted) {
          abortController.current.abort();
        }
        if (webexMeetingSettings) {
          updateSessionState({
            key: WEBEX_MEETING_SETTING_KEY,
            value: undefined,
          });
        }
        if (msteamsMeetingSettings) {
          updateSessionState({
            key: MS_TEAMS_MEETING_SETTING_KEY,
            value: undefined,
          });
        }
      }
      updateLocationWithDebounce(value);
    },
    [
      updateLocationWithDebounce,
      updateSessionState,
      webexMeetingSettings,
      msteamsMeetingSettings,
      isIntegrationAvailable,
      abortController,
    ]
  );
  const handleValidateButtonClick = () => {
    validateUrl(location, { password: webexMeetingPassword, userEmail: userEmailAddress });
  };
  const renderValidationMessage = () => (
    <StyledActionArea>
      <span
        className={classnames({
          success: isAutoAttendanceEnabled,
          warning: true,
        })}
      >
        {getValidationMessage({
          webAutoAttendanceSettings,
          canRetry,
          isIntegrationAvailable,
          isLocationEmpty,
        })}
        {canRetry && (
          <Button
            className={'retryButton'}
            onClick={handleValidateButtonClick}
            size="small"
            type="text"
          >
            Retry
          </Button>
        )}
      </span>
    </StyledActionArea>
  );
  const renderAttendanceToggle = () => (
    <StyledAutoAttendanceToggle
      isAutoAttendanceEnabled={isAutoAttendanceEnabled}
      updateAutoAttendanceStatus={updateAutoAttendanceStatus}
      webinarSource={webinarSource}
      isReadOnlyModeEnabled={isReadOnlyModeEnabled}
    />
  );
  const renderCheckForAutoValidation = () => (
    <div className={'sessionLocationTitle'}>
      <Button
        size={'small'}
        type="text"
        onClick={handleValidateButtonClick}
        className={classnames('iconValidate', 'validateUrlButton')}
      >
        Check for auto attendance
      </Button>
    </div>
  );
  const renderPasswordSection = () => (
    <PasswordSection
      key="passwordsection"
      validationMessage={
        webexMeetingPassword && !validationInProgress
          ? WEBEX_PASSWORD_SECTION_TEXTS.INVALID_PASSWORD_VALIDATION_TEXT
          : ''
      }
      password={webexMeetingPassword}
      onSave={handlePasswordSave}
      onClear={handlePasswordClear}
      isLoading={validationInProgress}
      loadingState={validationInProgress ? renderLoadingState() : null}
    />
  );
  const showLastValidationStatus = () => (
    <UserAuthValidation
      updateAutoAttendanceSettings={updateAutoAttendanceSettings}
      userEmailAddress={userEmailAddress}
      webinarSource={webinarSource!}
      location={location}
    />
  );
  const getSideEffectRender = () => {
    if (isIntegrationAvailable) {
      if (showErrorMessageWithCTA) {
        return showLastValidationStatus();
      }
      if (showPasswordSection) {
        return renderPasswordSection();
      }
      if (validationInProgress) {
        return renderLoadingState();
      }
      if (isLinkValidated) {
        return renderAttendanceToggle();
      }
      if (
        [
          SESSION_WEBINAR_URL_VALIDATION_STATUS_TYPES.INTEGRATION_AUTHENTICATION_ERROR,
          SESSION_WEBINAR_URL_VALIDATION_STATUS_TYPES.INTEGRATION_AUTH_NOT_SETUP,
        ].includes(lastValidityStatus)
      ) {
        return (
          <React.Fragment>
            {renderCheckForAutoValidation()}
            {renderValidationMessage()}
          </React.Fragment>
        );
      }
      return renderValidationMessage();
    }
    return null;
  };
  const handlePasswordSave = (value: string) => {
    updateSessionState({
      key: WEBEX_MEETING_SETTING_KEY,
      value: { password: value },
    });
    validateUrl(location, { password: value });
  };
  const handlePasswordClear = () => {
    if (webexMeetingSettings) {
      updateSessionState({
        key: WEBEX_MEETING_SETTING_KEY,
        value: undefined,
      });
    }
  };
  useEffect(() => {
    if (validateUrlOnMount) {
      location &&
        validateUrl(location, {
          userEmail: userEmailAddress,
        });
      resetValidateUrlOnMount && resetValidateUrlOnMount(false);
    }
    if (
      checkToShowPasswordSection({
        isIntegrationAvailable,
        webinarSource,
        lastValidityStatus,
        integrationDataMap,
      })
    ) {
      setShowPasswordSection(true);
    }
    if (
      checkToShowAuthSection({
        isIntegrationAvailable,
        webinarSource,
        lastValidityStatus,
        integrationDataMap,
      })
    ) {
      setShowErrorMessageWithCTA(true);
    }
    return () => {
      isComponentUnmounted.current = false;
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return (
    <StyledSessionLocationWrapper>
      <Input
        name="location"
        placeholder={props.intl.formatMessage(LOCATION_TYPE_PLACEHOLDERS.LINK)}
        className={'locationWidth'}
        wrapperClassName={'locationWrapper'}
        value={location}
        onChange={handleInputChange}
      />
      {getSideEffectRender()}
    </StyledSessionLocationWrapper>
  );
}
export default injectIntl(WebinarLocation);
