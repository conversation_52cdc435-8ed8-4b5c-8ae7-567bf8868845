import styled from 'styled-components';

import { tokens } from '@mindtickle/styles/lib';

import AutoAttendanceToggle from '../AutoAttendanceToggle';

export const StyledAutoAttendanceToggle = styled(AutoAttendanceToggle)`
  margin-top: 16px;
  border-radius: 8px;
  padding: 16px;
  background: ${tokens.bgTokens.COLOR_BG_SECONDARY};
`;

export const StyledPasswordSection = styled.div`
  margin-top: 16px;
  border-radius: 8px;
  padding: 16px;
  background: ${tokens.bgTokens.COLOR_BG_SECONDARY};
  width: 100%;
  .clear-button,
  .save-button {
    opacity: 0;
  }
  &:hover {
    .clear-button,
    .save-button {
      opacity: 1;
    }
  }
  .verification-loader {
    margin-top: 0px;
  }
`;

export const StyledPasswordSectionHeading = styled.div`
  font-size: 12px;
  line-height: 16px;
  color: ${tokens.textTokens.COLOR_TEXT_DEFAULT};
`;

export const StyledSessionLocationWrapper = styled.div`
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10px;
  & .sessionLocationTitle {
    margin-top: 16px;
    flex-basis: 50%;
    line-height: 16px;

    & .iconValidate {
      color: ${tokens.brandTokens.COLOR_BRAND_SECONDARY};
      cursor: pointer;
      padding-left: 0;
    }
    & .validateUrlButton {
      height: unset;
      font-size: 13px;
    }
  }
  & .flex {
    display: flex;
    flex-wrap: wrap;
  }

  & .sessionLocationInput {
    flex-basis: 100%;
    margin-top: 0;
  }

  & .locationWidth {
    width: 100%;
  }
  & .locationWrapper {
    width: 100%;
  }
`;
