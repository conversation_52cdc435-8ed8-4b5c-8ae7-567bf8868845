import type { Dispatch, SetStateAction } from 'react';

import type { TSessionObject, TTimezone, TUpdateSessionState } from '../../../typeDefs';

export interface TEnrollment {
  session: TSessionObject;
  updateSessionState: TUpdateSessionState;
  isReadOnlyModeEnabled: boolean;
  isPublishedOngoingPastSession: boolean;
  switchToEventDrawer: () => void;
  eventData: any;
}

export interface TEnrollmentLimitSection {
  session: TSessionObject;
  updateSessionState: TUpdateSessionState;
  isReadOnlyModeEnabled: boolean;
  isEventLevelEnrollmentEnabled: boolean;
  event: any;
}

export interface TEnrollmentFreezeSection {
  session: TSessionObject;
  updateSessionState: TUpdateSessionState;
  isReadOnlyModeEnabled: boolean;
  isPublishedOngoingPastSession: boolean;
  isEventLevelEnrollmentEnabled: boolean;
  event: any;
}

export interface TEnrollmentFreeze {
  sessionStartTime: number;
  enrollmentFreezeStatus: string;
  updateSessionState: TUpdateSessionState;
  enrollmentFreezeTime: number;
  disableFreezeEdit: boolean;
  timezone: TTimezone;
  isReadOnlyModeEnabled: boolean;
}

export interface TSelectAbsolute {
  sessionStartTime: number;
  freezeDate: number;
  setFreezeDate: Dispatch<SetStateAction<number>>;
  validateError: boolean;
  disableFreezeEdit: boolean;
  timezone: TTimezone;
  isFreezePast: boolean;
  isReadModeEnabled: boolean;
}

export interface TSelectRelative {
  numDays: number;
  setNumDays: Dispatch<SetStateAction<number>>;
  disableFreezeEdit: boolean;
  isFreezePast: boolean;
  isReadModeEnabled: boolean;
}

export interface TEnrollmentFreezeDatepicker {
  validateError?: boolean;
  isPast?: boolean;
  disabled: boolean;
}

export interface TEnrollmentFreezeInput {
  isPast?: boolean;
}
