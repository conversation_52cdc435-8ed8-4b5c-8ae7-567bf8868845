import { useCallback, useState, useRef, useEffect } from 'react';

import { errorToast, successToast } from '@mindtickle/toast';
import { noop } from '@mindtickle/utils';

import { WEBINAR_SOURCE_LABELS } from '~/modules/Admin/config/sessions.constants';
import { getIntegrationSourceByWebinarSource } from '~/modules/Admin/utils/sessionEdit';

import { PRE_WEBINAR_MEETING_CREATION_TEXTS } from '../../../../../../../constants';
import { LOADER_TIMEOUT } from '../../../../WebinarMeetings/components/AuthModal/hooks/constants';
import { useAuth } from '../../../../WebinarMeetings/components/AuthModal/hooks/useAuth';
import { validateMeeting } from '../../../../WebinarMeetings/components/AuthModal/hooks/utils';

function clearLoaderTimer(loaderTimer: NodeJS.Timeout | undefined) {
  if (loaderTimer !== undefined) {
    clearTimeout(loaderTimer);
  }
}

export default function useUserAuthValidation({
  location,
  updateAutoAttendanceSettings,
  webinarSource,
}: {
  location: string;
  webinarSource: string;
  updateAutoAttendanceSettings: (data: any) => void;
}) {
  const integrationSource = getIntegrationSourceByWebinarSource(webinarSource);
  const [asyncStatus, setAsyncStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [validationInProgress, setValidationInProgress] = useState(false);
  const loaderTimer = useRef<ReturnType<typeof setTimeout>>();
  const isUnmountedRef = useRef(false);
  const onTimeoutCallbackRef = useRef<() => void>(noop);
  const isAsyncStatusLoading = asyncStatus === 'loading';
  const closeAuthModal = () => {
    setIsModalOpen(false);
  };
  const openAuthModal = () => {
    setIsModalOpen(true);
  };
  // Note: before modifying this function please note it gets called in auth modal also
  const handleValidationSuccessMessageAndData = useCallback(
    (data: any) => {
      successToast({
        message: PRE_WEBINAR_MEETING_CREATION_TEXTS.ULR_VALIDATED_SUCCESSFULLY(
          WEBINAR_SOURCE_LABELS[webinarSource]
        ),
        timeout: 3000,
      });
      updateAutoAttendanceSettings(data);
    },
    [updateAutoAttendanceSettings, webinarSource]
  );

  const handleErrorStateAndMessage = useCallback(() => {
    setAsyncStatus('error');
    errorToast({
      message: PRE_WEBINAR_MEETING_CREATION_TEXTS.WEBINAR_AUTH_VALIDATE_FAILED_MESSAGE(
        WEBINAR_SOURCE_LABELS[webinarSource]
      ),
      timeout: 3000,
    });
  }, [webinarSource]);

  const handleValidationFlow = async (hostEmail: string) => {
    setValidationInProgress(true);
    const response: any = await validateMeeting({
      location,
      webinarSource: webinarSource,
      hostEmail,
    });
    if (isUnmountedRef.current) {
      return;
    }
    if (response?.errorCode) {
      handleErrorStateAndMessage();
    } else if (response?.data) {
      const { data, canRetry, ...restValidationData } = response;
      setAsyncStatus('success');
      handleValidationSuccessMessageAndData({
        validationData: restValidationData,
        data,
        canRetry,
      });
    }
    setValidationInProgress(false);
  };
  const onTimeoutCallback = useCallback(() => {
    if (isAsyncStatusLoading) {
      handleErrorStateAndMessage();
    }
  }, [isAsyncStatusLoading, handleErrorStateAndMessage]);

  onTimeoutCallbackRef.current = onTimeoutCallback;

  const handleOnAbruptAuthWindowClose = useCallback(() => {
    clearLoaderTimer(loaderTimer.current);
    onTimeoutCallback();
  }, [onTimeoutCallback]);

  const { startAuth } = useAuth({
    onSuccessfulAuth: async ({ hostEmail }) => {
      clearLoaderTimer(loaderTimer.current);
      if (!isModalOpen) {
        await handleValidationFlow(hostEmail);
      }
    },
    onError: () => {
      clearLoaderTimer(loaderTimer.current);
      handleErrorStateAndMessage();
    },
    onAbruptAuthWindowClose: handleOnAbruptAuthWindowClose,
    integrationSource: integrationSource,
    isWaitingForAuth: isAsyncStatusLoading && !validationInProgress,
  });
  const handleOnAuthPress = () => {
    setAsyncStatus('loading');
    clearLoaderTimer(loaderTimer.current);
    loaderTimer.current = setTimeout(() => onTimeoutCallbackRef.current(), LOADER_TIMEOUT);
    startAuth();
  };
  useEffect(
    () => () => {
      isUnmountedRef.current = true;
      clearLoaderTimer(loaderTimer.current);
    },
    []
  );
  return {
    validationInProgress,
    setValidationInProgress,
    integrationSource,
    isModalOpen,
    handleOnAuthPress,
    handleValidationSuccessMessageAndData,
    loading: isAsyncStatusLoading,
    closeAuthModal,
    openAuthModal,
  };
}
