import styled from 'styled-components';

import { THEME_PREFIX_CLS } from '~/config/constants';

export const StyledInstructorSection = styled.div`
  .instructor-section-divider {
    margin-top: 17px;
    margin-bottom: 20px;
  }
`;

export const StyledInstructorHeader = styled.div<{ isReadOnlyModeEnabled: boolean }>`
  display: flex;
  align-items: center;
  margin-bottom: ${props => (props.isReadOnlyModeEnabled ? '0px' : '16px')};
  & .addCohostText {
    margin: 1px 8px 0px 8px;
  }
  .deleteInstructorIcon {
    cursor: pointer;
  }
`;

export const StyledInstructorContent = styled.div`
  & .instructor-input-col {
    max-width: 45%;
  }
  & .instructor-input-row {
    justify-content: space-between;
    align-items: baseline;
  }
  & .${THEME_PREFIX_CLS}-form-item-label > label::before {
    display: none !important;
  }
  & .${THEME_PREFIX_CLS}-form-item {
    margin-bottom: 16px !important;
  }
  & .instructor-description {
    .ql-container {
      height: 70px;
    }
  }
`;
