import { WEBINAR_MEETING_STATUS_OPERATION } from './constants';

import type { TMeetingStatusTrack } from '../../typeDefs';

export function modifyMeetingStatusTrack(
  oldMeetingStatusTrack: TMeetingStatusTrack,
  operation: string,
  value?: any
) {
  const meetingStatusTrack = { ...oldMeetingStatusTrack };
  if (operation === WEBINAR_MEETING_STATUS_OPERATION.IN_PROGRESS) {
    meetingStatusTrack.meetingInProgress = value;
  }
  if (operation === WEBINAR_MEETING_STATUS_OPERATION.MEETING_INVALID_TIME) {
    meetingStatusTrack.meetingInvalidTimeEnum = value;
  }
  if (operation === WEBINAR_MEETING_STATUS_OPERATION.CLEAR_MODE_DATA) {
    meetingStatusTrack.createNewOnTimeCorrection = undefined;
  }
  return meetingStatusTrack;
}
