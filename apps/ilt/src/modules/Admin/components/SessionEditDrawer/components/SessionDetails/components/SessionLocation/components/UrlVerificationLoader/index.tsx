import { FormattedMessage } from 'react-intl';

import Loader from '@mindtickle/loader';

import {
  SESSION_AUTO_ATTENDANCE_MESSAGES,
  WEBINAR_SOURCE_LABELS,
} from '~/modules/Admin/config/sessions.constants';
import { getWebinarSourceByIntegration } from '~/modules/Admin/utils/sessionEdit';

import useUrlValidation from './hooks/useUrlValidation';
import { StyledActionArea } from './styles';

import type { TUrlVerificationLoader } from '../../typeDefs';

export { useUrlValidation };

const UrlVerificationLoader = ({ loading, integrationSource }: TUrlVerificationLoader) => {
  if (!loading) {
    return null;
  }
  const webinarSource = getWebinarSourceByIntegration(integrationSource || '');
  const verificationMessage = webinarSource ? (
    <FormattedMessage
      {...SESSION_AUTO_ATTENDANCE_MESSAGES.URL_VERIFICATION_IN_PROGRESS}
      values={{ sourceLabel: WEBINAR_SOURCE_LABELS[webinarSource] }}
    />
  ) : (
    ''
  );
  return (
    // verification-loader class used in password input styling
    <StyledActionArea className="verification-loader">
      <Loader size="sizeXSmall" className={'urlLoader'} />
      <span className={'loading'}>{verificationMessage}</span>
    </StyledActionArea>
  );
};

export default UrlVerificationLoader;
