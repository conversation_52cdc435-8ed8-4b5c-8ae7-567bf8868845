import styled from 'styled-components';

import { tokens, mixins, theme } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS } from '~/config/constants';

import type { TEnrollmentFreezeInput } from '../../../../typeDefs';

export const EnrollmentFreezeInput = styled.div<TEnrollmentFreezeInput>`
  display: flex;
  float: left;
  margin-top: 16px;
  margin-right: 5px;
  font-family: ${theme.fontFamily.DEFAULT};
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;

  .${THEME_PREFIX_CLS}-input-number {
    width: 65px;
    input {
      padding-left: 10px;
      text-align: center;
    }

    ${({ isPast }) =>
      isPast &&
      `
            border: solid 1.5px ${tokens.borderTokens.COLOR_BORDER_DANGER};
        `}
  }

  > .input-style-box {
    float: left;
    border: 1px solid ${tokens.iconTokens.COLOR_ICON_INVERSE_TERTIARY};
    padding: 0px 20px;
    border-radius: 4px;
    background-color: ${tokens.bgTokens.COLOR_BG_TERTIARY};
    color: ${tokens.textTokens.COLOR_TEXT_DEFAULT};
    line-height: 24px;
    margin-top: 2px;
    margin-bottom: 16px;
    ${({ isPast }) =>
      isPast &&
      `
            border: solid 1.5px ${tokens.borderTokens.COLOR_BORDER_DANGER};
        `}
  }

  > .input-side-text {
    margin-left: 5px;
    margin-top: 5px;
    font-size: 13px;
    line-height: 18px;
  }

  .${THEME_PREFIX_CLS}-form-item-explain-error {
    display: none;
  }

  .${THEME_PREFIX_CLS}-form-item {
    margin-bottom: 14px;
  }
`;

export const StyledEnrollmentFreezeDaysView = styled.div`
  ${mixins.darkText()}
  margin-bottom: 12px;
  margin-top: 12px;
`;
