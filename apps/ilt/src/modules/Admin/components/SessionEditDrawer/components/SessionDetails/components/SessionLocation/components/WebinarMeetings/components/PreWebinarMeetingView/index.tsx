import { useState, useEffect, useRef } from 'react';

import { errorToast, successToast } from '@mindtickle/toast';

import { TOAST_WRAPPER_ID } from '~/modules/Admin/components/SessionEditDrawer/constants';
import {
  WEBINAR_HOST_ERRORS,
  WEBINAR_SOURCE_LABELS,
  WEBINAR_CREATION_ERRORS,
  SESSION_WEBINAR_STATUS_TYPES,
} from '~/modules/Admin/config/sessions.constants';
import { createWebinarMeeting } from '~/modules/Admin/utils/sessionEdit';

import { PRE_WEBINAR_MEETING_CREATION_TEXTS } from '../../../../../../constants';
import { StyledLoader } from '../../styles';
import AuthModal from '../AuthModal';
import HostInputEmailModal from '../HostInputModal';

import type { ICreateMeetingParams, TPreWebinarMeetingView } from '../../typeDefs';

const checkHostError = (webinarErrorCode: string) => WEBINAR_HOST_ERRORS.includes(webinarErrorCode);
const checkMeetingError = (webinarErrorCode: string) =>
  WEBINAR_CREATION_ERRORS.includes(webinarErrorCode);

export const createMeeting = async ({
  hostEmail,
  webinarSource,
  endTimeWithOffSet,
  startTimeWithOffSet,
  meetingTitle,
  handleMeetingCreationSuccess,
  handleHostError,
  handleMeetingError,
  setCreationInProgress,
  setCreationErrInvalidHost,
  isUnmounted,
  onCancel,
  setHostEmail,
}: ICreateMeetingParams) => {
  setCreationInProgress(true);
  setCreationErrInvalidHost((value: any) => ({ ...value, hasError: false }));
  setHostEmail(''); // clearing previously entered to show enmpty value if in host modal if it fails again for invalid host
  let error: any;
  let meetingData;
  try {
    const response = await createWebinarMeeting({
      webinarSource: webinarSource,
      endTimeWithOffSet,
      startTimeWithOffSet,
      meetingTitle,
      hostEmail,
    });
    if (response && !response.errorCode && response.status === true) {
      meetingData = response.meetingVo;
    } else {
      error = response;
    }
  } catch (err) {
    error = err;
  }

  if (!isUnmounted.current) {
    if (meetingData) {
      handleMeetingCreationSuccess(meetingData);
    } else if (error && checkHostError(error.errorCode)) {
      handleHostError(error);
    } else if (error && checkMeetingError(error.errorCode)) {
      handleMeetingError(error);
    } else {
      errorToast({
        message: PRE_WEBINAR_MEETING_CREATION_TEXTS['MEETING_CREATION_FAILED_MESSAGE'](
          WEBINAR_SOURCE_LABELS[webinarSource]
        ),
        timeout: 3000,
      });
      onCancel();
    }
    !isUnmounted.current && setCreationInProgress(false);
  }
};

const PreWebinarMeetingView = (props: TPreWebinarMeetingView) => {
  const {
    endTimeWithOffSet,
    startTimeWithOffSet,
    meetingTitle,
    onCancel,
    onMeetingCreation,
    initialHostEmail,
    webinarSource,
    integrationSource,
    isUserLevelAuthEnabled,
  } = props;

  const [creationInProgress, setCreationInProgress] = useState(false);
  const [hostEmail, setHostEmail] = useState('');
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);

  const openAuthModal = () => setIsAuthModalOpen(true);
  const closeAuthModal = () => {
    onCancel();
    setIsAuthModalOpen(false);
  };
  const [creationErrInvalidHost, setCreationErrInvalidHost] = useState({
    hasError: false,
    errorCount: 0,
    error: '',
  });
  const isUnmounted = useRef(false);
  const webinarMeetingLabel = WEBINAR_SOURCE_LABELS[webinarSource];

  const handleMeetingCreationSuccess = (meetingData: any) => {
    if (isUnmounted.current) {
      return;
    }
    successToast({
      message: PRE_WEBINAR_MEETING_CREATION_TEXTS.MEETING_CREATED_SUCCESSFULLY(webinarMeetingLabel),
      timeout: 3000,
    });
    onMeetingCreation(meetingData);
    !isUnmounted.current && setCreationInProgress(false);
  };

  const handleHostError = (error: any) => {
    if (isUserLevelAuthEnabled) {
      openAuthModal();
      return;
    }
    setCreationErrInvalidHost(value => ({
      hasError: true,
      errorCount: value.errorCount + 1,
      error: error.errorCode,
    }));
  };

  const handleMeetingError = (error: any) => {
    errorToast({
      message:
        PRE_WEBINAR_MEETING_CREATION_TEXTS['MEETING_CREATION_FAILED_AUTH_ERROR'](
          webinarMeetingLabel
        ),
      timeout: 3000,
      mountId: TOAST_WRAPPER_ID,
    });
    onCancel();
  };

  const onOk = (value: string) => {
    createMeeting({
      hostEmail: value,
      webinarSource,
      endTimeWithOffSet,
      startTimeWithOffSet,
      meetingTitle,
      handleMeetingCreationSuccess,
      handleHostError,
      handleMeetingError,
      setCreationInProgress,
      setCreationErrInvalidHost,
      isUnmounted,
      onCancel,
      setHostEmail,
    });
  };

  useEffect(() => {
    createMeeting({
      hostEmail: initialHostEmail,
      webinarSource,
      endTimeWithOffSet,
      startTimeWithOffSet,
      meetingTitle,
      handleMeetingCreationSuccess,
      handleHostError,
      handleMeetingError,
      setCreationInProgress,
      setCreationErrInvalidHost,
      isUnmounted,
      onCancel,
      setHostEmail,
    });
    return () => {
      isUnmounted.current = true;
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      {creationInProgress && (
        <StyledLoader
          size="sizeBig"
          type="Full"
          message={PRE_WEBINAR_MEETING_CREATION_TEXTS['CREATING_WEBINAR_MEETING_MESSAGE'](
            webinarMeetingLabel
          )}
        />
      )}
      {!isAuthModalOpen && creationErrInvalidHost.hasError && (
        <HostInputEmailModal
          onCancel={onCancel}
          onOk={onOk}
          hostEmail={hostEmail}
          modalTitle={
            creationErrInvalidHost.errorCount > 1
              ? PRE_WEBINAR_MEETING_CREATION_TEXTS['INVALID_HOST_MULTIPLE_TIME_TITLE']
              : creationErrInvalidHost.error === SESSION_WEBINAR_STATUS_TYPES.RATE_LIMIT_BREACHED
              ? PRE_WEBINAR_MEETING_CREATION_TEXTS['CURRENT_HOST_RATE_LIMIT_BREACHED_TITLE'](
                  webinarMeetingLabel
                )
              : PRE_WEBINAR_MEETING_CREATION_TEXTS['INVALID_HOST_TITLE'](webinarMeetingLabel)
          }
          modalContent={
            creationErrInvalidHost.errorCount > 1
              ? creationErrInvalidHost.error === SESSION_WEBINAR_STATUS_TYPES.RATE_LIMIT_BREACHED
                ? PRE_WEBINAR_MEETING_CREATION_TEXTS['ENTERED_HOST_RATE_LIMIT_BREACHED']
                : PRE_WEBINAR_MEETING_CREATION_TEXTS['INVALID_HOST_MULTIPLE_TIME_CONTENT'](
                    webinarMeetingLabel
                  )
              : creationErrInvalidHost.error === SESSION_WEBINAR_STATUS_TYPES.RATE_LIMIT_BREACHED
              ? PRE_WEBINAR_MEETING_CREATION_TEXTS['CURRENT_HOST_RATE_LIMIT_BREACHED']
              : PRE_WEBINAR_MEETING_CREATION_TEXTS['INVALID_HOST_CONTENT'](webinarMeetingLabel)
          }
          okText={PRE_WEBINAR_MEETING_CREATION_TEXTS['OK_BUTTON_TEXT']}
          cancelText={PRE_WEBINAR_MEETING_CREATION_TEXTS['CANCEL_BUTTON_TEXT']}
          setHostEmail={setHostEmail}
          loading={creationInProgress}
        ></HostInputEmailModal>
      )}
      {isAuthModalOpen && (
        <AuthModal
          handleMeetingCreationSuccess={handleMeetingCreationSuccess}
          hostEmail={initialHostEmail}
          onCancel={closeAuthModal}
          webinarSource={webinarSource}
          integrationSource={integrationSource}
          endTimeWithOffSet={endTimeWithOffSet}
          startTimeWithOffSet={startTimeWithOffSet}
          meetingTitle={meetingTitle}
          setCreationInProgress={setCreationInProgress}
          creationInProgress={creationInProgress}
          flow="create"
        />
      )}
    </>
  );
};

export default PreWebinarMeetingView;
