import { useRef } from 'react';

import { FormattedMessage } from 'react-intl';

import Button from '@mindtickle/button';
import Modal from '@mindtickle/modal';

import { MASKED_MODAL_INDEX } from '~/config/constants';
import { DELETE_LC } from '~/modules/Admin/config/live-challenges.constants';

import { TDeleteModal, TModalFooter } from '../../typeDefs';

import { DeleteActions, StyledDeleteModalContainer } from './styles';

const ModalFooter = ({ onCancel, onOk }: TModalFooter) => (
  <DeleteActions>
    <Button type="text" onClick={onCancel} className={'cancelButton'}>
      <FormattedMessage {...DELETE_LC.CANCEL} />
    </Button>
    <Button type="danger" onClick={onOk} className={'okButton'}>
      <FormattedMessage {...DELETE_LC.DELETE_BUTTON} />
    </Button>
  </DeleteActions>
);

const DeleteModalTitle = () => <FormattedMessage {...DELETE_LC.TITLE} />;

const DeleteModal = ({ confirmDeleteLc, onCancelDelete }: TDeleteModal) => {
  const modalContainerRef = useRef<HTMLDivElement>(null);

  return (
    <StyledDeleteModalContainer ref={modalContainerRef}>
      <Modal
        title={<DeleteModalTitle />}
        visible={true}
        onOk={confirmDeleteLc}
        onCancel={onCancelDelete}
        centered={true}
        getPopupContainer={() => modalContainerRef.current}
        maskClosable={false}
        keyboard={false}
        type={'small'}
        zIndex={MASKED_MODAL_INDEX}
        footer={<ModalFooter onCancel={onCancelDelete} onOk={confirmDeleteLc} />}
      >
        <FormattedMessage {...DELETE_LC.DESCRIPTION} />
      </Modal>
    </StyledDeleteModalContainer>
  );
};

export default DeleteModal;
