import styled from 'styled-components';

import { tokens, theme } from '@mindtickle/styles/lib';

import { tabContainerCommonMixin } from '../../styles';

export const StyledLCTabContainer = styled.div`
  ${tabContainerCommonMixin()}
`;
export const StyledCreateLiveChallengeContainer = styled.div`
  width: 60%;
  margin: auto;
  /* padding-top: 25%; */
  display: flex;
  flex-direction: column;
  vertical-align: middle;
  height: 100%;
  margin: 20% auto 0;
  text-align: center;
  align-items: center;
  & .createLCButton {
    width: 40%;
  }
`;

export const CreateLiveChallenge = styled.div`
  .createLiveChallengeHeader {
    display: flex;
    align-items: center;
    color: black;
    font-weight: bold;
    margin-bottom: 16px;
  }

  .createLiveChallengeButton {
    color: ${tokens.textTokens.COLOR_TEXT_ACCENT};
    cursor: pointer;
  }

  .tooltipWrapper {
    margin-left: 8px;

    .tooltipIcon {
      cursor: pointer;
    }
  }
`;

export const CreateLCModalSubHeader = styled.div`
  color: ${tokens.textTokens.COLOR_TEXT_TERTIARY};
  font-family: ${theme.fontFamily.DEFAULT};

  a {
    color: ${tokens.textTokens.COLOR_TEXT_ACCENT};
  }
`;

export const UploadLCCsv = styled.div`
  margin-top: 24px;
  margin-bottom: 24px;

  .dragger {
    padding: 48px;

    .draggerContent {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      min-height: 380px;

      .addCsvText {
        margin-top: 24px;
        color: ${tokens.textTokens.COLOR_TEXT_DEFAULT};
        font-family: ${theme.fontFamily.DEFAULT};
      }

      .fileInfo {
        color: ${tokens.textTokens.COLOR_TEXT_DEFAULT};
        font-family: ${theme.fontFamily.DEFAULT};
        font-size: 12px;
      }
    }
  }
`;

export const uploadIconStyle = {
  height: 180,
  width: 180,
};

export const liveChallengeIconStyle = {
  height: 300,
};
