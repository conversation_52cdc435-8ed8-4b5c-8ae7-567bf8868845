import CHECKIN_UI from '~/modules/Admin/messages/sessions/checkin/checkinUI';

// export const EMAIL_REGEX = /^([a-zA-Z0-9_\-\.]+)@([a-zA-Z0-9_\-\.]+)\.([a-zA-Z]{2,5})$/;
// export const MAX_CHECKIN_FACILITATORS = 5;
export const DEFAULT_TIMER_STATE = {
  hours: 0,
  minutes: 0,
};
export const CHECKIN = 'checkin';

export const EMAIL_TYPES = {
  FACILITATORS: 'facilitators',
  INSTRUCTOR: 'instructor',
};

export const CHECKIN_HOURS_ALLOWED_BEFORE_SESSION = 1;
export const CHECKIN_HOURS_ALLOWED_AFTER_SESSION = 24;

export const CHECKIN_UI_LABELS = {
  SAVE_BUTTON_LABEL: CHECKIN_UI.SAVE_BUTTON_LABEL,
  CANCEL_BUTTON_LABEL: CHECKIN_UI.CANCEL_BUTTON_LABEL,
  CLOSE_BUTTON_LABEL: CHECKIN_UI.CLOSE_BUTTON_LABEL,
  SHARE_BUTTON_LABEL: CHECKIN_UI.SHARE_BUTTON_LABEL,
  SELF_CHECKIN_LABEL: CHECKIN_UI.SELF_CHECKIN_LABEL,
  SELF_CHECKIN_TOGGLE_LABEL: CHECKIN_UI.SELF_CHECKIN_TOGGLE_LABEL,
  SEND_EMAIL_TO_INSTRUCTORS: CHECKIN_UI.SEND_EMAIL_TO_INSTRUCTORS,
  SEND_EMAIL_TO_FACILITATORS: CHECKIN_UI.SEND_EMAIL_TO_FACILITATORS,
  ADD_AS_COHOST: CHECKIN_UI.ADD_AS_COHOST,
  MAX_5_FACILITATORS_LABEL: CHECKIN_UI.MAX_5_FACILITATORS_LABEL,
  ADD_FACILITATOR_LABEL: CHECKIN_UI.ADD_FACILITATOR_LABEL,
  CHECKIN_TIME_DURATION_LABEL: CHECKIN_UI.CHECKIN_TIME_DURATION_LABEL,
  CHECKIN_TIME_BEFORE_SESSION: CHECKIN_UI.CHECKIN_TIME_BEFORE_SESSION,
  CHECKIN_TIME_AFTER_SESSION: CHECKIN_UI.CHECKIN_TIME_AFTER_SESSION,
  CHECKIN_MAX_TIME_BEFORE_SESSION: CHECKIN_UI.CHECKIN_MAX_TIME_BEFORE_SESSION,
  CHECKIN_MAX_TIME_AFTER_SESSION: CHECKIN_UI.CHECKIN_MAX_TIME_AFTER_SESSION,
  SEND_EMAIL_TO_FACILITATORS_TOOLTIP: CHECKIN_UI.SEND_EMAIL_TO_FACILITATORS_TOOLTIP,
  SEND_EMAIL_TO_INSTRUCTORS_TOOLTIP: CHECKIN_UI.SEND_EMAIL_TO_INSTRUCTORS_TOOLTIP,
  CHECKIN_TIME_DURATION_TOOLTIP: CHECKIN_UI.CHECKIN_TIME_DURATION_TOOLTIP,
  CHECKIN_CODE_LABEL: CHECKIN_UI.CHECKIN_CODE_LABEL,
  COPY_TO_CLIPBOARD_TOOLTIP: CHECKIN_UI.COPY_TO_CLIPBOARD_TOOLTIP,
  COPIED_TO_CLIPBOARD_TOOLTIP: CHECKIN_UI.COPIED_TO_CLIPBOARD_TOOLTIP,
  SHARE_QR_LABEL: CHECKIN_UI.SHARE_QR_LABEL,
  SHARE_QR_INFO: CHECKIN_UI.SHARE_QR_INFO,
  SHARE_QR_SUCCESS: CHECKIN_UI.SHARE_QR_SUCCESS,
  SHARE_QR_FAILURE: CHECKIN_UI.SHARE_QR_FAILURE,
  SHARE_QR_TOOLTIP: CHECKIN_UI.SHARE_QR_TOOLTIP,
  USE_CHECKIN_CODE_INFO: CHECKIN_UI.USE_CHECKIN_CODE_INFO,
  VALIDITY_PERIOD: CHECKIN_UI.VALIDITY_PERIOD,
  CHECKIN_DETAILS_EMAIL_INFO: CHECKIN_UI.CHECKIN_DETAILS_EMAIL_INFO,
  CHECKIN_CODE_DOWNLOAD: CHECKIN_UI.CHECKIN_CODE_DOWNLOAD,
  CHECKIN_DETAILS_SUPPORT_EMAIL: '<EMAIL>',
  CHECKIN_ACTION_DISABLED: CHECKIN_UI.CHECKIN_ACTION_DISABLED,
  CHECKIN_DISABLED_ZOOM_1: CHECKIN_UI.CHECKIN_DISABLED_ZOOM_1,
  CHECKIN_DISABLED_ZOOM_2: CHECKIN_UI.CHECKIN_DISABLED_ZOOM_2,
  CHECKIN_SHARE_VIA_EMAIL_DISABLED_UNPUBLISHED:
    CHECKIN_UI.CHECKIN_SHARE_VIA_EMAIL_DISABLED_UNPUBLISHED,
};
