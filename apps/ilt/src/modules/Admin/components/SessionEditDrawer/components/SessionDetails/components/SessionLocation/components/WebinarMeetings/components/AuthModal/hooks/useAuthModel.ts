import { useCallback, useEffect, useReducer, useRef } from 'react';

import { errorToast } from '@mindtickle/toast';

import { WEBINAR_SOURCE_LABELS } from '~/modules/Admin/config/sessions.constants';
import {
  required as checkRequired,
  email as checkValidEmail,
} from '~/modules/Admin/utils/formValidator';

import { PRE_WEBINAR_MEETING_CREATION_TEXTS } from '../../../../../../../constants';

import { initialState, LOADER_TIMEOUT } from './constants';
import { UseAuthModalParams } from './typeDefs';
import { useAuth } from './useAuth';
import { createMeeting, useAuthModalReducer, validateMeeting } from './utils';

function clearLoaderTimer(loaderTimer: NodeJS.Timeout | undefined) {
  if (loaderTimer !== undefined) {
    clearTimeout(loaderTimer);
  }
}

const useStateHandlers = () => {
  const [state, dispatch] = useReducer(useAuthModalReducer, initialState);
  const loaderTimer = useRef<ReturnType<typeof setTimeout>>();
  const isUnmountedRef = useRef(false);
  const switchToHostEmail = useCallback(() => {
    clearLoaderTimer(loaderTimer.current);
    dispatch({ type: 'SWITCH_TO_HOST_EMAIL' });
  }, []);
  const switchToAuthenticate = useCallback(() => dispatch({ type: 'SWITCH_TO_AUTHENTICATE' }), []);
  const addHostEmail = (email: string) => dispatch({ type: 'ADD_HOST_EMAIL', payload: email });
  const setAsyncStatus = useCallback(
    (status: 'idle' | 'loading' | 'success' | 'error') =>
      dispatch({ type: 'ASYNC_STATUS', payload: status }),
    []
  );
  const setSubmitDisabled = useCallback(
    (isDisabled: boolean) => dispatch({ type: 'SET_SUBMIT_DISABLED', payload: isDisabled }),
    []
  );
  const setValidationError = (error: string) =>
    dispatch({ type: 'SET_VALIDATION_ERROR', payload: error });

  return {
    state,
    loaderTimer,
    isUnmountedRef,
    switchToHostEmail,
    switchToAuthenticate,
    addHostEmail,
    setAsyncStatus,
    setSubmitDisabled,
    setValidationError,
  };
};

const useWebinarAuthFlow = ({
  hostEmail,
  webinarSource,
  integrationSource,
  meetingTitle,
  startTimeWithOffSet,
  endTimeWithOffSet,
  onSuccessfulMeetingCreation,
  creationInProgress,
  setCreationInProgress,
  validationInProgress,
  setValidationInProgress,
  flow,
  meetingUrl,
  handleMeetingValidationSuccess,
  initialFlow = 'authenticate',
}: UseAuthModalParams) => {
  const {
    state,
    loaderTimer,
    isUnmountedRef,
    switchToHostEmail,
    switchToAuthenticate,
    addHostEmail,
    setAsyncStatus,
    setSubmitDisabled,
    setValidationError,
  } = useStateHandlers();

  const handleOnSuccessFullCreation = ({ meetingData }: { meetingData: any }) => {
    setAsyncStatus('success');
    onSuccessfulMeetingCreation(meetingData);
  };

  const handleOnSuccessFullValidation = ({ response }: { response: any }) => {
    const { data, canRetry, ...restValidationData } = response;
    setAsyncStatus('success');
    handleMeetingValidationSuccess &&
      handleMeetingValidationSuccess({
        validationData: restValidationData,
        data,
        canRetry,
      });
  };

  const handleValidationFlow = async (hostEmail: string) => {
    setValidationInProgress(true);
    setAsyncStatus('loading');
    const response: any = await validateMeeting({
      location: meetingUrl!,
      webinarSource: webinarSource,
      hostEmail,
    });
    if (isUnmountedRef.current) {
      return;
    }
    if (response?.errorCode) {
      handleErrorStateAndMessage();
    } else if (response?.data) {
      handleOnSuccessFullValidation({ response });
    }
    setValidationInProgress(false);
  };

  const handleCreationFlow = async (hostEmail: string) => {
    setCreationInProgress(true);
    const { meetingData, error } = await createMeeting({
      webinarSource: webinarSource,
      endTimeWithOffSet,
      startTimeWithOffSet,
      meetingTitle,
      hostEmail,
    });
    if (isUnmountedRef.current) {
      return;
    }
    addHostEmail(hostEmail);
    if (error) {
      handleErrorStateAndMessage({
        errorMessage: PRE_WEBINAR_MEETING_CREATION_TEXTS['MEETING_CREATION_FAILED_MESSAGE'](
          WEBINAR_SOURCE_LABELS[webinarSource]
        ),
      });
    } else if (meetingData) {
      handleOnSuccessFullCreation({ meetingData });
    }
    setCreationInProgress(false);
  };

  const handleErrorStateAndMessage = useCallback(
    ({ errorMessage }: { errorMessage?: string } = {}) => {
      setAsyncStatus('error');
      errorToast({
        message:
          errorMessage ||
          (flow === 'validate'
            ? PRE_WEBINAR_MEETING_CREATION_TEXTS.WEBINAR_AUTH_VALIDATE_FAILED_MESSAGE(
                WEBINAR_SOURCE_LABELS[webinarSource]
              )
            : PRE_WEBINAR_MEETING_CREATION_TEXTS.WEBINAR_AUTH_FAILED_MESSAGE(
                WEBINAR_SOURCE_LABELS[webinarSource]
              )),
        timeout: 3000,
      });
      setSubmitDisabled(false);
    },
    [flow, setAsyncStatus, setSubmitDisabled, webinarSource]
  );
  const onTimeoutCallback = useCallback(() => {
    if (state.asyncStatus === 'loading') {
      handleErrorStateAndMessage();
    }
  }, [handleErrorStateAndMessage, state.asyncStatus]);
  const onTimeoutCallbackRef = useRef(onTimeoutCallback);
  onTimeoutCallbackRef.current = onTimeoutCallback; // to assign latest ref of callback even if rerendered

  const handleAbruptAuthWindowClose = useCallback(() => {
    clearLoaderTimer(loaderTimer.current);
    onTimeoutCallback();
  }, [onTimeoutCallback, loaderTimer]);

  const { startAuth } = useAuth({
    onSuccessfulAuth: async ({ hostEmail }) => {
      clearLoaderTimer(loaderTimer.current);
      if (flow === 'validate') {
        await handleValidationFlow(hostEmail);
      } else if (flow === 'create') {
        await handleCreationFlow(hostEmail);
      }
    },
    onError: () => {
      clearLoaderTimer(loaderTimer.current);
      handleErrorStateAndMessage();
    },
    onAbruptAuthWindowClose: handleAbruptAuthWindowClose,
    integrationSource,
    isWaitingForAuth:
      state.asyncStatus === 'loading' && !(creationInProgress || validationInProgress),
  });

  const validateEmail = () => {
    if (checkValidEmail(state.hostEmail)) {
      setValidationError('Please enter a valid email address');
      return false;
    } else if (checkRequired(state.hostEmail)) {
      setValidationError('Please enter a valid email address');
      return false;
    } else {
      setValidationError('');
      setAsyncStatus('loading');
      setSubmitDisabled(true);
      return true;
    }
  };

  const handleMeetingCreation = async () => {
    setCreationInProgress(true);
    const { meetingData, error } = await createMeeting({
      webinarSource: webinarSource,
      endTimeWithOffSet,
      startTimeWithOffSet,
      meetingTitle,
      hostEmail: state.hostEmail,
    });
    if (isUnmountedRef.current) {
      return;
    }
    if (error) {
      handleErrorStateAndMessage();
      switchToAuthenticate();
    } else if (meetingData) {
      handleOnSuccessFullCreation({ meetingData });
    }
    setCreationInProgress(false);
  };

  const handleMeetingValidation = async () => {
    setValidationInProgress(true);
    const response: any = await validateMeeting({
      location: meetingUrl!,
      webinarSource: webinarSource,
      hostEmail: state.hostEmail.length ? state.hostEmail : hostEmail,
    });
    if (isUnmountedRef.current) {
      return;
    }
    if (response?.errorCode) {
      handleErrorStateAndMessage();
      switchToAuthenticate();
    } else if (response?.data) {
      handleOnSuccessFullValidation({ response });
    }
    setValidationInProgress(false);
  };

  const startWebFlowAuth = async () => {
    setAsyncStatus('loading');
    clearLoaderTimer(loaderTimer.current);
    loaderTimer.current = setTimeout(() => onTimeoutCallbackRef.current(), LOADER_TIMEOUT);
    startAuth();
  };

  const onPressOk = async () => {
    if (state.flow === 'authenticate') {
      startWebFlowAuth();
    } else if (state.flow === 'hostEmail') {
      if (validateEmail()) {
        if (flow === 'create') {
          await handleMeetingCreation();
        } else if (flow === 'validate') {
          await handleMeetingValidation();
        }
      }
    }
  };

  const onPressEnter = async (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter' && state.asyncStatus !== 'loading') {
      onPressOk();
    }
  };

  useEffect(
    () => () => {
      isUnmountedRef.current = true;
      clearLoaderTimer(loaderTimer.current);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps -- this acts as componentUnmount
    []
  );

  useEffect(() => {
    if (initialFlow === 'authenticate') {
      switchToAuthenticate();
    } else if (initialFlow === 'hostEmail') {
      switchToHostEmail();
    }
  }, [initialFlow, switchToAuthenticate, switchToHostEmail]);

  return {
    state,
    switchToHostEmail,
    switchToAuthenticate,
    startWebFlowAuth,
    addHostEmail,
    setAsyncStatus,
    setSubmitDisabled,
    setValidationError,
    onPressEnter,
    onPressOk,
  };
};

export default useWebinarAuthFlow;
