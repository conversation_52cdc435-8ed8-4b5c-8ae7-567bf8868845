import { FormattedMessage } from 'react-intl';
import { useUserAuth } from 'ui_shell/Auth';

import Divider from '@mindtickle/divider';
import Radio from '@mindtickle/radio';
import { Title } from '@mindtickle/typography';

import SessionEnrolmentEmailSettings from '~/modules/Admin/components/SessionEditDrawer/components/AttendanceCompletion/components/SessionEnrolmentEmailSettings';
import {
  COMPLETION_CRITERIA_ENUM,
  COMPLETION_CRITERIA_TEXTS,
} from '~/modules/Admin/config/sessions.constants';

import SessionAutoAttendance from './components/SessionAutoAttendance';
import SessionCheckIn from './components/SessionCheckIn';
import {
  StyledAttendanceCompletionWrapper,
  StyledCompletionCriteriaContainer,
  StyledRadioWrapper,
} from './styles';

import type { TAttendanceCompletion } from './typeDefs';
import type { RadioChangeEvent } from 'antd';

const AttendanceCompletion = (props: TAttendanceCompletion) => {
  const {
    tempFeatureConfig: {
      isCaptureAutoAttendanceFeatureEnabled = false,
      isEnrollmentThresholdEmailEnabled = false,
    },
  } = useUserAuth();
  const {
    session,
    updateSessionState,
    onShareCheckInCode,
    mode,
    isReadOnlyModeEnabled,
    operationStatus,
    form,
    companySettings,
    moduleDetails,
    isSessionTimeModified,
  } = props;

  const companyAttendanceSettings = companySettings?.attendanceSettings;
  const sessionAttendanceSettings = session?.webAutoAttendanceSettings?.thresholdConfig;
  const moduleAttendanceSettings = moduleDetails?.attendanceSettings;

  const companyEnrolmentEmailSettings = companySettings?.enrolmentThresholdEmailSettings;
  const sessionEnrolmentEmailSettings = session?.enrolmentThresholdEmailSettings || {};
  const moduleEnrolmentEmailSettings = moduleDetails?.enrolmentThresholdEmailSettings || {};

  const moduleId = moduleDetails?.moduleId || '';
  const companyId = moduleDetails?.companyId || '';

  const renderCompletionCriteria = () => (
    <StyledCompletionCriteriaContainer>
      <Title level={3}>Completion criteria</Title>

      <StyledRadioWrapper>
        <div className="completion-text"> Mark session as complete when:</div>
        <Radio.Group
          className={'completion-criteria-radio-group'}
          value={session?.completionCriteria}
          onChange={(e: RadioChangeEvent) =>
            updateSessionState({ key: 'completionCriteria', value: e.target.value })
          }
          disabled={isReadOnlyModeEnabled}
        >
          <Radio className={'radio-option-style'} value={COMPLETION_CRITERIA_ENUM.ATTENDED}>
            <FormattedMessage {...COMPLETION_CRITERIA_TEXTS[COMPLETION_CRITERIA_ENUM.ATTENDED]} />
          </Radio>
          <Radio
            className={'radio-option-style'}
            value={COMPLETION_CRITERIA_ENUM.ATTENDED_OR_WATCHED}
          >
            <FormattedMessage
              {...COMPLETION_CRITERIA_TEXTS[COMPLETION_CRITERIA_ENUM.ATTENDED_OR_WATCHED]}
            />
          </Radio>
        </Radio.Group>
      </StyledRadioWrapper>
    </StyledCompletionCriteriaContainer>
  );

  return (
    <StyledAttendanceCompletionWrapper>
      <SessionCheckIn
        session={props.session}
        updateSessionState={updateSessionState}
        mode={mode}
        isReadOnlyModeEnabled={isReadOnlyModeEnabled}
        operationStatus={operationStatus}
        form={form}
        onShareCheckInCode={onShareCheckInCode}
      />
      {isCaptureAutoAttendanceFeatureEnabled &&
        session?.webAutoAttendanceSettings?.isAutoAttendanceEnabled &&
        companyAttendanceSettings?.durationBasedAttendanceEnabled && (
          <>
            <Divider className="attendance-checkin-divider" />
            <SessionAutoAttendance
              companyAttendanceSettings={companyAttendanceSettings}
              moduleAttendanceSettings={moduleAttendanceSettings}
              sessionAttendanceSettings={sessionAttendanceSettings}
              session={props.session}
              updateSessionState={updateSessionState}
              mode={mode}
              isReadOnlyModeEnabled={isReadOnlyModeEnabled}
            />
          </>
        )}
      {isEnrollmentThresholdEmailEnabled && companyEnrolmentEmailSettings?.isEnabled && (
        <>
          <Divider className="attendance-checkin-divider" />
          <SessionEnrolmentEmailSettings
            moduleEnrolmentEmailSettings={moduleEnrolmentEmailSettings}
            sessionEnrolmentEmailSettings={sessionEnrolmentEmailSettings}
            session={props.session}
            updateSessionState={updateSessionState}
            mode={mode}
            isReadOnlyModeEnabled={isReadOnlyModeEnabled}
            companyId={companyId}
            moduleId={moduleId}
            isSessionTimeModified={isSessionTimeModified}
          />
        </>
      )}
      <Divider className="attendance-checkin-divider" />
      {renderCompletionCriteria()}
    </StyledAttendanceCompletionWrapper>
  );
};

export default AttendanceCompletion;
