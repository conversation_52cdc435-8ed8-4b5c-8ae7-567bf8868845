import styled from 'styled-components';

import { tokens, theme } from '@mindtickle/styles/lib';
export const LCLink = styled.div`
  display: flex;
  align-items: center;
  color: ${tokens.deprecatedTokens.COLOR_DEPRECATED_NAVBAR2};
  font-family: ${theme.fontFamily.DEFAULT};

  .lcLinkType {
    color: ${tokens.textTokens.COLOR_TEXT_DEFAULT};
    font-weight: 600;
    font-size: 14px;
    line-height: 20px;
  }

  .lcLink {
    color: ${tokens.textTokens.COLOR_TEXT_SECONDARY};
    margin-left: 4px;
    padding: 3px 8px;
    border-radius: 4px;
    display: flex;
    align-items: center;

    .copyIcon {
      min-width: 14px;
      min-height: 14px;
      margin-left: 8px;
      cursor: pointer;
    }

    a {
      max-width: 520px;
    }
  }
`;

export const StyledLiveChallengeLinkContainer = styled.div`
  border: 1px solid ${tokens.borderTokens.COLOR_BORDER_DEFAULT};
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding: 16px 24px;
  gap: 12px;
  border-radius: 8px;
  width: fit-content;
  margin-top: 16px;
`;
