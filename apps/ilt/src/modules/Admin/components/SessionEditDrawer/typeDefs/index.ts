import { LabelHTMLAttributes, ReactNode } from 'react';

import { MS_TEAMS_MEETING_SETTING_KEY } from '~/modules/Admin/constants/sessions';

import type { TILT_ENTITIES } from '../../SessionsTable/typeDefs';
import type { TEntityWithBasicInfo } from '../../SessionsWrapper/typeDefs';

export interface TSessionObject {
  entityType?: TILT_ENTITIES;
  parentId?: string;
  sessions?: string[];
  id?: string;
  name: string;
  description: string;
  type: string;
  attachments: any[];
  maxScore: number;
  locationType: string;
  location: string;
  startTime: number;
  endTime: number;
  originalStartTime: number;
  originalEndTime: number;
  localStartTime?: string;
  localEndTime?: string;
  duration?: TDuration;
  minStartTime?: number;
  timezone: TTimezone;
  currentOffset?: number;
  reminderEnabled: boolean;
  reminders: number[];
  instructors: TInstructor[];
  isInstructorAvailable: boolean;
  sendInstructorMail: boolean;
  facilitators: string[];
  isFacilitatorAvailable: boolean;
  autoEnroll: boolean;
  maxSeats: number;
  maxSeatEnabled: boolean;
  waitingListEnabled: boolean;
  notifyWaitingList: boolean;
  enrollmentFreezeStatus: string;
  enrollmentFreezeEpoch: number;
  isCancelled: boolean;
  isPublished?: boolean;
  cancellationReason: string;
  checkInSettings: TCheckInSettings;
  webAutoAttendanceSettings: TWebAutoAttendanceSettings;
  webexMeetingSettings?: TWebinarMeetingSettings;
  zoomMeetingSettings?: TWebinarMeetingSettings;
  [MS_TEAMS_MEETING_SETTING_KEY]?: TWebinarMeetingSettings;
  liveChallenge?: {
    uniqueCode: string;
  };
  completionCriteria?: string;
  enrolmentThresholdEmailSettings?: any;
}

export interface TDuration {
  displayValue: string;
  value: number;
}

export interface TTimezone {
  displayName: string;
  hasdst: boolean;
  isdst: boolean;
  name: string;
  offset: number;
  region: string;
  shortDisplayName: string;
  originalTimezoneString: string;
  id: string;
}
export interface TInstructor {
  description: string;
  name: string;
  email: string;
}

export interface TCheckInSettings {
  isSelfCheckInActivated: boolean;
  isSelfCheckInTimeRestricted: boolean;
  secondsRelativeToSessionStartToAllowCheckIn: number;
  secondsRelativeToSessionStopToAllowCheckIn: number;
  secondsBeforeCheckInStartToTriggerMails: number;
  selfCheckInCode: string;
  selfCheckInQrCodeUrl: string;
  informInstructorsAboutCheckIn: boolean;
  informFacilitatorsAboutCheckin: boolean;
}

export interface TWebAutoAttendanceSettings {
  isLinkValidated: boolean;
  isAutoAttendanceEnabled: boolean;
  lastValidityStatus: string;
  thresholdConfig?: any;
  webinarSource?: string;
  webMeetingSettings?: any;
}

export interface TWebinarMeetingSettings {
  id: string;
  hostEmail: string;
  meetingNumber: string;
  password: string;
  hostKey: string;
  type: string;
  coHosts: TCoHost[];
}

export interface TCoHost {
  email: string;
  id?: string;
}

export interface TIntegrationDataMap {
  [key: string]: {
    id: number;
    paramsValue: {
      [key: string]: any;
    };
    enabled: boolean;
    tag: string;
    platformClientSettingId: number;
    source: string;
    cname: string;
  };
}

export interface updateSessionStateProps {
  key: string;
  value: any;
}

export type TUpdateSessionState = ({ key, value }: updateSessionStateProps) => void;

export type TUpdateMeetingStatus = (operation: string, value?: any) => void;

export type TOnLocationTypeChange = (action: string, value: any, extraValue: any) => void;

export interface TIntegrationInfo {
  isIntegrationAvailable: boolean;
  integrationSourceList?: string[];
  integrationDataMap: TIntegrationDataMap;
}

export interface TMeetingStatusTrack {
  meetingInProgress: any;
  meetingInvalidTimeEnum: string;
  createNewOnTimeCorrection: any;
}

export interface TLabeledSessionInfo extends LabelHTMLAttributes<HTMLLabelElement> {
  labelText: string | React.ReactNode;
  overlayClassName?: string;
  children: ReactNode;
}

export interface TSessionDrawerTitle {
  mode: string;
  sessionName: string;
  sessionId?: string;
  triggerEditAction: ({ id, entityType, parentId }: TEntityWithBasicInfo) => void;
  isReadOnlyModeEnabled: boolean;
  eventName?: string;
  eventId?: string;
  showEditButton: boolean;
}
