export const DRAWER_TABS = {
  SESSION_DETAILS: 'sessionDetails',
  INSTRUCTOR_FACILITATOR: 'instructorFacilitator',
  IN_SESSION_ACTIVITY: 'liveChallenges',
  ENROLLMENT: 'enrollment',
  ATTENDANCE: 'attendance',
  REMINDERS: 'reminders',
};

export const DRAWER_INSTRUCTOR_FACILITATOR_TABS = {
  INSTRUCTOR_SECTION: 'instructorSection',
  FACILITATOR_SECTION: 'facilitatorSection',
};

export const DRAWER_TAB_TITLES = {
  [DRAWER_TABS.SESSION_DETAILS]: 'Session details',
  [DRAWER_TABS.INSTRUCTOR_FACILITATOR]: 'Instructors and facilitators',
  [DRAWER_TABS.IN_SESSION_ACTIVITY]: 'In-session activity',
  [DRAWER_TABS.ENROLLMENT]: 'Enrollment',
  [DRAWER_TABS.ATTENDANCE]: 'Attendance and completion',
  [DRAWER_TABS.REMINDERS]: 'Reminders',
};

export const TOAST_WRAPPER_ID = 'sessionEditTaostWrapperId';

export const errorFieldsTabsMapping = {
  [DRAWER_TABS.SESSION_DETAILS]: [
    'sessionName',
    'cohostEmail',
    'sessionStartDate',
    'sessionStartTime',
    'sessionEndDate',
    'sessionEndTime',
  ],
  [DRAWER_TABS.INSTRUCTOR_FACILITATOR]: ['instructorName', 'instructorEmail', 'facilitatorEmail'],
  [DRAWER_TABS.ENROLLMENT]: [
    'enrollmentFreezeNumDays',
    'enrollmentFreezeDate',
    'enrollmentFreezeTime',
  ],
  [DRAWER_TABS.ATTENDANCE]: ['checkinTimeBeforeSession', 'checkinTimeAfterSession'],
  [DRAWER_TABS.REMINDERS]: ['reminder'],
};

export const errorFieldsTabsMappingForInstructorFacilitator = {
  [DRAWER_INSTRUCTOR_FACILITATOR_TABS.INSTRUCTOR_SECTION]: ['instructorName', 'instructorEmail'],
  [DRAWER_INSTRUCTOR_FACILITATOR_TABS.FACILITATOR_SECTION]: ['facilitatorEmail'],
};
