import type { TCategoryConfig, TCategoryTags } from './typeDefs';

export const checkIfTagSelected = (tagId: string, selectedTags: ReadonlyArray<string>) =>
  selectedTags.indexOf(tagId) !== -1;

export const localSearch = (categoryConfig: TCategoryConfig | null, query: string) => {
  if (!categoryConfig) {
    return null;
  }
  let result = categoryConfig
    .map(category => {
      if (!category) {
        return {
          id: '',
          name: '',
          tags: [],
        };
      }
      let searchedCategory: TCategoryTags = {
        id: category.id,
        name: category.name,
        tags: [],
      };
      const lowerCaseQuery = query.toLowerCase(),
        lowerCaseCategoryName = searchedCategory.name.toLowerCase(),
        nameMatch = lowerCaseCategoryName.includes(lowerCaseQuery);
      searchedCategory.tags = nameMatch
        ? [...category.tags]
        : category.tags.filter(tag => tag && tag.name.toLowerCase().includes(lowerCaseQuery));
      return searchedCategory;
    })
    .filter(category => category.tags.length);
  return result;
};

export const getCategoryOptions = (categoryConfig: any) => {
  const categoryOptions: Array<{ readonly key: string; readonly content: string }> = [];
  if (categoryConfig && categoryConfig.length > 0) {
    categoryConfig.forEach((category: any) => {
      if (category) {
        categoryOptions.push({
          key: category.id,
          content: category.name,
        });
      }
    });
  }

  return categoryOptions;
};
