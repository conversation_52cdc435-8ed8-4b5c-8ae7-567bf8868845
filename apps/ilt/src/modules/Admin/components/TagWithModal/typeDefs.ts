export type TTagWithModalProps = {
  selectedTags?: ReadonlyArray<string>;
  categoryConfig?: ReadonlyArray<{
    readonly id: string;
    readonly description: string;
    readonly name: string;
    tags: ReadonlyArray<{
      readonly id: string;
      readonly name: string;
    } | null> | null;
  } | null> | null;
  suggestedTags?: ReadonlyArray<{
    readonly id: string;
    readonly name: string;
  } | null> | null;
  visible: boolean;
  onCancel: () => void;
  onApply: () => void;
  onTagClose: (a: string) => void;
  children?: React.ReactNode;
  newTagsList: Array<TTag>;
  deletedTagsList: Array<string>;
  onTagSelect?: (a: boolean, b: string) => void;
  onTagCreate?: (a: string, b: string) => void;
  creating?: boolean;
  canCreateTags: boolean;
};

export type TTagsCategoryProps = {
  selectedTags: ReadonlyArray<string>;
  canCreateTags: boolean;
  categoryConfig: ReadonlyArray<{
    readonly id: string;
    readonly description: string;
    readonly name: string;
    tags: ReadonlyArray<{
      readonly id: string;
      readonly name: string;
    } | null> | null;
  } | null> | null;
  suggestedTags: ReadonlyArray<{
    readonly id: string;
    readonly name: string;
  } | null> | null;
  onTagSelect: (a: boolean, c: string) => void;
  onTagCreate: (a: string, b: string) => void;
  creating: boolean;
};

export type TModalFooterProps = {
  onCancel: () => void;
  onOk: () => void;
  newTagsList: Array<TTag>;
  onTagClose: (a: string) => void;
  applyDisabled: boolean;
};

export type TSuggestedTag = {
  readonly id: string;
  readonly name: string;
};

export type THighlightSectionCallbackTypes = {
  selectedTags: ReadonlyArray<number>;
  onTagSelect: (selected: number, title: string, id: string) => void;
};

export type TTag = {
  id: string;
  name: string;
};

export type TClosableTagProps = {
  tag: TTag;
  onTagClose: (a: string) => void;
};

export type TCategoryConfig = ReadonlyArray<{
  readonly id: string;
  readonly description: string;
  readonly name: string;
  tags: ReadonlyArray<{
    readonly id: string;
    readonly name: string;
  } | null>;
} | null> | null;

export type TCategoryTags = {
  readonly id: string;
  readonly name: string;
  tags: Array<{
    readonly id: string;
    readonly name: string;
  } | null>;
};
