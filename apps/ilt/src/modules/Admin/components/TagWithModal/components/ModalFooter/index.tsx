import { injectIntl } from 'react-intl';

import Button from '@mindtickle/button';

import messages from '../../messages';
import {
  StyledModalFooter,
  StyledAppliedTagsWrapper,
  StyledAppliedTagsButtonWrapper,
} from '../../styles';
import ClosableTag from '../ClosableTag';

import type { TModalFooterProps, TTag } from '../../typeDefs';
import type { InjectedIntlProps } from 'react-intl';

const ModalFooter = ({
  onCancel,
  onOk,
  onTagClose,
  newTagsList,
  applyDisabled,
  intl,
}: TModalFooterProps & InjectedIntlProps) => {
  const tagsLength = newTagsList.length;
  return (
    <StyledModalFooter>
      <StyledAppliedTagsWrapper>
        {tagsLength > 0 && (
          <>
            <span className="apply-tags-count">
              {intl.formatMessage(tagsLength > 1 ? messages.SELECTED_TAGS : messages.SELECTED_TAG, {
                tagsLength,
              })}
            </span>
            <div className="applied-tags-list-added">
              {newTagsList.map((tag: TTag) => (
                <ClosableTag key={tag.id} tag={tag} onTagClose={onTagClose} />
              ))}
            </div>
          </>
        )}
      </StyledAppliedTagsWrapper>
      <StyledAppliedTagsButtonWrapper>
        <Button type="secondary" onClick={onCancel}>
          {intl.formatMessage(messages.CANCEL)}
        </Button>
        <Button type="primary" onClick={onOk} disabled={applyDisabled}>
          {intl.formatMessage(messages.APPLY_TAGS)}
        </Button>
      </StyledAppliedTagsButtonWrapper>
    </StyledModalFooter>
  );
};

export default injectIntl(ModalFooter);
