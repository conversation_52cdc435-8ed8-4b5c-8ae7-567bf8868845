import { injectIntl } from 'react-intl';

import Modal from '@mindtickle/modal';
import { noop } from '@mindtickle/utils';

import ModalFooter from './components/ModalFooter';
import TagsCategory from './components/TagsCategory';
import { MODAL_PROPS } from './constants';
import messages from './messages';
import { TagsWrapper } from './styles';

import type { TTagWithModalProps } from './typeDefs';
import type { InjectedIntlProps } from 'react-intl';

const TagWithModal = (props: TTagWithModalProps & InjectedIntlProps) => {
  const {
    intl,
    selectedTags = [],
    newTagsList = [],
    deletedTagsList = [],
    categoryConfig = [],
    suggestedTags = [],
    onTagClose,
    visible,
    onCancel: handleCancelButton,
    onApply,
    children,
    onTagSelect = noop,
    onTagCreate = noop,
    creating = false,
    canCreateTags,
  } = props;
  const applyDisabled = !(
    (deletedTagsList && deletedTagsList.length > 0) ||
    (newTagsList && newTagsList.length > 0)
  );
  const handleApplyClick = () => {
    if (!applyDisabled) {
      onApply();
    }
  };

  return (
    <TagsWrapper>
      <Modal
        visible={visible}
        onCancel={handleCancelButton}
        onOk={handleCancelButton}
        size="large"
        title={intl.formatMessage(messages.APPLY_TAGS)}
        align="top"
        footer={
          <ModalFooter
            onCancel={handleCancelButton}
            onOk={handleApplyClick}
            newTagsList={newTagsList}
            onTagClose={onTagClose}
            applyDisabled={applyDisabled}
          />
        }
        {...MODAL_PROPS}
      >
        {children || (
          <TagsCategory
            selectedTags={selectedTags}
            categoryConfig={categoryConfig}
            suggestedTags={suggestedTags}
            onTagSelect={onTagSelect}
            onTagCreate={onTagCreate}
            creating={creating}
            canCreateTags={canCreateTags}
          />
        )}
      </Modal>
    </TagsWrapper>
  );
};

export default injectIntl(TagWithModal);
