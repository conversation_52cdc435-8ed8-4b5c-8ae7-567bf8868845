import styled from 'styled-components';

import { mixins, tokens } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS } from '~/config/constants';

export const StyledTagsQuestion = styled.div`
  flex: 20%;

  .createTag {
    width: 541px;
    margin-top: 0px;

    .${THEME_PREFIX_CLS}-tag {
      margin-top: 6px;
      position: relative;
      top: 6px;
    }
  }

  .${THEME_PREFIX_CLS}-select {
    margin-left: 0px;
    margin-right: 8px;
  }

  .question-index {
    ${mixins.blackText()};
    font-weight: 600;
  }

  .suggested-tags-wrapper {
    padding: 16px;
    background: ${tokens.bgTokens.COLOR_BG_TERTIARY};
  }

  .suggested-tags-heading {
    ${mixins.smallGreyLink()};
    margin-bottom: 15px;
    text-transform: lowercase;
    display: inline-block;
    &::first-letter {
      text-transform: uppercase;
    }
  }

  .selected-tags {
    .category-name {
      ${mixins.truncate('160px')};
    }

    .suggested-tags:not(.${THEME_PREFIX_CLS}-tag-checkable-checked) {
      background-color: ${tokens.bgTokens.COLOR_BG_DEFAULT};
    }
  }

  .${THEME_PREFIX_CLS}-tooltip-inner {
    white-space: normal;
  }

  .${THEME_PREFIX_CLS}-tag-checkable {
    margin-bottom: 12px;
  }

  .category-container {
    overflow-y: auto;
    max-height: calc(100vh - 334px);
    padding-bottom: 12px;
    transition: padding 0.8s linear 0s;
    min-height: 300px;
  }

  &&& {
    .info-tooltip {
      margin: 0 8px 0px 0;
      font-size: 12px;
      color: ${tokens.iconTokens.COLOR_ICON_SECONDARY};
    }
    .category-header {
      display: inline-block;
      margin-right: 10px;
      ${mixins.smallBlackText()};
      text-transform: lowercase;

      &::first-letter {
        text-transform: uppercase;
      }
    }
  }
`;
