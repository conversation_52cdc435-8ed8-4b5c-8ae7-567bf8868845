import styled from 'styled-components';

import Icon from '@mindtickle/icon';
import { tokens, theme, mixins } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS } from '~/config/constants';

const { fontSizes, fontWeight, lineHeight } = theme;

export const TagsWrapper = styled.div`
  display: flex;
  align-items: center;
`;

export const TagsText = styled.div`
  display: flex;
  color: ${tokens.textTokens.COLOR_TEXT_SECONDARY};
  margin-right: 8px;
  font-style: normal;
  font-weight: ${fontWeight.SEMIBOLD};
  font-size: ${fontSizes.SMALLTEXT};
  line-height: ${lineHeight.LABEL};
`;

export const StyleTagsBox = styled.div`
  background: ${tokens.bgTokens.COLOR_BG_TERTIARY};
  border: 1px solid ${tokens.borderTokens.COLOR_BORDER_SECONDARY};
  box-sizing: border-box;
  border-radius: 24px;
  padding: 2px 12px;
  margin-right: 12px;
  font-size: ${fontSizes.SMALLTEXT};
  height: 24px;
`;

export const StyledAddIcon = styled(Icon)`
  cursor: pointer;
  background: ${tokens.bgTokens.COLOR_BG_DEFAULT};
  border: 1px solid ${tokens.borderTokens.COLOR_BORDER_DEFAULT};
  box-sizing: border-box;
  border-radius: 24px;
  padding: 6px 6px;
  margin-right: 24px;
  font-size: 10px;
  color: ${tokens.textTokens.COLOR_TEXT_SECONDARY};
`;

export const StyledModalFooter = styled.div`
  ${mixins.clearfix()};
`;

export const StyledAppliedTagsWrapper = styled.div`
  text-align: left;
  float: left;
  width: 74%;

  .apply-tags-count {
    ${mixins.smallGreyLink()};
    float: left;
    line-height: 26px;
    margin-right: 10px;
    width: 110px;
  }

  .applied-tags-list-added {
    max-height: 100px;
    overflow-y: auto;
  }

  && {
    .${THEME_PREFIX_CLS}-tag {
      background-color: ${tokens.bgTokens.COLOR_BG_ACCENT_SELECTED};
      border-color: ${tokens.borderTokens.COLOR_BORDER_ACCENT};
      color: ${tokens.textTokens.COLOR_TEXT_ACCENT};
    }
  }
`;

export const StyledAppliedTagsButtonWrapper = styled.div`
  float: right;
`;
