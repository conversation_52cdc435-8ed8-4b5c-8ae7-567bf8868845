import PropTypes from 'prop-types';

import { Defaults } from '~/mt-ui-core/config/env.config';

export const routesPropTypes = {
  setActiveTab: PropTypes.func.isRequired,
  stages: PropTypes.object,
  seriesData: PropTypes.shape({
    id: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    permissions: PropTypes.object,
    seriesLevelMailSettings: PropTypes.object,
    sequentiallyLockedSeries: PropTypes.bool.isRequired,
  }).isRequired,
  moduleData: PropTypes.shape({
    id: PropTypes.string.isRequired,
    type: PropTypes.any,
    name: PropTypes.string.isRequired,
    isPublished: PropTypes.bool.isRequired,
    mappedSeries: PropTypes.array.isRequired,
    moduleRelevance: PropTypes.string,
  }).isRequired,
  companyData: PropTypes.shape({
    id: PropTypes.string.isRequired,
    url: PropTypes.string.isRequired,
  }).isRequired,
  companySettings: PropTypes.shape({
    showILTWithPermission: PropTypes.bool.isRequired,
    attendanceSettings: PropTypes.object.isRequired,
    enrolmentThresholdEmailSettings: PropTypes.object.isRequired,
  }).isRequired,
  integrations: PropTypes.object,
  userData: PropTypes.shape({
    permissions: PropTypes.object.isRequired,
    isSiteOwner: PropTypes.bool.isRequired,
    taggingEnabled: PropTypes.bool.isRequired,
    tempFeatureConfig: PropTypes.object.isRequired,
  }).isRequired,
  enabledFeatures: PropTypes.shape({
    moduleRelevanceEnabled: PropTypes.bool.isRequired,
    isUIDEnabled: PropTypes.bool.isRequired,
  }).isRequired,
  actions: PropTypes.shape({
    onSettingUpdate: PropTypes.func.isRequired,
    onPublish: PropTypes.func.isRequired,
    moduleUpdater: PropTypes.func.isRequired,
    learnersCountUpdater: PropTypes.func.isRequired,
    onInvite: PropTypes.func.isRequired,
  }),
};

export const routesDefaultProps = {
  stages: Defaults.moduleLifeCycle,
};
