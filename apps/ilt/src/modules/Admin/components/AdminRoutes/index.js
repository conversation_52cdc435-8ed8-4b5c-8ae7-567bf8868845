import { Routes, Route, Navigate } from 'react-router-dom';
import { LIFECYCLE_STAGES } from 'ui_shell/GlobalConstants';
import { haveStageAccess } from 'ui_shell/Permissions';

import PageTitle from '~/components/PageTitle';
import ErrorBoundary from '~/modules/Admin/components/ErrorBoundary';
import Publish from '~/modules/Admin/components/PublishWrapper';
import Settings from '~/modules/Admin/containers/Settings';

import ModuleRoutes from '../../config/routes';
import { ActiveNavTabSetter, TAB_TYPES } from '../NavBarTabs';
import PrivateRoute from '../PrivateRoute';
import SessionsRoute from '../SessionsRoute';

import { settingsConstProp } from './constants';
import { routesPropTypes, routesDefaultProps } from './propTypes';

const {
  [LIFECYCLE_STAGES.BUILD]: buildPath,
  [LIFECYCLE_STAGES.PUBLISH]: publishPath,
  [LIFECYCLE_STAGES.SETTINGS]: settingsPath,
} = ModuleRoutes.lifecycle;

function AdminRoutes(props) {
  const {
    setActiveTab,
    moduleData,
    seriesData,
    userData,
    companyData,
    actions,
    baseUrl,
    stages,
    enabledFeatures,
    integrations,
    companySettings,
  } = props;
  const haveAccess = stage =>
    haveStageAccess(stage, userData.permissions, seriesData.permissions, {
      isSiteOwner: userData.isSiteOwner,
    });
  const getProps = () => ({
    [LIFECYCLE_STAGES.BUILD]: {
      moduleData,
      seriesData,
      companyData,
      enabledFeatures,
      extraConfig: userData.tempFeatureConfig,
    },
    [LIFECYCLE_STAGES.PUBLISH]: {
      isPublished: moduleData.isPublished,
      onPublish: actions.onPublish,
      showInviteOptions: false,
      mappedSeries: moduleData.mappedSeries,
      defaultInviteOption: 'INVITE_ALL',
      inviteTabName: 'Sessions',
    },
    [LIFECYCLE_STAGES.INVITE]: {
      moduleData,
      seriesData,
      isSiteOwner: userData.isSiteOwner,
      mappedSeries: moduleData.mappedSeries,
      taggingEnabled: userData.taggingEnabled,
      globalPermissions: userData.permissions,
      onSettingUpdate: actions.onSettingUpdate,
      onLearnerRemoval: actions.learnersCountUpdater,
      seriesPermissions: seriesData.permissions,
      enabledFeatures,
      defaultModuleRelevance: moduleData.moduleRelevance,
      extraConfig: userData.tempFeatureConfig,
      onInvite: actions.onInvite,
    },
    [LIFECYCLE_STAGES.SETTINGS]: {
      moduleData,
      companySettings,
      moduleUpdater: actions.moduleUpdater,
      onSettingUpdate: actions.onSettingUpdate,
      taggingEnabled: userData.taggingEnabled,
      globalPermissions: userData.permissions,
      seriesLevelMailSettings: seriesData.seriesLevelMailSettings,
      sequentiallyLockedSeries: seriesData.sequentiallyLockedSeries,
      tabDisplayName: settingsConstProp.TABLE_DISPLAY_NAME,
      extraConfig: userData.tempFeatureConfig,
      enableSettings: settingsConstProp.ENABLE_SETTINGS,
    },
    COMMON_PROPS: {
      baseUrl: baseUrl,
      stages: stages,
      stageRoutes: ModuleRoutes.lifecycle,
      moduleId: moduleData.id,
      seriesId: seriesData.id,
      companyId: companyData.id,
      moduleName: moduleData.name,
      moduleType: moduleData.type,
      tabType: moduleData.type,
      isPublished: moduleData.isPublished,
      integrations,
    },
  });

  const customProps = getProps();
  const tabType = moduleData.type;

  return (
    <Routes>
      <Route
        path={`${buildPath}/*`}
        element={
          <SessionsRoute
            customProps={customProps}
            haveAccess={haveAccess}
            setActiveTab={setActiveTab}
          />
        }
      />
      <Route
        path={`${settingsPath}/*`}
        element={
          <PrivateRoute authenticated={haveAccess(LIFECYCLE_STAGES.SETTINGS)}>
            <ActiveNavTabSetter tabKey={TAB_TYPES.SETTINGS} setActiveTab={setActiveTab} />
            <PageTitle tabName={stages[LIFECYCLE_STAGES.SETTINGS]} tabType={tabType} />
            {/** TODO: to implement the props according to need */}
            <ErrorBoundary>
              <Settings
                moduleData={moduleData}
                companySettings={companySettings}
                moduleUpdater={actions.moduleUpdater}
              />
            </ErrorBoundary>
          </PrivateRoute>
        }
      />
      <Route
        path={`${publishPath}/*`}
        element={
          <PrivateRoute authenticated={haveAccess(LIFECYCLE_STAGES.PUBLISH)}>
            <ActiveNavTabSetter tabKey={TAB_TYPES.PUBLISH} setActiveTab={setActiveTab} />
            <PageTitle tabName={stages[LIFECYCLE_STAGES.PUBLISH]} tabType={tabType} />
            <ErrorBoundary>
              <Publish
                onPublish={actions.onPublish}
                moduleId={moduleData.id}
                seriesId={seriesData.id}
                companyId={companyData.id}
                moduleType={moduleData.type}
                isPublished={moduleData.isPublished}
                defaultInviteOption={'INVITE_ALL'}
                showInviteOptions={false}
              />
            </ErrorBoundary>
          </PrivateRoute>
        }
      />
      {/* <Redirect to={`${match.url}${buildPath}`} /> */}
      <Route path="*" element={<Navigate to={`${buildPath}`} replace />} />
    </Routes>
  );
}

AdminRoutes.propTypes = routesPropTypes;
AdminRoutes.defaultProps = routesDefaultProps;

export default AdminRoutes;
