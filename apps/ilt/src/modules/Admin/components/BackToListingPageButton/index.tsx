import { Link } from 'react-router-dom';

import Icon, { ICON_MAP } from '@mindtickle/icon';

import { BackButtonRow } from './styles';

import type { TProps } from './typeDefs';

export default function BackToEntityListingPage({ link, buttonText = 'Back' }: TProps) {
  return (
    <BackButtonRow>
      <Link to={link} className={'back-link-wrapper'}>
        <Icon type={ICON_MAP.LEFT_ARROW} className={'back-icon'} />
        <div className="back-row-text">{buttonText}</div>
      </Link>
    </BackButtonRow>
  );
}
