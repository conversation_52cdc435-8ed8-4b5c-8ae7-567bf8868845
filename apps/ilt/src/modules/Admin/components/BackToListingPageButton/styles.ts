import styled from 'styled-components';

import { tokens, mixins } from '@mindtickle/styles/lib';

export const BackButtonRow = styled.div`
  background-color: ${tokens.bgTokens.COLOR_BG_DEFAULT};
  padding: 16px 24px;
  display: flex;
  .back-link-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  .back-icon {
    color: ${tokens.iconTokens.COLOR_ICON_DEFAULT};
  }
  .back-row-text {
    ${mixins.smallDarkLink()};
  }
`;
