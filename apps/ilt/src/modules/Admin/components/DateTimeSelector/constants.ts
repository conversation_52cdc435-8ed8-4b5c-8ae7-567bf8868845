export const Format = {
  date: 'MMM DD, YYYY',
  time: 'hh:mm a',
};
export const TYPES = {
  DATE: 'date',
  TIME: 'time',
};
export const HOURS = [
  1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24,
];
export const MINUTE_STEP = 5;
export const MINUTES = (function (minuteStep) {
  let MINUTES_IN_HOUR = 60;
  let minutes = [];
  for (let i = 0; i < MINUTES_IN_HOUR; i += minuteStep) {
    minutes.push(i);
  }
  return minutes;
})(MINUTE_STEP);
export const BUFFER_MINUTES = 0;
