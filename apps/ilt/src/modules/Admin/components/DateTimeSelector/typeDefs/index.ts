export interface TDateTimeSelector {
  value?: number;
  ok?: (...args: any[]) => any;
  cancel?: (...args: any[]) => any;
  withBtns?: boolean;
  disableDateRange?: boolean;
  disableTimeRange?: boolean;
  min?: number;
  max?: number;
  disable?: boolean;
  disableTimeEdit?: boolean;
  id?: string;
  errorStatus?: boolean;
  datePickerWrapperClassName?: string;
}

export interface TDatePicker {
  id?: string;
  errorStatus?: boolean;
  value: number;
  disabledDate: any;
  select: Function;
  isDateDisabled?: boolean;
  wrapperClassName?: string;
}

export interface TTimePicker {
  id?: string;
  errorStatus?: boolean;
  value: number;
  disabledHours: any;
  disabledMinutes: any;
  select: Function;
  isTimeDisabled?: boolean;
}
