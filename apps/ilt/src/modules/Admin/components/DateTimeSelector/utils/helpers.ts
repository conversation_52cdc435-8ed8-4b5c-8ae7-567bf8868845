import moment from 'moment';

import { HOURS, MINUTES } from '../constants';

export const getDateStart = function (value: number) {
  return +moment(value).startOf('day');
};
export const getDayTime = function (value: number) {
  let current = +moment(value);
  let dayStart = +moment(value).startOf('day');
  return current - dayStart;
};

export const calculateFilteredHoursRange = (date: any, min: number, max: number) => {
  let filteredHours = [];

  if (date === getDateStart(+moment(min)) && date === getDateStart(+moment(max))) {
    filteredHours = HOURS.filter(
      hour =>
        hour < moment(min).hour() ||
        (hour === moment(min).hour() && moment(min).minute() > 55) ||
        hour > moment(max).hour()
    );
    filteredHours.pop();
    if (moment(min).hour() > 0) filteredHours.push(0);
    return filteredHours;
  }
  if (date === getDateStart(+moment(min))) {
    filteredHours = HOURS.filter(
      hour =>
        hour < moment(min).hour() || (hour === moment(min).hour() && moment(min).minute() > 55)
    );
    if (moment(min).hour() > 0) filteredHours.push(0);
    return filteredHours;
  }
  if (date === getDateStart(+moment(max))) {
    filteredHours = HOURS.filter(hour => hour > moment(max).hour());
    filteredHours.pop();
    return filteredHours;
  }
};

export const calculateFilteredMinutesRange = (
  date: any,
  min?: number,
  max?: number,
  hour?: number
) => {
  const startMomentObj = min ? moment(min) : moment();
  const endMomentObj = max ? moment(max) : moment();
  let filteredMinutes = [];

  if (
    date === getDateStart(+startMomentObj) &&
    date === getDateStart(+endMomentObj) &&
    hour === startMomentObj.hour() &&
    hour === endMomentObj.hour()
  ) {
    filteredMinutes = MINUTES.filter(
      minute =>
        (hour === moment(min).hour() && minute < startMomentObj.minute()) ||
        (hour === moment(max).hour() && minute > endMomentObj.minute())
    );
    return filteredMinutes;
  }
  if (date === getDateStart(+startMomentObj) && hour === startMomentObj.hour()) {
    filteredMinutes = MINUTES.filter(minute => minute < startMomentObj.minute());
    return filteredMinutes;
  }
  if (date === getDateStart(+endMomentObj) && hour === endMomentObj.hour()) {
    filteredMinutes = MINUTES.filter(minute => minute > endMomentObj.minute());
    return filteredMinutes;
  }
};

export const disableDateRanges = ({ startDate, endDate }: { startDate?: Date; endDate?: Date }) =>
  function disabledDate(current: moment.Moment) {
    let startCheck = true;
    let endCheck = true;
    if (startDate) {
      startCheck = current && current < moment(startDate).startOf('day');
    }
    if (endDate) {
      endCheck = current && current > moment(endDate).add(1, 'days').startOf('day');
    }
    return (startDate && startCheck) || (endDate && endCheck);
  };

export const checkDateHour = (date: any, min?: number, max?: number) => {
  if (min && max) {
    return date === getDateStart(+moment(min)) || date === getDateStart(+moment(max));
  }
  if (min) {
    return date === getDateStart(+moment(min));
  }
  if (max) {
    return date === getDateStart(+moment(max));
  }
};

export const checkDateMinutes = (date: any, hour: number, min?: number, max?: number) => {
  const startMomentObj = min ? moment(min) : moment();
  const endMomentObj = max ? moment(max) : moment();

  if (min && max) {
    return (
      (date === getDateStart(+startMomentObj) && hour === startMomentObj.hour()) ||
      (date === getDateStart(+endMomentObj) && hour === endMomentObj.hour())
    );
  }
  if (min) {
    return date === getDateStart(+startMomentObj) && hour === startMomentObj.hour();
  }
  if (max) {
    return date === getDateStart(+endMomentObj) && hour === endMomentObj.hour();
  }
};
