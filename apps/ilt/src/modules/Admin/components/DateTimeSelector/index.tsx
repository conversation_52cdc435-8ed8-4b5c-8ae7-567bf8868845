import { useEffect, useRef, useState } from 'react';

import moment from 'moment';

import Button from '@mindtickle/button';
import Grid from '@mindtickle/grid';
import Icon from '@mindtickle/icon';
import { noop } from '@mindtickle/utils';

import DatePickerWrapper from './components/DatePickerWrapper';
import TimePickerWrapper from './components/TimePickerWrapper';
import { TYPES, HOURS, MINUTES } from './constants';
import { StyledDateTimeSelectorWrapper } from './styles';
import {
  calculateFilteredHoursRange,
  calculateFilteredMinutesRange,
  checkDateHour,
  checkDateMinutes,
  disableDateRanges,
  getDateStart,
  getDayTime,
} from './utils/helpers';

import type { TDateTimeSelector } from './typeDefs';
const { Row, Col } = Grid;

const DateTimeSelector = (props: TDateTimeSelector) => {
  const {
    cancel,
    withBtns,
    disableTimeEdit,
    disableDateRange,
    disableTimeRange,
    min,
    max,
    id,
    errorStatus,
  } = props;
  const getInitialState = (value: any) => ({
    [TYPES.DATE]: getDateStart(value),
    [TYPES.TIME]: getDayTime(value),
  });
  const initialState = getInitialState(props.value);
  const [date, setDate] = useState(initialState[TYPES.DATE]);
  const [time, setTime] = useState(initialState[TYPES.TIME]);
  const dateTime = date + time;
  const currentPropsRef = useRef(props);
  const prevPropsRef = useRef(props);

  useEffect(() => {
    // Set the current props to the prev props ref
    prevPropsRef.current = currentPropsRef.current;
    // Update the current props ref
    currentPropsRef.current = props;
  });

  useEffect(() => {
    const prevProps = prevPropsRef.current;
    if (prevProps.value !== props.value) {
      const initialState = getInitialState(props.value);
      setDate(initialState[TYPES.DATE]);
      setTime(initialState[TYPES.TIME]);
    }
  }, [props.value]);

  const select = (type: string, value: number) => {
    const { withBtns, ok } = props;
    let updatedDate = date;
    let updatedTime = time;
    switch (type) {
      case TYPES.DATE:
        updatedDate = getDateStart(value);
        setDate(updatedDate);
        break;
      case TYPES.TIME:
        updatedTime = getDayTime(value);
        setTime(updatedTime);
        break;
      default:
    }

    !withBtns && ok?.(updatedDate + updatedTime);
  };

  const disabledDate = (date: any) => {
    const { min, disable } = props;
    const current = disable ? moment(min) : moment();
    return date && date < current.startOf('day');
  };

  const disabledHours = () => {
    const { min, disable } = props;
    const current = disable ? moment(min) : moment();
    if (date === getDateStart(+current) && disable) {
      const filteredHours = HOURS.filter(
        hour => hour < current.hour() || (hour === current.hour() && current.minute() > 45)
      );
      filteredHours.push(0);
      return filteredHours;
    }
    return [];
  };

  const disabledHoursRange = () => {
    const { min, max, disable } = props;
    let filteredHours: any[] = [];

    if (checkDateHour(date, min, max) && disable) {
      if (min && max) {
        return calculateFilteredHoursRange(date, min, max);
      }

      if (min) {
        filteredHours = HOURS.filter(
          hour =>
            hour < moment(min).hour() || (hour === moment(min).hour() && moment(min).minute() > 55)
        );
        if (moment(min).hour() > 0) filteredHours.push(0);
      }

      if (max) {
        filteredHours = HOURS.filter(hour => hour > moment(max).hour());
        filteredHours.pop();
      }
    }

    return filteredHours;
  };

  const disabledMinutes = (hour: number) => {
    const { min, disable } = props;
    const momentObj = disable ? moment(min) : moment();
    if (date === getDateStart(+momentObj) && hour === momentObj.hour() && disable) {
      const filteredMinutes = MINUTES.filter(minute => minute < momentObj.minute());
      return filteredMinutes;
    }
    return [];
  };

  const disabledMinutesRange = (hour: number) => {
    const { min, max, disable } = props;
    const startMomentObj = min ? moment(min) : moment();
    const endMomentObj = max ? moment(max) : moment();

    let filteredMinutes: any[] = [];

    if (checkDateMinutes(date, hour, min, max) && disable) {
      if (min && max) {
        return calculateFilteredMinutesRange(date, min, max, hour);
      }

      if (min) {
        filteredMinutes = MINUTES.filter(minute => minute < startMomentObj.minute());
      }

      if (max) {
        filteredMinutes = MINUTES.filter(minute => minute > endMomentObj.minute());
      }
    }
    return filteredMinutes;
  };

  const onOkBtn = () => {
    const timestamp = date + time;
    props.ok?.(timestamp);
  };

  return (
    <StyledDateTimeSelectorWrapper>
      <Row align="middle">
        <Col span={13}>
          <DatePickerWrapper
            id={id}
            value={dateTime}
            disabledDate={
              disableDateRange
                ? disableDateRanges({
                    endDate: max ? new Date(max) : undefined,
                    startDate: min ? new Date(min) : undefined,
                  })
                : disabledDate
            }
            select={select}
            isDateDisabled={disableTimeEdit}
            errorStatus={errorStatus}
            wrapperClassName={props.datePickerWrapperClassName}
          />
        </Col>
        <Col span={10}>
          <TimePickerWrapper
            id={id}
            value={dateTime}
            disabledHours={disableTimeRange ? disabledHoursRange : disabledHours}
            disabledMinutes={disableTimeRange ? disabledMinutesRange : disabledMinutes}
            select={select}
            isTimeDisabled={disableTimeEdit}
            errorStatus={errorStatus}
          />
        </Col>
      </Row>

      {withBtns && !disableTimeEdit && (
        <span className="dateTime_roundBtns">
          <Button name="ok" type="PrimaryRoundBtn" onClick={onOkBtn} className="marginL5">
            <Icon type="tick" />
          </Button>
          <Button name="cancel" type="DefaultRoundBtn" className="marginL5" onClick={cancel}>
            <Icon type="close" />
          </Button>
        </span>
      )}
    </StyledDateTimeSelectorWrapper>
  );
};

DateTimeSelector.defaultProps = {
  value: Date.now(),
  withBtns: true,
  ok: noop,
  cancel: noop,
};

export default DateTimeSelector;
