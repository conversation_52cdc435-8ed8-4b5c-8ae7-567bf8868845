import moment from 'moment';

import { DatePicker } from '@mindtickle/date-filter';
import Form from '@mindtickle/form';

import { Format, TYPES } from '../../constants';

import { StyledDatePicker } from './styles';

import type { TDatePicker } from '../../typeDefs';

const DatePickerWrapper = (props: TDatePicker) => {
  const { id, errorStatus, value, disabledDate, select, isDateDisabled } = props;
  const datePickerChildProps = {
    defaultValue: value ? moment(value) : undefined,
    value: value ? moment(value) : undefined,
    format: Format[TYPES.DATE as keyof typeof Format],
    disabledDate: disabledDate,
    showToday: false,
    onChange: (value: any) => select(TYPES.DATE, +value),
    disabled: isDateDisabled,
    showTime: false,
    allowClear: false,
    className: 'datePicker',
    wrapperClassName: props.wrapperClassName || 'date-picker-wrapper-class',
    ...(errorStatus && { status: 'error' }),
  };
  const dateLabel =
    id === 'sessionStart' ? 'Start date' : id === 'sessionEnd' ? 'End date' : 'Date';

  return (
    <StyledDatePicker errorStatus={errorStatus}>
      <Form.Item
        name={`${id}Date`}
        label={dateLabel}
        labelCol={{ span: 24 }}
        wrapperCol={{ span: 24 }}
        dependencies={[`${id}Time`]}
        rules={[
          {
            validator: (_: any, value: any) => {
              if (errorStatus) {
                return Promise.reject();
              }
              return Promise.resolve();
            },
          },
        ]}
      >
        <DatePicker {...datePickerChildProps} />
      </Form.Item>
    </StyledDatePicker>
  );
};

export default DatePickerWrapper;
