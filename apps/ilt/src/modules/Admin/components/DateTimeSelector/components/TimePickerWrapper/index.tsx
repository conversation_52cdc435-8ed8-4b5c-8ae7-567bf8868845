import moment from 'moment';

import Form from '@mindtickle/form';
import TimePicker from '@mindtickle/time-picker';

import { Format, MINUTE_STEP, TYPES } from '../../constants';

import { StyledTimePicker } from './styles';

import type { TTimePicker } from '../../typeDefs';

const TimePickerWrapper = (props: TTimePicker) => {
  const { id, errorStatus, value, disabledHours, disabledMinutes, select, isTimeDisabled } = props;

  const timePickerChildProps = {
    allowEmpty: false,
    format: Format[TYPES.TIME as keyof typeof Format],
    value: value ? moment(value) : undefined,
    defaultValue: value ? moment(value) : undefined,
    hideDisabledOptions: true,
    disabledHours: disabledHours,
    disabledMinutes: disabledMinutes,
    minuteStep: MINUTE_STEP,
    onChange: (value: any) => select(TYPES.TIME, +value),
    disabled: isTimeDisabled,
    allowClear: false,
    showNow: false,
    className: 'timePicker',
    ...(errorStatus && { status: 'error' }),
  };
  const timeLabel =
    id === 'sessionStart' ? 'Start time' : id === 'sessionEnd' ? 'End time' : 'Time';

  return (
    <StyledTimePicker errorStatus={errorStatus}>
      <Form.Item
        name={`${id}Time`}
        label={timeLabel}
        labelCol={{ span: 24 }}
        wrapperCol={{ span: 24 }}
        dependencies={[`${id}Date`]}
        rules={[
          {
            validator: (_: any, value: any) => {
              if (errorStatus) {
                return Promise.reject();
              }
              return Promise.resolve();
            },
          },
        ]}
      >
        <TimePicker {...timePickerChildProps} />
      </Form.Item>
    </StyledTimePicker>
  );
};

export default TimePickerWrapper;
