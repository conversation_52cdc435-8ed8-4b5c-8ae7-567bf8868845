import styled, { css } from 'styled-components';

import { tokens } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS } from '~/config/constants';

export const StyledDatePicker = styled.div<{ errorStatus?: boolean }>`
  .date-picker-wrapper-class {
    .${THEME_PREFIX_CLS}-picker-cell-inner {
      font-weight: normal;
    }
    .${THEME_PREFIX_CLS}-picker {
      ${props =>
        props.errorStatus &&
        css`
          &,
          &:hover,
          &.${THEME_PREFIX_CLS}-picker-disabled {
            border-color: ${tokens.borderTokens.COLOR_BORDER_DANGER};
          }
        `}
    }
  }
`;
