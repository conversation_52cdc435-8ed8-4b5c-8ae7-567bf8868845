import styled, { css } from 'styled-components';

import { tokens } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS } from '~/config/constants';

export const StyledTimePicker = styled.div<{ errorStatus?: boolean }>`
  .${THEME_PREFIX_CLS}-picker {
    ${props =>
      props.errorStatus &&
      css`
        &,
        &:hover,
        &.${THEME_PREFIX_CLS}-picker-disabled {
          border-color: ${tokens.borderTokens.COLOR_BORDER_DANGER};
        }
      `}
  }
`;
