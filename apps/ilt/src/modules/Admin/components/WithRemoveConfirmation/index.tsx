import Modal from '@mindtickle/modal';

import { THEME_PREFIX_CLS } from '~/config/constants';

import MESSAGES from './messages';

const WithRemoveConfirmation = ({ callback, learners, learnersMap }: any) => {
  const titleText =
    learners.length > 1
      ? `Remove ${learners.length} selected learners from ILT?`
      : `Remove ${learnersMap[learners[0]]?.name} from ILT?`;

  const contentText = (
    <div>
      <div>
        {learners.length > 1
          ? MESSAGES.REMOVE_MULTIPLE_LEARNER_TEXT1
          : MESSAGES.REMOVE_SINGLE_LEARNER_TEXT1}
      </div>
      <div>
        {learners.length > 1
          ? MESSAGES.REMOVE_MULTIPLE_LEARNER_TEXT2
          : MESSAGES.REMOVE_SINGLE_LEARNER_TEXT2}
      </div>
    </div>
  );

  Modal.confirm({
    prefixCls: THEME_PREFIX_CLS,
    title: titleText,
    content: contentText,
    cancelButtonText: 'Cancel',
    okButtonText: 'Yes, remove',
    onOk: callback,
    size: 'small',
    isDestructiveAction: true,
    footerBtnProps: {
      okButtonProps: {
        type: 'danger',
      },
      cancelButtonProps: {
        type: 'text',
      },
    },
  });
};

export default WithRemoveConfirmation;
