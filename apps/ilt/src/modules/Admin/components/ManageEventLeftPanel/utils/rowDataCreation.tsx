import type { TILTEntities } from '~/modules/Admin/typeDefs';

import SessionStatusProcessor from '../../SessionStatusProcessor';
import EntityOperations from '../components/EntityOperations';
import EventOrSessionName from '../components/EventOrSessionName';
import { StyledLineContainer } from '../styles';

// TODO change the type of session variable info
export function createRowData({ event: entity, actions }: { event: any; actions: any }) {
  return {
    key: `${entity.id}`,
    sessionName: (
      <EventOrSessionName
        entityType={entity.entityType as TILTEntities}
        type={entity.type}
        name={entity.name}
        sessionStatus={SessionStatusProcessor(entity)}
        isPublished={entity.isPublished}
        dropdownComponent={
          <EntityOperations
            isSessionCancelled={entity.isCancelled}
            isSessionLive={entity.isOngoing}
            isSessionPublished={entity.isPublished}
            isSessionOver={entity.isOver}
            actions={actions}
            sessionId={entity.id}
            liveChallenge={entity.liveChallenge}
            isPartialStaticData={entity.isPartialStaticData}
            entityType={entity.entityType}
          />
        }
      />
    ),
    childSessions: entity.sessions,
    entityType: entity.entityType as TILTEntities,
    data: entity,
  };
}

export function createChildRowData({
  session: entity,
  actions,
  parentId,
  isParentCancelled = false,
}: {
  session: any;
  actions: any;
  parentId: string;
  isParentCancelled: boolean;
}) {
  return {
    key: `${entity.id}`,
    columnMinusFirst: '',
    columnZero: (
      <StyledLineContainer className="outer-line">
        <div className="inner-line" />
      </StyledLineContainer>
    ),
    sessionName: (
      <EventOrSessionName
        entityType={entity.entityType as TILTEntities}
        type={entity.type}
        name={entity.name}
        startTime={entity.startTime}
        sessionStatus={SessionStatusProcessor(entity)}
        isPublished={entity.isPublished}
        hasCheckbox={true}
        dropdownComponent={
          <EntityOperations
            isSessionCancelled={entity.isCancelled}
            isSessionLive={entity.isOngoing}
            isSessionPublished={entity.isPublished}
            isSessionOver={entity.isOver}
            actions={actions}
            sessionId={entity.id}
            liveChallenge={entity.liveChallenge}
            isPartialStaticData={entity.isPartialStaticData}
            entityType={entity.entityType}
            parentId={parentId}
            isParentCancelled={isParentCancelled}
          />
        }
      />
    ),
    isPublished: entity.isPublished,
  };
}
