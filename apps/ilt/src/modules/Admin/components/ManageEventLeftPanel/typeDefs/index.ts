import type { TILTEntities } from '~/modules/Admin/typeDefs';

import type { TTableEntityObject } from '../../SessionsTable/typeDefs';
import type { TTableActions } from '../../SessionsWrapper/typeDefs';

export interface TSessionsTable {
  data: any;
  actions: TTableActions;
  selectedId: string;
  status: {
    isLoading: boolean;
    loaded: boolean;
    hasError: boolean;
  };
  totalEventSessionsCount: number;
}

export interface TExpandedTableData {
  data: TTableEntityObject[];
  parentId: string;
  isParentCancelled: boolean;
  tableColumns: any;
  actions: TTableActions;
  selectedId: string;
}

export interface TEventOrSessionName {
  name: string;
  sessionStatus: string;
  hasCheckbox?: boolean;
  startTime?: string;
  entityType: TILTEntities;
  type: string;
  dropdownComponent: React.ReactNode;
  isPublished?: boolean;
}

export interface TSessionOperations {
  isSessionOver: boolean;
  isSessionLive: boolean;
  isSessionPublished: boolean;
  isSessionCancelled: boolean;
  isParentCancelled?: boolean;
  actions: TTableActions;
  sessionId: string;
  liveChallenge: any;
  isPartialStaticData: boolean;
  entityType: TILTEntities;
  parentId?: string;
}

export interface TAddUploadSession {
  disableAddSession: boolean;
  disableUploadSession: boolean;
  onAddSession: () => void;
  onUploadSession: () => void;
}
