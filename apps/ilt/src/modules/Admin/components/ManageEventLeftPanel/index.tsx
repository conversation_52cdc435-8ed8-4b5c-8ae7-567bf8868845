import { useEffect, useMemo, useState } from 'react';

import cs from 'classnames';
import queryString from 'query-string';
import { useNavigate } from 'react-router-dom';

import Icon, { ICON_MAP } from '@mindtickle/icon';
import IconWithGradient from '@mindtickle/icon-with-gradient';
import Loader from '@mindtickle/loader';
import { noop } from '@mindtickle/utils';

import { MAX_SESSIONS_WITHIN_EVENT } from '~/modules/Admin/constants/events';
import { ILT_ENTITIES } from '~/modules/Admin/constants/module';
import { checkIsEvent } from '~/modules/Admin/utils/checkEntityType';
import { CREATE_SESSION_QUERY_PARAM, OPEN_DRAWER_QUERY_PARAM } from '~/utils/constants';

import AddUploadSession from './components/AddUploadSession';
import EventSessionsTable from './components/EventSessionsTable';
import {
  StyledIconCheckbox,
  Styled<PERSON>ine,
  StyledLineWrapper,
  StyledNoDataWrapper,
  StyledManageEventLeftPanel,
  StyledTable,
} from './styles';
import {
  getManageEventLeftPanelTableColumns,
  getManageEventLeftPanelTableColumnsWithChildren as getSessionTableColumnsWithChild,
} from './utils';
import { createRowData } from './utils/rowDataCreation';

import type { TSessionsTable } from './typeDefs';

const ManageEventLeftPanel = (props: TSessionsTable) => {
  const [openDrawerTriggered, setOpenDrawerTriggered] = useState(false);
  const { data, actions, selectedId, status, totalEventSessionsCount } = props;
  const loading = !status.loaded;
  const navigate = useNavigate();
  const { uploadSessionsWithinEvent, createSessionWithinEvent } = actions;
  const tableDataNew = [];
  const isSessionCountBreached = totalEventSessionsCount === MAX_SESSIONS_WITHIN_EVENT;
  const sessionsTableColumns = getManageEventLeftPanelTableColumns();
  const sessionTableColumnsWithChild = getSessionTableColumnsWithChild();

  const event = data[0];
  if (event) {
    tableDataNew.push(
      createRowData({
        event,
        actions,
      })
    );
  }

  useEffect(() => {
    const queryParams = queryString.parse(window.location.search);
    const openDrawer = queryParams?.[OPEN_DRAWER_QUERY_PARAM];
    if (!openDrawerTriggered && event && openDrawer && openDrawer === CREATE_SESSION_QUERY_PARAM) {
      createSessionWithinEvent({ parentId: event.id });
      setOpenDrawerTriggered(true);
    }
  }, [createSessionWithinEvent, event, openDrawerTriggered]);

  const rowSelection = useMemo(
    () => ({
      renderCell: (_checked: boolean, record: any) => (
        <StyledIconCheckbox className="render-cell-icon-checkbox-container" key={record.key}>
          <Icon
            className="render-cell-icon"
            type={checkIsEvent(record.entityType) ? ICON_MAP.EVENT : ICON_MAP.SESSION}
          />
          {checkIsEvent(record.entityType) && (
            <StyledLineWrapper className="header-line-wrapper">
              <StyledLine className="header-line" />
            </StyledLineWrapper>
          )}
        </StyledIconCheckbox>
      ),
      columnWidth: 32,
    }),
    []
  );

  if (loading) {
    return (
      <StyledManageEventLeftPanel className={'with-full-loader'} loading={loading}>
        <Loader size="sizeSmall" />
      </StyledManageEventLeftPanel>
    );
  }

  return (
    <StyledManageEventLeftPanel loading={loading}>
      <AddUploadSession
        onAddSession={() => {
          createSessionWithinEvent({ parentId: event.id });
        }}
        onUploadSession={() => {
          uploadSessionsWithinEvent({ parentId: event.id });
        }}
        disableAddSession={event.isCancelled || isSessionCountBreached}
        disableUploadSession={event.isCancelled || isSessionCountBreached}
      />
      <StyledTable
        dataSource={tableDataNew}
        columns={sessionsTableColumns}
        className="multiday-table"
        hasSeparators={false}
        showHeader={false}
        onRow={() => ({
          onClick: () => {
            navigate(`.`);
          },
        })}
        rowClassName={(record: any) =>
          cs({
            selected: selectedId === record.key,
          })
        }
        expandable={{
          expandedRowRender: (record: {
            childSessions: any[];
            key: string;
            data: { isCancelled?: boolean };
          }) => (
            <EventSessionsTable
              tableColumns={sessionTableColumnsWithChild}
              data={record.childSessions}
              key={record.key}
              parentId={record.key}
              isParentCancelled={record.data.isCancelled || false}
              actions={actions}
              selectedId={selectedId}
            />
          ),
          columnWidth: 0,
          rowExpandable: (record: any) => record.entityType === ILT_ENTITIES.EVENT,
          showExpandColumn: true,
          columnTitle: <Icon type={'collapseAllExpanded'} className="collapse-icon" />,
          expandIcon: () => {},
        }}
        emptyTableData={
          <StyledNoDataWrapper>
            {!loading && (
              <div>
                <IconWithGradient gradient={true} type="emptySessions" className="no-data-icon" />
                <div className={'no-result-text'}>{'No results found'}</div>
              </div>
            )}
          </StyledNoDataWrapper>
        }
        defaultExpandAllRows={true}
        rowSelection={rowSelection}
        loaderType={'spin'}
        infiniteScroll={true}
        isMultiSelect={true}
        fetchData={noop}
        hasMore={false}
        threshold={0.9}
        pagination={false}
        loading={!!loading}
        loadingMore={false}
        windowScroll={true}
        scroll={undefined}
        footer={null}
      />
    </StyledManageEventLeftPanel>
  );
};

export default ManageEventLeftPanel;
