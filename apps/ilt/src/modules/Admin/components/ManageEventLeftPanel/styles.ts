import styled from 'styled-components';

import { tokens, mixins } from '@mindtickle/styles/lib';
import Table from '@mindtickle/table';

import { ADMIN_NAVBAR_HEIGHT, THEME_PREFIX_CLS, EVENT_TABLE_ROW_HEIGHT } from '~/config/constants';

const LEFT_PANEL_TABLE_HEIGHT = `calc(100vh - ${ADMIN_NAVBAR_HEIGHT + EVENT_TABLE_ROW_HEIGHT}px)`;

export const StyledManageEventLeftPanel = styled.div<{ loading: boolean }>`
  position: relative;
  width: 100%;
  height: 100%;
  display: inline-flex;
  flex-direction: column;
  border-right: 1px solid ${tokens.borderTokens.COLOR_BORDER_DEFAULT};

  .${THEME_PREFIX_CLS}-table-body {
    &::after {
      display: none;
    }
  }

  .${THEME_PREFIX_CLS}-table-sticky-header {
    top: ${props => (props.loading ? 0 : ADMIN_NAVBAR_HEIGHT)}px !important;
    position: sticky !important;
    z-index: 2;
  }
  &.with-full-loader {
    border-top: 1px solid ${tokens.borderTokens.COLOR_BORDER_DEFAULT};
    min-height: 50vh;
  }
`;

export const StyledNoDataWrapper = styled.div`
  padding: 200px 0;

  .no-data-icon {
    margin: 0 auto;
  }

  .no-result-text {
    ${mixins.blackLink}
    text-align: center;
    padding: 1px 0 0 0;
    letter-spacing: 0;
    margin-top: 20px;
  }
`;

export const StyledLineWrapper = styled.div`
  display: flex;
  justify-content: center;
  position: absolute;
  width: 14px;
  height: 30px;
  top: 22px;

  .header-line {
    background-color: ${tokens.bgTokens.COLOR_BG_DISABLED};
    border: 0px;
    border-radius: 0px;
    height: 30px;
    top: 0;
    left: 0;
    width: 1px;
    position: relative;
    transform: translateX(-50%);
  }
`;

export const StyledLine = styled.div`
  border-left: 1px solid ${tokens.borderTokens.COLOR_BORDER_DEFAULT};
  border-bottom: 1px solid ${tokens.borderTokens.COLOR_BORDER_DEFAULT};
  border-radius: 0 4px;
  width: 12px;
  height: 72px;
  position: absolute;
  top: -42px;
  left: 22px;
  z-index: 1;
`;

export const StyledLineContainer = styled.div`
  border-left: 1px solid ${tokens.borderTokens.COLOR_BORDER_DEFAULT};
  width: 14px;
  height: calc(100% + 16px);
  position: absolute;
  top: -10px;
  left: 23px;
  z-index: 1;

  .inner-line {
    width: 12px;
    border-bottom: 1px solid ${tokens.borderTokens.COLOR_BORDER_DEFAULT};
    border-left: 1px solid ${tokens.borderTokens.COLOR_BORDER_DEFAULT};
    position: absolute;
    top: 50%;
    left: -1px;
    transform: translateY(calc(-50% - 3px));
    border-radius: 0 0 0 4px;
    height: 10px;
  }
`;

export const StyledIconCheckbox = styled.div`
  display: flex;
  justify-content: center;
  position: relative;
  height: 16px;
  width: 16px;
  .render-cell-icon {
    font-size: 16px;
    color: ${tokens.iconTokens.COLOR_ICON_DEFAULT};
  }
`;

export const StyledTable = styled(Table)`
  &.multiday-table {
    z-index: 1;
    top: ${ADMIN_NAVBAR_HEIGHT}px;
    position: sticky;

    .${THEME_PREFIX_CLS}-table table {
      border-collapse: collapse;
    }

    .${THEME_PREFIX_CLS}-table-row {
      &.${THEME_PREFIX_CLS}-table-row-level-0 {
        cursor: pointer;

        &:last-child {
          .outer-line {
            border-bottom: 1px solid ${tokens.borderTokens.COLOR_BORDER_DEFAULT};
            height: calc(50% + 8px);
            border-radius: 0 0 0 4px;

            .inner-line {
              display: none;
            }
          }
        }

        &.selected {
          background-color: ${tokens.bgTokens.COLOR_BG_ACCENT_SELECTED};

          .session-or-event-name-tooltip {
            font-weight: 600;
          }
          .more-options-menu {
            display: flex;
          }
        }
        &:hover {
          .more-options-menu {
            display: flex;
          }
        }

        &.${THEME_PREFIX_CLS}-table-row-selected {
          td.${THEME_PREFIX_CLS}-table-column-sort {
            background-color: ${tokens.bgTokens.COLOR_BG_ACCENT_SELECTED};
          }
        }
      }

      .${THEME_PREFIX_CLS}-table-expanded-row {
        .${THEME_PREFIX_CLS}-table-cell {
          padding: 6px 0px 6px 12px;
          border-radius: 0;
        }
      }

      td.${THEME_PREFIX_CLS}-table-column-sort {
        background-color: transparent;
      }

      .${THEME_PREFIX_CLS}-table-cell {
        border-radius: 0;
      }
    }

    .${THEME_PREFIX_CLS}-table-row-expand-icon-cell {
      padding-right: 12px;
      padding-left: 24px;
    }

    .${THEME_PREFIX_CLS}-table-cell {
      padding-right: 0;

      &.${THEME_PREFIX_CLS}-table-selection-column {
        padding: 0;
        padding-left: 16px;
      }
    }

    .${THEME_PREFIX_CLS}-table-expanded-row.${THEME_PREFIX_CLS}-table-expanded-row-level-1 {
      .${THEME_PREFIX_CLS}-table-cell {
        padding: 0;
      }

      .expanded-table {
        .${THEME_PREFIX_CLS}-table-row.${THEME_PREFIX_CLS}-table-row-level-0 {
          .${THEME_PREFIX_CLS}-table-cell {
            padding: 6px 0 6px 12px;
            border-radius: 0;
          }

          &.selected {
            background-color: ${tokens.bgTokens.COLOR_BG_ACCENT_SELECTED};

            .session-or-event-name-tooltip {
              font-weight: 600;
            }

            .more-options-menu {
              display: flex;
            }
          }

          &:hover {
            .more-options-menu {
              display: flex;
            }
          }

          &.${THEME_PREFIX_CLS}-table-row-selected {
            td.${THEME_PREFIX_CLS}-table-column-sort {
              background-color: ${tokens.bgTokens.COLOR_BG_ACCENT_SELECTED};
            }
          }
        }

        .${THEME_PREFIX_CLS}-table-container {
          max-height: ${LEFT_PANEL_TABLE_HEIGHT};
          overflow: auto;
          border-radius: 0;
        }
      }
    }
  }
`;
