import styled from 'styled-components';

import PrimaryButton from '@mindtickle/button/lib/PrimaryButton';
import SecondaryButton from '@mindtickle/button/lib/SecondaryButton';
import { tokens } from '@mindtickle/styles/lib';

export const StyledPrimaryButton = styled(PrimaryButton)``;

export const StyledSecondaryButton = styled(SecondaryButton)``;

export const StyledAddUploadSession = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 24px;
  border-top: 1px solid ${tokens.borderTokens.COLOR_BORDER_DEFAULT};
  border-bottom: 1px solid ${tokens.borderTokens.COLOR_BORDER_DEFAULT};
`;
