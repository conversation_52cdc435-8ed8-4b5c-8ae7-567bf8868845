import { injectIntl } from 'react-intl';

import Icon, { ICON_MAP } from '@mindtickle/icon';

import { getEntitiesOperationList } from '~/modules/Admin/components/SessionsTable/utils/getEntitiesOperationList';
import { getBodyAsPopupContainer } from '~/utils';

import { StyledDropdown } from './styles';

import type { TSessionOperations } from '../../typeDefs';
import type { InjectedIntlProps } from 'react-intl';

const EntityOperations = (props: TSessionOperations & InjectedIntlProps) => {
  const {
    isSessionOver,
    isSessionLive,
    isSessionPublished,
    isSessionCancelled,
    isParentCancelled = false,
    actions,
    sessionId,
    liveChallenge,
    isPartialStaticData,
    entityType,
    parentId,
    intl,
  } = props;
  const menuConfig = getEntitiesOperationList({
    isSessionOver,
    isSessionLive,
    isSessionPublished,
    isSessionCancelled,
    isParentCancelled,
    actions,
    sessionId,
    liveChallenge,
    isPartialStaticData,
    entityType,
    parentId,
    intl,
    isEventManagePage: true,
  });
  return (
    <StyledDropdown
      placement="bottomLeft"
      getPopupContainer={getBodyAsPopupContainer}
      className="more-options-dropdown"
      actionsConfig={menuConfig}
      TriggerComponent={<Icon className="more-options-menu" type={ICON_MAP.MORE_VERTICAL2} />}
    />
  );
};

export default injectIntl(EntityOperations);
