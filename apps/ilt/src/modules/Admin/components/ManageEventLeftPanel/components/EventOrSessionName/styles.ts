import styled from 'styled-components';

import { mixins, tokens } from '@mindtickle/styles/lib';

import { THEME_PREFIX_CLS } from '~/config/constants';

export const StyledIconCheckbox = styled.div`
  display: flex;
  justify-content: center;
  position: relative;
  height: 16px;
  width: 16px;

  .child-checkbox .${THEME_PREFIX_CLS}-checkbox {
    top: 0;
  }
  .render-cell-icon {
    color: ${tokens.iconTokens.COLOR_ICON_DEFAULT};
    font-size: 16px;
    &.icon-session {
      font-size: 15px;
    }
  }
`;

export const StyledColumnEventOrSessionNameContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;

  .more-options-dropdown {
    position: absolute;
    right: 12px;

    .${THEME_PREFIX_CLS}-dropdown-trigger {
      &.${THEME_PREFIX_CLS}-dropdown-open .more-options-menu {
        display: flex;
      }
    }
  }

  .more-options-menu {
    font-size: 16px;
    cursor: pointer;
    color: ${tokens.textTokens.COLOR_TEXT_DEFAULT};
    display: none;

    &.${THEME_PREFIX_CLS}-dropdown-open {
      display: flex;
    }

    &:before {
      display: flex;
      width: 16px;
      justify-content: center;
    }
  }
`;

export const StyledEventOrSessionInfoContainer = styled.div`
  ${mixins.smallDarkLink};
  display: flex;
  align-items: center;
  gap: 4px;
  margin-top: 4px;

  .icon-style {
    font-size: 10px;
    color: ${tokens.iconTokens.COLOR_ICON_DEFAULT};
  }
`;

export const StyledEventOrSessionName = styled.div`
  ${mixins.blackText};
  text-align: left;

  .session-or-event-name-tooltip {
    word-break: break-word;
    max-width: calc(100% - 28px);
  }
`;
