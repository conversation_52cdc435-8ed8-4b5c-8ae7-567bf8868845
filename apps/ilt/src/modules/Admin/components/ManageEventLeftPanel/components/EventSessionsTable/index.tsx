import cs from 'classnames';
import { useNavigate } from 'react-router-dom';

import Table from '@mindtickle/table';

import { createChildRowData } from '../../utils/rowDataCreation';

import type { TExpandedTableData } from '../../typeDefs';

const ExpandedTableData = (props: TExpandedTableData) => {
  const navigate = useNavigate();
  const { data, selectedId, tableColumns, actions, parentId, isParentCancelled } = props;

  const childrenTableData = data.reduce((acc, session) => {
    acc.push(
      createChildRowData({
        session,
        actions,
        parentId,
        isParentCancelled,
      })
    );
    return acc;
  }, [] as any[]);
  return (
    <Table
      columns={tableColumns}
      showHeader={false}
      dataSource={childrenTableData}
      isMultiSelect={false}
      hasSeparators={false}
      pagination={false}
      tableLayout="fixed"
      className="expanded-table"
      rowClassName={(record: any) =>
        cs({
          selected: record.key === selectedId,
        })
      }
      onRow={(record: any) => ({
        onClick: () => {
          navigate(`session/${record.key}`);
        },
      })}
    />
  );
};

export default ExpandedTableData;
