import { FormattedMessage } from 'react-intl';

import EllipsisTooltip from '@mindtickle/ellipsis-tooltip';
import Icon, { ICON_MAP } from '@mindtickle/icon';
import Tooltip from '@mindtickle/tooltip';

import { ILT_ENTITIES } from '~/modules/Admin/constants/module';
import entityTypeLabels from '~/modules/Admin/messages/iltEntityTypes';
import { sessionDateTimeFormatter } from '~/modules/Admin/utils';
import {
  getEntityFormatTypeIcon,
  getEntityFormatTypeIconTooltip,
} from '~/modules/Admin/utils/entityFormatType';

import {
  StyledColumnEventOrSessionNameContainer,
  StyledIconCheckbox,
  StyledEventOrSessionInfoContainer,
  StyledEventOrSessionName,
} from './styles';

import type { TEventOrSessionName } from '../../typeDefs';

const EventOrSessionName = ({
  name,
  entityType,
  type,
  sessionStatus,
  hasCheckbox,
  startTime,
  dropdownComponent,
  isPublished,
}: TEventOrSessionName) => {
  const isEvent = entityType === ILT_ENTITIES.EVENT;
  const entityTypeLabel = isEvent ? entityTypeLabels.EVENT : entityTypeLabels.SESSION;

  const renderContent = (
    <StyledColumnEventOrSessionNameContainer>
      {hasCheckbox && (
        <StyledIconCheckbox className="icon-checkbox-container">
          <Icon className="render-cell-icon" type={ICON_MAP.SESSION} />
        </StyledIconCheckbox>
      )}
      <div>
        <StyledEventOrSessionName>
          <EllipsisTooltip
            wrapperClassName="session-or-event-name-tooltip"
            placement="top"
            title={name}
            showTooltipWhenEllipsis={true}
            linesToClamp={2}
            getPopupContainer={() => document.body}
          >
            {name}
          </EllipsisTooltip>
        </StyledEventOrSessionName>
        <StyledEventOrSessionInfoContainer>
          <Tooltip title={<FormattedMessage {...getEntityFormatTypeIconTooltip(type)} />}>
            <Icon type={getEntityFormatTypeIcon(type)} className={'icon-style'} />
          </Tooltip>
          {!isEvent ? (
            sessionDateTimeFormatter(startTime)
          ) : (
            <FormattedMessage {...entityTypeLabel} />
          )}
          {sessionStatus}
        </StyledEventOrSessionInfoContainer>
      </div>
      {dropdownComponent}
    </StyledColumnEventOrSessionNameContainer>
  );
  return renderContent;
};

export default EventOrSessionName;
