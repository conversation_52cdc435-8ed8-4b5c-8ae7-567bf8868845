import { FormattedMessage } from 'react-intl';

import Icon, { ICON_MAP } from '@mindtickle/icon';

import messages from '../../messages';

import { StyledAddUploadSession, StyledPrimaryButton, StyledSecondaryButton } from './styles';

import type { TAddUploadSession } from '../../typeDefs';

const AddUploadSession = ({
  onAddSession,
  onUploadSession,
  disableAddSession,
  disableUploadSession,
}: TAddUploadSession) => (
  <StyledAddUploadSession>
    <StyledPrimaryButton
      onClick={onAddSession}
      disabled={disableAddSession}
      className="add-session"
    >
      <FormattedMessage {...messages.ADD_SESSION} />
    </StyledPrimaryButton>
    <StyledSecondaryButton onClick={onUploadSession} disabled={disableUploadSession}>
      <Icon className="upload-session-icon" type={ICON_MAP.UPLOADNEWLIST} />
    </StyledSecondaryButton>
  </StyledAddUploadSession>
);

export default AddUploadSession;
