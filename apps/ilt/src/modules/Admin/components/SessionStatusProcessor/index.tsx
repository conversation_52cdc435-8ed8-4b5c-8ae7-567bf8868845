import over from 'lodash/over';
import reduce from 'lodash/reduce';

import { BG_SHADE, STATUS_TYPE } from '@mindtickle/badge';

import { SESSION_STATUS_TYPE_LABELS } from '~/modules/Admin/config/sessions.constants';
import {
  StyledStyledUnpublishedSessionToken,
  StyledCancelledSessionToken,
  StyledPastSessionToken,
  StyledUpcomingSessionToken,
  StyledLiveSession,
} from '~/modules/Admin/styles/sessionStatus';

const getStatusUnpublished = (data: any) => {
  const { isPublished } = data;
  if (!isPublished) {
    return (
      <StyledStyledUnpublishedSessionToken bgShade={BG_SHADE.LIGHT} status={STATUS_TYPE.WARNING}>
        {SESSION_STATUS_TYPE_LABELS.UNPUBLISHED}
      </StyledStyledUnpublishedSessionToken>
    );
  }
  return false;
};

const getStatusCancelled = (data: any) => {
  const { isCancelled } = data;
  if (isCancelled) {
    return (
      <StyledCancelledSessionToken bgShade={BG_SHADE.LIGHT} status={STATUS_TYPE.STOP}>
        {SESSION_STATUS_TYPE_LABELS.CANCELLED}
      </StyledCancelledSessionToken>
    );
  }
  return false;
};

const getStatusOver = (data: any) => {
  const { isOver } = data;
  if (isOver) {
    return (
      <StyledPastSessionToken bgShade={BG_SHADE.LIGHT} status={STATUS_TYPE.PROCESSING}>
        {SESSION_STATUS_TYPE_LABELS.PAST}
      </StyledPastSessionToken>
    );
  }
  return false;
};

const getStatusUpcoming = (data: any) => {
  const { isUpcoming } = data;
  if (isUpcoming) {
    return (
      <StyledUpcomingSessionToken bgShade={BG_SHADE.LIGHT} status={STATUS_TYPE.SUCCESS}>
        {SESSION_STATUS_TYPE_LABELS.UPCOMING}
      </StyledUpcomingSessionToken>
    );
  }
  return false;
};

const getStatusLive = () => (
  <StyledLiveSession>
    <div className={'dot'} />
    <div className={'live-text'}>{SESSION_STATUS_TYPE_LABELS.LIVE}</div>
    <div className="clear" />
  </StyledLiveSession>
);

const getSessionStatusHelper = over(
  getStatusUnpublished,
  getStatusCancelled,
  getStatusOver,
  getStatusUpcoming,
  getStatusLive
);

const getSessionStatusIterator = (result: any, node: any) => {
  if (!result) {
    return node;
  }
  return result;
};

const SessionStatusProcessor = (session: any) => {
  const sessionStatus = getSessionStatusHelper(session);
  return reduce(sessionStatus, getSessionStatusIterator);
};

export default SessionStatusProcessor;
