export function flexContainer(
  options: {
    readonly flexDirection?: 'column' | 'row';
    readonly alignItems?: 'center' | 'flex-start' | 'flex-end' | 'baseline';
    readonly justifyContent?:
      | 'center'
      | 'flex-start'
      | 'flex-end'
      | 'space-between'
      | 'space-around';
  } = {}
) {
  return `
      display: flex;
      flex-direction: ${options.flexDirection || 'row'};
      justify-content: ${options.justifyContent || 'flex-start'};
      align-items: ${options.alignItems || 'stretch'};
    `;
}
