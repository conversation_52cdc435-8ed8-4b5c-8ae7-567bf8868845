import moment from 'moment-timezone';

import { timezonesList, oldTimezonesList } from '~/mt-timezones';

export const getTimezoneFromString = timezoneString => {
  const tzObj = JSON.parse(timezoneString);

  const timezoneOffset = tzObj.offset;
  const timezone = {
    displayName: tzObj.name,
    hasdst: false,
    isdst: false,
    name: tzObj.name,
    offset: tzObj.offset / (1000 * 60 * 60),
    region: tzObj.id.indexOf('/') ? tzObj.id.split('/')[1] : 'Unknown',
    shortDisplayName: tzObj.name.split(' ')[0],
    originalTimezoneString: timezoneString,
    id: tzObj.id,
  };
  return { timezoneOffset, timezone };
};

const getTimezoneDetails = () => {
  const result = [];
  timezonesList.forEach(timezone => {
    let offsetStr = moment
      .utc()
      .startOf('day')
      .add(Math.abs(timezone.offset * 60), 'minutes')
      .format('hh:mm');
    offsetStr =
      timezone.offset < 0 ? `-${offsetStr}` : timezone.offset > 0 ? `+${offsetStr}` : '+00:00';

    timezone.utc.forEach(region => {
      if (region.indexOf('/') > -1) region = region.split(/\/(.+)/)[1];
      region = region.replace('_', ' ');

      let regionDisplayName = `(GMT${offsetStr} ${timezone.value} - ${timezone.abbr} ) ${region}`;

      result.push({
        region: region,
        offset: timezone.offset,
        isdst: timezone.isdst,
        hasdst: timezone.hasdst ? true : false,
        code: timezone.abbr,
        name: timezone.value,
        displayName: regionDisplayName,
        shortDisplayName: `GMT ${offsetStr}`,
      });
    });
  });
  return result.sort((a, b) => a.offset - b.offset);
};

const getOldTimezoneDetails = () => {
  const result = [];
  oldTimezonesList.forEach(timezone => {
    let offsetStr = moment
      .utc()
      .startOf('day')
      .add(Math.abs(timezone.offset * 60), 'minutes')
      .format('hh:mm');
    offsetStr = timezone.offset < 0 ? `-${offsetStr}` : `+${offsetStr}`;

    timezone.utc.forEach(region => {
      result.push({
        region: region,
        offset: timezone.offset,
        isdst: timezone.isdst,
        code: timezone.abbr,
        name: timezone.value,
        displayName: `${region} (${offsetStr})`,
        shortDisplayName: `GMT ${offsetStr}`,
      });
    });
  });
  return result.sort((a, b) => a.offset - b.offset);
};

export const getTimezoneDetailsByKey = (key, value) => {
  if (!key) return {};
  if (value[0] === '(' || key === 'offset') {
    // eslint-disable-next-line eqeqeq
    return getTimezoneDetails().find(tz => tz[key] == value);
  } else {
    // eslint-disable-next-line eqeqeq
    return getOldTimezoneDetails().find(tz => tz[key] == value);
  }
};

const getOffsetDifference = (timestamp, offset) => {
  const time = moment(timestamp);
  if (time.isDST()) {
    while (!time.isDST()) {
      time.subtract(1, 'month');
    }
  }
  const currentOffset = time.utcOffset() * 60000;
  return currentOffset - offset;
};

export const addOffsetDiff = (timestamp, offset) => {
  if (offset !== 0 && !offset) return timestamp;
  offset = getOffsetDifference(timestamp, offset);
  return new Date(timestamp + offset).getTime();
};

export const subtractOffsetDiff = (timestamp, offset) => {
  if (offset !== 0 && !offset) return timestamp;
  offset = getOffsetDifference(timestamp, offset);
  return new Date(timestamp - offset).getTime();
};

export const offsetRemovedTimestamp = (timestamp, timezoneOffsetInHours = 0) => {
  const browserOffsetInMilliseconds = new Date().getTimezoneOffset() * 60 * 1000;
  const timezoneOffsetInMilliseconds = timezoneOffsetInHours * 3600 * 1000;
  return timestamp - browserOffsetInMilliseconds - timezoneOffsetInMilliseconds;
};

export const getCurrTimeWithOffset = offset => {
  let d = new Date();
  let utc = d.getTime() + d.getTimezoneOffset() * 60000;
  let nd = new Date(utc + 3600000 * offset);
  return nd.getTime();
};
