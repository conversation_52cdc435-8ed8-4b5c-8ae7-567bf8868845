import ReactDOM from 'react-dom';

import Toast, { TOAST_TYPES } from '@mindtickle/toast';

const TOAST_DIV_ID = 'toast';

const getClonedToastElement = (() => {
  let toastElement;
  return id => {
    if (!toastElement) {
      toastElement = document.getElementById(TOAST_DIV_ID);
    }
    const clonedToastNode = toastElement.cloneNode();
    clonedToastNode.id = id;
    return clonedToastNode;
  };
})();

export function createToast({ options, mountOn, onHideCalled }) {
  const show = ({ message, type = TOAST_TYPES.SUCCESS, freeze, ...rest }) => {
    if (!mountOn) {
      return;
    }
    if (message) {
      setTimeout(() => {
        mountOn &&
          ReactDOM.render(
            <Toast message={message} type={type} onHide={hideToast} {...rest} freeze={freeze} />,
            mountOn
          );
      }, 0);
    }
  };

  function hideToast() {
    if (!mountOn) {
      return;
    }
    ReactDOM.render(null, mountOn);
    setTimeout(onHideCalled, 0);
  }
  show(options);
}

export function multipleToast({ messages = [], toastStartPosition = 0 } = {}) {
  const TOAST_GAP = 44;
  messages.forEach((value, index) => {
    const _clonedToastElement = getClonedToastElement(`_multipleToastDiv${index}`);
    _clonedToastElement.style.marginTop = toastStartPosition * TOAST_GAP + index * TOAST_GAP + 'px';
    document.body.appendChild(_clonedToastElement);

    createToast({
      options: value,
      mountOn: _clonedToastElement,
      onHideCalled: () => document.body.removeChild(_clonedToastElement),
    });
  });
}
