import queryString from 'query-string';

import { MT_MODULES } from '~/config/global.config';
import type { TEntityWithBasicInfo } from '~/modules/Admin/components/SessionsWrapper/typeDefs';
import { ILT_ENTITY_LEARNER_URL_IDENTIFIERS } from '~/modules/Admin/constants/module';
import { checkIsEvent } from '~/modules/Admin/utils/checkEntityType';

const typeUrlMap = {
  [MT_MODULES.ILT]: 'new/ui/learner/ilt',
};

export const getSeriesUrl = (seriesId: string, isNewDashboard?: boolean) => {
  if (!seriesId) return null;

  if (isNewDashboard) return `/new/ui/admin/program/${seriesId}/modules`;

  return ['dashboard#series', seriesId, 'all'].join('/');
};

export const getModuleUrl = (
  lsDomain: string,
  type: string,
  moduleId: string,
  seriesId: string
) => {
  const partialUrl = typeUrlMap[type];
  if (lsDomain && partialUrl && moduleId && seriesId) {
    return [`${window.location.protocol}/`, lsDomain, partialUrl, seriesId, moduleId].join('/');
  }
  return;
};

export const getIltSessionUrl = (
  { companyUrl, moduleType, moduleId, seriesId }: any,
  { id, entityType }: TEntityWithBasicInfo
) => {
  const moduleUrl = getModuleUrl(companyUrl, moduleType, moduleId, seriesId);
  if (moduleUrl && id) {
    return [
      moduleUrl,
      checkIsEvent(entityType)
        ? ILT_ENTITY_LEARNER_URL_IDENTIFIERS.EVENT
        : ILT_ENTITY_LEARNER_URL_IDENTIFIERS.SESSION,
      id,
    ].join('/');
  }
  return;
};

export const getUserProfileUrl = (userId: string) => {
  // eslint-disable-next-line no-restricted-globals
  const domain = location.origin;
  return `${domain}/ui/user/${userId}`;
};

export const getRemoteUrl = (path: string) =>
  `${path}/${process.env.FEDERATION_REMOTE_FILE_NAME}.js`;

const getFinalAnalyticsUrl = (path: string, query: any) => {
  if (path[0] !== '/') {
    path = `\${path}`;
  }

  if (path.indexOf('?') !== -1) {
    throw new Error('pass path without query params');
  }

  const urlPath = `/analytics${path}`;
  if (!query || Object.keys(query).length === 0) {
    return urlPath;
  } else {
    const linkQueryString = queryString.stringify(query, { arrayFormat: 'bracket' });
    return `${urlPath}?${linkQueryString}`;
  }
};

export const getIltAnalyticsUrl = ({
  seriesId,
  moduleId,
  moduleName,
}: {
  seriesId: string;
  moduleId: string;
  moduleName: string;
}) => {
  const query = {
    gameType: 'ilt',
    inSeries: [seriesId],
    name: moduleName,
  };
  return getFinalAnalyticsUrl(`/modules/${moduleId}/ilt/overview`, query);
};
