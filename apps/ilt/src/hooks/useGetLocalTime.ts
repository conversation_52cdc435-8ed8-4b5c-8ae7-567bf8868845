import { useState, useEffect } from 'react';

import TimezonesService from '~/modules/Admin/api/timezonesService';

interface ITimezone {
  local_time: Array<string>;
}
export function useGetLocalTime({ epoch, timezoneId }: { epoch: number; timezoneId: string }) {
  const [localTime, setLocalTime] = useState<ITimezone | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<any>(null);

  useEffect(() => {
    const fetchLocalTime = async () => {
      try {
        setLoading(true);
        if (!epoch || !timezoneId) {
          return;
        }
        const data = await TimezonesService.getLocalTime<ITimezone>({ timezoneId, epoch });
        setLocalTime(data);
        setLoading(false);
      } catch (error) {
        setError(error);
        setLoading(false);
      }
    };
    fetchLocalTime();
  }, [epoch, timezoneId]);

  return { localTime, loading, error };
}
