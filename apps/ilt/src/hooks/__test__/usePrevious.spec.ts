import { renderHook } from '@testing-library/react-hooks';

import usePrevious from '../usePrevious';

describe('usePrevious hook', () => {
  it('returns the previous value', () => {
    const { result, rerender } = renderHook(({ value }) => usePrevious(value), {
      initialProps: { value: 'initial' },
    });

    expect(result.current).toBeUndefined();

    rerender({ value: 'updated' });

    expect(result.current).toBe('initial');
  });
});
