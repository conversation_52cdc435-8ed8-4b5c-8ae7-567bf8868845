import { useCallback, useMemo } from 'react';
import type { Dispatch, SetStateAction } from 'react';

import { useNavigate, useLocation } from 'react-router-dom';

import { addQuery, parseQuery, removeQuery } from '@mindtickle/utils/url';
type ReturnType<T> = [T, Dispatch<SetStateAction<T>>];

function updateUrl(url: string, navigate: (arg0: string) => void) {
  const path = url.split(window.location.origin)[1];
  navigate(path);
}

function setValueInUrl<T>(navigate: any, key: string, value: T) {
  const newUrl = addQuery(window.location.href, { [key]: JSON.stringify(value) });
  updateUrl(newUrl, navigate);
}

function getValueFromUrl(location: any, key: string) {
  return JSON.parse(parseQuery(location.search, key)[key] || JSON.stringify(''));
}

function removeKeyFromUrl<T>(navigate: any, key: string, value: T) {
  const newUrl = removeQuery(window.location.href, { [key]: JSON.stringify(value) });
  updateUrl(newUrl, navigate);
}

export default function useURLAsState<T>(key: string, defaultValue: object) {
  const location = useLocation();
  const navigate = useNavigate();

  const state: T = useMemo(
    () => getValueFromUrl(location, key) || defaultValue,
    [location, key, defaultValue]
  );

  const setState = useCallback(
    (value: T) => setValueInUrl<T>(navigate, key, value),
    [key, navigate]
  );

  const unmount = useCallback(() => removeKeyFromUrl(navigate, key, state), [navigate, key, state]);

  return [state, setState, unmount];
}

export const createWithUseURLAsState =
  ({ key, defaultValue }: { key: string; defaultValue: object }) =>
  (WrappedComponent: React.ComponentType<any>) =>
  (props: any) => {
    const withFiltersAsState = (WrappedComponent: React.ComponentType<any>) => {
      // eslint-disable-next-line react-hooks/rules-of-hooks
      const [state, updateState] = useURLAsState(key, defaultValue);
      props = {
        [key]: state,
        [`update${key}`]: updateState,
        ...props,
      };
      return <WrappedComponent {...props} />;
    };
    return withFiltersAsState(WrappedComponent);
  };
